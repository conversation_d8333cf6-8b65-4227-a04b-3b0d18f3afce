defmodule ServiceManagerWeb.UserAuth do
  use ServiceManagerWeb, :verified_routes

  import Plug.Conn
  import Phoenix.Controller

  alias ServiceManager.Accounts

  # Make the remember me cookie valid for 60 days.
  # If you want bump or reduce this value, also change
  # the token expiry itself in UserToken.
  @max_age 60 * 60 * 24 * 60
  @remember_me_cookie "_service_manager_web_user_remember_me"
  @remember_me_options [sign: true, max_age: @max_age, same_site: "Lax"]
  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Logs the user in.

  It renews the session ID and clears the whole session
  to avoid fixation attacks. See the renew_session
  function to customize this behaviour.

  It also sets a `:live_socket_id` key in the session,
  so LiveView sessions are identified and automatically
  disconnected on log out. The line can be safely removed
  if you are not using LiveView.
  """
  def log_in_user(conn, user, params \\ %{}) do
    token = Accounts.generate_user_session_token(user)
    user_return_to = get_session(conn, :user_return_to)

    conn
    |> renew_session()
    |> put_token_in_session(token)
    |> maybe_write_remember_me_cookie(token, params)
    |> redirect(to: user_return_to || signed_in_path(conn))
  end

  defp maybe_write_remember_me_cookie(conn, token, %{"remember_me" => "true"}) do
    put_resp_cookie(conn, @remember_me_cookie, token, @remember_me_options)
  end

  defp maybe_write_remember_me_cookie(conn, _token, _params) do
    conn
  end

  # This function renews the session ID and erases the whole
  # session to avoid fixation attacks. If there is any data
  # in the session you may want to preserve after log in/log out,
  # you must explicitly fetch the session data before clearing
  # and then immediately set it after clearing, for example:
  #
  #     defp renew_session(conn) do
  #       preferred_locale = get_session(conn, :preferred_locale)
  #
  #       conn
  #       |> configure_session(renew: true)
  #       |> clear_session()
  #       |> put_session(:preferred_locale, preferred_locale)
  #     end
  #
  defp renew_session(conn) do
    delete_csrf_token()

    conn
    |> configure_session(renew: true)
    |> clear_session()
  end

  @doc """
  Logs the user out.

  It clears all session data for safety. See renew_session.
  """
  def log_out_user(conn) do
    user_token = get_session(conn, :user_token)
    user_token && Accounts.delete_user_session_token(user_token)

    if live_socket_id = get_session(conn, :live_socket_id) do
      ServiceManagerWeb.Endpoint.broadcast(live_socket_id, "disconnect", %{})
    end

    conn
    |> renew_session()
    |> delete_resp_cookie(@remember_me_cookie)
    |> redirect(to: ~p"/")
  end

  @doc """
  Authenticates the user by looking into the session
  and remember me token.
  """

  defp get_device_id(conn) do
    # Generate a unique device ID based on user agent and IP
    user_agent = get_req_header(conn, "user-agent") |> List.first() || "unknown"
    remote_ip = conn.remote_ip |> :inet.ntoa() |> to_string()
    :crypto.hash(:sha256, "#{user_agent}:#{remote_ip}") |> Base.encode16()
  end

  def fetch_current_user(conn, _opts) do
    {user_token, conn} = ensure_user_token(conn)
    user = user_token && Accounts.get_user_by_session_token(user_token)

    # device_id = get_device_id(conn)
    # now = DateTime.utc_now()

    # # ServiceManager.Schemas.ActiveDevice.all()
    # device = %{
    #   device_id: device_id,
    #   last_seen_at: now,
    #   user_id: user.id,
    #   user_type: "web"
    # }

    # ServiceManagerWeb.Presence.update(
    #   self(),
    #   "users:presence",
    #   "user:#{user.id}",
    #   device
    # )

    # ServiceManagerWeb.Presence.track(
    #   self(),
    #   "users:presence",
    #   "user:#{user.id}",
    #   device
    # )

    assign(conn, :current_user, user)
  end

  defp ensure_user_token(conn) do
    if token = get_session(conn, :user_token) do
      {token, conn}
    else
      conn = fetch_cookies(conn, signed: [@remember_me_cookie])

      if token = conn.cookies[@remember_me_cookie] do
        {token, put_token_in_session(conn, token)}
      else
        {nil, conn}
      end
    end
  end

  @doc """
  Handles mounting and authenticating the current_user in LiveViews.

  ## `on_mount` arguments

    * `:mount_current_user` - Assigns current_user
      to socket assigns based on user_token, or nil if
      there's no user_token or no matching user.

    * `:ensure_authenticated` - Authenticates the user from the session,
      and assigns the current_user to socket assigns based
      on user_token.
      Redirects to login page if there's no logged user.

    * `:redirect_if_user_is_authenticated` - Authenticates the user from the session.
      Redirects to signed_in_path if there's a logged user.

  ## Examples

  Use the `on_mount` lifecycle macro in LiveViews to mount or authenticate
  the current_user:

      defmodule ServiceManagerWeb.PageLive do
        use ServiceManagerWeb, :live_view

        on_mount {ServiceManagerWeb.UserAuth, :mount_current_user}
        ...
      end

  Or use the `live_session` of your router to invoke the on_mount callback:

      live_session :authenticated, on_mount: [{ServiceManagerWeb.UserAuth, :ensure_authenticated}] do
        live "/profile", ProfileLive, :index
      end
  """
  def on_mount(:mount_current_user, _params, session, socket) do
    {:cont, mount_current_user(socket, session)}
  end

  def on_mount(:ensure_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_if_rights_are_assigned_to_user(socket)} do
      {false, true} ->
        {:cont, socket}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(
           :error,
           "Unauthenticated: User role is not assigned to you! Please contact Admin"
         )
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      _anything ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "You must log in to access this page.")
         |> Phoenix.LiveView.redirect(to: ~p"/users/log_in")}
    end
  end

  def on_mount(:ensure_backend_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_if_rights_are_assigned_to_user(socket)} do
      {false, true} ->
        {:cont, socket}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(
           :error,
           "Unauthenticated: User role is not assigned to you! Please contact Admin"
         )
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {true, _} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "You must log in to access this page.")
         |> Phoenix.LiveView.redirect(to: ~p"/users/log_in")}
    end
  end

  def on_mount(:backend_redirect_if_user_is_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {socket.assigns.current_user, check_if_rights_are_assigned_to_user(socket)} do
      {nil, _} ->
        {:cont, socket}

      {_, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(
           :error,
           "Unauthenticated: User role is not assigned to you! Please contact Admin"
         )
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {_, true} ->
        {:halt, Phoenix.LiveView.redirect(socket, to: signed_in_path(socket))}
    end
  end

  def on_mount(:redirect_if_user_is_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_if_rights_are_assigned_to_user(socket)} do
      {false, true} ->
        {:halt, Phoenix.LiveView.redirect(socket, to: signed_in_path(socket))}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(
           :error,
           "Unauthenticated: User role is not assigned to you! Please contact Admin"
         )
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {true, _} ->
        {:cont, socket}
    end
  end

  defp mount_current_user(socket, session) do
    Phoenix.Component.assign_new(socket, :current_user, fn ->
      if user_token = session["user_token"] do
        Accounts.get_user_by_session_token(user_token)
      end
    end)
    |> Phoenix.Component.assign_new(:page_key, fn -> "home" end)
    |> Phoenix.Component.assign_new(:filter_params, fn -> @query_params end)
    |> Phoenix.Component.assign_new(:selected_column, fn -> "inserted_at" end)
  end

  def check_if_rights_are_assigned_to_user(socket) do
    if is_nil(socket.assigns.current_user) == false do
      if is_nil(socket.assigns.current_user.user_permissions) do
        false
      else
        true
      end
    else
      false
    end
  end

  @doc """
  Used for routes that require the user to not be authenticated.
  """
  def redirect_if_user_is_authenticated(conn, _opts) do
    if conn.assigns[:current_user] do
      conn
      |> redirect(to: signed_in_path(conn))
      |> halt()
    else
      conn
    end
  end

  @doc """
  Used for routes that require the user to be authenticated.

  If you want to enforce the user email is confirmed before
  they use the application at all, here would be a good place.
  """
  def require_authenticated_user(conn, _opts) do
    if conn.assigns[:current_user] do
      conn
    else
      conn
      |> put_flash(:error, "You must log in to access this page.")
      |> maybe_store_return_to()
      |> redirect(to: ~p"/users/log_in")
      |> halt()
    end
  end

  defp put_token_in_session(conn, token) do
    conn
    |> put_session(:user_token, token)
    |> put_session(:live_socket_id, "users_sessions:#{Base.url_encode64(token)}")
  end

  defp maybe_store_return_to(%{method: "GET"} = conn) do
    put_session(conn, :user_return_to, current_path(conn))
  end

  defp maybe_store_return_to(conn), do: conn

  defp signed_in_path(_conn), do: ~p"/mobileBanking"
end
