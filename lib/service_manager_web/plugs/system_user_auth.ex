defmodule ServiceManagerWeb.Plugs.SystemUserAuth do
  use ServiceManagerWeb, :verified_routes

  import Plug.Conn
  import Phoenix.Controller

  alias ServiceManager.Context.SystemAuthorizationContext, as: Accounts

  # Make the remember me cookie valid for 60 days.
  @max_age 60 * 60 * 24 * 60
  @remember_me_cookie "_service_manager_web_user_remember_me"
  @remember_me_options [sign: true, max_age: @max_age, same_site: "Lax"]
  @query_params Application.compile_env(:service_manager, :query_params)

  @doc """
  Logs the user in.

  It renews the session ID and clears the whole session
  to avoid fixation attacks. See the renew_session
  function to customize this behaviour.

  It also sets a `:live_socket_id` key in the session,
  so LiveView sessions are identified and automatically
  disconnected on log out.
  """
  def log_in_user(conn, user, params \\ %{}) do
    token = Accounts.generate_user_session_token(user)
    user_return_to = get_session(conn, :user_return_to)

    conn
    |> renew_session()
    |> put_token_in_session(token)
    |> maybe_write_remember_me_cookie(token, params)
    |> redirect(to: user_return_to || signed_in_path(conn))
  end

  defp maybe_write_remember_me_cookie(conn, token, %{"remember_me" => "true"}) do
    put_resp_cookie(conn, @remember_me_cookie, token, @remember_me_options)
  end

  defp maybe_write_remember_me_cookie(conn, _token, _params) do
    conn
  end

  defp renew_session(conn) do
    preferred_locale = get_session(conn, :preferred_locale)

    conn
    |> configure_session(renew: true)
    |> clear_session()
    |> put_session(:preferred_locale, preferred_locale)
  end

  @doc """
  Logs the user out by clearing session data and cookies.
  """
  def log_out_user(conn) do
    user_token = get_session(conn, :user_token)
    user_token && Accounts.delete_user_session_token(user_token)

    if live_socket_id = get_session(conn, :live_socket_id) do
      ServiceManagerWeb.Endpoint.broadcast(live_socket_id, "disconnect", %{})
    end

    conn
    |> renew_session()
    |> delete_resp_cookie(@remember_me_cookie)
    |> redirect(to: ~p"/")
  end

  @doc """
  Authenticates the user by looking into the session
  and remember me token.
  """
  def fetch_current_user(conn, _opts) do
    {user_token, conn} = ensure_user_token(conn)
    user = user_token && Accounts.get_user_by_session_token(user_token)
    assign(conn, :current_user, user)
  end

  defp ensure_user_token(conn) do
    if token = get_session(conn, :user_token) do
      {token, conn}
    else
      conn = fetch_cookies(conn, signed: [@remember_me_cookie])

      if token = conn.cookies[@remember_me_cookie] do
        {token, put_token_in_session(conn, token)}
      else
        {nil, conn}
      end
    end
  end

  @doc """
  Handles mounting and authenticating the current_user in LiveViews.
  """
  def on_mount(:mount_current_user, _params, session, socket) do
    {:cont, mount_current_user(socket, session)}
  end

  def on_mount(:ensure_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_user_permissions(socket)} do
      {false, true} ->
        {:cont, socket}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "Unauthorized: Missing required role permissions")
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      _anything ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "You must log in to access this page.")
         |> Phoenix.LiveView.redirect(to: ~p"/users/log_in")}
    end
  end

  def on_mount(:ensure_backend_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_user_permissions(socket)} do
      {false, true} ->
        {:cont, socket}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "Unauthorized: Missing required role permissions")
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {true, _} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "You must log in to access this page.")
         |> Phoenix.LiveView.redirect(to: ~p"/users/log_in")}
    end
  end

  def on_mount(:backend_redirect_if_user_is_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {socket.assigns.current_user, check_user_permissions(socket)} do
      {nil, _} ->
        {:cont, socket}

      {_, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "Unauthorized: Missing required role permissions")
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {_, true} ->
        {:halt, Phoenix.LiveView.redirect(socket, to: signed_in_path(socket))}
    end
  end

  def on_mount(:redirect_if_user_is_authenticated, _params, session, socket) do
    socket = mount_current_user(socket, session)

    case {is_nil(socket.assigns.current_user), check_user_permissions(socket)} do
      {false, true} ->
        {:halt, Phoenix.LiveView.redirect(socket, to: signed_in_path(socket))}

      {false, false} ->
        {:halt,
         socket
         |> Phoenix.LiveView.put_flash(:error, "Unauthorized: Missing required role permissions")
         |> Phoenix.LiveView.push_navigate(to: "/users/log_out", replace: true)}

      {true, _} ->
        {:cont, socket}
    end
  end

  defp mount_current_user(socket, session) do
    Phoenix.Component.assign_new(socket, :current_user, fn ->
      if user_token = session["user_token"] do
        Accounts.get_user_by_session_token(user_token)
      end
    end)
    |> Phoenix.Component.assign_new(:page_key, fn -> "home" end)
    |> Phoenix.Component.assign_new(:filter_params, fn -> @query_params end)
    |> Phoenix.Component.assign_new(:selected_column, fn -> "inserted_at" end)
    |> Phoenix.Component.assign_new(:current_path, fn ->
      data = Phoenix.LiveView.get_connect_info(socket, :uri)
      # data = Phoenix.LiveView.get_connect_params(socket)
      if data do
        data.path
      else
        "/"
      end
    end)
  end

  defp check_user_permissions(socket) do
    case socket.assigns.current_user do
      nil -> false
      user -> not is_nil(user.user_permissions)
    end
  end

  @doc """
  Used for routes that require the user to not be authenticated.
  """
  def redirect_if_user_is_authenticated(conn, _opts) do
    if conn.assigns[:current_user] do
      conn
      |> redirect(to: signed_in_path(conn))
      |> halt()
    else
      conn
    end
  end

  @doc """
  Used for routes that require the user to be authenticated.
  """
  def require_authenticated_user(conn, _opts) do
    if conn.assigns[:current_user] do
      conn
    else
      conn
      |> put_flash(:error, "You must log in to access this page.")
      |> maybe_store_return_to()
      |> redirect(to: ~p"/users/log_in")
      |> halt()
    end
  end

  defp put_token_in_session(conn, token) do
    conn
    |> put_session(:user_token, token)
    |> put_session(:live_socket_id, "users_sessions:#{Base.url_encode64(token)}")
  end

  defp maybe_store_return_to(%{method: "GET"} = conn) do
    put_session(conn, :user_return_to, current_path(conn))
  end

  defp maybe_store_return_to(conn), do: conn

  defp signed_in_path(_conn), do: ~p"/mobileBanking"
end
