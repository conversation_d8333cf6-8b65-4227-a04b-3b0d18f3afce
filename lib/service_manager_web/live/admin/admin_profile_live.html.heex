<div class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5">
  <div class="mb-1 w-full">
    <div class="mb-4">
      <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl">Profile</h1>
    </div>
  </div>
</div>
<div class="flex flex-col">
  <div class="flex flex-col items-center pb-10 mt-8">
    <div class="relative mb-4">
      <%= if @current_user.profile_picture do %>
        <img
          class="w-24 h-24 rounded-full shadow-lg object-cover"
          src={@current_user.profile_picture}
          alt="Profile picture"
        />
      <% else %>
        <div class="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center">
          <i class="fas fa-user text-4xl text-gray-400"></i>
        </div>
      <% end %>
      <div class="absolute bottom-0 right-0">
        <button
          type="button"
          phx-click="show_upload_modal"
          class="cursor-pointer flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full hover:bg-blue-700 focus:ring-4 focus:ring-blue-300"
        >
          <i class="fas fa-camera text-white text-sm"></i>
        </button>
      </div>
    </div>
    <h5 class="mb-1 text-xl font-medium text-gray-900">
      <%= "#{@current_user.first_name} #{@current_user.last_name}" %>
    </h5>
    <span class="text-sm text-gray-500"><%= @current_user.user_permissions.name %></span>
  </div>

  <div class="grid grid-cols-1 px-4 py-6 lg:grid-cols-2 gap-6 lg:gap-8">
    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Basic Details</h3>
      <div class="space-y-6">
        <div>
          <label class="text-sm font-medium text-gray-600">Full Name</label>
          <p class="mt-2 text-gray-900">
            <%= "#{@current_user.first_name} #{@current_user.last_name}" %>
          </p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Email</label>
          <p class="mt-2 text-gray-900"><%= @current_user.email %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Phone Number</label>
          <p class="mt-2 text-gray-900"><%= @current_user.phone_number %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Customer Number</label>
          <p class="mt-2 text-gray-900"><%= @current_user.customer_no %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Account Number</label>
          <p class="mt-2 text-gray-900"><%= @current_user.account_number || "Not set" %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Date of Birth</label>
          <p class="mt-2 text-gray-900">
            <%= if @current_user.date_of_birth,
              do: Calendar.strftime(@current_user.date_of_birth, "%B %d, %Y"),
              else: "Not set" %>
          </p>
        </div>
      </div>
    </div>

    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Address Information</h3>
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-600">Address</label>
          <p class="mt-2 text-gray-900"><%= @current_user.address || "Not set" %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">City</label>
          <p class="mt-2 text-gray-900"><%= @current_user.city || "Not set" %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">State/Province</label>
          <p class="mt-2 text-gray-900"><%= @current_user.state || "Not set" %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">ZIP/Postal Code</label>
          <p class="mt-2 text-gray-900"><%= @current_user.zip || "Not set" %></p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-600">Country</label>
          <p class="mt-2 text-gray-900"><%= @current_user.country || "Not set" %></p>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 px-4 py-6 lg:grid-cols-2 gap-6 lg:gap-8">
    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Account Settings</h3>
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-600">Account Status</label>
          <p class="mt-2 text-gray-900">
            <%= if @current_user.approved do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Approved
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pending Approval
              </span>
            <% end %>
          </p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-600">Account Balance</label>
          <p class="mt-2 text-gray-900">
            <%= Number.Currency.number_to_currency(@current_user.account_balance) %>
          </p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-600">Role</label>
          <p class="mt-2 text-gray-900"><%= @current_user.user_permissions.name %></p>
        </div>
      </div>
    </div>

    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 class="mb-4 text-lg font-medium text-gray-900">Notification Preferences</h3>
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <span class="text-gray-600">Email Notifications</span>
          <label class="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={@current_user.email_notifications}
              class="sr-only peer"
              phx-click="toggle_notification"
              phx-value-type="email"
            />
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
            </div>
          </label>
        </div>

        <div class="flex items-center justify-between">
          <span class="text-gray-600">SMS Notifications</span>
          <label class="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={@current_user.sms_notifications}
              class="sr-only peer"
              phx-click="toggle_notification"
              phx-value-type="sms"
            />
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
            </div>
          </label>
        </div>

        <div class="flex items-center justify-between">
          <span class="text-gray-600">Push Notifications</span>
          <label class="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={@current_user.push_notifications}
              class="sr-only peer"
              phx-click="toggle_notification"
              phx-value-type="push"
            />
            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
            </div>
          </label>
        </div>
      </div>
    </div>
  </div>

  <div class="px-4 py-6">
    <button
      phx-click="show_edit_modal"
      class="w-full sm:w-auto text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center"
    >
      <i class="fas fa-edit mr-2"></i> Edit Profile
    </button>
  </div>
</div>

<%= if @show_edit_modal do %>
  <.live_component
    module={ServiceManagerWeb.Backend.EditProfileComponent}
    id="edit-profile"
    current_user={@current_user}
  />
<% end %>

<%= if @show_upload_modal do %>
  <.live_component
    module={ServiceManagerWeb.Backend.UploadProfilePictureComponent}
    id="upload-profile-picture"
    current_user={@current_user}
  />
<% end %>
