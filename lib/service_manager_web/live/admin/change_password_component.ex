defmodule ServiceManagerWeb.Live.Admin.ChangePasswordComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Accounts
  alias ServiceManager.Contexts.SystemUserManagementContext

  @impl true
  def render(assigns) do
    ~H"""
    <div
      id="change-password-modal"
      class="relative z-50"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
      <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
            <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                  <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">
                    Change Password
                  </h3>
                  <div class="mt-4">
                    <.form for={@form} phx-submit="save" phx-change="validate" phx-target={@myself}>
                      <div class="space-y-4">
                        <div>
                          <.input
                            field={@form[:current_password]}
                            type="password"
                            label="Current Password"
                            required
                            phx-debounce="blur"
                          />
                        </div>
                        <div>
                          <.input
                            field={@form[:password]}
                            type="password"
                            label="New Password"
                            required
                            phx-debounce="blur"
                          />
                        </div>
                        <div>
                          <.input
                            field={@form[:password_confirmation]}
                            type="password"
                            label="Confirm New Password"
                            required
                            phx-debounce="blur"
                          />
                        </div>
                      </div>
                      <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                        <.button type="submit" phx-disable-with="Changing..." class="w-full sm:w-auto">
                          Change Password
                        </.button>
                        <button
                          type="button"
                          class="mt-3 sm:mt-0 sm:mr-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:w-auto"
                          phx-click="close"
                          phx-target={@myself}
                        >
                          Cancel
                        </button>
                      </div>
                    </.form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    IO.inspect(%{current_user: user} = assigns, label: "|||||||||||||||||||||||||||||||||")
    changeset = SystemUserManagementContext.change_data(user)

    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  defp assign_form(socket) do
    form =
      to_form(%{
        "current_password" => "",
        "password" => "",
        "password_confirmation" => ""
      })

    assign(socket, form: form)
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset =
      SystemUserManagementContext.change_password_data(socket.assigns.current_user, params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("validate", %{"admin_users" => params}, socket) do
    changeset =
      SystemUserManagementContext.change_password_data(socket.assigns.current_user, params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  @impl true
  def handle_event("save", %{"form" => params}, socket) do
    case SystemUserManagementContext.update_user_password(socket.assigns.current_user, params) do
      {:ok, _user} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password updated successfully.")
         |> push_navigate(to: ~p"/mobileBanking/profile")}

      {:error, :data_struct, %Ecto.Changeset{} = changeset, _} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Attempted password change",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  def handle_event("save", %{"admin_users" => params}, socket) do
    case SystemUserManagementContext.update_user_password(socket.assigns.current_user, params) do
      {:ok, _user} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password updated successfully.")
         |> push_navigate(to: ~p"/mobileBanking/profile")}

      {:error, :data_struct, %Ecto.Changeset{} = changeset, _} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Attempted password change",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  def handle_event("close", _, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking")}
  end
end
