defmodule ServiceManagerWeb.Backend.AdminProfileLive do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Schemas.AdminUsers
  alias ServiceManager.Repo

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket,
        show_edit_modal: false,
        show_upload_modal: false
      )

    if connected?(socket) do
      current_user = socket.assigns.current_user |> Repo.preload(:user_permissions)

      {:ok,
       socket
       |> assign(current_user: current_user)
       |> assign(page_title: "My Profile")}
    else
      {:ok, socket}
    end
  end

  @impl true
  def handle_event("show_upload_modal", _params, socket) do
    {:noreply, assign(socket, show_upload_modal: true)}
  end

  @impl true
  def handle_event("toggle_notification", %{"type" => type}, socket) do
    current_user = socket.assigns.current_user
    field = String.to_existing_atom("#{type}_notifications")

    attrs = %{field => !Map.get(current_user, field)}

    case Repo.update(AdminUsers.update_changeset(current_user, attrs)) do
      {:ok, updated_user} ->
        {:noreply,
         socket
         |> assign(current_user: updated_user)
         |> put_flash(:info, "Notification preference updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Error updating notification preference")}
    end
  end

  @impl true
  def handle_event("show_edit_modal", _params, socket) do
    {:noreply, assign(socket, show_edit_modal: true)}
  end

  @impl true
  def handle_info({:user_updated, updated_user}, socket) do
    {:noreply,
     socket
     |> assign(current_user: updated_user)
     |> assign(show_edit_modal: false)
     |> assign(show_upload_modal: false)
     |> put_flash(:info, "Profile updated successfully")}
  end

  @impl true
  def handle_info(:close_modal, socket) do
    {:noreply, assign(socket, show_edit_modal: false)}
  end

  @impl true
  def handle_info(:close_upload_modal, socket) do
    {:noreply, assign(socket, show_upload_modal: false)}
  end
end
