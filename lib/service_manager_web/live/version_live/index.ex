defmodule ServiceManagerWeb.Backend.VersionLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Versions
  alias ServiceManager.Versions.{Version, Issue, QAndA, Message}
  alias ServiceManagerWeb.MessageComponent

  @impl true
  import Phoenix.Component
  import MessageComponent

  def mount(_params, _session, socket) do
    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, "versions")
    end

    {:ok,
     socket
     |> assign(:page_title, "Version Management")
     |> assign(:versions, list_versions())
     |> assign(:selected_version, nil)
     |> assign(:active_tab, "changelog")
     |> assign(:show_version_form, false)
     |> assign(:show_changelog_form, false)
     |> assign(:show_issue_form, false)
     |> assign(:show_qa_form, false)
     |> assign(:version_form, to_form(%{}))
     |> assign(:changelog_form, to_form(%{}))
     |> assign(:issue_form, to_form(%{}))
     |> assign(:qa_form, to_form(%{}))
     |> assign(:message_form, to_form(%{}))
     |> assign(:reply_to_message_id, nil)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  def handle_event("show-version", %{"id" => id}, socket) do
    version = Versions.get_version!(String.to_integer(id))

    {:noreply,
     socket
     |> assign(:selected_version, version)
     |> assign(:active_tab, "changelog")}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Version Management")
    |> assign(:version, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    version = Versions.get_version!(id)

    socket
    |> assign(:page_title, "Version #{version.version_number}")
    |> assign(:selected_version, version)
  end

  @impl true
  def handle_event("select-tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  def handle_event("new-version", _, socket) do
    {:noreply, assign(socket, :show_version_form, true)}
  end

  def handle_event("cancel-version", _, socket) do
    {:noreply, assign(socket, :show_version_form, false)}
  end

  def handle_event("new-changelog", _, socket) do
    {:noreply, assign(socket, :show_changelog_form, true)}
  end

  def handle_event("cancel-changelog", _, socket) do
    {:noreply, assign(socket, :show_changelog_form, false)}
  end

  def handle_event("save-changelog", params, socket) do
    changelog_params =
      Map.merge(params, %{
        "version_id" => socket.assigns.selected_version.id
      })

    case Versions.create_changelog_item(changelog_params) do
      {:ok, _changelog} ->
        {:noreply,
         socket
         |> put_flash(:info, "Changelog item added successfully")
         |> assign(:show_changelog_form, false)
         |> assign(:selected_version, Versions.get_version!(socket.assigns.selected_version.id))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         assign(socket, changelog_form: to_form(Map.put(params, :errors, changeset.errors)))}
    end
  end

  def handle_event("new-issue", _, socket) do
    {:noreply, assign(socket, :show_issue_form, true)}
  end

  def handle_event("cancel-issue", _, socket) do
    {:noreply, assign(socket, :show_issue_form, false)}
  end

  def handle_event("save-issue", params, socket) do
    issue_params =
      Map.merge(params, %{
        "version_id" => socket.assigns.selected_version.id,
        "status" => "open",
        "created_by" => socket.assigns.current_user.id
      })

    case Versions.create_issue(issue_params) do
      {:ok, _issue} ->
        {:noreply,
         socket
         |> put_flash(:info, "Issue created successfully")
         |> assign(:show_issue_form, false)
         |> assign(:selected_version, Versions.get_version!(socket.assigns.selected_version.id))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         assign(socket, issue_form: to_form(Map.put(params, :errors, changeset.errors)))}
    end
  end

  def handle_event("new-qa", _, socket) do
    {:noreply, assign(socket, :show_qa_form, true)}
  end

  def handle_event("cancel-qa", _, socket) do
    {:noreply, assign(socket, :show_qa_form, false)}
  end

  def handle_event("save-qa", params, socket) do
    qa_params =
      Map.merge(params, %{
        "version_id" => socket.assigns.selected_version.id,
        "status" => "open",
        "created_by" => socket.assigns.current_user.id
      })

    case Versions.create_qa(qa_params) do
      {:ok, _qa} ->
        {:noreply,
         socket
         |> put_flash(:info, "Question created successfully")
         |> assign(:show_qa_form, false)
         |> assign(:selected_version, Versions.get_version!(socket.assigns.selected_version.id))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, qa_form: to_form(Map.put(params, :errors, changeset.errors)))}
    end
  end

  def handle_event("save-version", params, socket) do
    version_params =
      Map.merge(params, %{
        "release_date" => parse_datetime(params["release_date"])
      })

    case Versions.create_version(version_params) do
      {:ok, version} ->
        notify_subscribers({:version_created, version})

        {:noreply,
         socket
         |> put_flash(:info, "Version created successfully")
         |> assign(:show_version_form, false)
         |> assign(:versions, list_versions())}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_event("save-message", %{"value" => value}, socket) when is_binary(value) do
    # Handle form-encoded value
    decoded_params = URI.decode_query(value)
    handle_message_save(decoded_params, socket)
  end

  def handle_event("save-message", params, socket) do
    # Handle direct params
    handle_message_save(params, socket)
  end

  defp handle_message_save(params, socket) do
    message_params = %{
      "content" => params["content"],
      "messageable_type" => params["messageable_type"],
      "messageable_id" => params["messageable_id"],
      "type" => params["type"],
      "user_id" => socket.assigns.current_user.id,
      "parent_id" => socket.assigns.reply_to_message_id
    }

    case Versions.create_message(message_params) do
      {:ok, message} ->
        notify_subscribers({:message_created, message})

        {:noreply,
         socket
         |> put_flash(:info, "Message posted successfully")
         |> assign(:reply_to_message_id, nil)
         |> assign(:selected_version, Versions.get_version!(socket.assigns.selected_version.id))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         assign(socket, message_form: to_form(Map.put(params, :errors, changeset.errors)))}
    end
  end

  def handle_event("show-reply-form", %{"message-id" => id}, socket) do
    {:noreply, assign(socket, :reply_to_message_id, String.to_integer(id))}
  end

  def handle_event("hide-reply-form", _, socket) do
    {:noreply, assign(socket, :reply_to_message_id, nil)}
  end

  def handle_event("upvote-qa", %{"id" => id}, socket) do
    qa = Versions.get_qa!(id)
    {:ok, updated_qa} = Versions.upvote_qa(qa)
    notify_subscribers({:qa_updated, updated_qa})

    {:noreply,
     socket
     |> assign(:selected_version, Versions.get_version!(socket.assigns.selected_version.id))}
  end

  @impl true
  def handle_info({:version_created, _version}, socket) do
    {:noreply, assign(socket, :versions, list_versions())}
  end

  def handle_info({:issue_created, _issue}, socket) do
    if socket.assigns.selected_version do
      {:noreply,
       assign(
         socket,
         :selected_version,
         Versions.get_version!(socket.assigns.selected_version.id)
       )}
    else
      {:noreply, socket}
    end
  end

  def handle_info({:qa_created, _qa}, socket) do
    if socket.assigns.selected_version do
      {:noreply,
       assign(
         socket,
         :selected_version,
         Versions.get_version!(socket.assigns.selected_version.id)
       )}
    else
      {:noreply, socket}
    end
  end

  def handle_info({:message_created, _message}, socket) do
    if socket.assigns.selected_version do
      {:noreply,
       assign(
         socket,
         :selected_version,
         Versions.get_version!(socket.assigns.selected_version.id)
       )}
    else
      {:noreply, socket}
    end
  end

  def handle_info({:qa_updated, _qa}, socket) do
    if socket.assigns.selected_version do
      {:noreply,
       assign(
         socket,
         :selected_version,
         Versions.get_version!(socket.assigns.selected_version.id)
       )}
    else
      {:noreply, socket}
    end
  end

  defp list_versions do
    Versions.list_versions()
  end

  defp notify_subscribers(msg) do
    Phoenix.PubSub.broadcast(ServiceManager.PubSub, "versions", msg)
  end

  defp parse_datetime(nil), do: nil

  defp parse_datetime(datetime_string) do
    case DateTime.from_iso8601(datetime_string <> ":00Z") do
      {:ok, datetime, _} -> datetime
      _ -> nil
    end
  end
end
