<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-8">
    <h1 class="text-3xl font-bold"><%= @page_title %></h1>
    <button
      phx-click="new-version"
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
    >
      New Version
    </button>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Versions List -->
    <div class="col-span-3 bg-white rounded-lg shadow p-4">
      <h2 class="text-xl font-semibold mb-4">Versions</h2>
      <div class="space-y-2">
        <%= for version <- @versions do %>
          <div
            class={"p-3 rounded cursor-pointer #{if @selected_version && @selected_version.id == version.id, do: "bg-blue-100", else: "hover:bg-gray-100"}"}
            phx-click="show-version"
            phx-value-id={version.id}
          >
            <div class="font-medium"><%= version.version_number %></div>
            <div class="text-sm text-gray-600"><%= version.title %></div>
            <div class="text-xs text-gray-500">
              <%= version.status %> • <%= version.version_type %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    <!-- Version Details -->
    <div class="col-span-9">
      <%= if @selected_version do %>
        <div class="bg-white rounded-lg shadow">
          <div class="p-6 border-b">
            <h2 class="text-2xl font-bold"><%= @selected_version.title %></h2>
            <div class="mt-2 text-gray-600"><%= @selected_version.description %></div>
            <div class="mt-4 flex items-center space-x-4 text-sm text-gray-500">
              <span class="px-2 py-1 bg-gray-100 rounded">
                <%= @selected_version.version_number %>
              </span>
              <span class="px-2 py-1 bg-gray-100 rounded">
                <%= @selected_version.version_type %>
              </span>
              <span class={"px-2 py-1 rounded #{if @selected_version.status == "published", do: "bg-green-100 text-green-800", else: "bg-yellow-100 text-yellow-800"}"}>
                <%= @selected_version.status %>
              </span>
              <span>
                Released: <%= Calendar.strftime(@selected_version.release_date, "%Y-%m-%d") %>
              </span>
            </div>
          </div>
          <!-- Tabs -->
          <div class="border-b px-6">
            <nav class="-mb-px flex space-x-8">
              <button
                phx-click="select-tab"
                phx-value-tab="changelog"
                class={"py-4 px-1 border-b-2 font-medium text-sm #{if @active_tab == "changelog", do: "border-blue-500 text-blue-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}"}
              >
                Changelog
              </button>
              <button
                phx-click="select-tab"
                phx-value-tab="issues"
                class={"py-4 px-1 border-b-2 font-medium text-sm #{if @active_tab == "issues", do: "border-blue-500 text-blue-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}"}
              >
                Issues
              </button>
              <button
                phx-click="select-tab"
                phx-value-tab="qa"
                class={"py-4 px-1 border-b-2 font-medium text-sm #{if @active_tab == "qa", do: "border-blue-500 text-blue-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}"}
              >
                Q&A
              </button>
            </nav>
          </div>
          <!-- Tab Content -->
          <div class="p-6">
            <%= case @active_tab do %>
              <% "changelog" -> %>
                <div>
                  <div class="flex justify-between mb-4">
                    <h3 class="text-lg font-semibold">Changelog Items</h3>
                    <button
                      phx-click="new-changelog"
                      class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                    >
                      Add Changelog Item
                    </button>
                  </div>

                  <div class="space-y-6">
                    <%= for item <- @selected_version.changelog_items do %>
                      <div class="border-l-4 border-blue-500 pl-4">
                        <div class="font-medium"><%= item.title %></div>
                        <div class="mt-1 text-gray-600"><%= item.description %></div>
                        <div class="mt-2 text-sm text-gray-500">
                          Type: <%= item.item_type %>
                        </div>
                        <div class="mt-4 border-t pt-4">
                          <div class="messages space-y-4">
                            <%= for message <- item.messages do %>
                              <.message
                                message={message}
                                allow_replies={true}
                                show_replies={true}
                              />
                            <% end %>
                          </div>
                          <div class="mt-4">
                            <.simple_form
                              for={@message_form}
                              phx-submit="save-message"
                              class="flex gap-2"
                            >
                              <.input type="hidden" name="messageable_type" value="ChangelogItem" />
                              <.input type="hidden" name="messageable_id" value={item.id} />
                              <.input type="hidden" name="type" value="changelog" />
                              <.input
                                field={@message_form[:content]}
                                type="text"
                                placeholder="Add a comment..."
                                class="flex-1"
                              />
                              <button
                                type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                              >
                                Send
                              </button>
                            </.simple_form>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% "issues" -> %>
                <div>
                  <div class="flex justify-between mb-4">
                    <h3 class="text-lg font-semibold">Issues</h3>
                    <button
                      phx-click="new-issue"
                      class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                    >
                      Create Issue
                    </button>
                  </div>

                  <div class="space-y-6">
                    <%= for issue <- @selected_version.issues do %>
                      <div class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <h3 class="font-medium"><%= issue.title %></h3>
                          <div class="flex items-center space-x-2">
                            <span class={"px-2 py-1 text-sm rounded #{case issue.status do
                              "closed" -> "bg-green-100 text-green-800"
                              "wip" -> "bg-yellow-100 text-yellow-800"
                              _ -> "bg-red-100 text-red-800"
                            end}"}>
                              <%= issue.status %>
                            </span>
                            <span class={"px-2 py-1 text-sm rounded #{case issue.priority do
                              "low" -> "bg-gray-100 text-gray-800"
                              "medium" -> "bg-blue-100 text-blue-800"
                              "high" -> "bg-orange-100 text-orange-800"
                              "critical" -> "bg-red-100 text-red-800"
                            end}"}>
                              <%= issue.priority %>
                            </span>
                          </div>
                        </div>
                        <div class="mt-2 text-gray-600"><%= issue.description %></div>
                        <div class="mt-4 text-sm text-gray-500">
                          Created by <%= issue.creator.first_name %> <%= issue.creator.last_name %>
                          <%= if issue.assignee do %>
                            • Assigned to <%= issue.assignee.first_name %> <%= issue.assignee.last_name %>
                          <% end %>
                        </div>
                        <div class="mt-4 border-t pt-4">
                          <div class="messages space-y-4">
                            <%= for message <- issue.messages do %>
                              <.message
                                message={message}
                                allow_replies={true}
                                show_replies={true}
                              />
                            <% end %>
                          </div>
                          <div class="mt-4">
                            <.simple_form
                              for={@message_form}
                              phx-submit="save-message"
                              class="flex gap-2"
                            >
                              <.input type="hidden" name="messageable_type" value="Issue" />
                              <.input type="hidden" name="messageable_id" value={issue.id} />
                              <.input type="hidden" name="type" value="issue" />
                              <.input
                                field={@message_form[:content]}
                                type="text"
                                placeholder="Add a comment..."
                                class="flex-1"
                              />
                              <button
                                type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                              >
                                Send
                              </button>
                            </.simple_form>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% "qa" -> %>
                <div>
                  <div class="flex justify-between mb-4">
                    <h3 class="text-lg font-semibold">Questions & Answers</h3>
                    <button
                      phx-click="new-qa"
                      class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                    >
                      Ask Question
                    </button>
                  </div>

                  <div class="space-y-6">
                    <%= for qa <- @selected_version.q_and_as do %>
                      <div class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <h3 class="font-medium"><%= qa.title %></h3>
                          <div class="flex items-center space-x-2">
                            <span class={"px-2 py-1 text-sm rounded #{if qa.status == "answered", do: "bg-green-100 text-green-800", else: "bg-yellow-100 text-yellow-800"}"}>
                              <%= qa.status %>
                            </span>
                            <button
                              phx-click="upvote-qa"
                              phx-value-id={qa.id}
                              class="flex items-center space-x-1 text-gray-500 hover:text-gray-700"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-5 w-5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                              <span><%= qa.votes_count %></span>
                            </button>
                          </div>
                        </div>
                        <div class="mt-2 text-gray-600"><%= qa.question %></div>
                        <div class="mt-4 text-sm text-gray-500">
                          Asked by <%= qa.creator.first_name %> <%= qa.creator.last_name %>
                        </div>
                        <div class="mt-4 border-t pt-4">
                          <div class="messages space-y-4">
                            <%= for message <- qa.messages do %>
                              <.message
                                message={message}
                                allow_replies={true}
                                show_replies={true}
                              />
                            <% end %>
                          </div>
                          <div class="mt-4">
                            <.simple_form
                              for={@message_form}
                              phx-submit="save-message"
                              class="flex gap-2"
                            >
                              <.input type="hidden" name="messageable_type" value="QAndA" />
                              <.input type="hidden" name="messageable_id" value={qa.id} />
                              <.input type="hidden" name="type" value="qa" />
                              <.input
                                field={@message_form[:content]}
                                type="text"
                                placeholder="Add an answer..."
                                class="flex-1"
                              />
                              <button
                                type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                              >
                                Send
                              </button>
                            </.simple_form>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="bg-white rounded-lg shadow p-6 text-center text-gray-500">
          Select a version to view details
        </div>
      <% end %>
    </div>
  </div>
</div>
<!-- Version Form Modal -->
<%= if @show_version_form do %>
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
      <h2 class="text-2xl font-bold mb-4">New Version</h2>
      <.simple_form for={@version_form} id="version-form" phx-submit="save-version">
        <.input
          field={@version_form[:version_number]}
          type="text"
          label="Version Number"
          placeholder="1.0.0"
        />
        <.input
          field={@version_form[:title]}
          type="text"
          label="Title"
          placeholder="Initial Release"
        />
        <.input field={@version_form[:description]} type="textarea" label="Description" rows={3} />
        <.input
          field={@version_form[:version_type]}
          type="select"
          label="Version Type"
          options={["major", "minor", "patch"]}
        />
        <.input
          field={@version_form[:status]}
          type="select"
          label="Status"
          options={["draft", "published"]}
        />
        <.input field={@version_form[:release_date]} type="datetime-local" label="Release Date" />

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            phx-click="cancel-version"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Cancel
          </button>
          <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </.simple_form>
    </div>
  </div>
<% end %>
<!-- Changelog Form Modal -->
<%= if @show_changelog_form do %>
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
      <h2 class="text-2xl font-bold mb-4">Add Changelog Item</h2>
      <.simple_form for={@changelog_form} id="changelog-form" phx-submit="save-changelog">
        <.input
          field={@changelog_form[:item_type]}
          type="select"
          label="Type"
          options={["feature", "enhancement", "bugfix", "security"]}
        />
        <.input
          field={@changelog_form[:title]}
          type="text"
          label="Title"
          placeholder="What changed?"
        />
        <.input
          field={@changelog_form[:description]}
          type="textarea"
          label="Description"
          rows={3}
        />

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            phx-click="cancel-changelog"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Cancel
          </button>
          <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </.simple_form>
    </div>
  </div>
<% end %>
<!-- Issue Form Modal -->
<%= if @show_issue_form do %>
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
      <h2 class="text-2xl font-bold mb-4">Create Issue</h2>
      <.simple_form for={@issue_form} id="issue-form" phx-submit="save-issue">
        <.input field={@issue_form[:title]} type="text" label="Title" placeholder="Issue summary" />
        <.input field={@issue_form[:description]} type="textarea" label="Description" rows={3} />
        <.input
          field={@issue_form[:priority]}
          type="select"
          label="Priority"
          options={["low", "medium", "high", "critical"]}
        />

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            phx-click="cancel-issue"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Cancel
          </button>
          <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </.simple_form>
    </div>
  </div>
<% end %>
<!-- Q&A Form Modal -->
<%= if @show_qa_form do %>
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
    <div class="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
      <h2 class="text-2xl font-bold mb-4">Ask Question</h2>
      <.simple_form for={@qa_form} id="qa-form" phx-submit="save-qa">
        <.input field={@qa_form[:title]} type="text" label="Title" placeholder="Question summary" />
        <.input field={@qa_form[:question]} type="textarea" label="Question" rows={3} />

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            phx-click="cancel-qa"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
          >
            Cancel
          </button>
          <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </.simple_form>
    </div>
  </div>
<% end %>
