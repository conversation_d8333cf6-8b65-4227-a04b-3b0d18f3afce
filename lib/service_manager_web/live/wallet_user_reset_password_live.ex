defmodule ServiceManagerWeb.WalletUserResetPasswordLive do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.WalletAccounts

  def render(assigns) do
    ~H"""
    <div class="mx-auto max-w-sm">
      <.header class="text-center">Reset Password</.header>

      <.simple_form
        for={@form}
        id="reset_password_form"
        phx-submit="reset_password"
        phx-change="validate"
      >
        <.error :if={@form.errors != []}>
          Oops, something went wrong! Please check the errors below.
        </.error>

        <.input field={@form[:password]} type="password" label="New password" required />
        <.input
          field={@form[:password_confirmation]}
          type="password"
          label="Confirm new password"
          required
        />
        <:actions>
          <.button phx-disable-with="Resetting..." class="w-full">Reset Password</.button>
        </:actions>
      </.simple_form>

      <p class="text-center text-sm mt-4">
        <.link href={~p"/walletusers/register"}>Register</.link>
        | <.link href={~p"/walletusers/log_in"}>Log in</.link>
      </p>
    </div>
    """
  end

  def mount(params, _session, socket) do
    socket = assign_wallet_user_and_token(socket, params)

    form_source =
      case socket.assigns do
        %{wallet_user: wallet_user} ->
          WalletAccounts.change_wallet_user_password(wallet_user)

        _ ->
          %{}
      end

    {:ok, assign_form(socket, form_source), temporary_assigns: [form: nil]}
  end

  # Do not log in the wallet_user after reset password to avoid a
  # leaked token giving the wallet_user access to the account.
  def handle_event("reset_password", %{"wallet_user" => wallet_user_params}, socket) do
    case WalletAccounts.reset_wallet_user_password(socket.assigns.wallet_user, wallet_user_params) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password reset successfully.")
         |> redirect(to: ~p"/walletusers/log_in")}

      {:error, changeset} ->
        {:noreply, assign_form(socket, Map.put(changeset, :action, :insert))}
    end
  end

  def handle_event("validate", %{"wallet_user" => wallet_user_params}, socket) do
    changeset =
      WalletAccounts.change_wallet_user_password(socket.assigns.wallet_user, wallet_user_params)

    {:noreply, assign_form(socket, Map.put(changeset, :action, :validate))}
  end

  defp assign_wallet_user_and_token(socket, %{"token" => token}) do
    if wallet_user = WalletAccounts.get_wallet_user_by_reset_password_token(token) do
      assign(socket, wallet_user: wallet_user, token: token)
    else
      socket
      |> put_flash(:error, "Reset password link is invalid or it has expired.")
      |> redirect(to: ~p"/")
    end
  end

  defp assign_form(socket, %{} = source) do
    assign(socket, :form, to_form(source, as: "wallet_user"))
  end
end
