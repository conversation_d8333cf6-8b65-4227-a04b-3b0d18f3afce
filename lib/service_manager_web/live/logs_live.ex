defmodule ServiceManagerWeb.LogsLive do
  use ServiceManagerWeb, :live_view
  require Logger

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      :timer.send_interval(1000, :update_logs)
    end

    current_dir = File.cwd!()
    {:ok, files} = File.ls(current_dir)

    socket =
      socket
      |> assign(:current_dir, current_dir)
      |> assign(:files, sort_files(current_dir, files))
      |> assign(:selected_file, nil)
      |> assign(:logs, [])

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex">
        <!-- File Browser -->
        <div class="w-1/4 pr-4">
          <div class="bg-gray-100 p-4 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Files</h2>
            <div class="mb-4">
              <button class="text-blue-600 hover:text-blue-800" phx-click="parent_dir">
                ..
              </button>
              <p class="text-sm text-gray-600 truncate"><%= @current_dir %></p>
            </div>
            <div class="space-y-2">
              <%= for {name, type} <- @files do %>
                <div class="flex items-center">
                  <%= if type == :directory do %>
                    <button
                      class="flex items-center text-blue-600 hover:text-blue-800"
                      phx-click="navigate"
                      phx-value-path={name}
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                        />
                      </svg>
                      <%= name %>
                    </button>
                  <% else %>
                    <button
                      class="flex items-center text-gray-700 hover:text-gray-900"
                      phx-click="select_file"
                      phx-value-path={name}
                    >
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <%= name %>
                    </button>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
        <!-- Log Viewer -->
        <div class="w-3/4">
          <div class="bg-gray-900 text-gray-100 p-4 rounded-lg shadow-lg overflow-auto h-[600px] font-mono">
            <%= if @selected_file do %>
              <%= for log <- @logs do %>
                <div class="whitespace-pre-wrap mb-1"><%= log %></div>
              <% end %>
            <% else %>
              <div class="text-gray-500 text-center mt-4">Select a file to view its contents</div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("navigate", %{"path" => path}, socket) do
    new_path = Path.join(socket.assigns.current_dir, path)
    {:ok, files} = File.ls(new_path)

    {:noreply,
     socket
     |> assign(:current_dir, new_path)
     |> assign(:files, sort_files(new_path, files))
     |> assign(:selected_file, nil)
     |> assign(:logs, [])}
  end

  def handle_event("parent_dir", _params, socket) do
    current_dir = socket.assigns.current_dir
    parent_dir = Path.dirname(current_dir)

    # Don't go above root directory
    if parent_dir == current_dir do
      {:noreply, socket}
    else
      {:ok, files} = File.ls(parent_dir)

      {:noreply,
       socket
       |> assign(:current_dir, parent_dir)
       |> assign(:files, sort_files(parent_dir, files))
       |> assign(:selected_file, nil)
       |> assign(:logs, [])}
    end
  end

  def handle_event("select_file", %{"path" => path}, socket) do
    full_path = Path.join(socket.assigns.current_dir, path)
    logs = read_file_contents(full_path)

    {:noreply,
     socket
     |> assign(:selected_file, full_path)
     |> assign(:logs, logs)}
  end

  @impl true
  def handle_info(:update_logs, socket) do
    case socket.assigns.selected_file do
      nil ->
        {:noreply, socket}

      path ->
        logs = read_file_contents(path)
        {:noreply, assign(socket, :logs, logs)}
    end
  end

  defp sort_files(current_dir, files) do
    files
    |> Enum.map(fn file ->
      path = Path.join(current_dir, file)
      {file, if(File.dir?(path), do: :directory, else: :file)}
    end)
    |> Enum.sort_by(fn {name, type} -> {if(type == :directory, do: "0", else: "1"), name} end)
  end

  defp read_file_contents(path) do
    case File.read(path) do
      {:ok, content} ->
        content
        |> String.split("\n")
        |> Enum.reject(&(&1 == ""))
        # Keep last 100 lines
        |> Enum.take(-100)

      {:error, reason} ->
        Logger.error("Failed to read file #{path}: #{inspect(reason)}")
        []
    end
  end
end
