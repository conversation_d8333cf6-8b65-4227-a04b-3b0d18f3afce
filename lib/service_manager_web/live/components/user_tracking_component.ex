defmodule ServiceManagerWeb.Live.Components.UserTrackingComponent do
  @moduledoc """
  LiveView component for tracking user navigation through the application.
  This component handles the events from the UserTrackingHook.
  """
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.UserTrackingContext
  alias ServiceManager.Schemas.Tracking.UserTracking

  @impl true
  def mount(socket) do
    socket =
      socket
      |> Phoenix.LiveView.assign_new(:session_id, fn -> socket.assigns[:session_id] || socket.id end)
      |> Phoenix.LiveView.assign_new(:device_id, fn -> socket.assigns[:device_id] end)
      |> Phoenix.LiveView.assign_new(:device_name, fn -> socket.assigns[:device_name] end)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div id="user-tracking-component" phx-hook="UserTrackingHook"></div>
    """
  end

  @impl true
  def handle_event("track_page_entry", params, socket) do
    user = socket.assigns[:current_user]
    wallet_user = socket.assigns[:current_wallet_user]
    session_id = socket.assigns[:session_id] || socket.id

    # Prepare tracking data
    tracking_params = %{
      "page_path" => params["page_path"],
      "page_name" => params["page_name"],
      "section" => params["section"],
      "action" => params["action"],
      "previous_page_path" => params["previous_page_path"],
      "session_id" => session_id,
      "ip_address" => get_client_ip(socket),
      "user_agent" => params["user_agent"],
      "device_id" => socket.assigns[:device_id],
      "device_name" => socket.assigns[:device_name]
    }

    # Add user identification if available
    tracking_params =
      cond do
        user != nil ->
          Map.put(tracking_params, "user_id", user.id)

        wallet_user != nil ->
          Map.put(tracking_params, "wallet_user_id", wallet_user.id)

        true ->
          tracking_params
      end

    # Create tracking record
    case UserTrackingContext.create_entry_tracking(tracking_params) do
      {:ok, _tracking} ->
        {:noreply, socket}

      {:error, _changeset} ->
        # Log error but don't disrupt user experience
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("track_page_exit", params, socket) do
    user = socket.assigns[:current_user]
    wallet_user = socket.assigns[:current_wallet_user]
    session_id = socket.assigns[:session_id] || socket.id

    # Find the most recent tracking record for this user/session
    tracking_record =
      cond do
        user != nil ->
          UserTrackingContext.get_latest_user_tracking(user_id: user.id)

        wallet_user != nil ->
          UserTrackingContext.get_latest_user_tracking(wallet_user_id: wallet_user.id)

        true ->
          UserTrackingContext.get_latest_user_tracking(session_id: session_id)
      end

    if tracking_record do
      # Update the tracking record with exit information
      tracking_params =
        %{
          "next_page_path" => params["next_page_path"],
          "status" => params["status"]
        }
        |> Map.merge(
          if Map.has_key?(params, "duration"), do: %{"duration" => params["duration"]}, else: %{}
        )

      case UserTrackingContext.update_with_exit_tracking(tracking_record, tracking_params) do
        {:ok, _tracking} ->
          {:noreply, socket}

        {:error, _changeset} ->
          # Log error but don't disrupt user experience
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  # Helper function to get client IP address
  defp get_client_ip(socket) do
    socket.assigns[:client_ip] ||
      (socket.assigns[:conn] && socket.assigns[:conn].remote_ip |> :inet.ntoa() |> to_string()) ||
      "unknown"
  end
end
