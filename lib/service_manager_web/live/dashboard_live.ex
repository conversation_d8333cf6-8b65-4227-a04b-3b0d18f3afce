defmodule ServiceManagerWeb.Backend.DashboardLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Statistics.Cache

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket), do: :timer.send_interval(1000, self(), :update)
    {:ok, assign(socket, statistics: Cache.get_statistics())}
  end

  @impl true
  def handle_info(:update, socket) do
    {:noreply, assign(socket, statistics: Cache.get_statistics())}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold mb-4">Dashboard</h1>
      <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h2 class="text-xl font-semibold mb-2">Statistics</h2>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p>Total Users: <%= @statistics.total_users %></p>
            <p>Total Accounts: <%= @statistics.total_accounts %></p>
            <p>Total Cards: <%= @statistics.total_cards %></p>
            <p>Total Transactions: <%= @statistics.total_transactions %></p>
            <p>Total Bank Accounts: <%= @statistics.total_bank_accounts %></p>
          </div>
          <div>
            <p>Total Beneficiaries: <%= @statistics.total_beneficiaries %></p>
            <p>Total Fund Accounts: <%= @statistics.total_fund_accounts %></p>
            <p>Total Transfers: <%= @statistics.total_transfers %></p>
            <p>Total Tokens: <%= @statistics.total_tokens %></p>
            <p>Last Updated: <%= format_datetime(@statistics.updated_at) %></p>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp format_datetime(nil), do: "N/A"

  defp format_datetime(datetime) do
    datetime
    |> DateTime.from_naive!("Etc/UTC")
    |> DateTime.to_string()
  end
end
