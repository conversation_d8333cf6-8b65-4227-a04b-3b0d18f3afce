defmodule ServiceManagerWeb.OnlineUsersLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManagerWeb.Presence
  alias ServiceManager.Accounts

  @presence_topic "users:presence"

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, @presence_topic)
    end

    online_users =
      Presence.list(@presence_topic)
      |> Enum.map(fn {_key, %{metas: [meta | _]}} -> meta end)
      |> fetch_users()

    {:ok, assign(socket, online_users: online_users)}
  end

  @impl true
  def handle_info(%Phoenix.Socket.Broadcast{event: "presence_diff"}, socket) do
    online_users =
      Presence.list(@presence_topic)
      |> Enum.map(fn {_key, %{metas: [meta | _]}} -> meta end)
      |> fetch_users()

    {:noreply, assign(socket, online_users: online_users)}
  end

  defp fetch_users(presence_metas) do
    user_ids = Enum.map(presence_metas, & &1.user_id)
    users = Accounts.list_users_by_ids(user_ids)

    Enum.map(presence_metas, fn meta ->
      user = Enum.find(users, &(&1.id == meta.user_id))
      Map.put(meta, :user, user)
    end)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white shadow rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">Online Users</h2>
      <div class="space-y-4">
        <%= for user_meta <- @online_users do %>
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <div>
              <%= if user_meta.user do %>
                <span class="font-medium"><%= user_meta.user.email %></span>
                <span class="text-sm text-gray-500">
                  (Last seen: <%= Calendar.strftime(user_meta.last_seen_at, "%Y-%m-%d %H:%M:%S") %>)
                </span>
              <% else %>
                <span class="text-gray-500">Unknown User</span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
