<div class="min-h-screen bg-gray-50 relative">
  <%= if @loading do %>
    <div class="absolute inset-0 bg-gray-900/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-4 flex items-center gap-3">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span class="text-gray-700">Loading...</span>
      </div>
    </div>
  <% end %>
  <!-- Node Status Bar -->
  <div class="bg-gray-800 text-white py-2 px-4 mb-8">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <span class="text-gray-400">Current Node:</span>
          <span class="font-semibold"><%= @current_node %></span>
        </div>
        <div class="flex items-center gap-2">
          <span class="text-gray-400">Selected Node:</span>
          <span class="font-semibold"><%= @selected_node %></span>
          <%= if @current_node == @selected_node do %>
            <span class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
              (current)
            </span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Node Selector -->
    <div class="mb-8">
      <label for="node-select" class="block text-sm font-medium text-gray-700">Select Node</label>
      <form phx-submit="select_node" class="mt-1 flex items-center gap-2">
        <select
          id="node-select"
          name="node"
          value={@selected_node}
          class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        >
          <%= for node <- @available_nodes do %>
            <option value={node} selected={node == @selected_node}>
              <%= node %>
              <%= if node == @current_node do %>
                (current)
              <% end %>
            </option>
          <% end %>
        </select>
        <button
          type="submit"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Switch Node
        </button>
      </form>
    </div>
    <!-- Metrics Header -->
    <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-5">
      <div class="relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md">
        <dt class="truncate text-sm font-medium text-gray-500">Total Features</dt>
        <dd class="mt-2 text-3xl font-semibold tracking-tight text-gray-900">
          <%= @metrics.total_features %>
        </dd>
        <div class="absolute bottom-0 left-0 h-1 w-full bg-purple-500"></div>
      </div>

      <div class="relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md">
        <dt class="truncate text-sm font-medium text-gray-500">Enabled</dt>
        <dd class="mt-2 text-3xl font-semibold tracking-tight text-green-600">
          <%= @metrics.enabled_count %>
        </dd>
        <div class="absolute bottom-0 left-0 h-1 w-full bg-green-500"></div>
      </div>

      <div class="relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md">
        <dt class="truncate text-sm font-medium text-gray-500">Disabled</dt>
        <dd class="mt-2 text-3xl font-semibold tracking-tight text-red-600">
          <%= @metrics.disabled_count %>
        </dd>
        <div class="absolute bottom-0 left-0 h-1 w-full bg-red-500"></div>
      </div>

      <div class="relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md">
        <dt class="truncate text-sm font-medium text-gray-500">Unregistered</dt>
        <dd class="mt-2 text-3xl font-semibold tracking-tight text-orange-500">
          <%= @metrics.unregistered_count %>
        </dd>
        <div class="absolute bottom-0 left-0 h-1 w-full bg-orange-500"></div>
      </div>

      <div class="relative overflow-hidden rounded-lg bg-white p-6 shadow-sm transition-all hover:shadow-md">
        <dt class="truncate text-sm font-medium text-gray-500">Categories</dt>
        <dd class="mt-2 text-3xl font-semibold tracking-tight text-blue-600">
          <%= @metrics.categories_count %>
        </dd>
        <div class="absolute bottom-0 left-0 h-1 w-full bg-blue-500"></div>
      </div>
    </div>
    <!-- Search and Filters -->
    <div class="mt-8 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div class="relative flex-1">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path
              fill-rule="evenodd"
              d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <input
          type="text"
          placeholder="Search features..."
          phx-keyup="search"
          value={@search}
          class="block w-full rounded-lg border-0 py-3 pl-10 pr-4 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        />
      </div>

      <div class="w-full sm:w-48">
        <select
          phx-change="filter"
          class="block w-full rounded-lg border-0 py-3 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        >
          <option value="all">All Features</option>
          <option value="enabled">Enabled</option>
          <option value="disabled">Disabled</option>
          <option value="unregistered">Unregistered</option>
        </select>
      </div>
    </div>
    <!-- Categories Grid -->
    <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <%= for {category, features} <- @features_by_category do %>
        <div class="overflow-hidden rounded-lg bg-white shadow-sm ring-1 ring-gray-200">
          <div class="border-b border-gray-200 bg-gray-50 px-4 py-3">
            <div class="flex items-center justify-between">
              <h3 class="text-base font-semibold leading-6 text-gray-900">
                <%= String.capitalize(category) %>
              </h3>
              <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                <%= length(features) %>
              </span>
            </div>
          </div>

          <div class="divide-y divide-gray-200 p-4">
            <%= for feature <- filter_features(features, @search, @filter) do %>
              <div class="flex items-center gap-4 py-3">
                <div>
                  <button
                    phx-click="toggle"
                    phx-value-name={feature.name}
                    class={"relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 #{
                      cond do
                        feature.enabled -> "bg-green-500"
                        !feature.enabled -> "bg-red-500"
                        true -> "bg-orange-400"
                      end
                    }"}
                  >
                    <span class={"pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out #{
                      if(feature.enabled, do: "translate-x-5", else: "translate-x-0")
                    }"}>
                    </span>
                  </button>
                </div>

                <div class="min-w-0 flex-1">
                  <p class="truncate text-sm font-medium text-gray-900" title={feature.name}>
                    <%= feature.name %>
                  </p>
                  <%= if feature.description && feature.description != "" do %>
                    <p class="truncate text-sm text-gray-500" title={feature.description}>
                      <%= feature.description %>
                    </p>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
