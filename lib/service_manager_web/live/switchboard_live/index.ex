defmodule ServiceManagerWeb.Backend.SwitchboardLive.Index do
  use ServiceManagerWeb, :live_view
  require Logger
  alias ServiceManager.Processors.Switchboard
  alias Phoenix.LiveView.JS

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, "feature_flags")
    end

    current_node = Node.self()
    available_nodes = Switchboard.list_nodes()

    {:ok,
     socket
     |> assign(:loading, false)
     |> assign(:current_node, current_node)
     |> assign(:available_nodes, available_nodes)
     |> assign(:selected_node, current_node)
     |> assign(:metrics, Switchboard.get_metrics(current_node))
     |> assign(:features_by_category, group_features_by_category(current_node))
     |> assign(:search, "")
     |> assign(:filter, "all")}
  end

  @impl true
  def handle_event("toggle", %{"name" => name}, socket) do
    socket = assign(socket, :loading, true)

    # Get current state and toggle to opposite
    current_state = Switchboard.feature_enabled?(name, socket.assigns.selected_node)
    new_state = not current_state

    Logger.info("Toggle feature:")
    Logger.info("   - Name: #{name}")
    Logger.info("   - Node: #{socket.assigns.selected_node}")
    Logger.info("   - Current: #{current_state}")
    Logger.info("   - New: #{new_state}")

    {:ok, _flag} = Switchboard.toggle_feature(name, new_state, socket.assigns.selected_node)

    {:noreply,
     socket
     |> assign(:loading, false)
     |> assign(:metrics, Switchboard.get_metrics(socket.assigns.selected_node))
     |> assign(:features_by_category, group_features_by_category(socket.assigns.selected_node))}
  end

  def handle_event("select_node", params, socket) do
    Logger.info("Select node params: #{inspect(params)}")

    case params do
      %{"node" => node} ->
        Logger.info("Switching to node: #{node}")
        socket = assign(socket, :loading, true)

        # Add small delay to show loading state
        Process.sleep(100)

        {:noreply,
         socket
         |> assign(:loading, false)
         |> assign(:selected_node, node)
         |> assign(:available_nodes, Switchboard.list_nodes())
         |> assign(:metrics, Switchboard.get_metrics(node))
         |> assign(:features_by_category, group_features_by_category(node))}

      _ ->
        Logger.error("Invalid select_node params: #{inspect(params)}")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("search", %{"value" => search}, socket) do
    {:noreply, assign(socket, :search, search)}
  end

  @impl true
  def handle_event("filter", %{"value" => filter}, socket) do
    {:noreply, assign(socket, :filter, filter)}
  end

  @impl true
  def handle_info({:feature_updated, _flag}, socket) do
    node = socket.assigns.selected_node

    {:noreply,
     socket
     |> assign(:available_nodes, Switchboard.list_nodes())
     |> assign(:metrics, Switchboard.get_metrics(node))
     |> assign(:features_by_category, group_features_by_category(node))}
  end

  defp group_features_by_category(node) do
    Switchboard.get_all_features(node)
    |> Enum.group_by(& &1.category)
  end

  defp filter_features(features, search, filter) do
    features
    |> Enum.filter(fn feature ->
      name_matches =
        String.contains?(
          String.downcase(feature.name),
          String.downcase(search)
        )

      status_matches =
        case filter do
          "enabled" -> feature.enabled
          "disabled" -> not feature.enabled
          "unregistered" -> not feature.registered
          _ -> true
        end

      name_matches and status_matches
    end)
  end

  defp toggle_class(feature) do
    cond do
      not feature.registered -> "toggle-unregistered"
      feature.enabled -> "toggle-enabled"
      true -> "toggle-disabled"
    end
  end
end
