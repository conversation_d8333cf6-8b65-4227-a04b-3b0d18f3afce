defmodule ServiceManagerWeb.WalletUserSettingsLive do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.WalletAccounts

  def render(assigns) do
    ~H"""
    <.header class="text-center">
      Account Settings
      <:subtitle>Manage your account email address and password settings</:subtitle>
    </.header>

    <div class="space-y-12 divide-y">
      <div>
        <.simple_form
          for={@email_form}
          id="email_form"
          phx-submit="update_email"
          phx-change="validate_email"
        >
          <.input field={@email_form[:email]} type="email" label="Email" required />
          <.input
            field={@email_form[:current_password]}
            name="current_password"
            id="current_password_for_email"
            type="password"
            label="Current password"
            value={@email_form_current_password}
            required
          />
          <:actions>
            <.button phx-disable-with="Changing...">Change Email</.button>
          </:actions>
        </.simple_form>
      </div>
      <div>
        <.simple_form
          for={@password_form}
          id="password_form"
          action={~p"/walletusers/log_in?_action=password_updated"}
          method="post"
          phx-change="validate_password"
          phx-submit="update_password"
          phx-trigger-action={@trigger_submit}
        >
          <input
            name={@password_form[:email].name}
            type="hidden"
            id="hidden_wallet_user_email"
            value={@current_email}
          />
          <.input field={@password_form[:password]} type="password" label="New password" required />
          <.input
            field={@password_form[:password_confirmation]}
            type="password"
            label="Confirm new password"
          />
          <.input
            field={@password_form[:current_password]}
            name="current_password"
            type="password"
            label="Current password"
            id="current_password_for_password"
            value={@current_password}
            required
          />
          <:actions>
            <.button phx-disable-with="Changing...">Change Password</.button>
          </:actions>
        </.simple_form>
      </div>
    </div>
    """
  end

  def mount(%{"token" => token}, _session, socket) do
    socket =
      case WalletAccounts.update_wallet_user_email(socket.assigns.current_wallet_user, token) do
        :ok ->
          put_flash(socket, :info, "Email changed successfully.")

        :error ->
          put_flash(socket, :error, "Email change link is invalid or it has expired.")
      end

    {:ok, push_navigate(socket, to: ~p"/walletusers/settings")}
  end

  def mount(_params, _session, socket) do
    wallet_user = socket.assigns.current_wallet_user
    email_changeset = WalletAccounts.change_wallet_user_email(wallet_user)
    password_changeset = WalletAccounts.change_wallet_user_password(wallet_user)

    socket =
      socket
      |> assign(:current_password, nil)
      |> assign(:email_form_current_password, nil)
      |> assign(:current_email, wallet_user.email)
      |> assign(:email_form, to_form(email_changeset))
      |> assign(:password_form, to_form(password_changeset))
      |> assign(:trigger_submit, false)

    {:ok, socket}
  end

  def handle_event("validate_email", params, socket) do
    %{"current_password" => password, "wallet_user" => wallet_user_params} = params

    email_form =
      socket.assigns.current_wallet_user
      |> WalletAccounts.change_wallet_user_email(wallet_user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, email_form: email_form, email_form_current_password: password)}
  end

  def handle_event("update_email", params, socket) do
    %{"current_password" => password, "wallet_user" => wallet_user_params} = params
    wallet_user = socket.assigns.current_wallet_user

    case WalletAccounts.apply_wallet_user_email(wallet_user, password, wallet_user_params) do
      {:ok, applied_wallet_user} ->
        WalletAccounts.deliver_wallet_user_update_email_instructions(
          applied_wallet_user,
          wallet_user.email,
          &url(~p"/walletusers/settings/confirm_email/#{&1}")
        )

        info = "A link to confirm your email change has been sent to the new address."
        {:noreply, socket |> put_flash(:info, info) |> assign(email_form_current_password: nil)}

      {:error, changeset} ->
        {:noreply, assign(socket, :email_form, to_form(Map.put(changeset, :action, :insert)))}
    end
  end

  def handle_event("validate_password", params, socket) do
    %{"current_password" => password, "wallet_user" => wallet_user_params} = params

    password_form =
      socket.assigns.current_wallet_user
      |> WalletAccounts.change_wallet_user_password(wallet_user_params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, password_form: password_form, current_password: password)}
  end

  def handle_event("update_password", params, socket) do
    %{"current_password" => password, "wallet_user" => wallet_user_params} = params
    wallet_user = socket.assigns.current_wallet_user

    case WalletAccounts.update_wallet_user_password(wallet_user, password, wallet_user_params) do
      {:ok, wallet_user} ->
        password_form =
          wallet_user
          |> WalletAccounts.change_wallet_user_password(wallet_user_params)
          |> to_form()

        {:noreply, assign(socket, trigger_submit: true, password_form: password_form)}

      {:error, changeset} ->
        {:noreply, assign(socket, password_form: to_form(changeset))}
    end
  end
end
