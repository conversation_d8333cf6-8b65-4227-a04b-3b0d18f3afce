defmodule ServiceManagerWeb.Backend.CurrencyLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.CurrencyContext
  alias ServiceManager.Schemas.Currency
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/currencies"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    # Retrieve data based on params and generate pagination details
    data = CurrencyContext.retrieve(params)
    pagination = generate_pagination_details(data)

    # Remove existing streams from socket assigns
    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:currencies, data)}
  end

  def handle_params(params, _url, socket) do
    # Retrieve all data and generate pagination details
    data = CurrencyContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:currencies, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Currency")
    |> assign(:currency, CurrencyContext.get_currency!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Currency")
    |> assign(:currency, %Currency{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Currencies")
    |> assign(:currency, nil)
  end

  @impl true
  def handle_info(
        {ServiceManagerWeb.Backend.CurrencyLive.FormComponent, {:saved, currency}},
        socket
      ) do
    {:noreply, stream_insert(socket, :currencies, currency)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    currency = CurrencyContext.get_currency!(id)
    {:ok, _} = CurrencyContext.delete_currency(currency)

    Task.start(fn ->
      LogsContext.insert_log(
        "fatal",
        %{
          message: "Currency deleted successfully"
        },
        socket
      )
    end)

    {:noreply, stream_delete(socket, :currencies, currency)}
  end

  def handle_event("activate", %{"id" => id}, socket) do
    currency = CurrencyContext.get_currency!(id)

    CurrencyContext.update_data(currency, %{"status" => "active"}, socket.assigns.current_user)
    |> case do
      {:ok, resp} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Currency activation completed successfully",
              details: inspect(resp)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Currency activated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      error ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Currency activation failed",
              error: error
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:error, "Currency activation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("deactivate", %{"id" => id}, socket) do
    currency = CurrencyContext.get_currency!(id)

    CurrencyContext.update_data(currency, %{"status" => "inactive"}, socket.assigns.current_user)
    |> case do
      {:ok, resp} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Currency inactivated successfully",
              details: inspect(resp)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Currency inactivated successully")
         |> push_navigate(to: "#{@url}", replace: true)}

      error ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Currency inactivation failed",
              error: error
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:error, "Currency inactivation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
