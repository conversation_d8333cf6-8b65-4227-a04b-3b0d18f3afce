<.header>
  Currency <%= @currency.id %>
  <:subtitle>This is a currency record from your database.</:subtitle>
  <:actions>
    <.link
      patch={~p"/mobileBanking/currencies/#{@currency}/show/edit"}
      phx-click={JS.push_focus()}
    >
      <.button>Edit currency</.button>
    </.link>
  </:actions>
</.header>

<.list>
  <:item title="Code"><%= @currency.code %></:item>
  <:item title="Name"><%= @currency.name %></:item>
  <:item title="Symbol"><%= @currency.symbol %></:item>
  <:item title="Country"><%= @currency.country %></:item>
  <:item title="Minor unit"><%= @currency.minor_unit %></:item>
  <:item title="Status"><%= @currency.status %></:item>
  <:item title="Notes"><%= @currency.notes %></:item>
</.list>

<.back navigate={~p"/mobileBanking/currencies"}>Back to currencies</.back>

<.modal
  :if={@live_action == :edit}
  id="currency-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/currencies/#{@currency}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CurrencyLive.FormComponent}
    id={@currency.id}
    title={@page_title}
    action={@live_action}
    currency={@currency}
    patch={~p"/mobileBanking/currencies/#{@currency}"}
  />
</.modal>
