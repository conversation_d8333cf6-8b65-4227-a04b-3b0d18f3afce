defmodule ServiceManagerWeb.Backend.CurrencyLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.CurrencyContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage currency records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="currency-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.input field={@form[:code]} type="text" label="Code" />
          <.input field={@form[:name]} type="text" label="Name" />
          <.input field={@form[:symbol]} type="text" label="Symbol" />
          <.input field={@form[:country]} type="text" label="Country" />
          <.input field={@form[:exchange_rate]} type="number" label="Exchange rate" step="any" />
          <.input field={@form[:minor_unit]} type="number" label="Minor unit" />
        </div>
        <.input field={@form[:notes]} type="textarea" label="Notes" />
        <:actions>
          <.button phx-disable-with="Saving...">Save Currency</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{currency: currency} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(CurrencyContext.change_currency(currency))
     end)}
  end

  @impl true
  def handle_event("validate", %{"currency" => currency_params}, socket) do
    changeset = CurrencyContext.change_currency(socket.assigns.currency, currency_params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"currency" => currency_params}, socket) do
    save_currency(socket, socket.assigns.action, currency_params)
  end

  defp save_currency(socket, :edit, currency_params) do
    case CurrencyContext.update_currency(socket.assigns.currency, currency_params) do
      {:ok, currency} ->
        notify_parent({:saved, currency})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Currenncy Modified successfully",
              details: inspect(currency)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Currency updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Currenncy Modified Failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_currency(socket, :new, currency_params) do
    case CurrencyContext.create_currency(currency_params) do
      {:ok, currency} ->
        notify_parent({:saved, currency})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Currenncy created successfully",
              details: inspect(currency)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Currency created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "creation of a currency failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end
