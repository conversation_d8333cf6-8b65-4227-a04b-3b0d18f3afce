defmodule ServiceManagerWeb.Backend.CurrencyLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.CurrencyContext

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket, :current_path, Phoenix.LiveView.get_connect_params(socket)["current_path"])

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:currency, CurrencyContext.get_currency!(id))}
  end

  defp page_title(:show), do: "Show Currency"
  defp page_title(:edit), do: "Edit Currency"
end
