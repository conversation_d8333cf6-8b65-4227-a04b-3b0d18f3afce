<.header>
  Currency Management
  <:subtitle>
    Configure and manage supported currencies, their codes, symbols and status.
  </:subtitle>

  <:actions>
    <%= if can_create?(@current_user, :currencies) do %>
      <.link patch={~p"/mobileBanking/currencies/new"}>
        <.button>New Currency</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="currencies"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.currencies}
  row_click={fn {_id, currency} -> JS.navigate(~p"/mobileBanking/currencies/#{currency}") end}
>
  <:col :let={{_id, currency}} filter_item="code" label="Code"><%= currency.code %></:col>
  <:col :let={{_id, currency}} filter_item="name" label="Name"><%= currency.name %></:col>
  <:col :let={{_id, currency}} filter_item="symbol" label="Symbol"><%= currency.symbol %></:col>
  <:col :let={{_id, currency}} filter_item="country" label="Country">
    <%= currency.country %>
  </:col>
  <:col :let={{_id, currency}} filter_item="minor_unit" label="Minor unit">
    <%= currency.minor_unit %>
  </:col>
  <:col :let={{_id, currency}} filter_item="status" label="Status">
    <.status_pill status={currency.status} text={currency.status} />
  </:col>

  <:action :let={{id, dataset}}>
    <%= if has_any_permission?(@current_user, [:update, :delete, :activate], :currencies) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_activate?(@current_user, :currencies) do %>
          <%= if dataset.status == "active" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("deactivate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Deactivate
            </.link>
          <% else %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("activate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Activate
            </.link>
          <% end %>
        <% end %>

        <%= if can_update?(@current_user, :currencies) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/currencies/#{dataset}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :currencies) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: dataset.id}) |> hide("##{id}")}
            data-confirm="Are you sure?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="currency-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/currencies")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CurrencyLive.FormComponent}
    id={@currency.id || :new}
    title={@page_title}
    action={@live_action}
    currency={@currency}
    current_user={@current_user}
    patch={~p"/mobileBanking/currencies"}
  />
</.modal>
