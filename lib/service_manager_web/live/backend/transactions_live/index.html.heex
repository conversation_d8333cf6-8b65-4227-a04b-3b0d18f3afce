<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="transaction-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />
<br />

<.header>
  Listing Transactions
  <:actions>
    <.link patch={~p"/mobileBanking/transactions/new"}>
      <.button>Make Transafer</.button>
    </.link>
  </:actions>
</.header>

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/transactions/filter"}
  export_url={~p"/mobileBanking/transactions/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.patch(~p"/mobileBanking/transactions/#{data}/show") end}
>
  <:col :let={{_id, data}} filter_item="id" label="ID">
    <div class="text-[12px]"><%= data.id %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="id" label="TXN-Age">
    <%= if (age(data.inserted_at |> to_string(), 10)) == "new" do %>
      <span class="inline-flex items-center gap-x-1 rounded-md px-1 py-0.5 text-xs font-medium text-purple-700">
        <svg class="h-4 w-4 fill-green-500" viewBox="0 0 6 6" aria-hidden="true">
          <circle cx="3" cy="3" r="3" />
        </svg>
      </span>
    <% else %>
      <span class="inline-flex items-center gap-x-1 rounded-full px-1 py-0.5 text-xs font-medium text-yellow-800">
        <svg class="h-4 w-4 fill-yellow-500" viewBox="0 0 6 6" aria-hidden="true">
          <circle cx="3" cy="3" r="3" />
        </svg>
      </span>
    <% end %>
  </:col>
  <:col :let={{_id, data}} filter_item="reference" label="Internal Ref">
    <div class="text-[12px] whitespace-nowrap"><%= data.reference %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="reference" label="CBS Ref">
    <div class="text-[12px] whitespace-nowrap"><%= data.cbs_transaction_reference %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="type" label="Type">
    <div class="text-[12px] text-center">
      <%= if data.type == "direct-transfer" do %>
        DiT
      <% else %>
        <%= if data.type == "external-transfer" do %>
          ExT
        <% else %>
          <%= data.type |> String.first() |> String.upcase() %>
        <% end %>
      <% end %>
    </div>
  </:col>
  <:col :let={{_id, data}} filter_item="amount" label="Amount">
    <div class="text-[12px] text-right font-medium"><%= data.amount %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="from_account" label="Source Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.sender_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="to_account" label="Destination Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.receiver_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="status" label="Status">
    <.status_pill status={data.status} text={data.status} />
  </:col>
  <:col :let={{_id, data}} filter_item="inserted_at" label="Datetime">
    <div class="text-[12px] whitespace-nowrap"><%= data.inserted_at %></div>
  </:col>

  <:action :let={{id, data}}>
    <%= if can_update?(@current_user, :transactions) && data.status != "reversed" do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <.link
          patch={~p"/mobileBanking/transactions/#{data.id}/reverse"}
          class="block px-4 py-2 text-left text-sm text-orange-900 hover:bg-gray-10 hover:text-red-700"
        >
          Reverse
        </.link>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.FormComponent}
    id={@data.id || :new}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/transactions"}
  />
</.modal>
<!-- Charts Section -->
<div
  class="mt-8 grid grid-cols-2 gap-6"
  id="charts"
  phx-hook="Charts"
  data-chart-data={Jason.encode!(@chart_data)}
>
  <!-- Transaction Volume Over Time -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Transaction Volume Over Time</h3>
    <div id="volumeChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Status Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
    <div id="statusChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Amount Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Amount Distribution</h3>
    <div id="amountChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Type Breakdown -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Transaction Types</h3>
    <div id="typeChart" class="w-full h-[300px]"></div>
  </div>
</div>
<!-- ApexCharts Script -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts">
</script>

<.modal
  :if={@live_action == :show}
  id="transaction-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/transactions"}
  />
</.modal>

<.modal
  :if={@live_action == :reverse}
  id="reverse-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.ReverseReasonComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
    return_to={~p"/mobileBanking/transactions"}
  />
</.modal>
