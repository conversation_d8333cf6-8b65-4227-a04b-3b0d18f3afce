defmodule ServiceManagerWeb.Backend.TransactionsLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.BeneficieriesContext, as: MainContext

  alias ServiceManagerWeb.Api.Services.ProfileServiceController
  @url "/mobileBanking/beficiaries"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket, :current_path, Phoenix.LiveView.get_connect_params(socket)["current_path"])

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:data, MainContext.get_data!(id))}
  end

  defp page_title(:show), do: "Show User management"
  defp page_title(:edit), do: "Edit User management"

  @impl true
  def handle_event("toggle_status", %{"id" => id, "status" => status}, socket) do
    data = MainContext.get_data!(id)

    MainContext.update_data(
      data,
      %{"status" => toggle_state_status(status)},
      socket.assigns.current_user
    )
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Beneficiary updated successfully")
         |> push_navigate(to: @url <> "/#{id}", replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: @url, replace: true)}
    end
  end
end
