defmodule ServiceManagerWeb.Backend.TransactionsLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Transaction Details</h2>
          <p class="text-sm text-gray-500">ID: <%= @data.id %></p>
        </div>
        <div>
          <.status_pill status={@data.status} text={@data.status} />
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Transaction Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Transaction Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Reference</label>
              <p class="mt-1"><%= @data.reference %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">CBS Reference</label>
              <p class="mt-1"><%= @data.cbs_transaction_reference || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">External Reference</label>
              <p class="mt-1"><%= @data.external_reference || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Type</label>
              <p class="mt-1"><%= @data.type || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Amount</label>
              <p class="mt-1 font-medium">MWK <%= @data.amount %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Credit Amount</label>
              <p class="mt-1">MWK <%= @data.credit_amount %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Debit Amount</label>
              <p class="mt-1">MWK <%= @data.debit_amount %></p>
            </div>
          </div>
        </div>
        <!-- Account Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Source Account</label>
              <p class="mt-1"><%= @data.sender_account || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Destination Account</label>
              <p class="mt-1"><%= @data.receiver_account || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Description</label>
              <p class="mt-1"><%= @data.description || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Opening Balance</label>
              <p class="mt-1">MWK <%= @data.opening_balance || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Closing Balance</label>
              <p class="mt-1">MWK <%= @data.closing_balance || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Processing Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Processing Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Status</label>
              <p class="mt-1"><%= @data.status %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Callback Status</label>
              <p class="mt-1"><%= @data.callback_status || "--" %></p>
            </div>
            <%= if @data.transaction_details && @data.transaction_details["error"] do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Error Code</label>
                <p class="mt-1"><%= @data.transaction_details["error"]["code"] || "--" %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Error Description</label>
                <p class="mt-1"><%= @data.transaction_details["error"]["description"] || "--" %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Error Details</label>
                <p class="mt-1 text-sm whitespace-pre-wrap">
                  <%= @data.transaction_details["error"]["details"] || "--" %>
                </p>
              </div>
            <% end %>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @data.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @data.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Value Date</label>
              <p class="mt-1">
                <%= if @data.value_date do
                  @data.value_date |> to_string
                else
                  "--"
                end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
