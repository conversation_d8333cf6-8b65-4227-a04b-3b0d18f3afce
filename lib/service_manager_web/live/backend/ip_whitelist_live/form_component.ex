defmodule ServiceManagerWeb.Backend.IpWhitelistLive.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.IpWhitelist
  alias ServiceManager.WalletAccounts
  alias ServiceManager.ThirdParty

  @impl true
  def update(%{ip_whitelist: ip_whitelist} = assigns, socket) do
    changeset = IpWhitelist.changeset(ip_whitelist, %{})

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:changeset, changeset)
     |> assign(:wallet_users, list_wallet_users())
     |> assign(:third_party_api_keys, list_third_party_api_keys())}
  end

  @impl true
  def handle_event("validate", %{"ip_whitelist" => ip_whitelist_params}, socket) do
    changeset =
      socket.assigns.ip_whitelist
      |> IpWhitelist.changeset(ip_whitelist_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"ip_whitelist" => ip_whitelist_params}, socket) do
    save_ip_whitelist(socket, socket.assigns.action, ip_whitelist_params)
  end

  defp save_ip_whitelist(socket, :edit, ip_whitelist_params) do
    case Repo.update(IpWhitelist.changeset(socket.assigns.ip_whitelist, ip_whitelist_params)) do
      {:ok, _ip_whitelist} ->
        {:noreply,
         socket
         |> put_flash(:info, "IP whitelist entry updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp save_ip_whitelist(socket, :new, ip_whitelist_params) do
    # Add current user as user_id
    ip_whitelist_params = Map.put(ip_whitelist_params, "user_id", socket.assigns.current_user.id)

    case Repo.insert(IpWhitelist.changeset(%IpWhitelist{}, ip_whitelist_params)) do
      {:ok, _ip_whitelist} ->
        {:noreply,
         socket
         |> put_flash(:info, "IP whitelist entry created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp list_wallet_users do
    WalletAccounts.list_wallet_users()
  end

  defp list_third_party_api_keys do
    ThirdParty.list_api_keys()
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
      </.header>

      <.simple_form
        for={@changeset}
        id="ip-whitelist-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@changeset[:ip_address]} type="text" label="IP Address" />
        <.input field={@changeset[:description]} type="textarea" label="Description" />

        <.input
          field={@changeset[:status]}
          type="select"
          label="Status"
          options={[
            [key: "Active", value: :active],
            [key: "Inactive", value: :inactive]
          ]}
        />

        <.input
          field={@changeset[:environment]}
          type="select"
          label="Environment"
          options={[
            [key: "Development", value: :development],
            [key: "Staging", value: :staging],
            [key: "Production", value: :production]
          ]}
        />

        <.input
          field={@changeset[:risk_level]}
          type="select"
          label="Risk Level"
          options={[
            [key: "Low", value: :low],
            [key: "Medium", value: :medium],
            [key: "High", value: :high]
          ]}
        />

        <.input
          field={@changeset[:wallet_user_id]}
          type="select"
          label="Associated Wallet User"
          prompt="Select a wallet user"
          options={
            Enum.map(
              @wallet_users,
              &[key: "#{&1.first_name} #{&1.last_name} (#{&1.mobile_number})", value: &1.id]
            )
          }
        />

        <.input
          field={@changeset[:third_party_api_key_id]}
          type="select"
          label="Associated API Key"
          prompt="Select an API key"
          options={Enum.map(@third_party_api_keys, &[key: "#{&1.description}", value: &1.id])}
        />

        <.input field={@changeset[:expiry_date]} type="datetime-local" label="Expiry Date" />

        <:actions>
          <.button phx-disable-with="Saving...">Save IP Whitelist Entry</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end
end
