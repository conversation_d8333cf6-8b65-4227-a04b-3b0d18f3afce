defmodule ServiceManagerWeb.Backend.UserManagementLive.OpenAccountComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Schemas.User, as: UserManagementContext
  alias ServiceManager.Services.OpenAccountService
  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService

  alias ServiceManagerWeb.ProfileController
  require Logger

  # *************
  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.flash_group flash={@flash} />

      <.header>
        <%= @title %>
        <:subtitle>Use this form to open or create an account.</:subtitle>
      </.header>
      <%= if @details_fetched? do %>
        <.simple_form
          for={@form}
          id="user_management-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
            <.input field={@form[:first_name]} type="text" label="First Name" />
            <.input field={@form[:last_name]} type="text" label="Last Name" />
            <.input field={@form[:email]} type="email" label="Email" />

            <div class="grid grid-cols-2 gap-6 ">
              <.input
                field={@form[:identifier_type]}
                options={account_types()}
                type="select"
                label="Identifier Type"
              />
              <.input field={@form[:identifier_number]} type="text" label="Identifier Number" />
            </div>
            <.input field={@form[:phone]} type="text" label="Phone Number" />
            <.input field={@form[:country]} type="text" label="Country" />
            <.input field={@form[:street_address]} type="text" label="Street Address" />
            <.input field={@form[:city]} type="text" label="City" />
            <.input field={@form[:region]} type="text" label="Region/Province" />
            <.input field={@form[:zip]} type="text" label="Zip Code" />
          </div>

          <div class="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
            <.input field={@form[:initial_balance]} type="text" label="Balance" />
            <.input field={@form[:account_number]} type="text" label="Account Number" />
          </div>

          <div class="border-b border-fdh-blue pb-12">
            <div class="mt-10  grid grid-cols-1 md:grid-cols-3 gap-10 gap-y-20">
              <.input
                field={@form[:currency]}
                options={[{"USD", "USD"}, {"MWK", "MWK"}]}
                type="radio"
                label="Currency"
              />
              <.input
                field={@form[:account_type]}
                options={[{"Current", "current"}, {"Savings", "saving"}]}
                type="radio"
                label="Account Type"
              />
              <.input
                field={@form[:notifications]}
                options={[
                  {"Everything", "everything"},
                  {"Same as email", "email"},
                  {"No push notifications", "nothing"}
                ]}
                type="radio"
                label="Notifications"
              />
            </div>
          </div>

          <:actions>
            <.button phx-disable-with="Linking accounts...">Proceed to link</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="user_management-form" phx-target={@myself} phx-submit="lookup">
          <.input field={@form[:account_number]} type="text" label="Account Number" />
          <:actions>
            <.button phx-disable-with="Looking up...">Look for the account</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{user_management: user_management} = assigns, socket) do
    user_management = %UserManagementContext{}
    details_fetched? = false

    {:ok,
     socket
     |> assign(:details_fetched?, details_fetched?)
     |> assign(:user_management, user_management)
     |> assign(:data, %{})
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(UserManagementContext.change_data(%{}))
     end)}
  end

  @impl true
  def handle_event("validate", %{"user" => user_management_params}, socket) do
    changeset =
      UserManagementContext.change_data(socket.assigns.user_management, user_management_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("lookup", %{"user" => %{"account_number" => account_number}}, socket) do
    case get_account_details_per_account_number(account_number) do
      {:ok, params} ->
        changeset = UserManagementContext.change_data(socket.assigns.user_management, params)

        {:noreply,
         socket
         |> assign(:form, to_form(changeset, action: :validate))
         |> assign(:details_fetched?, true)
         |> assign(:data, params)
         |> assign(:user_management, struct(UserManagementContext, params))}

      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, message)
         |> assign(:details_fetched?, false)}
    end
  end

  def handle_event("save", params, socket) do
    save_user_management(socket, socket.assigns.action, params)
  end

  defp save_user_management(socket, _action, _user_params) do
    param = socket.assigns.data

    socket.assigns.data
    |> Map.put("name", "#{param["first_name"]} #{param["last_name"]}")
    # |> Map.put("balance", user_params["initial_balance"])
    |> Map.put("role", "user")
    |> Map.put("id_number", socket.assigns.data["identifier_number"] || "")
    # |> Map.put("account_type", "current")
    |> Map.put("notifications", "everything")
    |> ProfileController.link_account()
    # |> IO.inspect(label: "======*********===========")
    |> case do
      %{"status" => true} = resp ->
        {:noreply,
         socket
         |> put_flash(:info, resp["message"] || resp["message"] || "Account linked successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      resp ->
        {:noreply,
         socket
         |> put_flash(:error, resp["message"] || resp["message"] || "Account Linking failed")
         |> push_navigate(to: socket.assigns.patch, replace: true)}
    end
  rescue
    e in RuntimeError ->
      Logger.error(inspect(e))

      {:noreply,
       socket
       |> put_flash(:error, "An error occurred: #{e.message}")
       |> push_navigate(to: socket.assigns.patch, replace: true)}

    e ->
      Logger.error(inspect(e))

      {:noreply,
       socket
       |> put_flash(:error, "An unexpected error occurred")
       |> push_navigate(to: socket.assigns.patch, replace: true)}
  end

  def account_types() do
    [
      {"National Registration Number", "NRC"},
      {"Passport", "PASSPORT"}
    ]
  end

  def get_account_details_per_account_number(account_number) do
    case ProfileFromRemoteService.get_profile_by_account_number_v3(account_number) do
      {:error, %{reason: :timeout}} ->
        {:error, "Connection timed out. Please try again."}

      {:error, %Mint.TransportError{reason: reason}} ->
        {:error, "Connection error: #{reason}. Please try again."}

      {:error, error} when is_map(error) ->
        {:error, Map.get(error, :message, "Failed to fetch account details")}

      {:error, error} when is_binary(error) ->
        {:error, error}

      {:ok, response} when is_map(response) ->
        {:ok, response}

      unexpected ->
        Logger.error("Unexpected response format: #{inspect(unexpected)}")
        {:error, "Unexpected error occurred while fetching account details"}
    end
  end
end
