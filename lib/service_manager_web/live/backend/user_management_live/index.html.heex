<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="user-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />

<.header>
  Customers List
  <:actions>
    <%= if can_create?(@current_user, :customers) do %>
      <.link patch={~p"/mobileBanking/user_managements/openAccount"}>
        <.button>Register Customer</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<div id="user-management-container" phx-hook="UserManagementData">
  <.table
    id="user_managements"
    filter_params={@filter_params}
    pagination={@pagination}
    selected_column={@selected_column}
    filter_url={~p"/mobileBanking/user_managements/filter"}
    export_url={~p"/mobileBanking/user_managements/ExcelExportFilter"}
    show_filter={true}
    show_export={true}
    rows={@streams.user_managements}
    row_click={
      fn {_id, user_management} ->
        JS.patch(~p"/mobileBanking/user_managements/#{user_management}")
      end
    }
    class="max-w-full"
  >
    <:col :let={{_id, user_management}} filter_item="id" label="ID">
      <div class="text-[12px]"><%= user_management.id %></div>
    </:col>

    <:col :let={{_id, user_management}} filter_item="customer_no" label="Customer No">
      <div class="text-[12px]"><%= user_management.customer_no %></div>
    </:col>

    <:col :let={{_id, user_management}} filter_item="email" label="Username">
      <div class="text-[12px]"><%= user_management.username %></div>
    </:col>
    <:col :let={{_id, user_management}} filter_item="full_name" label="Full Name">
      <div class="text-[12px]">
        <%= if !is_nil(user_management.first_name) && !is_nil(user_management.last_name) do %>
          <%= "#{user_management.first_name} #{user_management.last_name}" %>
        <% end %>
      </div>
    </:col>
    <:col :let={{_id, user_management}} filter_item="phone_number" label="Phone Number">
      <div class="text-[12px]"><%= user_management.phone_number %></div>
    </:col>

    <:col :let={{_id, user_management}} filter_item="account_number" label="Account No">
      <div class="text-[12px]"><%= user_management.account_number %></div>
    </:col>

    <:col :let={{_id, user_management}} filter_item="disabled" label="Profile Status">
      <.status_pill
        status={!user_management.disabled}
        text={handle_disabled(user_management.disabled)}
      />
    </:col>
    <:col :let={{_id, user_management}} filter_item="approved" label="Account Status">
      <.status_pill
        status={user_management.approved}
        text={handle_approval(user_management.approved)}
      />
    </:col>

    <:col
      :let={{_id, user_management}}
      filter_item="registration_status"
      label="Registration Status"
    >
      <.status_pill
        status={user_management.approved && !user_management.disabled}
        text={handle_registration_status(user_management.approved, user_management.disabled)}
      />
    </:col>

    <:action :let={{id, user_management}}>
      <%= if has_any_permission?(@current_user, [:view, :update, :delete, :approve, :activate], :customers) do %>
        <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_update?(@current_user, :customers) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/user_managements/#{user_management}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_view?(@current_user, :customers) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/user_managements/#{user_management}"}
          >
            View Details
          </.link>
        <% end %>
        <%= if can_update?(@current_user, :customers) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={
              JS.push("reset_password",
                value: %{id: user_management.id, email: user_management.username}
              )
            }
            data-confirm="Are you sure you want to reset this user's password?"
          >
            Reset Password
          </.link>
        <% end %>
        <!-- Profile Status Management -->
        <%= if can_activate?(@current_user, :customers) do %>
          <%= if user_management.disabled do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("enable", value: %{id: user_management.id, email: user_management.username})
              }
              data-confirm="Are you sure you want to enable this profile?"
            >
              Enable Profile
            </.link>
          <% else %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("open_reason_modal", value: %{id: user_management.id, action: "disable"})
              }
            >
              Disable Profile
            </.link>
          <% end %>
        <% end %>
        <!-- Approval Status Management -->
        <%= if can_approve?(@current_user, :customers) do %>
          <%= if user_management.approved do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("open_reason_modal", value: %{id: user_management.id, action: "unapprove"})
              }
            >
              Deactivate Profile
            </.link>
          <% else %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("approve", value: %{id: user_management.id})}
              data-confirm="Are you sure you want to activate this profile?"
            >
              Activate Profile
            </.link>
          <% end %>
        <% end %>
        <!-- Registration Status Management -->
        <%= if can_activate?(@current_user, :customers) do %>
          <%= cond do %>
            <% user_management.approved && !user_management.disabled -> %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={
                  JS.push("open_reason_modal",
                    value: %{id: user_management.id, action: "deregister"}
                  )
                }
              >
                Deregister Profile
              </.link>
            <% user_management.approved && user_management.disabled -> %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("re_register", value: %{id: user_management.id})}
                data-confirm="Are you sure you want to re-register this profile?"
              >
                Re-register Profile
              </.link>
            <% true -> %>
              <!-- No registration management options for unapproved profiles -->
          <% end %>
        <% end %>
        <!-- Delete Profile Option -->
        <%= if can_delete?(@current_user, :customers) && (!user_management.approved || user_management.disabled) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-red-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: user_management.id})}
            data-confirm="Are you sure you want to permanently delete this profile? This action cannot be undone."
          >
            Delete Profile
          </.link>
        <% end %>
      </.dropdown>
      <% end %>
    </:action>
  </.table>

  <.modal
    :if={@live_action in [:new, :edit]}
    id="user_management-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/user_managements")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.UserManagementLive.FormComponent}
      id={@user_management.id || :new}
      title={@page_title}
      action={@live_action}
      current_user={@current_user}
      user_management={@user_management}
      patch={~p"/mobileBanking/user_managements"}
    />
  </.modal>
</div>
<!-- Reason Modal -->
<.modal :if={@show_reason_modal} id="reason-modal" show on_cancel={JS.push("close_reason_modal")}>
  <.header>
    <%= case @action_type do %>
      <% "disable" -> %>
        Disable Profile
      <% "unapprove" -> %>
        Deactivate Profile
      <% "deregister" -> %>
        Deregister Profile
    <% end %>
  </.header>

  <form phx-submit="save_with_reason">
    <div class="mt-6 space-y-4">
      <.input
        type="textarea"
        name="reason"
        label="Reason"
        value={@reason}
        phx-change="update_reason"
        required
      />
    </div>

    <div class="mt-6 flex justify-between">
      <.button type="button" phx-click="close_reason_modal">Cancel</.button>
      <.button
        type="submit"
        class="ml-3"
        disabled={@reason == ""}
        data-confirm={
          case @action_type do
            "disable" -> "Are you sure you want to disable this profile?"
            "unapprove" -> "Are you sure you want to deactivate this profile?"
            "deregister" -> "Are you sure you want to deregister this profile?"
            _ -> "Are you sure you want to proceed?"
          end
        }
      >
        <%= case @action_type do
          "disable" -> "Disable"
          "unapprove" -> "Deactivate"
          "deregister" -> "Deregister"
          _ -> "Save"
        end %>
      </.button>
    </div>
  </form>
</.modal>

<.modal
  :if={@live_action in [:open_account]}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/user_managements")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.UserManagementLive.OpenAccountComponent}
    id={:open_account}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    current_user={@current_user}
    patch={~p"/mobileBanking/user_managements"}
  />
</.modal>

<.modal
  :if={@live_action == :show}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/user_managements")}
  max-width="max-w-[95%]"
>
  <.live_component
    module={ServiceManagerWeb.Backend.UserManagementLive.ShowComponent}
    id={@user_management.id}
    title={@page_title}
    user_management={@user_management}
    patch={~p"/mobileBanking/user_managements"}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/user_managements")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.UserManagementLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/user_managements"}
  />
</.modal>
