<.header>
  User management <%= @user_management.id %>
  <:subtitle>This is a User record from your database.</:subtitle>
  <:actions>
    <%= if @user_management.approved do %>
      <.link
        phx-click={
          JS.push("disable", value: %{id: @user_management.id, email: @user_management.email})
        }
        data-confirm="Are you sure?"
      >
        <.button3>Disapprove</.button3>
      </.link>
    <% else %>
      <.link
        phx-click={
          JS.push("approve", value: %{id: @user_management.id, email: @user_management.email})
        }
        data-confirm="Are you sure?"
      >
        <.button2>Approve</.button2>
      </.link>
    <% end %>

    <.link
      patch={~p"/mobileBanking/user_managements/#{@user_management}/show/edit"}
      phx-click={JS.push_focus()}
    >
      <.button>Edit user_management</.button>
    </.link>
  </:actions>
</.header>

<.list>
  <:item title="Email"><%= @user_management.email %></:item>

  <:item title="Nickname"><%= @user_management.nickname %></:item>
  <:item title="First name"><%= @user_management.first_name %></:item>
  <:item title="Last name"><%= @user_management.last_name %></:item>
  <:item title="Phone number"><%= @user_management.phone_number %></:item>
  <:item title="Date of birth"><%= @user_management.date_of_birth %></:item>
  <:item title="Address"><%= @user_management.address %></:item>
  <:item title="City"><%= @user_management.city %></:item>
  <:item title="State"><%= @user_management.state %></:item>
  <:item title="Zip"><%= @user_management.zip %></:item>
  <:item title="Country"><%= @user_management.country %></:item>
</.list>

<.back navigate={~p"/mobileBanking/user_managements"}>Back to user_managements</.back>

<.modal
  :if={@live_action == :edit}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/user_managements/#{@user_management}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.UserManagementLive.FormComponent}
    id={@user_management.id}
    title={@page_title}
    action={@live_action}
    current_user={@current_user}
    user_management={@user_management}
    patch={~p"/mobileBanking/user_managements/#{@user_management}"}
  />
</.modal>
