defmodule ServiceManagerWeb.Backend.UserManagementLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">User Details</h2>
          <p class="text-sm text-gray-500">
            Customer No: <%= @user_management.customer_no || "--" %>
          </p>
        </div>
        <div class="text-right">
          <div class="inline-flex items-center px-4 py-2 rounded-md bg-gray-100">
            <div class={"w-2 h-2 rounded-full mr-2 #{if @user_management.approved, do: "bg-green-500", else: "bg-red-500"}"} />
            <span class="text-sm font-medium text-gray-700">
              <%= if @user_management.approved, do: "Approved", else: "Not Approved" %>
            </span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Personal Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Personal Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Full Name</label>
              <p class="mt-1">
                <%= "#{@user_management.first_name} #{@user_management.last_name}" %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Username</label>
              <p class="mt-1"><%= @user_management.username || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Email</label>
              <p class="mt-1"><%= @user_management.email || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Phone Number</label>
              <p class="mt-1"><%= @user_management.phone_number || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Date of Birth</label>
              <p class="mt-1"><%= @user_management.date_of_birth || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Address Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Address Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Address</label>
              <p class="mt-1"><%= @user_management.address || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">City</label>
              <p class="mt-1"><%= @user_management.city || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">State</label>
              <p class="mt-1"><%= @user_management.state || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">ZIP Code</label>
              <p class="mt-1"><%= @user_management.zip || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Country</label>
              <p class="mt-1"><%= @user_management.country || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Account Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Account Number</label>
              <p class="mt-1"><%= @user_management.account_number || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Account Type</label>
              <p class="mt-1"><%= @user_management.account_type || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Balance</label>
              <p class="mt-1">
                <%= if @user_management.account_balance do %>
                  <%= @user_management.account_balance %> <%= @user_management.currency || "MWK" %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Nickname</label>
              <p class="mt-1"><%= @user_management.nickname || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Allow Cheque Request Upload</label>
              <p class="mt-1">
                <%= if @user_management.allow_cheque_request_upload, do: "Yes", else: "No" %>
              </p>
            </div>
          </div>
        </div>
        <!-- System Status -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">System Status</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Account Status</label>
              <div class="mt-2 space-y-2">
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @user_management.disabled, do: "bg-red-500", else: "bg-green-500"}"} />
                  <span class="text-sm">
                    <%= if @user_management.disabled, do: "Disabled", else: "Enabled" %>
                  </span>
                </div>
                <%= if @user_management.disabled_reason do %>
                  <p class="text-sm text-gray-600 ml-4">
                    Reason: <%= @user_management.disabled_reason %>
                  </p>
                <% end %>
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @user_management.first_time_login, do: "bg-yellow-500", else: "bg-green-500"}"} />
                  <span class="text-sm">
                    <%= if @user_management.first_time_login,
                      do: "First Time Login",
                      else: "Returning User" %>
                  </span>
                </div>
              </div>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Approval Status</label>
              <div class="mt-2 space-y-2">
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @user_management.approved, do: "bg-green-500", else: "bg-red-500"}"} />
                  <span class="text-sm">
                    <%= if @user_management.approved, do: "Approved", else: "Not Approved" %>
                  </span>
                </div>
                <%= if @user_management.approval_reason do %>
                  <p class="text-sm text-gray-600 ml-4">
                    Reason: <%= @user_management.approval_reason %>
                  </p>
                <% end %>
              </div>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Registration Status</label>
              <div class="mt-2 space-y-2">
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @user_management.approved && !@user_management.disabled, do: "bg-green-500", else: "bg-red-500"}"} />
                  <span class="text-sm">
                    <%= cond do %>
                      <% @user_management.approved && !@user_management.disabled -> %>
                        Registered
                      <% @user_management.approved && @user_management.disabled -> %>
                        Deregistered
                      <% true -> %>
                        Not Registered
                    <% end %>
                  </span>
                </div>
                <%= if @user_management.deregistration_reason do %>
                  <p class="text-sm text-gray-600 ml-4">
                    Reason: <%= @user_management.deregistration_reason %>
                  </p>
                <% end %>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Sync Status</label>
              <div class="mt-2">
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{case @user_management.sync_status do
                    "synced" -> "bg-green-500"
                    "pending" -> "bg-yellow-500"
                    _ -> "bg-red-500"
                  end}"} />
                  <span class="text-sm"><%= @user_management.sync_status || "--" %></span>
                </div>
                <%= if @user_management.sync_error do %>
                  <p class="mt-1 text-sm text-red-600"><%= @user_management.sync_error %></p>
                <% end %>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Sync</label>
              <p class="mt-1">
                <%= if @user_management.last_sync_at do %>
                  <%= @user_management.last_sync_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
          </div>
        </div>
        <!-- Notification Settings -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Notification Settings</h3>
          <div class="space-y-4">
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <div class={"w-2 h-2 rounded-full #{if @user_management.email_notifications, do: "bg-green-500", else: "bg-red-500"}"} />
                <span class="text-sm">Email Notifications</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class={"w-2 h-2 rounded-full #{if @user_management.sms_notifications, do: "bg-green-500", else: "bg-red-500"}"} />
                <span class="text-sm">SMS Notifications</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class={"w-2 h-2 rounded-full #{if @user_management.push_notifications, do: "bg-green-500", else: "bg-red-500"}"} />
                <span class="text-sm">Push Notifications</span>
              </div>
            </div>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @user_management.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @user_management.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Confirmed At</label>
              <p class="mt-1">
                <%= if @user_management.confirmed_at do %>
                  <%= @user_management.confirmed_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
