defmodule ServiceManagerWeb.Backend.UserManagementLive.Index do
  use ServiceManagerWeb, :live_view
  import Ecto.Query

  alias ServiceManager.Contexts.UserManagementContext
  alias ServiceManager.Accounts.User, as: UserManagement
  alias ServiceManagerWeb.Api.Services.ProfileServiceController
  alias ServiceManagerWeb.ProfileController
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Api.Services.AuthenticationService
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/user_managements"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:stats, get_user_stats())
      |> assign(:show_reason_modal, false)
      |> assign(:action_type, nil)
      |> assign(:selected_user_id, nil)
      |> assign(:reason, "")

    {:ok, socket}
  end

  def handle_event("open_reason_modal", %{"id" => id, "action" => action}, socket) do
    {:noreply,
     socket
     |> assign(:show_reason_modal, true)
     |> assign(:action_type, action)
     |> assign(:selected_user_id, id)
     |> assign(:reason, "")}
  end

  def handle_event("close_reason_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_reason_modal, false)
     |> assign(:action_type, nil)
     |> assign(:selected_user_id, nil)
     |> assign(:reason, "")}
  end

  def handle_event("save_with_reason", _params, socket) do
    reason = socket.assigns.reason
    action = socket.assigns.action_type
    id = socket.assigns.selected_user_id

    case action do
      "disable" -> handle_disable(id, reason, socket)
      "unapprove" -> handle_unapprove(id, reason, socket)
      "deregister" -> handle_deregister(id, reason, socket)
      _ -> {:noreply, socket}
    end
  end

  def handle_event("update_reason", %{"reason" => reason}, socket) do
    {:noreply, assign(socket, :reason, reason)}
  end

  defp handle_disable(id, reason, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => true,
            "disabled_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been disabled")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to disable profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_unapprove(id, reason, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          UserManagement.update_changeset(user, %{
            "approved" => false,
            "approval_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been unapproved")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to unapprove profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_deregister(id, reason, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => true,
            "deregistration_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been deregistered")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to deregister profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp get_user_stats do
    total_users = Repo.aggregate(UserManagement, :count, :id)

    enabled_users =
      Repo.aggregate(from(u in UserManagement, where: u.disabled == false), :count, :id)

    disabled_users =
      Repo.aggregate(from(u in UserManagement, where: u.disabled == true), :count, :id)

    approved_users =
      Repo.aggregate(from(u in UserManagement, where: u.approved == true), :count, :id)

    unapproved_users =
      Repo.aggregate(from(u in UserManagement, where: u.approved == false), :count, :id)

    synced_users =
      Repo.aggregate(from(u in UserManagement, where: u.sync_status == "synced"), :count, :id)

    unsynced_users = total_users - synced_users

    [
      %{
        title: "Total Users",
        value: total_users
      },
      %{
        title: "Profile Status",
        value: "#{enabled_users} / #{disabled_users}",
        comparison: "enabled / disabled"
      },
      %{
        title: "Approval Status",
        value: "#{approved_users} / #{unapproved_users}",
        comparison: "approved / unapproved"
      },
      %{
        title: "CBS Sync Status",
        value: "#{synced_users} / #{unsynced_users}",
        comparison: "synced / unsynced"
      }
    ]
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = UserManagementContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:user_managements, data)}
  end

  def handle_params(params, url, socket) do
    data = UserManagementContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:user_managements, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit User management")
    |> assign(:user_management, UserManagementContext.get_data!(id))
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "User Details")
    |> assign(:user_management, UserManagementContext.get_data!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New User management")
    |> assign(:user_management, %UserManagement{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing User managements")
    |> assign(:user_management, nil)
  end

  defp apply_action(socket, :open_account, _params) do
    socket
    |> assign(:page_title, "Open An Account")
    |> assign(:user_management, %ServiceManager.Schemas.User{})
  end

  @impl true
  def handle_event("approve", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Use update_changeset to update the approved field
        changeset =
          UserManagement.update_changeset(user, %{
            "approved" => true
          })

        # Generate a random password (12 characters to meet minimum requirement)
        new_password =
          Enum.map(1..12, fn _ -> Enum.random(0..9) end)
          |> Enum.join()
          |> String.replace(~r/(\d{4})(?=\d)/, "\\1-")

        changeset =
          changeset
          |> UserManagement.update_password_changeset(%{
            password: new_password,
            first_time_login: true
          })

        case Repo.update(changeset) do
          {:ok, updated_user} ->
            message =
              "Your profile has been registered. Your username is #{updated_user.username} and temporary password is: #{new_password}. Please change it upon your first login."

            ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
              msisdn: updated_user.phone_number,
              message: message
            })

            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been approved")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to approve profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("unapprove", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Use update_changeset to update the approved field
        changeset =
          UserManagement.update_changeset(user, %{
            "approved" => false
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been unapproved")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to unapprove profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("reset_password", %{"id" => id, "email" => username}, socket) do
    # Generate a secure password following the policy (min 12 chars, max 72 chars)
    case AuthenticationService.reset_password(username) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password has been reset and sent to user's phone number")
         |> push_navigate(to: @url, replace: true)}

      {:error, _reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reset password")
         |> push_navigate(to: @url, replace: true)}
    end
  end

  def handle_event("disable", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Use update_changeset to update the disabled field
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => true
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been disabled")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to disable profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("enable", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Use update_changeset to update the disabled field
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => false
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been enabled")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to enable profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("delete", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        UserManagementContext.partial_delete(user, socket.assigns.current_user)
        |> case do
          {:ok, _deleted_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been deleted")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to delete profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("deregister", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Keep approved true but set disabled to true
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => true
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been deregistered")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to deregister profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("re_register", %{"id" => id}, socket) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        # Keep approved true but set disabled to false
        changeset =
          UserManagement.update_changeset(user, %{
            "disabled" => false
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:stats, get_user_stats())
             |> put_flash(:info, "Profile has been re-registered")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to re-register profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_registration_status(approved?, disabled?) do
    cond do
      approved? && !disabled? -> "Registered"
      approved? && disabled? -> "Deregistered"
      true -> "Not Registered"
    end
  end

  def handle_approval(is_approved?) do
    if is_approved? do
      "Account Active"
    else
      "Deactivated"
    end
  end

  def handle_disabled(is_approved?) do
    if is_approved? != true do
      "Profile Enabled"
    else
      "Profile Disabled"
    end
  end

  # Generate a secure password that meets the policy requirements
  defp generate_secure_password do
    # Define character sets
    # Removed confusing I,O
    uppercase = "ABCDEFGHJKLMNPQRSTUVWXYZ"
    # Removed confusing l,o
    lowercase = "abcdefghijkmnpqrstuvwxyz"
    # Removed confusing 0,1
    numbers = "********"
    special = "!@#$%^&*"

    # Ensure at least one of each type
    initial = [
      String.at(uppercase, :rand.uniform(String.length(uppercase)) - 1),
      String.at(lowercase, :rand.uniform(String.length(lowercase)) - 1),
      String.at(numbers, :rand.uniform(String.length(numbers)) - 1),
      String.at(special, :rand.uniform(String.length(special)) - 1)
    ]

    # Add 8 more random characters for a total of 12
    all_chars = uppercase <> lowercase <> numbers <> special

    remaining =
      for _ <- 1..8 do
        String.at(all_chars, :rand.uniform(String.length(all_chars)) - 1)
      end

    # Combine and shuffle
    (initial ++ remaining)
    |> Enum.shuffle()
    |> Enum.join()
  end
end
