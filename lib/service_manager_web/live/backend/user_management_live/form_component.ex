defmodule ServiceManagerWeb.Backend.UserManagementLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.UserManagementContext
  alias ServiceManager.Contexts.RolesAndPermissionsContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage user_management records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="user_management-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
          <.input field={@form[:first_name]} type="text" disabled label="First name" />
          <.input field={@form[:last_name]} type="text" disabled label="Last name" />
          <.input field={@form[:email]} type="text" disabled label="Email" />
          <.input field={@form[:nickname]} type="text" label="Nickname" />
          <.input field={@form[:phone_number]} type="phone" label="Phone number" />
          <.input field={@form[:date_of_birth]} type="date" disabled label="Date of birth" />
          <.input class="col-span-2" field={@form[:address]} type="text" disabled label="Address" />
          <.input field={@form[:city]} type="text" disabled label="City" />
          <.input field={@form[:state]} type="text" disabled label="State" />
          <.input field={@form[:zip]} type="text" disabled label="Zip" />
          <.input field={@form[:country]} type="text" disabled label="Country" />
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save User</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{user_management: user_management} = assigns, socket) do
    roles = RolesAndPermissionsContext.get_roles()

    {:ok,
     socket
     |> assign(:roles, roles)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(UserManagementContext.change_data(user_management))
     end)}
  end

  @impl true
  def handle_event("validate", %{"user" => user_management_params}, socket) do
    changeset =
      UserManagementContext.change_data(socket.assigns.user_management, user_management_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"user" => user_management_params}, socket) do
    save_user_management(socket, socket.assigns.action, user_management_params)
  end

  defp save_user_management(socket, :edit, user_management_params) do
    UserManagementContext.update_data(
      socket.assigns.user_management,
      user_management_params,
      socket.assigns.current_user
    )
    |> case do
      {:ok, user_management} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "User management updated successfully",
              details: inspect(user_management)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "User management updated successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "User management update failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_user_management(socket, :new, user_management_params) do
    UserManagementContext.insert_data(user_management_params, socket.assigns.current_user)
    |> case do
      {:ok, user_management} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "User management create successfully",
              details: inspect(user_management)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "User management created successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "User management create failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
