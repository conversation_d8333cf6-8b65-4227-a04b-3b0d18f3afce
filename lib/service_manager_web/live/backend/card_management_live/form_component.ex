defmodule ServiceManagerWeb.Backend.CardManagementLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaCard

  @impl true
  def update(%{card: card} = assigns, socket) do
    # Convert metadata to JSON string if it exists
    card =
      update_in(card.metadata, fn
        nil -> "{}"
        metadata when is_map(metadata) -> Jason.encode!(metadata, pretty: true)
        other -> other
      end)

    changeset = SchemaCard.changeset(card, %{})

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:changeset, changeset)}
  end

  @impl true
  def handle_event("validate", %{"schema_card" => card_params} = params, socket) do
    # Clean up params
    card_params =
      card_params
      |> maybe_parse_metadata()
      |> maybe_convert_boolean("is_virtual")

    changeset =
      socket.assigns.card
      |> SchemaCard.changeset(card_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  defp maybe_parse_metadata(params) do
    metadata =
      case params["metadata"] do
        nil ->
          "{}"

        "" ->
          "{}"

        metadata_string ->
          case Jason.decode(metadata_string) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    Map.put(params, "metadata", metadata)
  end

  defp maybe_convert_boolean(params, field) do
    value =
      case params[field] do
        "true" -> true
        "false" -> false
        true -> true
        false -> false
        _ -> false
      end

    Map.put(params, field, value)
  end

  def handle_event("save", %{"schema_card" => card_params} = params, socket) do
    # Clean up params
    card_params =
      card_params
      |> maybe_parse_metadata()
      |> maybe_convert_boolean("is_virtual")

    save_card(socket, socket.assigns.action, card_params)
  end

  defp save_card(socket, :edit, card_params) do
    case Repo.update(SchemaCard.update_changeset(socket.assigns.card, card_params)) do
      {:ok, _card} ->
        {:noreply,
         socket
         |> put_flash(:info, "Card updated successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp save_card(socket, :new, card_params) do
    case Repo.insert(SchemaCard.changeset(%SchemaCard{}, card_params)) do
      {:ok, _card} ->
        {:noreply,
         socket
         |> put_flash(:info, "Card created successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
      </.header>

      <.form
        :let={f}
        for={@changeset}
        id="card-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="space-y-8 divide-y divide-gray-200">
          <!-- Card Information -->
          <div class="space-y-6 pt-8">
            <div>
              <h3 class="text-lg font-medium leading-6 text-gray-900">Card Information</h3>
              <p class="mt-1 text-sm text-gray-500">Basic card details and settings.</p>
            </div>

            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <.input
                  field={f[:card_type]}
                  type="select"
                  label="Card Type"
                  options={[{"VISA", "visa"}, {"Mastercard", "mastercard"}]}
                />
              </div>

              <div>
                <.input field={f[:currency]} type="text" label="Currency" />
              </div>

              <div>
                <.input field={f[:daily_limit]} type="number" step="any" label="Daily Limit" />
              </div>

              <div>
                <.input field={f[:monthly_limit]} type="number" step="any" label="Monthly Limit" />
              </div>

              <div>
                <.input field={f[:is_virtual]} type="checkbox" label="Virtual Card" />
              </div>
            </div>
          </div>
          <!-- Linked Accounts -->
          <div class="space-y-6 pt-8">
            <div>
              <h3 class="text-lg font-medium leading-6 text-gray-900">Linked Accounts</h3>
              <p class="mt-1 text-sm text-gray-500">
                Link this card to a user, wallet, or beneficiary.
              </p>
            </div>

            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <.input field={f[:user_id]} type="text" label="Customer ID" />
              </div>

              <div>
                <.input field={f[:wallet_user_id]} type="text" label="Wallet User ID" />
              </div>

              <div>
                <.input field={f[:beneficiary_id]} type="text" label="Beneficiary ID" />
              </div>
            </div>
          </div>
          <!-- Additional Information -->
          <div class="space-y-6 pt-8">
            <div>
              <h3 class="text-lg font-medium leading-6 text-gray-900">Additional Information</h3>
              <p class="mt-1 text-sm text-gray-500">Optional metadata and settings.</p>
            </div>

            <div class="grid grid-cols-1 gap-y-6 gap-x-4">
              <div>
                <.input field={f[:metadata]} type="textarea" label="Metadata (JSON)" />
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 flex items-center justify-end gap-x-6">
          <.button phx-disable-with="Saving...">Save Card</.button>
        </div>
      </.form>
    </div>
    """
  end
end
