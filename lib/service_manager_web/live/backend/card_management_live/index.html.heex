<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="card-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />

<.header>
  Cards List
  <:actions>
    <.link patch={~p"/mobileBanking/cards/new"}>
      <.button>Issue New Card</.button>
    </.link>
  </:actions>
</.header>

<.table
  id="cards"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.cards}
  row_click={fn {_id, card} -> JS.patch(~p"/mobileBanking/cards/#{card}") end}
>
  <!-- Card Information -->
  <:col :let={{_id, card}} filter_item="card_number" label="Card Number">
    <%= if is_nil(card.card_number) || String.trim(card.card_number) == "",
      do: "--",
      else: card.card_number %>
  </:col>
  <:col :let={{_id, card}} filter_item="card_type" label="Type">
    <%= if is_nil(card.card_type) || String.trim(card.card_type) == "",
      do: "--",
      else: String.upcase(card.card_type) %>
  </:col>
  <:col :let={{_id, card}} filter_item="expiry_date" label="Expiry Date">
    <%= if is_nil(card.expiry_date), do: "--", else: Calendar.strftime(card.expiry_date, "%m/%y") %>
  </:col>
  <:col :let={{_id, card}} filter_item="currency" label="Currency">
    <%= if is_nil(card.currency) || String.trim(card.currency) == "",
      do: "--",
      else: card.currency %>
  </:col>
  <!-- Limits -->
  <:col :let={{_id, card}} filter_item="daily_limit" label="Daily Limit">
    <%= if is_nil(card.daily_limit), do: "--", else: "#{card.currency} #{card.daily_limit}" %>
  </:col>
  <:col :let={{_id, card}} filter_item="monthly_limit" label="Monthly Limit">
    <%= if is_nil(card.monthly_limit), do: "--", else: "#{card.currency} #{card.monthly_limit}" %>
  </:col>
  <!-- Status -->
  <:col :let={{_id, card}} filter_item="status" label="Status">
    <span class={status_color(card.status)}>
      <%= String.capitalize(card.status) %>
    </span>
  </:col>
  <:col :let={{_id, card}} filter_item="activation_status" label="Activation">
    <span class={activation_status_color(card.activation_status)}>
      <%= String.capitalize(card.activation_status) %>
    </span>
  </:col>
  <:col :let={{_id, card}} filter_item="last_used_at" label="Last Used">
    <%= if is_nil(card.last_used_at),
      do: "--",
      else: card.last_used_at |> to_string |> String.replace("T", " ") |> String.replace("Z", "") %>
  </:col>

  <:action :let={{id, card}}>
    <%= if has_any_permission?(@current_user, [:view, :update, :activate], :cards) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_update?(@current_user, :cards) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cards/#{card}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_view?(@current_user, :cards) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cards/#{card}"}
          >
            View Details
          </.link>
        <% end %>
        <!-- Card Status Management -->
        <%= if can_activate?(@current_user, :cards) do %>
          <%= if card.status == "active" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("block", value: %{id: card.id})}
              data-confirm="Are you sure you want to block this card?"
            >
              Block Card
            </.link>
          <% end %>

          <%= if card.status == "blocked" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("unblock", value: %{id: card.id})}
              data-confirm="Are you sure you want to unblock this card?"
            >
              Unblock Card
            </.link>
          <% end %>
          <!-- Activation Status Management -->
          <%= if card.activation_status == "pending" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("activate", value: %{id: card.id})}
              data-confirm="Are you sure you want to activate this card?"
            >
              Activate Card
            </.link>
          <% end %>
        <% end %>

        <%= if can_update?(@current_user, :cards) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("reset_pin", value: %{id: card.id})}
            data-confirm="Are you sure you want to reset this card's PIN?"
          >
            Reset PIN
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="card-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cards")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CardManagementLive.FormComponent}
    id={@card.id || :new}
    title={@page_title}
    action={@live_action}
    current_user={@current_user}
    card={@card}
    patch={~p"/mobileBanking/cards"}
  />
</.modal>

<.modal
  :if={@live_action == :show}
  id="card-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cards")}
  max-width="max-w-[95%]"
>
  <.live_component
    module={ServiceManagerWeb.Backend.CardManagementLive.ShowComponent}
    id={@card.id}
    title={@page_title}
    card={@card}
    patch={~p"/mobileBanking/cards"}
  />
</.modal>
