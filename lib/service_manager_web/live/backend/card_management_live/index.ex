defmodule ServiceManagerWeb.Backend.CardManagementLive.Index do
  use ServiceManagerWeb, :live_view
  import Ecto.Query
  import ServiceManagerWeb.Utilities.Sorting
  import ServiceManagerWeb.Utilities.Utils, only: [generate_pagination_details: 1]
  import ServiceManagerWeb.Utilities.PermissionHelpers

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaCard
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.Contexts.CardManagementContext

  @url "/mobileBanking/cards"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:stats, get_card_stats())

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = CardManagementContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:cards, data)}
  end

  def handle_params(params, _url, socket) do
    data = CardManagementContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:cards, data)
     |> assign(pagination: pagination)}
  end

  defp list_cards(params \\ %{}) do
    SchemaCard
    |> preload([:user, :wallet_user, :beneficiary])
    |> Repo.all()
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Card")
    |> assign(:card, get_card!(id))
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Card Details")
    |> assign(:card, get_card!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "Issue New Card")
    |> assign(:card, %SchemaCard{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Cards List")
    |> assign(:card, nil)
  end

  defp get_card!(id) do
    SchemaCard
    |> preload([:user, :wallet_user, :beneficiary])
    |> Repo.get!(id)
  end

  @impl true
  def handle_event("block", %{"id" => id}, socket) do
    card = get_card!(id)
    changeset = SchemaCard.update_changeset(card, %{"status" => "blocked"})

    case Repo.update(changeset) do
      {:ok, updated_card} ->
        updated_card = Repo.preload(updated_card, [:user, :wallet_user, :beneficiary])

        notify_user(
          updated_card,
          "Your card ending in #{String.slice(updated_card.card_number, -4, 4)} has been blocked. Please contact support if you did not request this action."
        )

        {:noreply,
         socket
         |> assign(:stats, get_card_stats())
         |> stream_insert(:cards, updated_card)
         |> put_flash(:info, "Card has been blocked")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to block card")}
    end
  end

  def handle_event("unblock", %{"id" => id}, socket) do
    card = get_card!(id)
    changeset = SchemaCard.update_changeset(card, %{"status" => "active"})

    case Repo.update(changeset) do
      {:ok, updated_card} ->
        updated_card = Repo.preload(updated_card, [:user, :wallet_user, :beneficiary])

        notify_user(
          updated_card,
          "Your card ending in #{String.slice(updated_card.card_number, -4, 4)} has been unblocked and is now active."
        )

        {:noreply,
         socket
         |> assign(:stats, get_card_stats())
         |> stream_insert(:cards, updated_card)
         |> put_flash(:info, "Card has been unblocked")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to unblock card")}
    end
  end

  def handle_event("activate", %{"id" => id}, socket) do
    card = get_card!(id)
    changeset = SchemaCard.update_changeset(card, %{"activation_status" => "activated"})

    case Repo.update(changeset) do
      {:ok, updated_card} ->
        updated_card = Repo.preload(updated_card, [:user, :wallet_user, :beneficiary])

        notify_user(
          updated_card,
          "Your card ending in #{String.slice(updated_card.card_number, -4, 4)} has been activated and is ready for use."
        )

        {:noreply,
         socket
         |> assign(:stats, get_card_stats())
         |> stream_insert(:cards, updated_card)
         |> put_flash(:info, "Card has been activated")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to activate card")}
    end
  end

  def handle_event("reset_pin", %{"id" => id}, socket) do
    card = get_card!(id)
    new_pin = generate_pin()

    changeset =
      SchemaCard.validate_pin_changeset(card, %{
        "pin_hash" => Bcrypt.hash_pwd_salt(new_pin),
        "pin_tries" => 0
      })

    case Repo.update(changeset) do
      {:ok, updated_card} ->
        updated_card = Repo.preload(updated_card, [:user, :wallet_user, :beneficiary])

        notify_user(
          updated_card,
          "Your card ending in #{String.slice(updated_card.card_number, -4, 4)} PIN has been reset. Your new PIN is: #{new_pin}"
        )

        {:noreply,
         socket
         |> assign(:stats, get_card_stats())
         |> stream_insert(:cards, updated_card)
         |> put_flash(:info, "Card PIN has been reset and sent to user")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reset card PIN")}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  defp notify_user(card, message) do
    if user = card.user || card.wallet_user do
      phone_number = user.phone_number

      if phone_number do
        Repo.insert(%SMSNotification{
          msisdn: phone_number,
          message: message
        })
      end
    end
  end

  defp generate_pin do
    for _ <- 1..4, into: "", do: Integer.to_string(Enum.random(0..9))
  end

  defp status_color(status) do
    base_classes = "px-2 py-1 text-xs font-medium rounded-full"

    status_specific_classes =
      case status do
        "active" -> "bg-green-100 text-green-800"
        "blocked" -> "bg-red-100 text-red-800"
        "expired" -> "bg-gray-100 text-gray-800"
        _ -> "bg-gray-100 text-gray-800"
      end

    "#{base_classes} #{status_specific_classes}"
  end

  defp activation_status_color(status) do
    base_classes = "px-2 py-1 text-xs font-medium rounded-full"

    status_specific_classes =
      case status do
        "activated" -> "bg-green-100 text-green-800"
        "pending" -> "bg-orange-100 text-orange-800"
        _ -> "bg-gray-100 text-gray-800"
      end

    "#{base_classes} #{status_specific_classes}"
  end

  defp get_card_stats do
    total_cards = Repo.aggregate(SchemaCard, :count, :id)
    active_cards = Repo.aggregate(from(c in SchemaCard, where: c.status == "active"), :count, :id)

    blocked_cards =
      Repo.aggregate(from(c in SchemaCard, where: c.status == "blocked"), :count, :id)

    expired_cards =
      Repo.aggregate(from(c in SchemaCard, where: c.status == "expired"), :count, :id)

    pending_cards =
      Repo.aggregate(from(c in SchemaCard, where: c.activation_status == "pending"), :count, :id)

    activated_cards =
      Repo.aggregate(
        from(c in SchemaCard, where: c.activation_status == "activated"),
        :count,
        :id
      )

    [
      %{
        title: "Total Cards",
        value: total_cards
      },
      %{
        title: "Card Status",
        value: "#{active_cards} / #{blocked_cards}",
        comparison: "active / blocked"
      },
      %{
        title: "Activation Status",
        value: "#{activated_cards} / #{pending_cards}",
        comparison: "activated / pending"
      },
      %{
        title: "Expired Cards",
        value: expired_cards
      }
    ]
  end
end
