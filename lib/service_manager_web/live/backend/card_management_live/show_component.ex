defmodule ServiceManagerWeb.Backend.CardManagementLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Card Details</h2>
          <p class="text-sm text-gray-500">Card Number: <%= mask_card_number(@card.card_number) %></p>
        </div>
        <div class="text-right space-y-2">
          <div class="inline-flex items-center px-4 py-2 rounded-md bg-gray-100">
            <div class={"w-2 h-2 rounded-full mr-2 #{status_indicator_color(@card.status)}"} />
            <span class="text-sm font-medium text-gray-700">
              <%= String.capitalize(@card.status) %>
            </span>
          </div>
          <div class="inline-flex items-center px-4 py-2 rounded-md bg-gray-100">
            <div class={"w-2 h-2 rounded-full mr-2 #{activation_indicator_color(@card.activation_status)}"} />
            <span class="text-sm font-medium text-gray-700">
              <%= String.capitalize(@card.activation_status) %>
            </span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Card Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Card Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Card Type</label>
              <p class="mt-1"><%= String.upcase(@card.card_type) %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Expiry Date</label>
              <p class="mt-1"><%= Calendar.strftime(@card.expiry_date, "%m/%y") %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Currency</label>
              <p class="mt-1"><%= @card.currency || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Card Type</label>
              <p class="mt-1">
                <%= if @card.is_virtual, do: "Virtual Card", else: "Physical Card" %>
              </p>
            </div>
          </div>
        </div>
        <!-- Limits & Usage -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Limits & Usage</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Daily Limit</label>
              <p class="mt-1"><%= @card.currency %> <%= @card.daily_limit %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Monthly Limit</label>
              <p class="mt-1"><%= @card.currency %> <%= @card.monthly_limit %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Used</label>
              <p class="mt-1">
                <%= if @card.last_used_at do %>
                  <%= @card.last_used_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">PIN Tries</label>
              <p class="mt-1"><%= @card.pin_tries %></p>
            </div>
          </div>
        </div>
        <!-- Linked Accounts -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Linked Accounts</h3>
          <div class="space-y-4">
            <%= if @card.user do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Customer Name</label>
                <p class="mt-1"><%= "#{@card.user.first_name} #{@card.user.last_name}" %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Customer Phone</label>
                <p class="mt-1"><%= @card.user.phone_number || "--" %></p>
              </div>
            <% end %>

            <%= if @card.wallet_user do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Wallet User</label>
                <p class="mt-1">
                  <%= "#{@card.wallet_user.first_name} #{@card.wallet_user.last_name}" %>
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Wallet Phone</label>
                <p class="mt-1"><%= @card.wallet_user.mobile_number || "--" %></p>
              </div>
            <% end %>

            <%= if @card.beneficiary do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Beneficiary</label>
                <p class="mt-1"><%= @card.beneficiary.name %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Beneficiary Account</label>
                <p class="mt-1"><%= @card.beneficiary.account_number || "--" %></p>
              </div>
            <% end %>
          </div>
        </div>
        <!-- Additional Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Additional Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Metadata</label>
              <pre class="mt-1 text-sm text-gray-700 bg-gray-50 p-2 rounded">
                <%= Jason.encode!(@card.metadata || %{}, pretty: true) %>
              </pre>
            </div>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @card.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @card.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp mask_card_number(nil), do: "--"

  defp mask_card_number(number) do
    last_four = String.slice(number, -4, 4)
    "•••• •••• •••• #{last_four}"
  end

  defp status_indicator_color(status) do
    case status do
      "active" -> "bg-green-500"
      "blocked" -> "bg-red-500"
      "expired" -> "bg-gray-500"
      _ -> "bg-gray-500"
    end
  end

  defp activation_indicator_color(status) do
    case status do
      "activated" -> "bg-green-500"
      "pending" -> "bg-orange-500"
      _ -> "bg-gray-500"
    end
  end
end
