defmodule ServiceManagerWeb.Backend.ExchangeRateLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.ExchangeRateContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage exchange_rate records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="exchange_rate-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input field={@form[:from_currency_code]} type="text" label="From currency code" />
        <.input field={@form[:to_currency_code]} type="text" label="To currency code" />
        <.input field={@form[:rate]} type="number" label="Rate" step="any" />
        <:actions>
          <.button phx-disable-with="Saving...">Save Exchange rate</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{exchange_rate: exchange_rate} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(ExchangeRateContext.change_exchange_rate(exchange_rate))
     end)}
  end

  @impl true
  def handle_event("validate", %{"exchange_rate" => exchange_rate_params}, socket) do
    changeset =
      ExchangeRateContext.change_exchange_rate(socket.assigns.exchange_rate, exchange_rate_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"exchange_rate" => exchange_rate_params}, socket) do
    save_exchange_rate(socket, socket.assigns.action, exchange_rate_params)
  end

  defp save_exchange_rate(socket, :edit, exchange_rate_params) do
    case ExchangeRateContext.update_exchange_rate(
           socket.assigns.exchange_rate,
           exchange_rate_params
         ) do
      {:ok, exchange_rate} ->
        notify_parent({:saved, exchange_rate})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Exchange rate updated successfully",
              details: inspect(exchange_rate)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Exchange rate updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Exchange rate updated failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_exchange_rate(socket, :new, exchange_rate_params) do
    case ExchangeRateContext.create_exchange_rate(exchange_rate_params) do
      {:ok, exchange_rate} ->
        notify_parent({:saved, exchange_rate})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Exchange rate created successfully",
              details: inspect(exchange_rate)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Exchange rate created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Exchange rate create failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end
