<.header>
  Exchange rate <%= @exchange_rate.id %>
  <:subtitle>This is a exchange_rate record from your database.</:subtitle>
  <:actions>
    <%= if can_update?(@current_user, :exchange_rates) do %>
      <.link
        patch={~p"/mobileBanking/exchange_rates/#{@exchange_rate}/show/edit"}
        phx-click={JS.push_focus()}
      >
        <.button>Edit exchange_rate</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.list>
  <:item title="From currency code"><%= @exchange_rate.from_currency_code %></:item>
  <:item title="To currency code"><%= @exchange_rate.to_currency_code %></:item>
  <:item title="Rate"><%= @exchange_rate.rate %></:item>
</.list>

<.back navigate={~p"/mobileBanking/exchange_rates"}>Back to exchange_rates</.back>

<.modal
  :if={@live_action == :edit}
  id="exchange_rate-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/exchange_rates/#{@exchange_rate}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.ExchangeRateLive.FormComponent}
    id={@exchange_rate.id}
    title={@page_title}
    action={@live_action}
    exchange_rate={@exchange_rate}
    patch={~p"/mobileBanking/exchange_rates/#{@exchange_rate}"}
  />
</.modal>
