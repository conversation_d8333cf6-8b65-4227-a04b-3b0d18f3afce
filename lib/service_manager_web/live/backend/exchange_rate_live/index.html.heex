<.header>
  Currency Exchange Rates Management
  <:actions>
    <%= if can_create?(@current_user, :exchange_rates) do %>
      <.link patch={~p"/mobileBanking/exchange_rates/new"}>
        <.button>New Exchange Rate</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="exchange_rates"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.exchange_rates}
  row_click={
    fn {_id, exchange_rate} ->
      cond do
        can_update?(@current_user, :exchange_rates) ->
          JS.navigate(~p"/mobileBanking/exchange_rates/#{exchange_rate}/edit")
        can_view?(@current_user, :exchange_rates) ->
          JS.navigate(~p"/mobileBanking/exchange_rates/#{exchange_rate}")
        true ->
          JS.navigate("#")
      end
    end
  }
>
  <:col :let={{_id, exchange_rate}} filter_item="from_currency_code" label="From currency">
    <%= exchange_rate.from_currency_code %>
  </:col>
  <:col :let={{_id, exchange_rate}} filter_item="to_currency_code" label="To currency">
    <%= exchange_rate.to_currency_code %>
  </:col>
  <:col :let={{_id, exchange_rate}} filter_item="rate" label="Rate">
    <%= exchange_rate.rate %>
  </:col>
  <:col :let={{_id, exchange_rate}} filter_item="updated_at" label="Date">
    <%= exchange_rate.updated_at %>
  </:col>

  <:action :let={{id, exchange_rate}}>
    <%= if has_any_permission?(@current_user, [:view, :update, :delete], :exchange_rates) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_update?(@current_user, :exchange_rates) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/exchange_rates/#{exchange_rate}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_view?(@current_user, :exchange_rates) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            navigate={~p"/mobileBanking/exchange_rates/#{exchange_rate}"}
          >
            View Details
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :exchange_rates) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: exchange_rate.id})}
            data-confirm="Are you sure?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="exchange_rate-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/exchange_rates")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.ExchangeRateLive.FormComponent}
    id={@exchange_rate.id || :new}
    title={@page_title}
    action={@live_action}
    exchange_rate={@exchange_rate}
    patch={~p"/mobileBanking/exchange_rates"}
  />
</.modal>
