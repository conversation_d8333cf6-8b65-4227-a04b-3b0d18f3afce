defmodule ServiceManagerWeb.Backend.ExchangeRateLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.ExchangeRateContext
  alias ServiceManager.Schemas.ExchangeRate
  alias ServiceManager.Services.Security.Authorization

  # Import permission helper functions
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/exchange_rates"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = ExchangeRateContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:exchange_rates, data)}
  end

  def handle_params(params, _url, socket) do
    data = ExchangeRateContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:exchange_rates, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Exchange rate")
    |> assign(:exchange_rate, ExchangeRateContext.get_exchange_rate!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Exchange rate")
    |> assign(:exchange_rate, %ExchangeRate{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Exchange rates")
    |> assign(:exchange_rate, nil)
  end

  @impl true
  def handle_info(
        {ServiceManagerWeb.Backend.ExchangeRateLive.FormComponent, {:saved, exchange_rate}},
        socket
      ) do
    {:noreply, stream_insert(socket, :exchange_rates, exchange_rate)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    exchange_rate = ExchangeRateContext.get_exchange_rate!(id)
    {:ok, _} = ExchangeRateContext.delete_exchange_rate(exchange_rate)

    {:noreply, stream_delete(socket, :exchange_rates, exchange_rate)}
  end

  # Permission helper functions for exchange rates management
  defp can_create_exchange_rates?(user), do: can_create?(user, :exchange_rates)
  defp can_update_exchange_rates?(user), do: can_update?(user, :exchange_rates)
  defp can_delete_exchange_rates?(user), do: can_delete?(user, :exchange_rates)
  defp can_view_exchange_rates?(user), do: can_view?(user, :exchange_rates)
  defp has_any_exchange_rates_action_permission?(user), do: has_any_permission?(user, [:view, :update, :delete], :exchange_rates)
end
