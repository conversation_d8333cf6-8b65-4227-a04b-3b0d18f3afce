defmodule ServiceManagerWeb.Backend.IpWhitelistLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.IpWhitelist
  import Ecto.Query
  import ServiceManagerWeb.Utilities.PermissionHelpers

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/ip-whitelist")

    if connected?(socket) do
      :timer.send_interval(30000, :refresh)
    end

    socket =
      socket
      |> assign(:whitelisted_ips, list_whitelisted_ips())
      |> assign(:filter_params, %{
        "environment" => "",
        "status" => "",
        "risk_level" => "",
        "search" => "",
        "page" => 1,
        "page_size" => 10,
        "sort_field" => "inserted_at",
        "sort_order" => "desc"
      })
      |> assign(:pagination, %{
        page_number: 1,
        page_size: 10,
        total_entries: Repo.aggregate(IpWhitelist, :count),
        total_pages: ceil(Repo.aggregate(IpWhitelist, :count) / 10)
      })
      |> assign(:selected_column, "inserted_at")

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "IP Whitelist")
    |> assign(:ip_whitelist, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "Add IP to Whitelist")
    |> assign(:ip_whitelist, %IpWhitelist{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Whitelisted IP")
    |> assign(:ip_whitelist, Repo.get!(IpWhitelist, id))
  end

  @impl true
  def handle_info(:refresh, socket) do
    {:noreply, assign(socket, :whitelisted_ips, list_whitelisted_ips())}
  end

  @impl true
  def handle_event("filter", params, socket) do
    filtered_ips = apply_filters(params)
    total_entries = length(filtered_ips)

    socket =
      socket
      |> assign(:whitelisted_ips, filtered_ips)
      |> assign(:filter_params, params)
      |> assign(:pagination, %{
        page_number: 1,
        page_size: socket.assigns.pagination.page_size,
        total_entries: total_entries,
        total_pages: ceil(total_entries / socket.assigns.pagination.page_size)
      })

    {:noreply, socket}
  end

  defp list_whitelisted_ips do
    IpWhitelist
    |> preload([:user, :wallet_user, :third_party_api_key])
    |> order_by([i], desc: i.inserted_at)
    |> Repo.all()
  end

  defp apply_filters(params) do
    IpWhitelist
    |> filter_by_environment(params["environment"])
    |> filter_by_status(params["status"])
    |> filter_by_risk_level(params["risk_level"])
    |> filter_by_search(params["search"])
    |> preload([:user, :wallet_user, :third_party_api_key])
    |> order_by([i], desc: i.inserted_at)
    |> Repo.all()
  end

  defp filter_by_environment(query, ""), do: query
  defp filter_by_environment(query, nil), do: query

  defp filter_by_environment(query, environment) do
    where(query, [i], i.environment == ^String.to_existing_atom(environment))
  end

  defp filter_by_status(query, ""), do: query
  defp filter_by_status(query, nil), do: query

  defp filter_by_status(query, status) do
    where(query, [i], i.status == ^String.to_existing_atom(status))
  end

  defp filter_by_risk_level(query, ""), do: query
  defp filter_by_risk_level(query, nil), do: query

  defp filter_by_risk_level(query, risk_level) do
    where(query, [i], i.risk_level == ^String.to_existing_atom(risk_level))
  end

  defp filter_by_search(query, ""), do: query
  defp filter_by_search(query, nil), do: query

  defp filter_by_search(query, search) do
    search = "%#{search}%"
    where(query, [i], ilike(i.ip_address, ^search) or ilike(i.description, ^search))
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.header>
      IP Whitelist Management
      <:actions>
        <%= if can_create?(@current_user, :ip_whitelist) do %>
          <.link
            patch={~p"/mobileBanking/ip-whitelist/new"}
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Add IP to Whitelist
          </.link>
        <% end %>
      </:actions>
    </.header>

    <.table
      id="ip_whitelist"
      rows={@whitelisted_ips}
      filter_params={@filter_params}
      pagination={@pagination}
      selected_column={@selected_column}
    >
      <:col :let={entry} label="Status">
        <span class={status_color(entry.status)}>
          <%= String.capitalize(to_string(entry.status)) %>
        </span>
      </:col>

      <:col :let={entry} label="IP Address">
        <div class="font-mono text-sm"><%= entry.ip_address %></div>
      </:col>

      <:col :let={entry} label="Environment">
        <span class={environment_color(entry.environment)}>
          <%= String.capitalize(to_string(entry.environment)) %>
        </span>
      </:col>

      <:col :let={entry} label="Risk Level">
        <span class={risk_level_color(entry.risk_level)}>
          <%= String.capitalize(to_string(entry.risk_level)) %>
        </span>
      </:col>

      <:col :let={entry} label="Description">
        <div class="text-sm"><%= entry.description %></div>
      </:col>

      <:col :let={entry} label="Access Count">
        <div class="text-sm font-medium"><%= entry.access_count %></div>
      </:col>

      <:col :let={entry} label="Last Accessed">
        <%= if entry.last_accessed_at do %>
          <div class="text-sm whitespace-nowrap">
            <%= Calendar.strftime(entry.last_accessed_at, "%Y-%m-%d %H:%M:%S") %>
          </div>
        <% else %>
          <div class="text-sm text-gray-500">Never</div>
        <% end %>
      </:col>

      <:col :let={entry} label="Expires">
        <%= if entry.expiry_date do %>
          <div class="text-sm whitespace-nowrap">
            <%= Calendar.strftime(entry.expiry_date, "%Y-%m-%d %H:%M:%S") %>
          </div>
        <% else %>
          <div class="text-sm text-gray-500">Never</div>
        <% end %>
      </:col>

      <:action :let={entry}>
        <%= if can_update?(@current_user, :ip_whitelist) do %>
          <.link
            patch={~p"/mobileBanking/ip-whitelist/#{entry}/edit"}
            class="text-blue-600 hover:text-blue-900"
          >
            Edit
          </.link>
        <% end %>
      </:action>
    </.table>

    <.modal
      :if={@live_action in [:new, :edit]}
      id="ip-whitelist-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/ip-whitelist")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.IpWhitelistLive.FormComponent}
        id={@ip_whitelist.id || :new}
        title={@page_title}
        action={@live_action}
        ip_whitelist={@ip_whitelist}
        current_user={@current_user}
        patch={~p"/mobileBanking/ip-whitelist"}
      />
    </.modal>
    """
  end

  defp status_color(:active),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"

  defp status_color(:inactive),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"

  defp environment_color(:production),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"

  defp environment_color(:staging),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"

  defp environment_color(:development),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp risk_level_color(:low),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"

  defp risk_level_color(:medium),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"

  defp risk_level_color(:high),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
end
