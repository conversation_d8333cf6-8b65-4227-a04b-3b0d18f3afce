defmodule ServiceManagerWeb.Backend.HomeLive.Index do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Statistics.Cache
  alias ServiceManager.Accounts
  alias ServiceManagerWeb.Presence
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Accounts.User
  alias ServiceManager.Repo
  import Ecto.Query
  @url "/mobileBanking"
  @presence_topic "users:presence"
  @megabyte 1024 * 1024

  defp get_navigation_path(title) do
    case title do
      "Total Account Transactions" -> ~p"/mobileBanking/transactions"
      "Total Wallet Transactions" -> ~p"/mobileBanking/WalletTransactions"
      "Number of Registered Users accounts" -> ~p"/mobileBanking/user_managements"
      "Number of Registered Wallets" -> ~p"/mobileBanking/WalletsOverview"
      _ -> "#"
    end
  end

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      :timer.send_interval(1000, self(), :update_metrics)
      :timer.send_interval(15000, self(), :update)
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, @presence_topic)
    end

    socket = assign(socket, :current_path, @url)

    # Only set active_sessions once at mount
    active_sessions =
      Presence.list(@presence_topic)
      |> Enum.map(fn {_key, %{metas: [meta | _]}} -> meta end)
      |> length()

    wallet_count = Repo.aggregate(WalletUser, :count)
    user_count = Repo.aggregate(User, :count)

    {:ok,
     assign(socket,
       statistics: Cache.get_statistics(),
       users: Accounts.get_last_five_users(),
       selected_view: :monthly,
       chart_data: get_chart_data(:monthly),
       distribution_data: get_distribution_data(),
       transactions_data: get_transactions_data(),
       active_sessions: active_sessions,
       metrics: get_metrics(),
       wallet_count: wallet_count,
       user_count: user_count
     )}
  end

  @impl true
  def handle_info(:update_metrics, socket) do
    metrics = get_metrics()

    {:noreply,
     socket
     |> push_event("update_metrics", %{
       uptime: metrics.uptime,
       memory: metrics.memory
     })}
  end

  @impl true
  def handle_info(:update, socket) do
    wallet_count = Repo.aggregate(WalletUser, :count)
    user_count = Repo.aggregate(User, :count)

    {:noreply,
     assign(socket,
       statistics: Cache.get_statistics(),
       users: Accounts.get_last_five_users(),
       selected_view: socket.assigns.selected_view,
       chart_data: get_chart_data(socket.assigns.selected_view),
       distribution_data: get_distribution_data(),
       transactions_data: get_transactions_data(),
       # Do NOT update active_sessions here
       active_sessions: socket.assigns.active_sessions,
       wallet_count: wallet_count,
       user_count: user_count
     )}
  end

  @impl true
  def handle_info(%Phoenix.Socket.Broadcast{event: "presence_diff"}, socket) do
    active_sessions =
      Presence.list(@presence_topic)
      |> Enum.map(fn {_key, %{metas: [meta | _]}} -> meta end)
      |> length()

    {:noreply, assign(socket, :active_sessions, active_sessions)}
  end

  # Catch-all clause for handling any other messages
  @impl true
  def handle_info(_, socket), do: {:noreply, socket}

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  @impl true
  def handle_event("change_view", %{"view" => view}, socket) do
    view = String.to_existing_atom(view)
    {:noreply, assign(socket, selected_view: view, chart_data: get_chart_data(view))}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Dashboard")
  end

  defp get_chart_data(:monthly) do
    current_year = DateTime.utc_now().year

    # Get monthly aggregates for regular transactions
    account_query =
      from t in Transaction,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) == ^current_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(MONTH FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(MONTH FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    # Get monthly aggregates for wallet transactions
    wallet_query =
      from t in WalletTransactions,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) == ^current_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(MONTH FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(MONTH FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    account_data =
      Repo.all(account_query)
      |> Enum.into(%{}, fn {month, sum} ->
        {Decimal.to_integer(month), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    wallet_data =
      Repo.all(wallet_query)
      |> Enum.into(%{}, fn {month, sum} ->
        {Decimal.to_integer(month), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    months = 1..12

    %{
      labels: months |> Enum.map(&Timex.month_name/1),
      wallet: months |> Enum.map(&(wallet_data[&1] || 0.0)),
      account: months |> Enum.map(&(account_data[&1] || 0.0))
    }
  end

  defp get_chart_data(:quarterly) do
    current_year = DateTime.utc_now().year

    # Get quarterly aggregates for regular transactions
    account_query =
      from t in Transaction,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) == ^current_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(QUARTER FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(QUARTER FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    # Get quarterly aggregates for wallet transactions
    wallet_query =
      from t in WalletTransactions,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) == ^current_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(QUARTER FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(QUARTER FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    account_data =
      Repo.all(account_query)
      |> Enum.into(%{}, fn {quarter, sum} ->
        {Decimal.to_integer(quarter), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    wallet_data =
      Repo.all(wallet_query)
      |> Enum.into(%{}, fn {quarter, sum} ->
        {Decimal.to_integer(quarter), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    quarters = 1..4

    %{
      labels: quarters |> Enum.map(&"Q#{&1}"),
      wallet: quarters |> Enum.map(&(wallet_data[&1] || 0.0)),
      account: quarters |> Enum.map(&(account_data[&1] || 0.0))
    }
  end

  defp get_chart_data(:yearly) do
    end_year = DateTime.utc_now().year
    start_year = end_year - 3

    # Get yearly aggregates for regular transactions
    account_query =
      from t in Transaction,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) >= ^start_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(YEAR FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    # Get yearly aggregates for wallet transactions
    wallet_query =
      from t in WalletTransactions,
        where: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at) >= ^start_year,
        where: t.status == "completed",
        group_by: fragment("EXTRACT(YEAR FROM ?)", t.inserted_at),
        select: {
          fragment("EXTRACT(YEAR FROM ?)", t.inserted_at),
          sum(t.amount)
        }

    account_data =
      Repo.all(account_query)
      |> Enum.into(%{}, fn {year, sum} ->
        {Decimal.to_integer(year), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    wallet_data =
      Repo.all(wallet_query)
      |> Enum.into(%{}, fn {year, sum} ->
        {Decimal.to_integer(year), (sum || Decimal.new(0)) |> Decimal.to_float()}
      end)

    years = start_year..end_year

    %{
      labels: years |> Enum.map(&Integer.to_string/1),
      wallet: years |> Enum.map(&(wallet_data[&1] || 0.0)),
      account: years |> Enum.map(&(account_data[&1] || 0.0))
    }
  end

  defp get_distribution_data do
    # Get total for regular transactions
    account_query =
      from t in Transaction,
        where: t.status == "completed",
        select: sum(t.amount)

    # Get total for wallet transactions
    wallet_query =
      from t in WalletTransactions,
        where: t.status == "completed",
        select: sum(t.amount)

    account_total = (Repo.one(account_query) || Decimal.new(0)) |> Decimal.to_float()
    wallet_total = (Repo.one(wallet_query) || Decimal.new(0)) |> Decimal.to_float()

    %{
      labels: ["Total"],
      wallet: [wallet_total],
      account: [account_total]
    }
  end

  defp get_transactions_data do
    # Get transaction types and their counts/amounts for regular transactions
    account_query =
      from t in Transaction,
        where: t.status == "completed",
        group_by: t.type,
        select: {t.type, count(t.id), sum(t.amount)}

    # Get transaction types and their counts/amounts for wallet transactions
    wallet_query =
      from t in WalletTransactions,
        where: t.status == "completed",
        group_by: t.type,
        select: {t.type, count(t.id), sum(t.amount)}

    account_data = Repo.all(account_query)
    wallet_data = Repo.all(wallet_query)

    # Combine all unique transaction types
    types =
      (account_data ++ wallet_data)
      |> Enum.map(fn {type, _, _} -> type end)
      |> Enum.uniq()
      |> Enum.sort()

    # Create maps for easy lookup
    account_map =
      Enum.into(account_data, %{}, fn {type, count, sum} ->
        {type, {count, (sum || Decimal.new(0)) |> Decimal.to_float()}}
      end)

    wallet_map =
      Enum.into(wallet_data, %{}, fn {type, count, sum} ->
        {type, {count, (sum || Decimal.new(0)) |> Decimal.to_float()}}
      end)

    %{
      labels: types,
      datasets: [
        %{
          label: "Account Transaction Count",
          data:
            types
            |> Enum.map(fn type ->
              case account_map[type] do
                {count, _} -> count
                nil -> 0
              end
            end),
          backgroundColor: "rgba(75, 192, 192, 0.6)",
          borderColor: "rgba(75, 192, 192, 1)",
          borderWidth: 1
        },
        %{
          label: "Wallet Transaction Count",
          data:
            types
            |> Enum.map(fn type ->
              case wallet_map[type] do
                {count, _} -> count
                nil -> 0
              end
            end),
          backgroundColor: "rgba(255, 99, 132, 0.6)",
          borderColor: "rgba(255, 99, 132, 1)",
          borderWidth: 1
        },
        %{
          label: "Account Transaction Amount",
          data:
            types
            |> Enum.map(fn type ->
              case account_map[type] do
                {_, amount} -> amount
                nil -> 0.0
              end
            end),
          backgroundColor: "rgba(75, 192, 192, 0.3)",
          borderColor: "rgba(75, 192, 192, 0.5)",
          borderWidth: 1
        },
        %{
          label: "Wallet Transaction Amount",
          data:
            types
            |> Enum.map(fn type ->
              case wallet_map[type] do
                {_, amount} -> amount
                nil -> 0.0
              end
            end),
          backgroundColor: "rgba(255, 99, 132, 0.3)",
          borderColor: "rgba(255, 99, 132, 0.5)",
          borderWidth: 1
        }
      ]
    }
  end

  defp get_metrics do
    memory = :erlang.memory()
    {uptime_ms, _} = :erlang.statistics(:wall_clock)

    total_mem = div(memory[:total], @megabyte)
    process_mem = div(memory[:processes], @megabyte)

    days = div(uptime_ms, 86_400_000)
    remaining_ms = rem(uptime_ms, 86_400_000)
    hours = div(remaining_ms, 3_600_000)
    remaining_ms = rem(remaining_ms, 3_600_000)
    minutes = div(remaining_ms, 60_000)
    seconds = div(rem(remaining_ms, 60_000), 1000)

    uptime =
      cond do
        days > 0 -> "#{days}d #{hours}h #{minutes}m #{seconds}s"
        hours > 0 -> "#{hours}h #{minutes}m #{seconds}s"
        minutes > 0 -> "#{minutes}m #{seconds}s"
        true -> "#{seconds}s"
      end

    %{
      uptime: uptime,
      memory: "#{process_mem}/#{total_mem}MB"
    }
  end

  def sum_third_dataset(transactions_data) do
    sum =
      transactions_data
      |> Map.get(:datasets, [])
      |> Enum.at(2, %{data: []})
      |> Map.get(:data, [])
      |> Enum.reduce(0, fn
        x, acc when is_number(x) -> acc + x
        _, acc -> acc
      end)
      # Ensure we have a float
      |> Kernel.+(0.0)

    if is_number(sum) do
      :erlang.float_to_binary(sum, decimals: 2)
    else
      "0.00"
    end
  rescue
    _ -> "0.00"
  end

  def sum_fourth_dataset_data(transactions_data) do
    sum =
      transactions_data
      |> Map.get(:datasets, [])
      |> Enum.at(3, %{data: []})
      |> Map.get(:data, [])
      |> Enum.reduce(0, fn
        x, acc when is_number(x) -> acc + x
        _, acc -> acc
      end)
      # Ensure we have a float
      |> Kernel.+(0.0)

    if is_number(sum) do
      :erlang.float_to_binary(sum, decimals: 2)
    else
      "0.00"
    end
  rescue
    _ -> "0.00"
  end
end
