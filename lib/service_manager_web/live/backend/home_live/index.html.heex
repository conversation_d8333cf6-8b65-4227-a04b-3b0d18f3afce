<div class="p-6 bg-gray-100">
  <h1 class="text-2xl font-bold mb-6">Dashboard</h1>
  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
    <.live_component
      module={ServiceManagerWeb.Backend.HomeLive.ActiveSessionsComponent}
      id="active-sessions"
      active_sessions={@active_sessions}
      phx-update="ignore"
    />

    <%= for {title, value, change, icon} <- [
      {"Total Account Transactions", @transactions_data.datasets |> Enum.at(0) |> Map.get(:data) |> Enum.sum() |> Integer.to_string(), 
       (if @transactions_data.datasets |> Enum.at(2) |> Map.get(:data) |> Enum.sum() > 0, do: "+", else: "-") <> "#{sum_third_dataset(@transactions_data)}", 
       "M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"},
      {"Total Wallet Transactions", @transactions_data.datasets |> Enum.at(1) |> Map.get(:data) |> Enum.sum() |> Integer.to_string(),
       (if @transactions_data.datasets |> Enum.at(3) |> Map.get(:data) |> Enum.sum() > 0, do: "+", else: "-") <> "#{sum_fourth_dataset_data(@transactions_data)}", 
       "M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"},
      {"Number of Registered Users accounts", Integer.to_string(@user_count), "", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},
      {"Number of Registered Wallets", Integer.to_string(@wallet_count), "", "M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"}
    ] do %>
      <.link navigate={get_navigation_path(title)} class="block">
        <div class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg hover:scale-105 cursor-pointer">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-sm font-medium text-gray-500"><%= title %></h3>
            <div class="bg-orange-500 text-white p-2 rounded-full transition duration-300 hover:bg-orange-600 hover:rotate-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={icon} />
              </svg>
            </div>
          </div>
          <p class="text-2xl font-bold text-gray-900">
            <%= value %>
            <span class={"text-sm #{if String.starts_with?(change, "+"), do: "text-green-500", else: "text-red-500"}"}>
              <%= change %>
            </span>
          </p>
        </div>
      </.link>
    <% end %>

    <.link navigate="/monitor/dashboard" class="block">
      <div
        class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg hover:scale-105 cursor-pointer"
        id="metrics-card"
        phx-hook="SystemMetrics"
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-sm font-medium text-gray-500">System Metrics</h3>
          <div class="bg-orange-500 text-white p-2 rounded-full transition duration-300 hover:bg-orange-600 hover:rotate-12">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
          </div>
        </div>
        <div>
          <p class="text-2xl font-bold text-gray-900">
            Uptime: <span id="metrics-uptime" phx-update="ignore"><%= @metrics.uptime %></span>
            <span class="text-sm text-gray-500">
              Memory: <span id="metrics-memory" phx-update="ignore"><%= @metrics.memory %></span>
            </span>
          </p>
        </div>
      </div>
    </.link>
  </div>

  <h1 class="text-1xl font-bold mb-6">Transaction Overview</h1>
  <!-- Charts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800">TOP STATISTICS</h2>
        <div class="inline-flex rounded-md shadow-sm">
          <button
            phx-click="change_view"
            phx-value-view="monthly"
            class={"#{if @selected_view == :monthly, do: 'bg-blue-500 text-white', else: 'bg-white text-gray-700'} px-3 py-1 text-xs font-medium border border-gray-200 rounded-l-lg hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700"}
          >
            Monthly
          </button>
          <button
            phx-click="change_view"
            phx-value-view="quarterly"
            class={"#{if @selected_view == :quarterly, do: 'bg-blue-500 text-white', else: 'bg-white text-gray-700'} px-3 py-1 text-xs font-medium border-t border-b border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700"}
          >
            Quarterly
          </button>
          <button
            phx-click="change_view"
            phx-value-view="yearly"
            class={"#{if @selected_view == :yearly, do: 'bg-blue-500 text-white', else: 'bg-white text-gray-700'} px-3 py-1 text-xs font-medium border border-gray-200 rounded-r-md hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700"}
          >
            Yearly
          </button>
        </div>
      </div>
      <div class="mb-4">
        <h3 class="text-gray-500 mb-2">Transaction Comparison</h3>
        <div class="h-64 w-full bg-gray-100 rounded-lg transition duration-300 hover:bg-gray-200">
          <canvas
            id="comparisonChart"
            phx-hook="ComparisonChart"
            data-chart-data={Jason.encode!(@chart_data)}
          >
          </canvas>
        </div>
      </div>
    </div>
    <div class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800">TOP ANALYTICS</h2>
      </div>
      <div class="mb-4">
        <h3 class="text-gray-500 mb-2">Wallet vs Account Distribution</h3>
        <div class="h-64 w-full bg-gray-100 rounded-lg transition duration-300 hover:bg-gray-200">
          <canvas
            id="distributionChart"
            phx-hook="DistributionChart"
            data-chart-data={Jason.encode!(@distribution_data)}
          >
          </canvas>
        </div>
      </div>
    </div>
  </div>
  <br />
  <!-- New Bill Payment Transactions Chart -->
  <div class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-semibold text-gray-800">TRANSACTIONS</h2>
    </div>
    <div class="mb-4">
      <h3 class="text-gray-500 mb-2">Comparison Between Wallet and Account Transactions</h3>
      <div class="h-96 w-full bg-gray-100 rounded-lg transition duration-300 hover:bg-gray-200">
        <canvas
          id="transactionsChart"
          phx-hook="TransactionsChart"
          data-chart-data={Jason.encode!(@transactions_data)}
        >
        </canvas>
      </div>
    </div>
  </div>
</div>
