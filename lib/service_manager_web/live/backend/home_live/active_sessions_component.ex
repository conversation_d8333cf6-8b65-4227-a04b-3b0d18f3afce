defmodule ServiceManagerWeb.Backend.HomeLive.ActiveSessionsComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="bg-white rounded-xl shadow-md p-6 transition duration-300 hover:shadow-lg hover:scale-105 cursor-pointer"
      phx-click={JS.navigate(~p"/mobileBanking/online-users")}
    >
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-sm font-medium text-gray-500">Number of Active Sessions</h3>
        <div class="bg-orange-500 text-white p-2 rounded-full transition duration-300 hover:bg-orange-600 hover:rotate-12">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        </div>
      </div>
      <p class="text-2xl font-bold text-gray-900">
        <%= @active_sessions %>
      </p>
    </div>
    """
  end
end
