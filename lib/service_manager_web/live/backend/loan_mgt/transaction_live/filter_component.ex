defmodule ServiceManagerWeb.Backend.LoanMgt.LoanTransactionsLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.LoanMgt.Embedded.EmbeddedTransaction

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter loan transactions</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:start_date]}
              type="date"
              label="Start Date"
              placeholder="Select start date"
            />
            <.input
              field={@form[:end_date]}
              type="date"
              label="End Date"
              placeholder="Select end date"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt="Select status"
              options={status_options()}
            />
            <.input
              field={@form[:type]}
              type="select"
              label="Type"
              prompt="Select type"
              options={type_options()}
            />
            <.input
              field={@form[:amount_from]}
              type="number"
              label="Amount From"
              step="0.01"
              placeholder="Enter minimum amount"
            />
            <.input
              field={@form[:amount_to]}
              type="number"
              label="Amount To"
              step="0.01"
              placeholder="Enter maximum amount"
            />
            <.input
              field={@form[:reference]}
              type="text"
              label="Reference"
              placeholder="Enter reference"
            />
            <.input
              field={@form[:payment_method]}
              type="text"
              label="Payment Method"
              placeholder="Enter payment method"
            />
            <.input
              field={@form[:external_reference]}
              type="text"
              label="External Reference"
              placeholder="Enter external reference"
            />
            <.input
              field={@form[:debit_account]}
              type="text"
              label="Debit Account"
              placeholder="Enter debit account"
            />
            <.input
              field={@form[:credit_account]}
              type="text"
              label="Credit Account"
              placeholder="Enter credit account"
            />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:start_date]} type="date" label="Start Date" />
            <.input field={@form[:end_date]} type="date" label="End Date" />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={status_options()}
            />
            <.input
              field={@form[:type]}
              type="select"
              label="Type"
              prompt=""
              options={type_options()}
            />
            <.input field={@form[:amount_from]} type="number" label="Amount From" step="0.01" />
            <.input field={@form[:amount_to]} type="number" label="Amount To" step="0.01" />
            <.input field={@form[:reference]} type="text" label="Reference" />
            <.input field={@form[:payment_method]} type="text" label="Payment Method" />
            <.input field={@form[:external_reference]} type="text" label="External Reference" />
            <.input field={@form[:debit_account]} type="text" label="Debit Account" />
            <.input field={@form[:credit_account]} type="text" label="Credit Account" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedTransaction{}
    changeset = EmbeddedTransaction.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedTransaction.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/loan_mgt/loan_transactions#{query}", replace: true)}
  end

  def type_options do
    [
      {"Disbursement", "DISBURSEMENT"},
      {"Repayment", "REPAYMENT"},
      {"Adjustment", "ADJUSTMENT"}
    ]
  end

  def status_options do
    [
      {"Pending", "PENDING"},
      {"Completed", "COMPLETED"},
      {"Failed", "FAILED"},
      {"Reversed", "REVERSED"}
    ]
  end
end
