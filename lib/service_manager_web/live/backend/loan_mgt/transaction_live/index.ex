defmodule ServiceManagerWeb.Backend.LoanMgt.LoanTransactionsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Embedded.EmbeddedTransaction

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, :filter_modal, false)}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.get_loan_transactions(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:transactions, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.get_loan_transactions(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:transactions, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Transactions")
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page, "Filter Transactions")
    |> assign(:data, %EmbeddedTransaction{})
    |> assign(:url, nil)
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page, "Excel Export Transactions")
    |> assign(:data, %EmbeddedTransaction{})
    |> assign(:url, "/mobileBanking/loan_mgt/loan_transactions/ExcelExport")
  end

  @impl true
  def handle_event("filter_modal", _params, socket) do
    {:noreply, assign(socket, :filter_modal, true)}
  end

  @impl true
  def handle_event("close_filter_modal", _params, socket) do
    {:noreply, assign(socket, :filter_modal, false)}
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/loan_transactions")}
  end
end
