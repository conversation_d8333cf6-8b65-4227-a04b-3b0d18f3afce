<.header>
  <%= @page %>
</.header>

<.table
  id="transactions"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/loan_transactions/filter"}
  export_url={~p"/mobileBanking/loan_mgt/loan_transactions/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.transactions}
>
  <:col :let={{_id, transaction}} filter_item="type" label="Type"><%= transaction.type %></:col>
  <:col :let={{_id, transaction}} filter_item="amount" label="Amount">
    <%= transaction.amount %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="description" label="Description">
    <%= transaction.description %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="status" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case transaction.status do
        "COMPLETED" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "FAILED" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= transaction.status %>
    </span>
  </:col>
  <:col :let={{_id, transaction}} filter_item="reference" label="Reference">
    <%= transaction.reference %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="value_date" label="Value Date">
    <%= transaction.value_date %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="transaction_date" label="Transaction Date">
    <%= transaction.transaction_date %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="payment_method" label="Payment Method">
    <%= transaction.payment_method %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="external_reference" label="External Reference">
    <%= transaction.external_reference %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="debit_account" label="Debit Account">
    <%= transaction.debit_account %>
  </:col>
  <:col :let={{_id, transaction}} filter_item="credit_account" label="Credit Account">
    <%= transaction.credit_account %>
  </:col>
</.table>

<%= if @filter_modal do %>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanTransactionsLive.FilterComponent}
    id="modal-component"
    params={@filter_params}
    page={@page}
  />
<% end %>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/loan_transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanTransactionsLive.FilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/loan_transactions"}
  />
</.modal>
