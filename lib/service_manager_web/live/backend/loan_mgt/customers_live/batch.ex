defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.Batch do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Embedded.EmbeddedBatchCustomer

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, filter_modal: false)}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.get_loan_customer_batches(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:batches, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.get_loan_customer_batches(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:batches, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Batches")
  end

  defp apply_action(socket, :filter, params) do
    socket
    |> assign(:page, "Filter Customer Batches")
    |> assign(:data, %EmbeddedBatchCustomer{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/customer_batches/filter")
  end

  defp apply_action(socket, :excel_export, params) do
    socket
    |> assign(:page, "Export Loan Batches")
    |> assign(:data, %EmbeddedBatchCustomer{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/customer_batches/ExcelExportFilter")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/customer_batches")}
  end
end
