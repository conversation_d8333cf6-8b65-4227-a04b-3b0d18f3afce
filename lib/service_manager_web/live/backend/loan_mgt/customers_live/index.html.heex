<.header>
  <%= @page %>
</.header>

<.table
  id="customers"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/customers/filter"}
  export_url={~p"/mobileBanking/loan_mgt/customers/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.customers}
>
  <:col :let={{_id, customer}} filter_item="name" label="Name"><%= customer.name %></:col>
  <:col :let={{_id, customer}} filter_item="email" label="Email"><%= customer.email %></:col>
  <:col :let={{_id, customer}} filter_item="phone_number" label="Phone">
    <%= customer.phone_number %>
  </:col>
  <:col :let={{_id, customer}} filter_item="account_type" label="Account Type">
    <%= customer.account_type %>
  </:col>
  <:col :let={{_id, customer}} filter_item="account_number" label="Account Number">
    <%= customer.account_number %>
  </:col>
  <:col :let={{_id, customer}} filter_item="eligibility_status" label="Eligibility Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case customer.eligibility_status do
        "ELIGIBLE" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "NOT_ELIGIBLE" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= customer.eligibility_status %>
    </span>
  </:col>
  <:col :let={{_id, customer}} filter_item="max_loan_amount" label="Max Loan Amount">
    <%= Number.Currency.number_to_currency(customer.max_loan_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, customer}} filter_item="product_name" label="Product Name">
    <%= customer.product_name %>
  </:col>
</.table>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/customers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.CustomerFilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/customers"}
  />
</.modal>
