<.header>
  <%= @page %>
</.header>

<.table
  id="batches"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/customer_batches/filter"}
  export_url={~p"/mobileBanking/loan_mgt/customer_batches/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.batches}
>
  <:col :let={{_id, batch}} filter_item="batch_reference" label="Batch Reference">
    <%= batch.batch_reference %>
  </:col>
  <:col :let={{_id, batch}} filter_item="filename" label="Filename"><%= batch.filename %></:col>
  <:col :let={{_id, batch}} filter_item="status" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case batch.status do
        "COMPLETED" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "FAILED" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= batch.status %>
    </span>
  </:col>
  <:col :let={{_id, batch}} filter_item="valid_count" label="Valid Count">
    <%= batch.valid_count %>
  </:col>
  <:col :let={{_id, batch}} filter_item="invalid_count" label="Invalid Count">
    <%= batch.invalid_count %>
  </:col>
  <:col :let={{_id, batch}} filter_item="item_count" label="Item Count">
    <%= batch.item_count %>
  </:col>
  <:col :let={{_id, batch}} filter_item="maker_name" label="Maker"><%= batch.maker_name %></:col>
  <:col :let={{_id, batch}} filter_item="partner_name" label="Partner">
    <%= batch.partner_name %>
  </:col>
  <:col :let={{_id, batch}} filter_item="inserted_at" label="Created At">
    <%= batch.inserted_at %>
  </:col>

  <:action :let={{_id, batch}}>
    <.dropdown id={"dropdown-#{_id}"} label="Options">
      <.link
        patch={~p"/mobileBanking/loan_mgt/customers/#{batch.batch_reference}/batch_customers"}
        class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
      >
        <.icon name="hero-pencil-square" class="w-4 h-4 mr-2" /> View Entries
      </.link>
    </.dropdown>
  </:action>
</.table>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/customer_batches")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.BatchFilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/customer_batches"}
  />
</.modal>
