defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.Upload do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Repo
  alias ServiceManager.Accounts
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.LoanMgt.Helpers.ExcelHandler
  alias ServiceManager.LoanMgt.Schemas.{Upload, Customer, CustomerBatch}

  @impl true
  def mount(_params, _session, socket) do
    changeset = LoanMgt.change_upload(%Upload{})
    products = loan_products()
    partnerships = loan_partnerships()
    sample_data = generate_sample_data()

    socket =
      socket
      |> assign(:products, products)
      |> assign(:partnerships, partnerships)
      |> assign(:selected_product, nil)
      |> assign(:upload_step, :select_file)
      |> assign(:uploaded_entries, [])
      |> assign(:sample_data, sample_data)
      |> assign(:upload, %Upload{})
      |> assign_form(changeset)
      |> allow_upload(:uploaded_file,
        accept: ~w(.xlsx .csv),
        max_entries: 1,
        max_file_size: 10_000_000,
        auto_upload: true,
        progress: &handle_progress/3,
        required: true
      )

    {:ok, socket}
  end

  defp generate_sample_data do
    [
      %{
        account_number: "ACC001",
        account_type: "BANK_ACCOUNT",
        first_name: "John",
        last_name: "Doe",
        phone: "260**********",
        max_loan_amount: "5000.00"
      },
      %{
        account_number: "ACC002",
        account_type: "BANK_ACCOUNT",
        first_name: "Jane",
        last_name: "Smith",
        phone: "260**********",
        max_loan_amount: "7500.00"
      }
    ]
  end

  defp handle_progress(:uploaded_file, entry, socket) do
    if entry.done? do
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp loan_products do
    LoanMgt.get_loan_products()
  end

  defp loan_partnerships do
    LoanMgt.get_loan_partnerships()
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, params, socket) do
    case target do
      "validate" ->
        validate(params, socket)

      "cancel_upload" ->
        {:noreply, socket |> assign(upload_step: :select_file, uploaded_entries: [])}

      "save" ->
        handle_save(socket, params)

      "confirm_upload" ->
        confirm_upload(socket)
    end
  end

  def validate(params, socket) do
    changeset =
      socket.assigns.upload
      |> LoanMgt.change_upload(params["upload"])
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_save(%{assigns: assigns} = socket, %{
        "upload" => %{"product_id" => product_id, "partner_id" => partner_id}
      }) do
    uploaded_files = save_file(socket)

    case uploaded_files do
      [:ok] ->
        case ExcelHandler.process_uploaded_file(assigns.uploads, product_id, partner_id) do
          {:ok, entries} ->
            {:noreply,
             socket
             |> assign(
               upload_step: :review_entries,
               uploaded_entries: entries,
               selected_product_id: product_id,
               selected_partner_id: partner_id
             )}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Error processing file: #{reason}")}
        end

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Please upload a file")}
    end
  end

  def save_file(socket) do
    upload_dir = Path.join(:code.priv_dir(:service_manager), "static/uploads/loans")
    File.mkdir_p!(upload_dir)

    consume_uploaded_entries(socket, :uploaded_file, fn %{path: path}, entry ->
      dest = Path.join(upload_dir, entry.client_name)
      {:ok, File.cp!(path, dest)}
    end)
  end

  def ext(entry) do
    [ext | _] = MIME.extensions(entry.client_type)
    ext
  end

  def confirm_upload(%{assigns: assigns} = socket) do
    case save_uploaded_entries(assigns) do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(upload_step: :select_file, uploaded_entries: [])
         |> put_flash(:info, "Customers uploaded successfully")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Error saving entries: #{reason}")}
    end
  end

  defp save_uploaded_entries(%{
         current_user: user,
         uploaded_entries: entries,
         selected_product_id: product_id,
         selected_partner_id: partner_id
       }) do
    valid_entries = Enum.filter(entries, &Map.get(&1, :valid?))

    if Enum.empty?(valid_entries) do
      {:error, "No valid entries to upload."}
    else
      ref_id = Utils.timestamp()

      batch_params = %{
        batch_reference: ref_id,
        filename: "Uploaded Entries",
        status: "ACTIVE",
        valid_count: Enum.count(valid_entries),
        invalid_count: Enum.count(entries) - Enum.count(valid_entries),
        item_count: Enum.count(entries),
        maker_id: user.id,
        partner_id: partner_id |> String.to_integer()
      }

      Ecto.Multi.new()
      |> Ecto.Multi.insert(:batch, CustomerBatch.changeset(%CustomerBatch{}, batch_params))
      |> Ecto.Multi.merge(fn _ ->
        log_batch_entries(valid_entries, user, product_id, partner_id, ref_id)
      end)
      |> Repo.transaction()
      |> case do
        {:ok, _multi} ->
          {:ok, "Entries Submitted for Approval."}

        {:error, _failed_operation, failed_value, _changes_so_far} ->
          reason = Utils.traverse_errors(failed_value.errors)
          {:error, reason}
      end
    end
  end

  defp log_batch_entries(valid_entries, user, product_id, partner_id, batch_ref) do
    customers =
      Enum.map(valid_entries, fn entry ->
        customer = get_account(entry, entry.account_type)

        Map.merge(entry, %{
          ref_id: batch_ref,
          eligibility_status: "ELIGIBLE",
          last_eligibility_check: DateTime.utc_now() |> DateTime.truncate(:second),
          maker_id: user.id,
          customer_id: customer.id,
          product_id: product_id |> String.to_integer(),
          partner_id: partner_id |> String.to_integer(),
          inserted_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second),
          updated_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
        })
        |> Map.delete(:valid?)
        |> Map.delete(:reason)
      end)

    Ecto.Multi.new()
    |> Ecto.Multi.insert_all(:entries, Customer, customers)
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :changeset, to_form(changeset))
  end

  defp get_account(%{account_number: account_number}, "WALLET"),
    do: Accounts.user_by_account_number(account_number)

  defp get_account(%{account_number: account_number}, _),
    do: Accounts.user_by_account_number(account_number)
end
