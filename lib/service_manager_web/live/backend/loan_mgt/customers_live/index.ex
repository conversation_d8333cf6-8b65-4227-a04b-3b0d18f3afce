defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Embedded.EmbeddedCustomer

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.get_loan_customers(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:customers, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.get_loan_customers(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:customers, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Customers")
  end

  defp apply_action(socket, :filter, params) do
    socket
    |> assign(:page, "Filter Loan Customers")
    |> assign(:data, %EmbeddedCustomer{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/customers/filter")
  end

  defp apply_action(socket, :excel_export, params) do
    socket
    |> assign(:page, "Export Loan Customers")
    |> assign(:data, %EmbeddedCustomer{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/customers/ExcelExportFilter")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/customers")}
  end
end
