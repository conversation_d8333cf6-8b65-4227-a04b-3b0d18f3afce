defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.CustomerFilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.LoanMgt.Embedded.EmbeddedCustomer

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter loan partnerships</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:customer_name]}
              type="text"
              label="Customer Name"
              placeholder="Filter by customer name"
            />
            <.input field={@form[:email]} type="text" label="Email" placeholder="Filter by email" />
            <.input
              field={@form[:phone_number]}
              type="text"
              label="Phone Number"
              placeholder="Filter by phone number"
            />
            <.input
              field={@form[:account_number]}
              type="text"
              label="Account Number"
              placeholder="Filter by account number"
            />
            <.input
              field={@form[:account_type]}
              type="select"
              label="Account Type"
              prompt="Select account type"
              options={[BANK_ACCOUNT: "BANK ACCOUNT", WALLET: "WALLET"]}
            />
            <.input
              field={@form[:eligibility_status]}
              type="select"
              label="Eligibility Status"
              prompt="Select status"
              options={[ELIGIBLE: "ELIGIBLE", INELIGIBLE: "INELIGIBLE"]}
            />
            <.input
              field={@form[:max_loan_amount]}
              type="number"
              step="0.01"
              label="Max Loan Amount"
              placeholder="Filter by max loan amount"
            />
            <.input
              field={@form[:product_name]}
              type="text"
              label="Product Name"
              placeholder="Filter by product name"
            />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:name]}
              type="text"
              label="Name"
              placeholder="Filter by partnership name"
            />
            <.input
              field={@form[:description]}
              type="text"
              label="Description"
              placeholder="Filter by description"
            />
            <.input
              field={@form[:contact_person]}
              type="text"
              label="Contact Person"
              placeholder="Filter by contact person"
            />
            <.input
              field={@form[:contact_email]}
              type="email"
              label="Contact Email"
              placeholder="Filter by contact email"
            />
            <.input
              field={@form[:contact_phone]}
              type="tel"
              label="Contact Phone"
              placeholder="Filter by contact phone"
            />
            <.input
              field={@form[:address]}
              type="text"
              label="Address"
              placeholder="Filter by address"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt="Select status"
              options={status_options()}
            />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedCustomer{}
    changeset = EmbeddedCustomer.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedCustomer.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/loan_mgt/customers#{query}", replace: true)}
  end

  def status_options do
    [
      {"Active", "ACTIVE"},
      {"Inactive", "INACTIVE"}
    ]
  end
end
