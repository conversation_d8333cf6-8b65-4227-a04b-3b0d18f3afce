defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.BatchCustomers do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt

  @impl true
  def mount(%{"batch_ref" => batch_ref}, _session, socket) do
    {:ok, assign(socket, batch_ref: batch_ref)}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.get_loan_customers_by_batch_reference(socket.assigns.batch_ref, params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:batch_customers, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.get_loan_customers_by_batch_reference(socket.assigns.batch_ref, params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:batch_customers, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Batch Customers")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/customer_batches")}
  end
end
