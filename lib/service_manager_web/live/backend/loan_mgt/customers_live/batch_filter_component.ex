defmodule ServiceManagerWeb.Backend.LoanMgt.LoanCustomersLive.BatchFilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.LoanMgt.Embedded.EmbeddedBatchCustomer

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter customer batches</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:batch_reference]}
              type="text"
              label="Batch Reference"
              placeholder="Filter by batch reference"
            />
            <.input
              field={@form[:filename]}
              type="text"
              label="Filename"
              placeholder="Filter by filename"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt="Select status"
              options={status_options()}
            />
            <.input
              field={@form[:valid_count]}
              type="number"
              label="Valid Count"
              placeholder="Filter by valid count"
            />
            <.input
              field={@form[:invalid_count]}
              type="number"
              label="Invalid Count"
              placeholder="Filter by invalid count"
            />
            <.input
              field={@form[:item_count]}
              type="number"
              label="Total Items"
              placeholder="Filter by total items"
            />
            <.input
              field={@form[:partner_id]}
              type="text"
              label="Partner ID"
              placeholder="Filter by partner ID"
            />
            <.input
              field={@form[:from]}
              type="date"
              label="Start Date"
              placeholder="Select start date"
            />
            <.input field={@form[:to]} type="date" label="End Date" placeholder="Select end date" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:batch_reference]} type="text" label="Batch Reference" />
            <.input field={@form[:filename]} type="text" label="Filename" />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={status_options()}
            />
            <.input field={@form[:valid_count]} type="number" label="Valid Count" />
            <.input field={@form[:invalid_count]} type="number" label="Invalid Count" />
            <.input field={@form[:item_count]} type="number" label="Total Items" />
            <.input field={@form[:partner_id]} type="text" label="Partner ID" />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedBatchCustomer{}
    changeset = EmbeddedBatchCustomer.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedBatchCustomer.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(
       to: "/mobileBanking/loan_mgt/customers/customer_batches#{query}",
       replace: true
     )}
  end

  def status_options do
    [
      {"Pending", "PENDING"},
      {"Processing", "PROCESSING"},
      {"Completed", "COMPLETED"},
      {"Failed", "FAILED"}
    ]
  end
end
