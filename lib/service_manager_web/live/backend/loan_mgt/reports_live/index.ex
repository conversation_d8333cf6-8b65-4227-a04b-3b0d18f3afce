defmodule ServiceManagerWeb.Backend.LoanMgt.LoanReportsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:data, [])
     |> assign(:filter_params, %{})}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.get_loans(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(:data, data)
     |> assign(pagination: pagination)
     |> stream(:loans, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.get_loans(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> assign(:data, data)
     |> stream(:loans, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Reports")
    |> assign(:live_action, :index)
  end

  defp apply_action(socket, :filter, params) do
    socket
    |> assign(:page, "Filter Loan Reports")
    |> assign(:live_action, :filter)
    |> assign(:url, ~p"/mobileBanking/loan_mgt/loan_reports/filter")
  end

  defp apply_action(socket, :excel_export, params) do
    socket
    |> assign(:page, "Export Loan Reports")
    |> assign(:live_action, :excel_export)
    |> assign(:url, ~p"/mobileBanking/loan_mgt/loan_reports/ExcelExportFilter")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/loan_reports")}
  end
end
