<.header>
  <%= @page %>
</.header>

<.table
  id="loans"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/loan_reports/filter"}
  export_url={~p"/mobileBanking/loan_mgt/loan_reports/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.loans}
>
  <:col :let={{_id, loan}} filter_item="customer_name" label="Customer Name">
    <%= loan.customer_name %>
  </:col>
  <:col :let={{_id, loan}} filter_item="product_name" label="Product Name">
    <%= loan.product_name %>
  </:col>
  <:col :let={{_id, loan}} filter_item="amount" label="Amount">
    <%= Number.Currency.number_to_currency(loan.amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="amount" label="Charge Amount">
    <%= Number.Currency.number_to_currency(loan.charge_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="amount" label="Disbursed Amount">
    <%= Number.Currency.number_to_currency(loan.disbursed_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="interest_rate" label="Interest Rate">
    <%= loan.interest_rate %>%
  </:col>
  <:col :let={{_id, loan}} filter_item="duration" label="Duration">
    <%= loan.duration %> months
  </:col>
  <:col :let={{_id, loan}} filter_item="total_repayment" label="Total Repayment">
    <%= Number.Currency.number_to_currency(loan.total_repayment, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="repaid_amount" label="Repaid Amount">
    <%= Number.Currency.number_to_currency(loan.repaid_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="remaining_balance" label="Remaining Balance">
    <%= Number.Currency.number_to_currency(loan.remaining_balance, unit: "MWK") %>
  </:col>
  <:col :let={{_id, loan}} filter_item="status" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case loan.status do
        "ACTIVE" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "CLOSED" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= loan.status %>
    </span>
  </:col>
  <:col :let={{_id, loan}} filter_item="repayment_status" label="Repayment Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case loan.repayment_status do
        "PAID" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "OVERDUE" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= loan.repayment_status %>
    </span>
  </:col>
  <:col :let={{_id, loan}} filter_item="account_type" label="Account Type">
    <%= loan.account_type %>
  </:col>
  <:col :let={{_id, loan}} filter_item="account_number" label="Account Number">
    <%= loan.account_number %>
  </:col>
</.table>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/loan_transactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanReportsLive.FilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/loan_reports"}
  />
</.modal>
