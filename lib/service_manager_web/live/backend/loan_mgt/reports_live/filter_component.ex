defmodule ServiceManagerWeb.Backend.LoanMgt.LoanReportsLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.LoanMgt.Embedded.EmbeddedReport

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter loan reports</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:customer_name]}
              type="text"
              label="Customer Name"
              placeholder="Enter customer name"
            />
            <.input
              field={@form[:product_name]}
              type="text"
              label="Product Name"
              placeholder="Enter product name"
            />
            <.input
              field={@form[:amount]}
              type="number"
              label="Amount"
              step="0.01"
              placeholder="Enter amount"
            />
            <.input
              field={@form[:total_repayment]}
              type="number"
              label="Total Repayment"
              step="0.01"
              placeholder="Enter total repayment"
            />
            <.input
              field={@form[:repaid_amount]}
              type="number"
              label="Repaid Amount"
              step="0.01"
              placeholder="Enter repaid amount"
            />
            <.input
              field={@form[:remaining_balance]}
              type="number"
              label="Remaining Balance"
              step="0.01"
              placeholder="Enter remaining balance"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={status_options()}
              placeholder="Select status"
            />
            <.input
              field={@form[:repayment_status]}
              type="select"
              label="Repayment Status"
              prompt=""
              options={repayment_status_options()}
              placeholder="Select repayment status"
            />
            <.input
              field={@form[:account_type]}
              type="select"
              label="Account Type"
              prompt=""
              options={account_type_options()}
              placeholder="Select account type"
            />
            <.input
              field={@form[:account_number]}
              type="text"
              label="Account Number"
              placeholder="Enter account number"
            />
            <.input
              field={@form[:from]}
              type="date"
              label="Start Date"
              placeholder="Select start date"
            />
            <.input field={@form[:to]} type="date" label="End Date" placeholder="Select end date" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:customer_name]} type="text" label="Customer Name" />
            <.input field={@form[:product_name]} type="text" label="Product Name" />
            <.input field={@form[:amount]} type="number" label="Amount" step="0.01" />
            <.input field={@form[:total_repayment]} type="number" label="Total Repayment" step="0.01" />
            <.input field={@form[:repaid_amount]} type="number" label="Repaid Amount" step="0.01" />
            <.input
              field={@form[:remaining_balance]}
              type="number"
              label="Remaining Balance"
              step="0.01"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={status_options()}
            />
            <.input
              field={@form[:repayment_status]}
              type="select"
              label="Repayment Status"
              prompt=""
              options={repayment_status_options()}
            />
            <.input
              field={@form[:account_type]}
              type="select"
              label="Account Type"
              prompt=""
              options={account_type_options()}
            />
            <.input field={@form[:account_number]} type="text" label="Account Number" />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedReport{}
    changeset = EmbeddedReport.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedReport.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/loan_mgt/loan_reports#{query}", replace: true)}
  end

  def status_options do
    [
      {"Pending", "PENDING"},
      {"Rejected", "REJECTED"},
      {"Disbursed", "DISBURSED"}
    ]
  end

  def repayment_status_options do
    [
      {"Pending", "PENDING"},
      {"Partial", "PARTIAL"},
      {"Completed", "COMPLETED"}
    ]
  end

  def account_type_options do
    [
      {"Bank Account", "BANK_ACCOUNT"},
      {"Wallet", "WALLET"}
    ]
  end
end
