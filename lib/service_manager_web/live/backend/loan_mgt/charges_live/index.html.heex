<.header>
  <%= @page %>
</.header>

<.table
  id="charges"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/charges/filter"}
  export_url={~p"/mobileBanking/loan_mgt/charges/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.charges}
>
  <:col :let={{_id, charge}} filter_item="amount" label="Amount">
    <%= Number.Currency.number_to_currency(charge.amount, unit: "MWK", precision: 2) %>
  </:col>
  <:col :let={{_id, charge}} filter_item="description" label="Description">
    <%= charge.description %>
  </:col>
  <:col :let={{_id, charge}} filter_item="status" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case charge.status do
        "COMPLETED" -> "bg-green-50 text-green-700 ring-green-600/20"
        "PENDING" -> "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
        "FAILED" -> "bg-red-50 text-red-700 ring-red-600/20"
        "BLOCKED" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= charge.status %>
    </span>
  </:col>
  <:col :let={{_id, charge}} filter_item="reference" label="Reference">
    <%= charge.reference %>
  </:col>
  <:col :let={{_id, charge}} filter_item="payment_method" label="Payment Method">
    <%= charge.payment_method %>
  </:col>
  <:col :let={{_id, charge}} filter_item="external_reference" label="External Reference">
    <%= charge.external_reference %>
  </:col>
  <:col :let={{_id, charge}} filter_item="debit_account" label="Debit Account">
    <%= charge.debit_account %>
  </:col>
  <:col :let={{_id, charge}} filter_item="credit_account" label="Credit Account">
    <%= charge.credit_account %>
  </:col>
</.table>

<%= if @filter_modal do %>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.ChargesLive.FilterComponent}
    id="modal-component"
    params={@filter_params}
    page={@page}
  />
<% end %>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/charges")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.ChargesLive.FilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/charges"}
  />
</.modal>
