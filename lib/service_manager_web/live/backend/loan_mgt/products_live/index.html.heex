<.header>
  <%= @page %>
  <:actions>
    <%= if can_create?(@current_user, :loan_products) do %>
      <div class="flex space-x-4">
        <.link patch={~p"/mobileBanking/loan_mgt/products/new"}>
          <.button>Add Product</.button>
        </.link>
      </div>
    <% end %>
  </:actions>
</.header>

<.table
  id="products"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/products/filter"}
  export_url={~p"/mobileBanking/loan_mgt/products/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.products}
>
  <:col :let={{_id, product}} filter_item="name" label="Name"><%= product.name %></:col>
  <:col :let={{_id, product}} filter_item="description" label="Description">
    <%= product.description %>
  </:col>
  <:col :let={{_id, product}} filter_item="loan_account" label="Loan Account">
    <%= product.loan_account %>
  </:col>
  <:col :let={{_id, product}} filter_item="collection_account" label="Collection Account">
    <%= product.collection_account %>
  </:col>
  <:col :let={{_id, product}} filter_item="min_amount" label="Min Amount">
    <%= Number.Currency.number_to_currency(product.min_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, product}} filter_item="max_amount" label="Max Amount">
    <%= Number.Currency.number_to_currency(product.max_amount, unit: "MWK") %>
  </:col>
  <:col :let={{_id, product}} filter_item="charge_type" label="Charge Type">
    <%= product.charge_type %>
  </:col>
  <:col :let={{_id, product}} filter_item="charge_rate" label="Charge Rate">
    <%= if product.charge_type == "PERCENTAGE" do %>
      <%= product.charge_rate %>%
    <% else %>
      <%= Number.Currency.number_to_currency(product.charge_rate, unit: "MWK") %>
    <% end %>
  </:col>
  <:col :let={{_id, product}} filter_item="charge_account" label="Charge Account">
    <%= product.charge_account %>
  </:col>
  <:col :let={{_id, product}} filter_item="default_duration" label="Default Duration(Months)">
    <%= product.default_duration %>
  </:col>
  <:col :let={{_id, product}} filter_item="is_active" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      if product.is_active do
        "bg-green-50 text-green-700 ring-green-600/20"
      else
        "bg-red-50 text-red-700 ring-red-600/20"
      end
    ]}>
      <%= if product.is_active, do: "ACTIVE", else: "INACTIVE" %>
    </span>
  </:col>

  <:action :let={{_id, product}}>
    <%= if can_update?(@current_user, :loan_products) do %>
      <.dropdown id={"dropdown-#{_id}"} label="Options">
        <.link
          patch={~p"/mobileBanking/loan_mgt/products/#{product.id}/edit"}
          class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
        >
          <.icon name="hero-pencil-square" class="w-4 h-4 mr-2" /> Edit Details
        </.link>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="loan_partnership_modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/products")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.FormComponent}
    id={@product.id || :new}
    title={@page}
    action={@live_action}
    user={@current_user}
    product={@product}
    return_to={~p"/mobileBanking/loan_mgt/products"}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/products")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.FilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/products"}
  />
</.modal>
