defmodule ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Schemas.Product
  alias ServiceManager.LoanMgt.Embedded.EmbeddedProduct
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, filter_modal: false)}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.list_loan_products(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:products, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.list_loan_products(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:data, data)
     |> stream(:products, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Products")
    |> assign(:product, nil)
    |> assign(:live_action, :index)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page, "New Loan Product")
    |> assign(:product, %Product{})
    |> assign(:live_action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page, "Edit Loan Product")
    |> assign(:product, LoanMgt.get_loan_product!(id))
    |> assign(:live_action, :edit)
  end

  defp apply_action(socket, :filter, params) do
    socket
    |> assign(:page, "Filter Loan Products")
    |> assign(:data, %EmbeddedProduct{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/products/filter")
  end

  defp apply_action(socket, :excel_export, params) do
    socket
    |> assign(:data, %EmbeddedProduct{})
    |> assign(:page, "Export Loan Products")
    |> assign(:live_action, :excel_export)
    |> assign(:url, ~p"/mobileBanking/loan_mgt/products/ExcelExportFilter")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/products")}
  end
end
