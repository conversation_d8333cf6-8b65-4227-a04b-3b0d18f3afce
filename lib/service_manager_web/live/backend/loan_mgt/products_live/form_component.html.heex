<div>
  <.flash_group flash={@flash} />

  <.header class="text-center">
    <%= @title %>
    <:subtitle>Use this form to open or create a product.</:subtitle>
  </.header>

  <.simple_form
    for={@changeset}
    id="loan-product-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <div class="mb-6">
      <h2 class="text-2xl font-semibold mb-4 flex items-center">
        <i class="mr-2 fas fa-money-bill-wave"></i>
        <%= @title %>
      </h2>
    </div>
    <div class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:name]}
          type="text"
          label="Product Name"
          placeholder="Enter Product Name"
        />
        <.input
          field={@changeset[:description]}
          type="text"
          label="Description"
          placeholder="Enter Description"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:loan_account]}
          type="text"
          label="Loan Account"
          placeholder="Enter Loan Account"
        />
        <.input
          field={@changeset[:collection_account]}
          type="text"
          label="Collection Account"
          placeholder="Enter Collection Account"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:min_amount]}
          type="number"
          step="0.01"
          label="Minimum Amount"
          placeholder="Enter Minimum Amount"
        />
        <.input
          field={@changeset[:max_amount]}
          type="number"
          step="0.01"
          label="Maximum Amount"
          placeholder="Enter Maximum Amount"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:charge_type]}
          type="select"
          label="Charge Type"
          options={[
            {"PERCENTAGE", "PERCENTAGE"},
            {"ACTUAL", "ACTUAL"},
            {"NONE", "NONE"}
          ]}
          prompt="Select Charge Type"
        />
        <.input
          field={@changeset[:charge_rate]}
          type="number"
          step="0.01"
          label="Charge Rate"
          placeholder="Enter Charge Rate"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:charge_account]}
          type="number"
          label="Charge Account"
          placeholder="Enter Charge Account"
        />
        <.input
          field={@changeset[:default_duration]}
          type="number"
          label="Default Duration(Months)"
          placeholder="Enter Default Duration"
        />
      </div>
    </div>
    <div class="mt-8 flex justify-end">
      <.button phx-disable-with="Saving...">Save</.button>
    </div>
  </.simple_form>
</div>
