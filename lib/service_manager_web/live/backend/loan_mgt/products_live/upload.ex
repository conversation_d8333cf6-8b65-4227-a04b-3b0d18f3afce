defmodule ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.Upload do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Repo
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.LoanMgt.Helpers.ExcelHandler
  alias ServiceManager.LoanMgt.Schemas.{Upload, Product}

  @impl true
  def mount(_params, _session, socket) do
    changeset = LoanMgt.change_upload(%Upload{})
    sample_data = generate_sample_data()

    socket =
      socket
      |> assign(:upload_step, :select_file)
      |> assign(:uploaded_entries, [])
      |> assign(:sample_data, sample_data)
      |> assign(:upload, %Upload{})
      |> assign_form(changeset)
      |> allow_upload(:uploaded_file,
        accept: ~w(.xlsx .csv),
        max_entries: 1,
        max_file_size: 10_000_000,
        auto_upload: true,
        progress: &handle_progress/3,
        required: true
      )

    {:ok, socket}
  end

  defp generate_sample_data do
    [
      %{
        name: "Personal Loan",
        description: "Short-term personal loan",
        min_amount: "1000.00",
        max_amount: "10000.00",
        interest_rate: "5.5",
        default_duration: "12"
      },
      %{
        name: "Business Loan",
        description: "Small business loan",
        min_amount: "5000.00",
        max_amount: "50000.00",
        interest_rate: "7.5",
        default_duration: "24"
      }
    ]
  end

  defp handle_progress(:uploaded_file, entry, socket) do
    if entry.done? do
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event(target, value, socket), do: handle_event_switch(target, value, socket)

  defp handle_event_switch(target, params, socket) do
    case target do
      "validate" -> {:noreply, socket}
      "cancel-upload" -> cancel_upload(socket, params)
      "save" -> handle_save(socket, params)
      "confirm_upload" -> confirm_upload(socket)
      "cancel_upload" -> cancel_upload(socket)
    end
  end

  def cancel_upload(socket, %{"ref" => ref}) do
    {:noreply, cancel_upload(socket, :uploaded_file, ref)}
  end

  def handle_save(%{assigns: assigns} = socket, _params) do
    uploaded_files = save_file(socket)

    case uploaded_files do
      [:ok] ->
        case ExcelHandler.process_loan_products_file(assigns.uploads) do
          {:ok, entries} ->
            {:noreply,
             socket
             |> assign(upload_step: :review_entries, uploaded_entries: entries)}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Error processing file: #{reason}")}
        end

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Please upload a file")}
    end
  end

  def save_file(socket) do
    upload_dir = Path.join(:code.priv_dir(:service_manager), "static/uploads/loans")
    File.mkdir_p!(upload_dir)

    consume_uploaded_entries(socket, :uploaded_file, fn %{path: path}, entry ->
      dest = Path.join(upload_dir, entry.client_name)
      {:ok, File.cp!(path, dest)}
    end)
  end

  def ext(entry) do
    [ext | _] = MIME.extensions(entry.client_type)
    ext
  end

  def confirm_upload(%{assigns: %{uploaded_entries: entries, current_user: user}} = socket) do
    case save_uploaded_entries(entries, user) do
      {:ok, _} ->
        {:noreply,
         socket
         |> assign(upload_step: :select_file, uploaded_entries: [])
         |> put_flash(:info, "Loan products uploaded successfully")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Error saving entries: #{reason}")}
    end
  end

  def cancel_upload(socket) do
    {:noreply,
     socket
     |> assign(upload_step: :select_file, uploaded_entries: [])
     |> put_flash(:info, "Upload cancelled")}
  end

  defp save_uploaded_entries(entries, user) do
    valid_entries = Enum.filter(entries, &Map.get(&1, :valid?))

    if Enum.empty?(valid_entries) do
      {:error, "No valid entries to upload."}
    else
      products =
        Enum.map(valid_entries, fn entry ->
          Map.merge(entry, %{
            maker_id: user.id,
            inserted_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second),
            updated_at: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
          })
          |> Map.delete(:valid?)
          |> Map.delete(:reason)
        end)

      Ecto.Multi.new()
      |> Ecto.Multi.insert_all(:products, Product, products)
      |> Repo.transaction()
      |> case do
        {:ok, _multi} ->
          {:ok, "Products Uploaded Succeffully."}

        {:error, _failed_operation, failed_value, _changes_so_far} ->
          reason = Utils.traverse_errors(failed_value.errors)
          {:error, reason}
      end
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :changeset, to_form(changeset))
  end
end
