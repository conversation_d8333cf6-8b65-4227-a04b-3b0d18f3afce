defmodule ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Repo
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.LoanMgt.Schemas.Product

  @impl true
  def update(%{product: product} = assigns, socket) do
    changeset = LoanMgt.change_loan_product(product)

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, socket) do
    case target do
      "validate" -> validate(params, socket)
      "save" -> save_loan_product(socket, socket.assigns.action, params)
    end
  end

  def validate(params, socket) do
    changeset =
      socket.assigns.product
      |> LoanMgt.change_loan_product(params["product"])
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  defp save_loan_product(%{assigns: assigns} = socket, type, params) do
    params = Utils.to_atomic_map(params)

    case handle_loan_products(socket, params, type) do
      {:ok, message} ->
        {:noreply,
         socket
         |> put_flash(:info, message)
         |> push_navigate(to: assigns.return_to, replace: true)}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, reason)
         |> push_navigate(to: socket.assigns.return_to, replace: true)}
    end
  end

  defp handle_loan_products(%{assigns: assigns}, %{product: product}, :new) do
    product = Map.merge(product, %{maker_id: assigns.user.id, status: "ACTIVE"})

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:product, Product.changeset(%Product{}, product))
    |> Repo.transaction()
    |> case do
      {:ok, _multi} ->
        {:ok, "Loan Product created successfully"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  defp handle_loan_products(%{assigns: assigns} = _socket, %{product: product}, :edit) do
    product = Map.merge(product, %{checker_id: assigns.user.id})

    Ecto.Multi.new()
    |> Ecto.Multi.update(:product, Product.changeset(assigns.product, product))
    |> Repo.transaction()
    |> case do
      {:ok, _multi} ->
        {:ok, "Loan Product edited successfully"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :changeset, to_form(changeset))
  end
end
