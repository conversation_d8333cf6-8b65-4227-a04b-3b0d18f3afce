defmodule ServiceManagerWeb.Backend.LoanMgt.LoanProductsLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.LoanMgt.Embedded.EmbeddedProduct

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter loan products</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:name]}
              type="text"
              label="Name"
              placeholder="Filter by product name"
            />
            <.input
              field={@form[:description]}
              type="text"
              label="Description"
              placeholder="Filter by description"
            />
            <.input
              field={@form[:loan_account]}
              type="text"
              label="Loan Account"
              placeholder="Filter by loan account"
            />
            <.input
              field={@form[:collection_account]}
              type="text"
              label="Collection Account"
              placeholder="Filter by collection account"
            />
            <.input
              field={@form[:min_amount]}
              type="number"
              label="Min Amount"
              placeholder="Filter by minimum amount"
              step="0.01"
            />
            <.input
              field={@form[:max_amount]}
              type="number"
              label="Max Amount"
              placeholder="Filter by maximum amount"
              step="0.01"
            />
            <.input
              field={@form[:interest_rate]}
              type="number"
              label="Interest Rate"
              placeholder="Filter by interest rate"
              step="0.01"
            />
            <.input
              field={@form[:default_duration]}
              type="number"
              label="Default Duration (Months)"
              placeholder="Filter by duration"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt="Select status"
              options={status_options()}
            />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input
              field={@form[:name]}
              type="text"
              label="Name"
              placeholder="Filter by product name"
            />
            <.input
              field={@form[:description]}
              type="text"
              label="Description"
              placeholder="Filter by description"
            />
            <.input
              field={@form[:loan_account]}
              type="text"
              label="Loan Account"
              placeholder="Filter by loan account"
            />
            <.input
              field={@form[:collection_account]}
              type="text"
              label="Collection Account"
              placeholder="Filter by collection account"
            />
            <.input
              field={@form[:min_amount]}
              type="number"
              label="Min Amount"
              placeholder="Filter by minimum amount"
              step="0.01"
            />
            <.input
              field={@form[:max_amount]}
              type="number"
              label="Max Amount"
              placeholder="Filter by maximum amount"
              step="0.01"
            />
            <.input
              field={@form[:interest_rate]}
              type="number"
              label="Interest Rate"
              placeholder="Filter by interest rate"
              step="0.01"
            />
            <.input
              field={@form[:default_duration]}
              type="number"
              label="Default Duration (Months)"
              placeholder="Filter by duration"
            />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt="Select status"
              options={status_options()}
            />
            <.input field={@form[:from]} type="date" label="Start Date" />
            <.input field={@form[:to]} type="date" label="End Date" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedProduct{}
    changeset = EmbeddedProduct.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedProduct.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/loan_mgt/products#{query}", replace: true)}
  end

  def status_options do
    [
      {"Active", "ACTIVE"},
      {"Inactive", "INACTIVE"}
    ]
  end
end
