<div>
  <.flash_group flash={@flash} />

  <.header class="text-center">
    Upload Loan Products
    <:subtitle>Use this form to upload loan product data</:subtitle>
  </.header>

  <div class="mb-6">
    <h2 class="text-lg font-medium mb-2">Excel Template Format</h2>
    <p class="text-sm text-gray-600 mb-2">Please ensure your Excel file follows this format:</p>
    <table class="w-full border-collapse border border-gray-300">
      <thead class="bg-gray-50">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Name
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Description
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Min Amount
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Max Amount
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Interest Rate
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Default Duration
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <%= for row <- @sample_data do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><%= row.name %></td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= row.description %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= row.min_amount %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= row.max_amount %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= row.interest_rate %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <%= row.default_duration %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <div class="mt-2">
      <a
        href={~p"/mobileBanking/loan_mgt/export/template?id=#{:product}"}
        class="text-blue-500 hover:text-blue-700"
      >
        Download CSV Template
      </a>
    </div>
  </div>

  <%= if @upload_step == :select_file do %>
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <.form for={@changeset} id="upload-form" phx-change="validate" phx-submit="save">
          <div class="space-y-6">
            <div>
              <label for="uploaded_file" class="block text-sm font-medium text-gray-700">
                Excel File
              </label>
              <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div class="space-y-1 text-center">
                  <svg
                    class="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <div class="flex text-sm text-gray-600">
                    <label
                      for="file-upload"
                      class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                    >
                      <span>Upload a file</span>
                      <.live_file_input
                        upload={@uploads.uploaded_file}
                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                    </label>
                    <p class="pl-1">or drag and drop</p>
                  </div>
                  <p class="text-xs text-gray-500">
                    Excel or CSV files up to 10MB
                  </p>
                </div>
              </div>
            </div>

            <%= for entry <- @uploads.uploaded_file.entries do %>
              <div class="flex items-center justify-between py-2">
                <div class="flex items-center">
                  <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="ml-2 text-sm text-gray-500"><%= entry.client_name %></span>
                </div>
                <button
                  type="button"
                  phx-click="cancel-upload"
                  phx-value-ref={entry.ref}
                  class="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                >
                  Remove
                </button>
              </div>
              <%= for err <- upload_errors(@uploads.uploaded_file, entry) do %>
                <p class="text-sm text-red-500"><%= err %></p>
              <% end %>
            <% end %>

            <div>
              <.button
                type="submit"
                class="mt-6 w-full bg-orange-400 text-white py-2 px-4 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
              >
                Upload
              </.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
  <% else %>
    <div>
      <h2 class="text-lg font-medium mb-4">Review Uploaded Entries</h2>
      <table class="w-full border-collapse border border-gray-300">
        <thead>
          <tr class="bg-gray-100">
            <th class="border border-gray-300 px-4 py-2">Name</th>
            <th class="border border-gray-300 px-4 py-2">Description</th>
            <th class="border border-gray-300 px-4 py-2">Min Amount</th>
            <th class="border border-gray-300 px-4 py-2">Max Amount</th>
            <th class="border border-gray-300 px-4 py-2">Interest Rate</th>
            <th class="border border-gray-300 px-4 py-2">Default Duration</th>
            <th class="border border-gray-300 px-4 py-2">Status</th>
            <th class="border border-gray-300 px-4 py-2">Reason</th>
          </tr>
        </thead>
        <tbody>
          <%= for entry <- @uploaded_entries do %>
            <tr>
              <td class="border border-gray-300 px-4 py-2"><%= entry.name %></td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.description %></td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.min_amount %></td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.max_amount %></td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.interest_rate %></td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.default_duration %></td>
              <td class="border border-gray-300 px-4 py-2">
                <%= if entry.valid?, do: "Valid", else: "Invalid" %>
              </td>
              <td class="border border-gray-300 px-4 py-2"><%= entry.reason %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
      <div class="mt-4 flex justify-end space-x-2">
        <.button phx-click="cancel_upload" class="bg-red-500 hover:bg-red-700">Cancel</.button>
        <.button phx-click="confirm_upload" class="bg-green-500 hover:bg-green-700">
          Confirm Upload
        </.button>
      </div>
    </div>
  <% end %>
</div>
