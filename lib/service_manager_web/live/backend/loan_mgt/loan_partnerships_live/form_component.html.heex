<div>
  <.flash_group flash={@flash} />

  <.header class="text-center">
    <%= @title %>
    <:subtitle>Use this form to open or create a Partner.</:subtitle>
  </.header>

  <.simple_form
    for={@changeset}
    id="loan-product-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <div class="mb-6">
      <h2 class="text-2xl font-semibold mb-4 flex items-center">
        <i class="mr-2 fas fa-money-bill-wave"></i>
        <%= @title %>
      </h2>
    </div>
    <div class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:name]}
          type="text"
          label="Company Name"
          placeholder="Enter Company Name"
        />
        <.input
          field={@changeset[:description]}
          type="text"
          label="Description"
          placeholder="Enter Description"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:partner_type]}
          type="select"
          label="Partner Type"
          options={["Bank", "Credit Union", "Fintech"]}
          prompt="Choose a partner type"
        />
        <.input
          field={@changeset[:contact_person]}
          type="text"
          label="Contact Person"
          placeholder="Enter Contact Person"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:contact_email]}
          type="email"
          label="Contact Email"
          placeholder="Enter Contact Email"
        />
        <.input
          field={@changeset[:contact_phone]}
          type="text"
          label="Contact Phone"
          placeholder="Enter Contact Phone"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <.input
          field={@changeset[:partnership_start_date]}
          type="date"
          label="Partnership Start Date"
        />
        <.input
          field={@changeset[:partnership_end_date]}
          type="date"
          label="Partnership End Date"
        />
      </div>
    </div>
    <div class="mt-8 flex justify-end">
      <.button phx-disable-with="Saving...">Save</.button>
    </div>
  </.simple_form>
</div>
