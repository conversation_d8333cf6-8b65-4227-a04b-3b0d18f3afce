defmodule ServiceManagerWeb.Backend.LoanMgt.LoanPartnershipsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Schemas.Partnerships
  alias ServiceManager.LoanMgt.Embedded.EmbeddedPartnership
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:data, [])
     |> assign(:filter_params, %{})}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = LoanMgt.list_loan_partnerships(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:partnerships, data)}
  end

  def handle_params(params, _url, socket) do
    data = LoanMgt.list_loan_partnerships(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:filter_params, params)
     |> stream(:partnerships, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page, "Loan Partnerships")
    |> assign(:partnership, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page, "New Loan Partnership")
    |> assign(:partnership, %Partnerships{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page, "Edit Loan Partnership")
    |> assign(:partnership, LoanMgt.get_loan_partnership!(id))
  end

  defp apply_action(socket, :filter, params) do
    socket
    |> assign(:page, "Filter Loan Partnerships")
    |> assign(:data, %EmbeddedPartnership{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/partnerships/filter")
  end

  defp apply_action(socket, :excel_export, params) do
    socket
    |> assign(:page, "Export Loan Partnerships")
    |> assign(:data, %EmbeddedPartnership{})
    |> assign(:url, ~p"/mobileBanking/loan_mgt/partnerships/ExcelExportFilter")
  end

  @impl true
  def handle_event("reload", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/loan_mgt/partnerships")}
  end

  @impl true
  def handle_event("update_loan_partnership_status", %{"id" => id}, socket) do
    partnership = LoanMgt.get_loan_partnerships!(id)
    new_status = if partnership.status == "ACTIVE", do: "INACTIVE", else: "ACTIVE"

    case LoanMgt.update_loan_partnerships(partnership, %{status: new_status}) do
      {:ok, updated_partnership} ->
        {:noreply,
         socket
         |> stream_insert(:partnerships, updated_partnership)
         |> put_flash(:info, "Loan Partnership status updated successfully")}

      {:error, _changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to update Loan Partnership status")}
    end
  end

  @impl true
  def handle_event("delete_loan_partnership", %{"id" => id}, socket) do
    partnership = LoanMgt.get_loan_partnerships!(id)

    case LoanMgt.delete_loan_partnerships(partnership) do
      {:ok, deleted_partnership} ->
        {:noreply,
         socket
         |> stream_delete(:partnerships, deleted_partnership)
         |> put_flash(:info, "Loan Partnership deleted successfully")}

      {:error, _changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to delete Loan Partnership")}
    end
  end
end
