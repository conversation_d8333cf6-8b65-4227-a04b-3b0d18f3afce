defmodule ServiceManagerWeb.Backend.LoanMgt.LoanPartnershipsLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Repo
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.LoanMgt.Schemas.Partnerships

  @impl true
  def update(%{partnership: partnership} = assigns, socket) do
    changeset = LoanMgt.change_loan_partnerships(partnership)

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event(target, params, socket), do: handle_event_switch(target, params, socket)

  def handle_event_switch(target, params, socket) do
    case target do
      "validate" -> validate(params, socket)
      "save" -> save_loan_partnerships(socket, socket.assigns.action, params)
    end
  end

  def validate(params, socket) do
    changeset =
      socket.assigns.partnership
      |> LoanMgt.change_loan_partnerships(params["partnerships"])
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  defp save_loan_partnerships(%{assigns: assigns} = socket, type, params) do
    params = Utils.to_atomic_map(params)

    case handle_loan_partnerships(socket, params, type) do
      {:ok, message} ->
        {:noreply,
         socket
         |> put_flash(:info, message)
         |> push_navigate(to: socket.assigns.return_to, replace: true)}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, reason)
         |> push_navigate(to: socket.assigns.return_to, replace: true)}
    end
  end

  defp handle_loan_partnerships(%{assigns: assigns} = socket, %{partnerships: partnerships}, :new) do
    partnerships = Map.merge(partnerships, %{maker_id: assigns.user.id, status: "ACTIVE"})

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:partnership, Partnerships.changeset(%Partnerships{}, partnerships))
    |> Repo.transaction()
    |> case do
      {:ok, _multi} ->
        {:ok, "Loan Partnership created successfully"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  defp handle_loan_partnerships(
         %{assigns: assigns} = _socket,
         %{partnerships: partnerships},
         :edit
       ) do
    partnerships = Map.merge(partnerships, %{checker_id: assigns.user.id})

    Ecto.Multi.new()
    |> Ecto.Multi.update(:partnerships, Partnerships.changeset(assigns.partnership, partnerships))
    |> Repo.transaction()
    |> case do
      {:ok, _multi} ->
        {:ok, "Loan Partnership edited successfully"}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :changeset, to_form(changeset))
  end
end
