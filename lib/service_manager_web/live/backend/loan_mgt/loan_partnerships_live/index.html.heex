<.header>
  <%= @page %>
  <:actions>
    <%= if can_create?(@current_user, :loan_partnerships) do %>
      <div class="flex space-x-4">
        <.link patch={~p"/mobileBanking/loan_mgt/partnerships/new"}>
          <.button>Add Partner</.button>
        </.link>
      </div>
    <% end %>
  </:actions>
</.header>

<.flash_group flash={@flash} />

<.table
  id="partnerships"
  filter_params={@filter_params}
  filter_url={~p"/mobileBanking/loan_mgt/partnerships/filter"}
  export_url={~p"/mobileBanking/loan_mgt/partnerships/ExcelExportFilter"}
  show_filter={true}
  show_export={false}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.partnerships}
>
  <:col :let={{_id, partnership}} filter_item="name" label="Name"><%= partnership.name %></:col>
  <:col :let={{_id, partnership}} filter_item="description" label="Description">
    <%= partnership.description %>
  </:col>
  <:col :let={{_id, partnership}} filter_item="partner_type" label="Partner Type">
    <%= partnership.partner_type %>
  </:col>
  <:col :let={{_id, partnership}} filter_item="contact_person" label="Contact Person">
    <%= partnership.contact_person %>
  </:col>
  <:col :let={{_id, partnership}} filter_item="contact_email" label="Contact Email">
    <%= partnership.contact_email %>
  </:col>
  <:col :let={{_id, partnership}} filter_item="contact_phone" label="Contact Phone">
    <%= partnership.contact_phone %>
  </:col>
  <:col :let={{_id, partnership}} filter_item="status" label="Status">
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset",
      case partnership.status do
        "ACTIVE" -> "bg-green-50 text-green-700 ring-green-600/20"
        "INACTIVE" -> "bg-red-50 text-red-700 ring-red-600/20"
        _ -> "bg-gray-50 text-gray-700 ring-gray-600/20"
      end
    ]}>
      <%= partnership.status %>
    </span>
  </:col>

  <:action :let={{_id, partnership}}>
    <%= if has_any_permission?(@current_user, [:update, :activate, :delete], :loan_partnerships) do %>
      <.dropdown id={"dropdown-#{_id}"} label="Options">
        <%= if can_update?(@current_user, :loan_partnerships) do %>
          <.link
            patch={~p"/mobileBanking/loan_mgt/partnerships/#{partnership.id}/edit"}
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            <.icon name="hero-pencil-square" class="w-4 h-4 mr-2" /> Edit Details
          </.link>
        <% end %>
        <%= if can_activate?(@current_user, :loan_partnerships) do %>
          <button
            phx-click="update_loan_partnership_status"
            phx-value-id={partnership.id}
            class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            <.icon
              name={if partnership.status == "ACTIVE", do: "hero-x-circle", else: "hero-check-circle"}
              class="w-4 h-4 mr-2"
            />
            <%= if partnership.status == "ACTIVE", do: "Deactivate", else: "Activate" %>
          </button>
        <% end %>
        <%= if can_delete?(@current_user, :loan_partnerships) do %>
          <button
            phx-click="delete_loan_partnership"
            phx-value-id={partnership.id}
            data-confirm="Are you sure?"
            class="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
          >
            <.icon name="hero-trash" class="w-4 h-4 mr-2" /> Delete
          </button>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="loan_partnership_modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/partnerships")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanPartnershipsLive.FormComponent}
    id={@partnership.id || :new}
    title={@page}
    action={@live_action}
    user={@current_user}
    partnership={@partnership}
    return_to={~p"/mobileBanking/loan_mgt/partnerships"}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/loan_mgt/partnerships")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LoanMgt.LoanPartnershipsLive.FilterComponent}
    id={:filters}
    title={@page}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/loan_mgt/partnerships"}
  />
</.modal>
