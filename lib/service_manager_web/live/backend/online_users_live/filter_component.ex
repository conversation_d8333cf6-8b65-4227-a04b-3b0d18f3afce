defmodule ServiceManagerWeb.Backend.OnlineUsersLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
      </.header>

      <.simple_form
        for={@form}
        id="filter-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <.input
          field={@form[:type]}
          type="select"
          label="User Type"
          prompt="All Types"
          options={[{"Mobile Banking", "mobile_banking"}, {"Wallet", "wallet"}]}
        />

        <.input
          field={@form[:status]}
          type="select"
          label="Status"
          prompt="All Statuses"
          options={[{"Online", "online"}, {"Offline", "offline"}]}
        />

        <.input
          field={@form[:search]}
          type="text"
          label="Search"
          placeholder="Search by name, email, or phone"
        />

        <:actions>
          <.button phx-disable-with="Filtering...">Apply Filter</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: data} = assigns, socket) do
    changeset = data |> change()

    {:ok,
     socket
     |> assign(assigns)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"data" => data_params}, socket) do
    changeset =
      socket.assigns.data
      |> change()
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"data" => data_params}, socket) do
    case socket.assigns.action do
      :filter ->
        {:noreply,
         socket
         |> put_flash(:info, "Filters applied successfully")
         |> push_patch(to: socket.assigns.patch)}

      :excel_export ->
        {:noreply,
         socket
         |> put_flash(:info, "Export started")
         |> push_patch(to: socket.assigns.patch)}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp change(data, attrs \\ %{}) do
    {data, %{type: :string, status: :string, search: :string}}
    |> Ecto.Changeset.cast(attrs, [:type, :status, :search])
  end
end
