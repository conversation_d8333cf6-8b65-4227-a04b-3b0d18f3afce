<.header>
  Listing of Beneficiaries
  <:actions>
    <%!-- <.link patch={~p"/mobileBanking/beficiaries/new"}>
      <.button>Add Beneficiary</.button>
    </.link> --%>
  </:actions>
</.header>
<br />

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.navigate(~p"/mobileBanking/beficiaries/#{data}") end}
>
  <:col :let={{_id, data}} filter_item="id" label="Created By">
    <%= if data.user do %>
      <%= data.user.first_name %> <%= data.user.last_name %>
    <% else %>
      <span class="text-gray-500"></span>
    <% end %>
  </:col>
  <:col :let={{_id, data}} filter_item="name" label="Beneficiary Name"><%= data.name %></:col>
  <:col :let={{_id, data}} filter_item="account_number" label="Account Number">
    <%= data.account_number %>
  </:col>
  <:col :let={{_id, data}} filter_item="bank_code" label="Bank code"><%= data.bank_code %></:col>
  <:col :let={{_id, data}} filter_item="beneficiary_type" label="Beneficiary Type">
    <%= data.beneficiary_type %>
  </:col>
  <:col :let={{_id, data}} filter_item="currency" label="Currency"><%= data.currency %></:col>
  <:col :let={{_id, data}} filter_item="status" label="Status">
    <%= if data.status == "active" do %>
      <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
        <%= data.status %>
      </span>
    <% else %>
      <span class="inline-flex items-center rounded-full bg-pink-50 px-2 py-1 text-xs font-medium text-pink-700 ring-1 ring-inset ring-pink-700/10">
        <%= data.status %>
      </span>
    <% end %>
  </:col>

  <:col :let={{_id, data}} filter_item="is_default" label="Is Default">
    <%= if data.is_default do %>
      <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
        Yes
      </span>
    <% else %>
      <span class="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-700/10">
        No
      </span>
    <% end %>
  </:col>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/beficiaries")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.BeficiariesLive.FormComponent}
    id={@data.id || :new}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/beficiaries"}
  />
</.modal>
