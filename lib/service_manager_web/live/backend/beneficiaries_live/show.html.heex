<.header>
  User management <%= @data.id %>
  <:subtitle>This is a Beneficiary record from your database.</:subtitle>
</.header>

<.list>
  <:item title="Names"><%= @data.name %></:item>
  <:item title="Name"><%= @data.account_number %></:item>
  <:item title="Nickname"><%= @data.bank_code %></:item>
  <:item title="First name"><%= @data.currency %></:item>
  <:item title="Last name"><%= @data.description %></:item>
  <:item title="Phone number"><%= @data.status %></:item>
  <:item title="Date of birth"><%= @data.is_default %></:item>
  <:item title="Date of birth"><%= @data.inserted_at %></:item>
  <:item title="Date of birth"><%= @data.updated_at %></:item>
</.list>

<.back navigate={~p"/mobileBanking/beficiaries"}>Go Back</.back>

<.modal
  :if={@live_action == :edit}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/beficiaries/#{@data}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.BeficiariesLive.FormComponent}
    id={@data.id}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/beficiaries/#{@data}"}
  />
</.modal>
