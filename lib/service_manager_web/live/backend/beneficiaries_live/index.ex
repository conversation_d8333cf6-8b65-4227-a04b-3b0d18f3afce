defmodule ServiceManagerWeb.Backend.BeficiariesLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.BeneficieriesContext, as: MainContext
  alias ServiceManager.Schemas.Accounts.SchemaBeneficiary, as: MainSchema

  @url "/mobileBanking/beficiaries"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = MainContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:data, data)}
  end

  def handle_params(params, _url, socket) do
    data = MainContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:data, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Beneficiary")
    |> assign(:data, MainContext.get_data!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Beneficiary")
    |> assign(:data, %MainSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Beneficiaries")
    |> assign(:data, nil)
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    data = MainContext.get_data!(id)
    {:ok, _} = MainContext.delete_data(data)

    {:noreply, stream_delete(socket, :data, data)}
  end

  def handle_event("toggle_status", %{"id" => id, "status" => status}, socket) do
    data = MainContext.get_data!(id)

    MainContext.update_data(
      data,
      %{"status" => toggle_state_status(status)},
      socket.assigns.current_user
    )
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Beneficiary updated successfully")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: @url, replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_approval(is_approved?) do
    if is_approved? do
      "Approved"
    else
      "Not Approved"
    end
  end
end
