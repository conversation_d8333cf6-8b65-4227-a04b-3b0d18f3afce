<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="fund-request-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />
<br />

<.header>
  Listing Fund Requests
</.header>

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/fund-requests/filter"}
  export_url={~p"/mobileBanking/fund-requests/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.patch(~p"/mobileBanking/fund-requests/#{data}/show") end}
>
  <:col :let={{_id, data}} filter_item="id" label="ID">
    <div class="text-[12px]"><%= data.id %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="transaction_reference" label="Reference">
    <div class="text-[12px] whitespace-nowrap"><%= data.transaction_reference %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="amount" label="Amount">
    <div class="text-[12px] text-right font-medium">MWK <%= data.amount %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="sender_account" label="From Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.sender_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="receiver_account" label="To Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.receiver_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="description" label="Description">
    <div class="text-[12px] whitespace-nowrap"><%= data.description %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="status" label="Status">
    <.status_pill status={data.status} text={data.status} />
  </:col>
  <:col :let={{_id, data}} filter_item="inserted_at" label="Created At">
    <div class="text-[12px] whitespace-nowrap"><%= data.inserted_at %></div>
  </:col>
  <:action :let={{_id, data}}>
    <%= if can_approve?(@current_user, :fund_requests) do %>
      <div class="sr-only">
        <.link navigate={~p"/mobileBanking/fund-requests/#{data}/show"}>Show</.link>
      </div>
      <.link
        phx-click="toggle_status"
        phx-value-id={data.id}
        phx-value-status={data.status}
        class="text-[12px] text-blue-600 hover:text-blue-900"
      >
        <%= case data.status do %>
          <% "pending" -> %>
            Approve
          <% "approved" -> %>
            Reject
          <% "rejected" -> %>
            Approve
        <% end %>
      </.link>
    <% end %>
  </:action>
</.table>
<!-- Charts Section -->
<div
  class="mt-8 grid grid-cols-2 gap-6"
  id="charts"
  phx-hook="Charts"
  data-chart-data={Jason.encode!(@chart_data)}
>
  <!-- Request Volume Over Time -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Request Volume Over Time</h3>
    <div id="volumeChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Status Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
    <div id="statusChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Amount Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Amount Distribution</h3>
    <div id="amountChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Type Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Request Types</h3>
    <div id="typeChart" class="w-full h-[300px]"></div>
  </div>
</div>
<!-- ApexCharts Script -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts">
</script>

<.modal
  :if={@live_action == :show}
  id="fund-request-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/fund-requests")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.FundRequestsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/fund-requests")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/fund-requests"}
  />
</.modal>
