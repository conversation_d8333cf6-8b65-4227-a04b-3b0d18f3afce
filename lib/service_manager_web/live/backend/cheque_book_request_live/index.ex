defmodule ServiceManagerWeb.Backend.ChequeBookRequestLive.Index do
  use ServiceManagerWeb, :live_view
  import Ecto.Query
  import ServiceManagerWeb.Utilities.Sorting
  import ServiceManagerWeb.Utilities.Utils, only: [generate_pagination_details: 1]
  import ServiceManagerWeb.Utilities.PermissionHelpers
  alias ServiceManager.Contexts.ChequeBookRequestsContext
  alias ServiceManager.Contexts.LogsContext
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeBookRequest
  alias ServiceManager.Repo

  @url "/mobileBanking/cheque-book-requests"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:live_action, :index)
      |> assign(:selected_column, "inserted_at")
      |> assign(:filter_params, %{
        "search" => "",
        "page" => 1,
        "page_size" => 10
      })
      |> assign(:stats, get_request_stats())
      |> assign(:show_rejection_modal, false)
      |> assign(:rejecting_request_id, nil)

    {:ok, socket}
  end

  defp get_request_stats do
    total_requests = Repo.aggregate(ChequeBookRequest, :count, :id)

    pending_requests =
      Repo.aggregate(
        from(r in ChequeBookRequest, where: r.request_status == "pending"),
        :count,
        :id
      )

    approved_requests =
      Repo.aggregate(
        from(r in ChequeBookRequest, where: r.request_status == "approved"),
        :count,
        :id
      )

    rejected_requests =
      Repo.aggregate(
        from(r in ChequeBookRequest, where: r.request_status == "rejected"),
        :count,
        :id
      )

    fulfilled_requests =
      Repo.aggregate(
        from(r in ChequeBookRequest, where: r.request_status == "fulfilled"),
        :count,
        :id
      )

    [
      %{
        title: "Total Requests",
        value: total_requests
      },
      %{
        title: "Request Status",
        value: "#{pending_requests} / #{approved_requests}",
        comparison: "pending / approved"
      },
      %{
        title: "Request Outcomes",
        value: "#{fulfilled_requests} / #{rejected_requests}",
        comparison: "fulfilled / rejected"
      }
    ]
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = ChequeBookRequestsContext.list_data(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:cheque_requests, data)}
  end

  def handle_params(params, _url, socket) do
    data = ChequeBookRequestsContext.list_data(params)
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:cheque_requests, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Cheque Book Requests")
    |> assign(:cheque_request, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Cheque Book Request")
    |> assign(:cheque_request, %ChequeBookRequest{})
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Cheque Book Requests")
    |> assign(:data, %{})
  end

  defp apply_action(socket, :export, _params) do
    socket
    |> assign(:page_title, "Export Cheque Book Requests")
    |> assign(:data, %{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Cheque Book Request")
    |> assign(:cheque_request, ChequeBookRequestsContext.get_data!(id))
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Cheque Book Request Details")
    |> assign(:cheque_request, ChequeBookRequestsContext.get_data!(id))
  end

  @impl true
  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.live_component
      module={ServiceManagerWeb.Components.StatsComponent}
      id="cheque-book-stats"
      title=""
      stats={@stats}
    />

    <br />
    <hr />

    <.header>
      Cheque Book Requests
      <:actions>
        <.link patch={~p"/mobileBanking/cheque-book-requests/new"}>
          <.button>New Cheque Book Request</.button>
        </.link>
      </:actions>
    </.header>

    <.table
      id="cheque_requests"
      rows={@streams.cheque_requests}
      filter_params={@filter_params}
      pagination={@pagination}
      selected_column={@selected_column}
      row_click={
        fn {_id, request} -> JS.patch(~p"/mobileBanking/cheque-book-requests/#{request.id}") end
      }
    >
      <:col :let={{_id, request}} filter_item="request_reference" label="Reference">
        <%= request.request_reference || "--" %>
      </:col>

      <:col :let={{_id, request}} filter_item="account_number" label="Account">
        <%= request.account_number || "--" %>
      </:col>

      <:col :let={{_id, request}} filter_item="customer_name" label="Customer">
        <%= if request.user do %>
          <%= "#{request.user.first_name} #{request.user.last_name}" %>
        <% else %>
          --
        <% end %>
      </:col>

      <:col :let={{_id, request}} filter_item="number_of_leaves" label="Leaves">
        <%= request.number_of_leaves || "--" %>
      </:col>

      <:col :let={{_id, request}} filter_item="request_status" label="Status">
        <span class={status_color(request.request_status)}>
          <%= String.capitalize(request.request_status) %>
        </span>
      </:col>

      <:col :let={{_id, request}} filter_item="inserted_at" label="Request Date">
        <%= if request.inserted_at do %>
          <%= request.inserted_at |> to_string |> String.replace("T", " ") |> String.replace("Z", "") %>
        <% else %>
          --
        <% end %>
      </:col>

      <:action :let={{id, request}}>
        <.dropdown id={"dropdown-#{id}"} label="Options">
          <%= if request.request_status not in ["pending", "rejected", "approved"] do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              patch={~p"/mobileBanking/cheque-book-requests/#{request.id}/edit"}
            >
              Edit
            </.link>
          <% end %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cheque-book-requests/#{request.id}"}
          >
            View Details
          </.link>

          <%= if request.request_status != "pending" and request.request_status != "rejected" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("change_status", value: %{id: request.id, status: "pending"})}
              data-confirm="Are you sure you want to mark this request as pending?"
            >
              Mark as Pending
            </.link>
          <% end %>

          <%= if request.request_status == "pending" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("change_status", value: %{id: request.id, status: "approved"})}
              data-confirm="Are you sure you want to approve this request?"
            >
              Approve Request
            </.link>
          <% end %>

          <%= if request.request_status == "pending" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("reject_request", value: %{id: request.id})}
              data-confirm="Please provide a rejection reason"
            >
              Reject Request
            </.link>
          <% end %>

          <%= if request.request_status == "approved" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("change_status", value: %{id: request.id, status: "fulfilled"})}
              data-confirm="Are you sure you want to mark this request as fulfilled?"
            >
              Mark as Fulfilled
            </.link>
          <% end %>
        </.dropdown>
      </:action>
    </.table>

    <.modal
      :if={@live_action in [:new, :edit]}
      id="cheque-request-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/cheque-book-requests")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.ChequeBookRequestLive.FormComponent}
        id={@cheque_request.id || :new}
        title={@page_title}
        action={@live_action}
        cheque_request={@cheque_request}
        current_user={@current_user}
        patch={~p"/mobileBanking/cheque-book-requests"}
      />
    </.modal>

    <.modal
      :if={@live_action == :show}
      id="cheque-request-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/cheque-book-requests")}
      max-width="max-w-[95%]"
    >
      <.live_component
        module={ServiceManagerWeb.Backend.ChequeBookRequestLive.ShowComponent}
        id={@cheque_request.id}
        title={@page_title}
        cheque_request={@cheque_request}
        patch={~p"/mobileBanking/cheque-book-requests"}
      />
    </.modal>

    <.modal
      :if={@show_rejection_modal}
      id="rejection-modal"
      show
      on_cancel={JS.push("cancel_rejection")}
    >
      <.header>
        Reject Cheque Book Request
      </.header>

      <form id="rejection-form" phx-submit="submit_rejection">
        <div class="space-y-4 py-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Rejection Reason</label>
            <div class="mt-1">
              <textarea
                name="rejection_reason"
                rows="3"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                required
              ></textarea>
            </div>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-end gap-x-6">
          <button
            type="button"
            class="text-sm font-semibold leading-6 text-gray-900"
            phx-click={JS.push("cancel_rejection")}
          >
            Cancel
          </button>
          <button
            type="submit"
            class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            phx-disable-with="Rejecting..."
          >
            Submit
          </button>
        </div>
      </form>
    </.modal>
    """
  end

  @impl true
  def handle_event("reject_request", %{"id" => id}, socket) do
    {:noreply,
     socket
     |> assign(:rejecting_request_id, id)
     |> assign(:show_rejection_modal, true)}
  end

  def handle_event("submit_rejection", %{"rejection_reason" => reason}, socket) do
    id = socket.assigns.rejecting_request_id
    cheque_request = ChequeBookRequestsContext.get_data!(id)

    changeset =
      ChequeBookRequest.changeset(cheque_request, %{
        "request_status" => "rejected",
        "rejection_reason" => reason,
        "rejected_at" => DateTime.utc_now()
      })

    case Repo.update(changeset) do
      {:ok, updated_request} ->
        if updated_request.user && updated_request.user.phone_number do
          Repo.insert(%ServiceManager.Notifications.SMSNotification{
            msisdn: updated_request.user.phone_number,
            message:
              "Your cheque book request (Ref: #{updated_request.request_reference}) has been rejected. Reason: #{reason}"
          })
        end

        {:noreply,
         socket
         |> assign(:show_rejection_modal, false)
         |> assign(:rejecting_request_id, nil)
         |> assign(:stats, get_request_stats())
         |> stream_insert(:cheque_requests, updated_request)
         |> put_flash(:info, "Request rejected successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> assign(:show_rejection_modal, false)
         |> assign(:rejecting_request_id, nil)
         |> put_flash(:error, "Failed to reject request")}
    end
  end

  def handle_event("cancel_rejection", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_rejection_modal, false)
     |> assign(:rejecting_request_id, nil)}
  end

  @impl true
  def handle_event("change_status", %{"id" => id, "status" => new_status}, socket) do
    cheque_request = ChequeBookRequestsContext.get_data!(id)

    changeset =
      ChequeBookRequest.changeset(cheque_request, %{
        "request_status" => new_status,
        "fulfilled_at" => if(new_status == "fulfilled", do: DateTime.utc_now(), else: nil),
        "issued_at" => if(new_status == "approved", do: DateTime.utc_now(), else: nil)
      })

    case Repo.update(changeset) do
      {:ok, updated_request} ->
        # Send SMS notification based on status
        message =
          case new_status do
            "pending" ->
              "Your cheque book request (Ref: #{updated_request.request_reference}) has been marked as pending. We will process it shortly."

            "approved" ->
              "Your cheque book request (Ref: #{updated_request.request_reference}) has been approved. You will be notified when it's ready for collection."

            "rejected" ->
              "Your cheque book request (Ref: #{updated_request.request_reference}) has been rejected. Please contact your branch for more information."

            "fulfilled" ->
              "Your cheque book request (Ref: #{updated_request.request_reference}) has been fulfilled and is ready for collection at your branch."
          end

        if updated_request.user && updated_request.user.phone_number do
          Repo.insert(%ServiceManager.Notifications.SMSNotification{
            msisdn: updated_request.user.phone_number,
            message: message
          })
        end

        {:noreply,
         socket
         |> assign(:stats, get_request_stats())
         |> stream_insert(:cheque_requests, updated_request)
         |> put_flash(:info, "Request status updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update request status")}
    end
  end

  defp status_color(status) do
    base_classes = "px-2 py-1 text-xs font-medium rounded-full"

    status_specific_classes =
      case status do
        "pending" -> "bg-orange-100 text-orange-800"
        "approved" -> "bg-blue-100 text-blue-800"
        "rejected" -> "bg-red-100 text-red-800"
        "fulfilled" -> "bg-green-100 text-green-800"
        _ -> "bg-gray-100 text-gray-800"
      end

    "#{base_classes} #{status_specific_classes}"
  end
end
