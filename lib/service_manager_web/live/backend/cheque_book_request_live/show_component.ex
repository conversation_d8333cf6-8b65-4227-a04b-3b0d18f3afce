defmodule ServiceManagerWeb.Backend.ChequeBookRequestLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-8 divide-y divide-gray-200">
      <div>
        <h2 class="text-base font-semibold leading-7 text-gray-900">Cheque Book Request Details</h2>
        <p class="mt-1 text-sm leading-6 text-gray-600">View details of the cheque book request.</p>
      </div>

      <div class="pt-6">
        <dl class="divide-y divide-gray-100">
          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Reference</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= @cheque_request.request_reference || "--" %>
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Account Number</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= @cheque_request.account_number || "--" %>
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Customer</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= if @cheque_request.user do %>
                <%= "#{@cheque_request.user.first_name} #{@cheque_request.user.last_name}" %>
              <% else %>
                --
              <% end %>
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Number of Leaves</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= @cheque_request.number_of_leaves || "--" %>
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Status</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <.status_pill
                status={@cheque_request.request_status == "approved"}
                text={String.capitalize(@cheque_request.request_status)}
              />
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Request Date</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= if @cheque_request.inserted_at do %>
                <%= @cheque_request.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              <% else %>
                --
              <% end %>
            </dd>
          </div>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Request Reason</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= @cheque_request.request_reason || "--" %>
            </dd>
          </div>

          <%= if @cheque_request.request_status == "rejected" do %>
            <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt class="text-sm font-medium leading-6 text-gray-900">Rejection Reason</dt>
              <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                <%= @cheque_request.rejection_reason || "--" %>
              </dd>
            </div>
          <% end %>

          <div class="px-4 py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt class="text-sm font-medium leading-6 text-gray-900">Last Updated</dt>
            <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <%= if @cheque_request.updated_at do %>
                <%= @cheque_request.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              <% else %>
                --
              <% end %>
            </dd>
          </div>
        </dl>
      </div>

      <div class="mt-6 flex items-center justify-end gap-x-6">
        <.link
          patch={@patch}
          class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        >
          Back
        </.link>
        <%= if @cheque_request.request_status not in ["pending", "rejected", "approved"] do %>
          <.link
            patch={~p"/mobileBanking/cheque-book-requests/#{@cheque_request.id}/edit"}
            class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Edit
          </.link>
        <% end %>
      </div>
    </div>
    """
  end
end
