defmodule ServiceManagerWeb.Backend.ChequeBookRequestLive.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.ChequeBookRequestsContext
  alias ServiceManager.Contexts.LogsContext
  import Phoenix.HTML.Form

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage cheque book requests.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="cheque-request-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <.input field={@form[:account_number]} type="text" label="Account Number" required />
          </div>

          <div>
            <.input field={@form[:branch_code]} type="text" label="Branch Code" />
          </div>

          <div>
            <.input
              field={@form[:number_of_leaves]}
              type="number"
              label="Number of Leaves"
              required
              min="1"
            />
          </div>

          <div>
            <.input
              field={@form[:request_status]}
              type="select"
              label="Status"
              options={["pending", "approved", "rejected", "fulfilled"]}
            />
          </div>

          <div class="md:col-span-2">
            <.input field={@form[:request_reason]} type="textarea" label="Request Reason" required />
          </div>

          <%= if input_value(@form, :request_status) == "rejected" do %>
            <div class="md:col-span-2">
              <.input
                field={@form[:rejection_reason]}
                type="textarea"
                label="Rejection Reason"
                required
              />
            </div>
          <% end %>

          <%= if input_value(@form, :request_status) in ["approved", "fulfilled"] do %>
            <div class="md:col-span-2">
              <div class="text-sm text-gray-500">
                <%= if input_value(@form, :request_status) == "approved" do %>
                  Issued at: <%= if @cheque_request.issued_at,
                    do: Calendar.strftime(@cheque_request.issued_at, "%Y-%m-%d %H:%M"),
                    else: "Not issued yet" %>
                <% end %>
                <%= if input_value(@form, :request_status) == "fulfilled" do %>
                  Fulfilled at: <%= if @cheque_request.fulfilled_at,
                    do: Calendar.strftime(@cheque_request.fulfilled_at, "%Y-%m-%d %H:%M"),
                    else: "Not fulfilled yet" %>
                <% end %>
              </div>
            </div>
          <% end %>

          <div class="md:col-span-2">
            <div class="text-sm text-gray-500">
              Reference: <%= @cheque_request.request_reference || "Will be generated" %>
            </div>
          </div>
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Save Cheque Book Request</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{cheque_request: cheque_request} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(ChequeBookRequestsContext.change_data(cheque_request))
     end)}
  end

  @impl true
  def handle_event("validate", %{"cheque_book_request" => params}, socket) do
    changeset =
      socket.assigns.cheque_request
      |> ChequeBookRequestsContext.change_data(params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, form: to_form(changeset))}
  end

  def handle_event("save", %{"cheque_book_request" => params}, socket) do
    save_cheque_request(socket, socket.assigns.action, params)
  end

  defp save_cheque_request(socket, :new, params) do
    case ChequeBookRequestsContext.insert_data(params, socket.assigns.current_user) do
      {:ok, cheque_request} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Cheque book request created successfully",
              details: inspect(cheque_request)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Cheque book request created successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Cheque book request create failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_cheque_request(socket, :edit, params) do
    case ChequeBookRequestsContext.update_data(
           socket.assigns.cheque_request,
           params,
           socket.assigns.current_user
         ) do
      {:ok, cheque_request} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Cheque book request updated successfully",
              details: inspect(cheque_request)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Cheque book request updated successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Cheque book request update failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
