<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="cheque-book-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />

<.header>
  Cheque Book Requests
  <:actions>
    <%= if can_create?(@current_user, :cheque_book_requests) do %>
      <.link patch={~p"/mobileBanking/cheque-book-requests/new"}>
        <.button>New Cheque Book Request</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="cheque_requests"
  rows={@streams.cheque_requests}
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  row_click={
    fn {_id, request} -> JS.patch(~p"/mobileBanking/cheque-book-requests/#{request.id}/edit") end
  }
>
  <:col :let={{_id, request}} filter_item="request_reference" label="Reference">
    <%= request.request_reference || "--" %>
  </:col>

  <:col :let={{_id, request}} filter_item="account_number" label="Account">
    <%= request.account_number || "--" %>
  </:col>

  <:col :let={{_id, request}} filter_item="customer_name" label="Customer">
    <%= if request.user do %>
      <%= "#{request.user.first_name} #{request.user.last_name}" %>
    <% else %>
      --
    <% end %>
  </:col>

  <:col :let={{_id, request}} filter_item="number_of_leaves" label="Leaves">
    <%= request.number_of_leaves || "--" %>
  </:col>

  <:col :let={{_id, request}} filter_item="request_status" label="Status">
    <.status_pill
      status={request.request_status == "approved"}
      text={String.capitalize(request.request_status)}
    />
  </:col>

  <:col :let={{_id, request}} filter_item="inserted_at" label="Request Date">
    <%= if request.inserted_at do %>
      <%= request.inserted_at |> to_string |> String.replace("T", " ") |> String.replace("Z", "") %>
    <% else %>
      --
    <% end %>
  </:col>

  <:action :let={{id, request}}>
    <%= if has_any_permission?(@current_user, [:view, :update, :delete], :cheque_book_requests) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_update?(@current_user, :cheque_book_requests) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cheque-book-requests/#{request.id}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_view?(@current_user, :cheque_book_requests) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cheque-book-requests/#{request.id}"}
          >
            View Details
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :cheque_book_requests) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: request.id})}
            data-confirm="Are you sure you want to delete this request?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="cheque-request-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cheque-book-requests")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.ChequeBookRequestLive.FormComponent}
    id={@cheque_request.id || :new}
    title={@page_title}
    action={@live_action}
    cheque_request={@cheque_request}
    current_user={@current_user}
    patch={~p"/mobileBanking/cheque-book-requests"}
  />
</.modal>

<.modal
  :if={@live_action == :show}
  id="cheque-request-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cheque-book-requests")}
  max-width="max-w-[95%]"
>
  <.live_component
    module={ServiceManagerWeb.Backend.ChequeBookRequestLive.ShowComponent}
    id={@cheque_request.id}
    title={@page_title}
    cheque_request={@cheque_request}
    patch={~p"/mobileBanking/cheque-book-requests"}
  />
</.modal>
