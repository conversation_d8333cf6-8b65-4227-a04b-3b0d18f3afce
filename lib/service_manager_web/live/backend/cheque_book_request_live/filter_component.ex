defmodule ServiceManagerWeb.Backend.ChequeBookRequestLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to filter cheque book requests.</:subtitle>
      </.header>

      <.simple_form for={%{}} as={:filter} id="filter-form" phx-target={@myself} phx-submit="save">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <.input type="text" label="Search" name="search" value={@filter_params["search"]} />

          <.input
            type="select"
            label="Status"
            name="status"
            value={@filter_params["status"]}
            options={["", "pending", "approved", "rejected", "fulfilled"]}
          />

          <.input type="date" label="From Date" name="from_date" value={@filter_params["from_date"]} />

          <.input type="date" label="End Date" name="end_date" value={@filter_params["end_date"]} />
        </div>

        <:actions>
          <.button phx-disable-with="Filtering...">Filter Results</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def handle_event("save", %{"filter" => params}, socket) do
    endpoint = "?" <> URI.encode_query(params)

    {:noreply,
     socket
     |> push_navigate(to: socket.assigns.url <> endpoint, replace: true)}
  end
end
