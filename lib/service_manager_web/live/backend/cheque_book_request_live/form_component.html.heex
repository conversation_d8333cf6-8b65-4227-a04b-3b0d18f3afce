<div>
  <h2 class="text-lg font-medium text-gray-900"><%= @title %></h2>

  <.form
    let={f}
    for={@changeset}
    id="cheque-request-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <div class="mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-4">
      <div class="sm:col-span-2">
        <%= label(f, :request_status, class: "block text-sm font-medium text-gray-700") %>
        <%= select(f, :request_status, ["pending", "approved", "rejected", "fulfilled"],
          class:
            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        ) %>
        <%= error_tag(f, :request_status) %>
      </div>

      <div>
        <%= label(f, :number_of_leaves, class: "block text-sm font-medium text-gray-700") %>
        <%= number_input(f, :number_of_leaves,
          class:
            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        ) %>
        <%= error_tag(f, :number_of_leaves) %>
      </div>

      <div class="sm:col-span-2">
        <%= label(f, :request_reason, class: "block text-sm font-medium text-gray-700") %>
        <%= textarea(f, :request_reason,
          class:
            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        ) %>
        <%= error_tag(f, :request_reason) %>
      </div>

      <%= if input_value(f, :request_status) == "rejected" do %>
        <div class="sm:col-span-2">
          <%= label(f, :rejection_reason, class: "block text-sm font-medium text-gray-700") %>
          <%= textarea(f, :rejection_reason,
            class:
              "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          ) %>
          <%= error_tag(f, :rejection_reason) %>
        </div>
      <% end %>

      <div class="sm:col-span-2">
        <%= label(f, :account_number, class: "block text-sm font-medium text-gray-700") %>
        <%= text_input(f, :account_number,
          class:
            "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        ) %>
        <%= error_tag(f, :account_number) %>
      </div>
    </div>

    <div class="mt-6 flex items-center justify-end gap-x-6">
      <%= link("Cancel", to: @return_to, class: "text-sm font-semibold leading-6 text-gray-900") %>
      <%= submit("Save",
        class:
          "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
      ) %>
    </div>
  </.form>
</div>
