defmodule ServiceManagerWeb.Backend.WalletsLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:filter_params, assigns.filter_params || %{})}
  end

  @impl true
  def handle_event("filter", %{"filter" => filter_params}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> Map.merge(filter_params)
      |> filter_encode_url()

    {:noreply,
     socket
     |> push_navigate(to: "#{socket.assigns.url}#{endpoint}", replace: true)}
  end

  def handle_event("excel_export", %{"filter" => filter_params}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> Map.merge(filter_params)
      |> filter_encode_url()

    {:noreply,
     socket
     |> push_navigate(to: "#{socket.assigns.url}/ExcelExportFilter#{endpoint}", replace: true)}
  end

  defp filter_encode_url(params) do
    endpoint = "?" <> URI.encode_query(params)
    {params, endpoint}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= if @action == :filter do %>
          Filter Data
        <% else %>
          Export Data
        <% end %>
      </.header>

      <.simple_form for={%{}} as={:filter} phx-submit={@action} phx-target={@myself}>
        <.input type="text" label="ID" name="id" value={@filter_params["id"]} />
        <.input type="text" label="Email" name="email" value={@filter_params["email"]} />
        <.input type="text" label="Full Name" name="full_name" value={@filter_params["full_name"]} />
        <.input
          type="text"
          label="Phone Number"
          name="mobile_number"
          value={@filter_params["mobile_number"]}
        />
        <.input type="text" label="ID Number" name="id_number" value={@filter_params["id_number"]} />
        <.input
          type="text"
          label="Wallet Tier"
          name="wallet_tier"
          value={@filter_params["wallet_tier"]}
        />

        <:actions>
          <.button>
            <%= if @action == :filter do %>
              Apply Filter
            <% else %>
              Export Data
            <% end %>
          </.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end
end
