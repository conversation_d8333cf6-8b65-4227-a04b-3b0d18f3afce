defmodule ServiceManagerWeb.Backend.WalletsLive.UpgradeFormComponent do
  use ServiceManagerWeb, :live_component
  require Logger

  alias ServiceManager.WalletAccounts
  alias ServiceManager.WalletAccounts.{WalletUser, TierManagement}

  @cwd "#{File.cwd!()}"
  @upload_directory "priv/static/images/wallet_profiles"
  @public_path "#{@cwd}/images/wallet_profiles"
  @nrb_verification_url "https://fdh-esb.ngrok.dev/erification/nrb/v1/post.json"

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Complete KYC information to upgrade account limits</:subtitle>
      </.header>

      <div class="mt-4 bg-gray-50 p-4 rounded-lg mb-6">
        <h3 class="font-semibold text-gray-900">Current Account Status</h3>
        <div class="mt-2">
          <%= if current_tier = TierManagement.get_tier!(@data.wallet_tier_id) do %>
            <div class="text-gray-800">
              <div class="flex items-center gap-2 mb-2">
                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 font-medium">
                  <%= current_tier.name %>
                </span>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-sm text-gray-700 mb-2">Tier Limits:</h4>
                  <ul class="list-disc list-inside text-sm text-gray-600">
                    <li>Transaction Limit: MWK <%= current_tier.max_transaction_amount %></li>
                    <li>Credit Limit: MWK <%= current_tier.maximum_credit_limit %></li>
                    <li>Debt Limit: MWK <%= current_tier.maximum_debt_limit %></li>
                    <li>Daily Limit: MWK <%= current_tier.daily_transaction_limit %></li>
                    <li>Monthly Limit: MWK <%= current_tier.monthly_transaction_limit %></li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-medium text-sm text-gray-700 mb-2">Completed KYC Information:</h4>
                  <ul class="list-disc list-inside text-sm text-gray-600">
                    <%= if @data.first_name do %>
                      <li>First Name: <%= @data.first_name %></li>
                    <% end %>
                    <%= if @data.last_name do %>
                      <li>Last Name: <%= @data.last_name %></li>
                    <% end %>
                    <%= if @data.mobile_number do %>
                      <li>Mobile Number: <%= @data.mobile_number %></li>
                    <% end %>
                    <%= if @data.email do %>
                      <li>Email: <%= @data.email %></li>
                    <% end %>
                    <%= if @data.date_of_birth do %>
                      <li>
                        Date of Birth: <%= Calendar.strftime(@data.date_of_birth, "%d %B %Y") %>
                      </li>
                    <% end %>
                    <%= if @data.address do %>
                      <li>Address: <%= @data.address %></li>
                    <% end %>
                    <%= if @data.city do %>
                      <li>City: <%= @data.city %></li>
                    <% end %>
                    <%= if @data.occupation do %>
                      <li>Occupation: <%= @data.occupation %></li>
                    <% end %>
                    <%= if @data.employer_name do %>
                      <li>Employer: <%= @data.employer_name %></li>
                    <% end %>
                    <%= if @data.source_of_funds do %>
                      <li>Source of Funds: <%= @data.source_of_funds %></li>
                    <% end %>
                    <%= if @data.id_number do %>
                      <li>ID Number: <%= @data.id_number %></li>
                    <% end %>
                    <%= if @data.id_image do %>
                      <li>ID Image: <%= @data.id_image %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <.simple_form
        for={@form}
        id="upgrade-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
        multipart
        onsubmit={
          selected_tier = Enum.find(@available_tiers, &(&1.id == @form[:wallet_tier_id].value))

          if selected_tier && selected_tier.kyc_rules &&
               Map.get(selected_tier.kyc_rules, "nrb_verification"),
             do: "event.preventDefault()"
        }
      >
        <div class="mt-4">
          <.input
            field={@form[:wallet_tier_id]}
            type="select"
            label="Select Tier"
            options={Enum.map(@available_tiers, &{&1.name, &1.id})}
            prompt="Select a tier level"
            required
          />
        </div>

        <% selected_tier = Enum.find(@available_tiers, &(&1.id == @form[:wallet_tier_id].value)) %>
        <%= if selected_tier && selected_tier.required_kyc_fields do %>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mt-4">
            <% kyc_fields = [
              first_name: "First Name",
              last_name: "Last Name",
              mobile_number: "Mobile Number",
              email: "Email",
              date_of_birth: "Date of Birth",
              address: "Physical Address",
              city: "City",
              occupation: "Occupation",
              employer_name: "Employer Name",
              source_of_funds: "Source of Funds",
              id_number: "ID Number",
              id_image: "ID Image"
            ] %>

            <%= for {field, label} <- kyc_fields do %>
              <% field_str = Atom.to_string(field) %>
              <%= if field_str in selected_tier.required_kyc_fields do %>
                <%= cond do %>
                  <% field == :id_image -> %>
                    <div class="space-y-2">
                      <label class="text-sm font-medium text-gray-700">
                        ID Image <span class="text-red-500">*</span>
                      </label>
                      <div class="mt-1">
                        <.live_file_input
                          upload={@uploads.id_image}
                          class="block w-full text-sm text-gray-500
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-full file:border-0
                          file:text-sm file:font-semibold
                          file:bg-blue-50 file:text-blue-700
                          hover:file:bg-blue-100"
                        />
                        <%= for entry <- @uploads.id_image.entries do %>
                          <div class="mt-2">
                            <.live_img_preview entry={entry} class="w-32 h-32 object-cover rounded" />
                          </div>
                          <%= for err <- upload_errors(@uploads.id_image, entry) do %>
                            <div class="mt-1 text-red-500 text-sm"><%= err %></div>
                          <% end %>
                        <% end %>
                      </div>
                      <%= if @data.id_image do %>
                        <div class="text-sm text-gray-500">
                          Current ID Image: <%= @data.id_image %>
                        </div>
                      <% end %>
                    </div>
                  <% field == :source_of_funds -> %>
                    <.input
                      field={@form[field]}
                      type="select"
                      label={label}
                      options={[
                        "Salary",
                        "Business Income",
                        "Investment Returns",
                        "Pension",
                        "Other"
                      ]}
                      prompt="Select source of funds"
                      required={true}
                    />
                  <% true -> %>
                    <.input
                      field={@form[field]}
                      type={cond do
                        field == :date_of_birth -> "date"
                        field == :mobile_number -> "phone"
                        true -> "text"
                      end}
                      label={label}
                      required={true}
                    />
                <% end %>
              <% end %>
            <% end %>
          </div>
        <% end %>

        <%= if selected_tier = Enum.find(@available_tiers, &(&1.id == @form[:wallet_tier_id].value)) do %>
          <%= if selected_tier.kyc_rules && Map.get(selected_tier.kyc_rules, "nrb_verification") do %>
            <div class="mt-4 bg-yellow-50 p-4 rounded-lg">
              <h3 class="font-semibold text-yellow-900">NRB Validation Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mt-4">
                <.input field={@form[:id_number]} type="text" label="ID Number" required />
                <.input field={@form[:surname]} type="text" label="Surname" required />
                <.input field={@form[:first_name]} type="text" label="First Name" required />
                <.input field={@form[:other_names]} type="text" label="Other Names" required />
                <.input
                  field={@form[:gender]}
                  type="select"
                  label="Gender"
                  options={[{"Male", "M"}, {"Female", "F"}]}
                  required
                />
                <.input
                  field={@form[:date_of_birth_string]}
                  type="text"
                  label="Date of Birth (DD/MM/YYYY)"
                  required
                />
                <.input
                  field={@form[:date_of_issue_string]}
                  type="text"
                  label="Date of Issue (DD/MM/YYYY)"
                  required
                />
                <.input
                  field={@form[:date_of_expiry_string]}
                  type="text"
                  label="Date of Expiry (DD/MM/YYYY)"
                  required
                />
                <.input
                  field={@form[:place_of_birth_district_name]}
                  type="text"
                  label="Place of Birth (District)"
                  required
                />
              </div>

              <div class="mt-4">
                <%= if @nrb_validation_status == :validating do %>
                  <div class="space-y-4">
                    <p class="text-sm text-gray-500">
                      Validating your information with the National Registration Bureau...
                    </p>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                      <div class="bg-blue-600 h-2.5 rounded-full animate-pulse w-full"></div>
                    </div>
                  </div>
                <% else %>
                  <%= if @nrb_validation_status == :success do %>
                    <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                      <div class="flex">
                        <div class="flex-shrink-0">
                          <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-green-800">Validation Successful</h3>
                          <div class="mt-2 text-sm text-green-700">
                            <p>Your information has been successfully validated.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>

                  <%= if @nrb_validation_status == :error do %>
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                      <div class="flex">
                        <div class="flex-shrink-0">
                          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-red-800">Validation Failed</h3>
                          <div class="mt-2 text-sm text-red-700">
                            <%= if @nrb_validation_result["status"] do %>
                              <p>Status: <%= @nrb_validation_result["status"] %></p>
                              <p>Reason: <%= @nrb_validation_result["statusReason"] %></p>
                              <p>Code: <%= @nrb_validation_result["statusCode"] %></p>
                            <% else %>
                              <p class="whitespace-pre-wrap">
                                <%= @nrb_validation_result["statusReason"] %>
                              </p>
                            <% end %>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>

                  <button
                    type="button"
                    phx-click="validate_nrb"
                    phx-target={@myself}
                    phx-disable-with="Validating..."
                    class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
                  >
                    Validate NRB Information
                  </button>
                <% end %>
              </div>
            </div>
          <% end %>

          <div class="mt-4 bg-blue-50 p-4 rounded-lg">
            <h3 class="font-semibold text-blue-900">Selected Tier Benefits:</h3>
            <ul class="list-disc list-inside mt-2 text-blue-800">
              <li>Transaction Limit: MWK <%= selected_tier.max_transaction_amount %></li>
              <li>Credit Limit: MWK <%= selected_tier.maximum_credit_limit %></li>
              <li>Debt Limit: MWK <%= selected_tier.maximum_debt_limit %></li>
              <li>Daily Limit: MWK <%= selected_tier.daily_transaction_limit %></li>
              <li>Monthly Limit: MWK <%= selected_tier.monthly_transaction_limit %></li>
            </ul>
            <p class="mt-2 text-sm text-blue-700">
              * All fields are required for KYC compliance
            </p>
          </div>
        <% end %>

        <:actions>
          <% selected_tier = Enum.find(@available_tiers, &(&1.id == @form[:wallet_tier_id].value)) %>
          <% nrb_verification_enabled =
            selected_tier && selected_tier.kyc_rules &&
              Map.get(selected_tier.kyc_rules, "nrb_verification") %>

          <%= if !nrb_verification_enabled || @nrb_validation_status == :success do %>
            <.button phx-disable-with="Upgrading...">Upgrade Account</.button>
          <% end %>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: wallet_user} = assigns, socket) do
    # If user has no tier, assign the default tier
    wallet_user =
      if is_nil(wallet_user.wallet_tier_id) do
        case TierManagement.get_default_tier() do
          nil ->
            wallet_user

          default_tier ->
            {:ok, updated_user} =
              WalletAccounts.update_wallet_user(wallet_user, %{
                "wallet_tier_id" => default_tier.id
              })

            updated_user
        end
      else
        wallet_user
      end

    # Get current tier's required fields
    current_tier = TierManagement.get_tier!(wallet_user.wallet_tier_id)
    required_fields = if current_tier, do: current_tier.required_kyc_fields, else: []

    # Convert required fields to atoms
    required_field_atoms = Enum.map(required_fields, &String.to_existing_atom/1)

    # Build initial params with only required fields
    initial_params =
      required_field_atoms
      |> Enum.reduce(%{"wallet_tier_id" => wallet_user.wallet_tier_id}, fn field, acc ->
        value = Map.get(wallet_user, field)
        Map.put(acc, Atom.to_string(field), value || "")
      end)

    # Add surname field with last_name value for NRB validation
    initial_params = Map.put(initial_params, "surname", wallet_user.last_name || "")

    # Create changeset with required fields and validate
    changeset =
      wallet_user
      |> WalletUser.upgrade_changeset(initial_params)
      |> Ecto.Changeset.validate_required(required_field_atoms)
      |> Map.put(:action, :validate)

    # Include current tier in available tiers for updates
    available_tiers = TierManagement.list_tiers()

    socket =
      socket
      |> assign(assigns)
      |> assign(:available_tiers, available_tiers)
      |> assign(:data, wallet_user)
      |> assign_form(changeset)
      |> assign(:show_nrb_modal, false)
      |> assign(:nrb_validation_status, :idle)
      |> assign(:nrb_validation_result, %{})
      |> assign(:upgrade_params, %{})
      |> allow_upload(:id_image,
        accept: ~w(.jpg .jpeg .png),
        max_entries: 1,
        # 10MB
        max_file_size: 10_000_000
      )

    {:ok, socket}
  end

  @impl true
  def handle_event("validate", %{"wallet_user" => params}, socket) do
    current_data = socket.assigns.data

    # Clean and convert wallet_tier_id
    params =
      params
      |> Enum.reject(fn {key, _} -> String.starts_with?(key, "_unused_") end)
      |> Map.new()
      |> Map.update("wallet_tier_id", current_data.wallet_tier_id, fn
        "" -> current_data.wallet_tier_id
        val when is_binary(val) -> String.to_integer(val)
        val -> val
      end)

    # Get selected tier and its required fields
    selected_tier =
      case params["wallet_tier_id"] do
        nil ->
          nil

        tier_id when is_binary(tier_id) ->
          Enum.find(socket.assigns.available_tiers, &(&1.id == String.to_integer(tier_id)))

        tier_id ->
          Enum.find(socket.assigns.available_tiers, &(&1.id == tier_id))
      end

    # Add the tier name to the type field for validation
    params =
      if selected_tier do
        Map.put(params, "type", selected_tier.name)
      else
        params
      end

    required_fields = if selected_tier, do: selected_tier.required_kyc_fields, else: []
    required_field_atoms = Enum.map(required_fields, &String.to_existing_atom/1)

    # Add NRB validation fields if enabled
    nrb_fields = [
      :id_number,
      :surname,
      :first_name,
      :other_names,
      :gender,
      :date_of_birth_string,
      :date_of_issue_string,
      :date_of_expiry_string,
      :place_of_birth_district_name
    ]

    all_required_fields =
      if selected_tier && selected_tier.kyc_rules &&
           Map.get(selected_tier.kyc_rules, "nrb_verification") do
        required_field_atoms ++ nrb_fields
      else
        required_field_atoms
      end

    # Filter and convert params based on required fields
    params =
      all_required_fields
      |> Enum.reduce(%{"wallet_tier_id" => params["wallet_tier_id"], "type" => params["type"]}, fn field, acc ->
        field_str = Atom.to_string(field)
        value = params[field_str]

        cond do
          # Handle date_of_birth conversion
          field == :date_of_birth && is_binary(value) && value != "" ->
            case Date.from_iso8601(value) do
              {:ok, date} -> Map.put(acc, field_str, date)
              _ -> Map.put(acc, field_str, Map.get(current_data, field))
            end

          # Ensure surname is populated with last_name
          field == :surname ->
            last_name = params["last_name"] || current_data.last_name
            Map.put(acc, field_str, last_name)

          # Keep non-empty values
          value && value != "" ->
            Map.put(acc, field_str, value)

          # Use existing data for empty values
          true ->
            Map.put(acc, field_str, Map.get(current_data, field))
        end
      end)

    changeset =
      current_data
      |> WalletUser.upgrade_changeset(params)
      |> Ecto.Changeset.validate_required(all_required_fields)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"wallet_user" => params}, socket) do
    current_data = socket.assigns.data

    # Clean and convert wallet_tier_id
    params =
      params
      |> Map.new()
      |> Map.update("wallet_tier_id", current_data.wallet_tier_id, fn
        "" -> current_data.wallet_tier_id
        val when is_binary(val) -> String.to_integer(val)
        val -> val
      end)

    # Get selected tier and its required fields
    selected_tier =
      case params["wallet_tier_id"] do
        nil ->
          nil

        tier_id when is_binary(tier_id) ->
          Enum.find(socket.assigns.available_tiers, &(&1.id == String.to_integer(tier_id)))

        tier_id ->
          Enum.find(socket.assigns.available_tiers, &(&1.id == tier_id))
      end

    # Add the tier name to the type field
    params =
      if selected_tier do
        Logger.info("Setting type field to: #{selected_tier.name}")
        Map.put(params, "type", selected_tier.name)
      else
        params
      end

    required_fields = if selected_tier, do: selected_tier.required_kyc_fields, else: []
    required_field_atoms = Enum.map(required_fields, &String.to_existing_atom/1)

    # Add NRB validation fields if enabled
    nrb_fields = [
      :id_number,
      :surname,
      :first_name,
      :other_names,
      :gender,
      :date_of_birth_string,
      :date_of_issue_string,
      :date_of_expiry_string,
      :place_of_birth_district_name
    ]

    nrb_verification_enabled =
      selected_tier && selected_tier.kyc_rules &&
        Map.get(selected_tier.kyc_rules, "nrb_verification")

    all_required_fields =
      if nrb_verification_enabled do
        required_field_atoms ++ nrb_fields
      else
        required_field_atoms
      end

    # Build params with only required fields
    # Start with essential fields that should always be included
    base_params = %{
      "wallet_tier_id" => params["wallet_tier_id"],
      "type" => params["type"]
    }

    params =
      all_required_fields
      |> Enum.reduce(base_params, fn field, acc ->
        field_str = Atom.to_string(field)
        value = params[field_str]

        cond do
          # Skip type field as it's already in base_params
          field == :type ->
            acc

          # Handle date_of_birth conversion
          field == :date_of_birth && is_binary(value) && value != "" ->
            case Date.from_iso8601(value) do
              {:ok, date} -> Map.put(acc, field_str, date)
              _ -> Map.put(acc, field_str, Map.get(current_data, field))
            end

          # Ensure surname is populated with last_name
          field == :surname ->
            last_name = params["last_name"] || current_data.last_name
            Map.put(acc, field_str, last_name)

          # Handle file upload for id_image
          field == :id_image ->
            case socket.assigns.uploads.id_image.entries do
              [entry | _] ->
                case handle_image_upload(socket, entry, current_data.id) do
                  {:ok, public_path} ->
                    Logger.info("Adding image path to params: #{public_path}")
                    Map.put(acc, field_str, public_path)

                  {:error, reason} ->
                    Logger.error("Image upload failed: #{inspect(reason)}")
                    Map.put(acc, field_str, Map.get(current_data, field))
                end

              _ ->
                Map.put(acc, field_str, Map.get(current_data, field))
            end

          # Keep non-empty values
          value && value != "" ->
            Map.put(acc, field_str, value)

          # Use existing data for empty values
          true ->
            Map.put(acc, field_str, Map.get(current_data, field))
        end
      end)

    # Add KYC completion fields if all required fields are present
    params =
      if Enum.all?(all_required_fields, fn field ->
           Map.has_key?(params, Atom.to_string(field))
         end) do
        Map.merge(params, %{
          "kyc_complete" => true,
          "kyc_verified_at" => DateTime.utc_now() |> DateTime.truncate(:second)
        })
      else
        params
      end

    Logger.info("Final params before upgrade: #{inspect(params)}")
    Logger.info("All required fields: #{inspect(all_required_fields)}")

    # Check if NRB validation is required
    if nrb_verification_enabled do
      # Show NRB validation modal and start validation process
      socket =
        socket
        |> assign(:upgrade_params, params)
        |> assign(:show_nrb_modal, true)
        |> assign(:nrb_validation_status, :validating)

      # Start NRB validation process immediately instead of sending a message
      {:noreply, perform_nrb_validation(socket, params)}
    else
      # Proceed with normal upgrade
      complete_upgrade(socket, params)
    end
  end

  def handle_event("close_nrb_modal", _, socket) do
    {:noreply, assign(socket, :show_nrb_modal, false)}
  end

  def handle_event("continue_upgrade", _, socket) do
    # Complete the upgrade with the stored params
    complete_upgrade(socket, socket.assigns.upgrade_params)
  end

  def handle_event("validate_nrb", _, socket) do
    # Get form data
    form_data = socket.assigns.form.source.changes
    current_data = socket.assigns.data

    # Check if all NRB fields are filled
    nrb_fields = [
      :id_number,
      :surname,
      :first_name,
      :other_names,
      :gender,
      :date_of_birth_string,
      :date_of_issue_string,
      :date_of_expiry_string,
      :place_of_birth_district_name
    ]

    # Build params for NRB validation
    params =
      nrb_fields
      |> Enum.reduce(%{}, fn field, acc ->
        field_str = Atom.to_string(field)
        value = Map.get(form_data, field) || Map.get(current_data, field)

        if value && value != "" do
          Map.put(acc, field_str, value)
        else
          acc
        end
      end)

    # Check if all required fields are present
    if Enum.all?(nrb_fields, fn field -> Map.has_key?(params, Atom.to_string(field)) end) do
      # Start validation
      socket =
        socket
        |> assign(:nrb_validation_status, :validating)

      # Perform NRB validation
      {:noreply, perform_nrb_validation(socket, params)}
    else
      # Show validation errors
      missing_fields =
        Enum.filter(nrb_fields, fn field ->
          !Map.has_key?(params, Atom.to_string(field))
        end)

      {:noreply,
       socket
       |> put_flash(
         :error,
         "Please fill in all NRB validation fields: #{Enum.map_join(missing_fields, ", ", &Atom.to_string/1)}"
       )}
    end
  end

  # Perform NRB validation directly instead of using handle_info
  defp perform_nrb_validation(socket, params) do
    # Prepare NRB validation payload
    nrb_payload = %{
      "IdNumber" => params["id_number"],
      "Surname" => params["surname"],
      "FirstName" => params["first_name"],
      "OtherNames" => params["other_names"],
      "Gender" => params["gender"],
      "DateOfBirthString" => params["date_of_birth_string"],
      "DateOfIssueString" => params["date_of_issue_string"],
      "DateOfExpiryString" => params["date_of_expiry_string"],
      "PlaceOfBirthDistrictName" => params["place_of_birth_district_name"]
    }

    Logger.info("Performing NRB validation with payload: #{inspect(nrb_payload)}")

    # Make HTTP request to NRB verification API
    case make_nrb_verification_request(params) do
      {:ok, result} ->
        # Update params with NRB validation results
        updated_params =
          Map.merge(params, %{
            "nrb_validation" => true,
            "nrb_response_code" => result["responseCode"],
            "nrb_response_message" => result["responseMessage"],
            "nrb_status" => result["status"],
            "nrb_status_reason" => result["statusReason"],
            "nrb_status_code" => result["statusCode"]
          })

        # Determine validation status
        validation_status = if result["status"] == "VALID", do: :success, else: :error

        socket
        |> assign(:nrb_validation_status, validation_status)
        |> assign(:nrb_validation_result, result)
        |> assign(:upgrade_params, updated_params)

      {:error, reason} ->
        Logger.error("NRB validation failed: #{inspect(reason)}")

        socket
        |> assign(:nrb_validation_status, :error)
        |> assign(:nrb_validation_result, %{
          "status" => "ERROR",
          "statusReason" => "Failed to connect to NRB service: #{inspect(reason)}",
          "statusCode" => "999"
        })
    end
  end

  defp complete_upgrade(socket, params) do
    Logger.info("Complete upgrade called with params: #{inspect(params)}")
    case WalletAccounts.update_wallet_user(socket.assigns.data, params) do
      {:ok, _wallet_user} ->
        notify_parent()

        {:noreply,
         socket
         |> assign(:show_nrb_modal, false)
         |> put_flash(:info, "Account upgraded successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Logger.error("Upgrade failed with changeset errors: #{inspect(changeset.errors)}")
        Logger.error("Changeset valid?: #{changeset.valid?}")
        {:noreply,
         socket
         |> assign(:show_nrb_modal, false)
         |> assign_form(changeset)}
    end
  end

  defp make_nrb_verification_request(params) do
    # Format the payload with the correct key names as expected by the API
    payload = %{
      "IdNumber" => params["id_number"],
      "Surname" => params["surname"],
      "FirstName" => params["first_name"],
      "OtherNames" => params["other_names"],
      "Gender" => params["gender"],
      "DateOfBirthString" => params["date_of_birth_string"],
      "DateOfIssueString" => params["date_of_issue_string"],
      "DateOfExpiryString" => params["date_of_expiry_string"],
      "PlaceOfBirthDistrictName" => params["place_of_birth_district_name"]
    }

    headers = [
      {"Content-Type", "application/json"},
      {"Authorization", "Basic YWRtaW46YWRtaW4="}
    ]

    Logger.info("Making HTTP request to NRB verification API with payload: #{inspect(payload)}")

    # Make the actual HTTP request with a 30-second timeout
    case HTTPoison.post(@nrb_verification_url, Jason.encode!(payload), headers,
           timeout: 30_000,
           recv_timeout: 30_000
         ) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body}}
      when status_code in 200..299 ->
        Logger.info("NRB verification API response: #{body}")

        # Parse the JSON response
        case Jason.decode(body) do
          {:ok, decoded} ->
            {:ok, decoded}

          {:error, reason} ->
            Logger.error("Failed to parse NRB verification API response: #{inspect(reason)}")
            {:error, "Failed to parse response: #{inspect(reason)}"}
        end

      {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
        Logger.error(
          "NRB verification API returned error status code: #{status_code}, body: #{body}"
        )

        # Try to parse the error response to extract validation errors
        error_message =
          try do
            case Jason.decode(body) do
              {:ok, %{"errors" => errors}} when is_map(errors) ->
                # Format validation errors
                errors_list =
                  errors
                  |> Enum.map(fn {field, messages} ->
                    "#{field}: #{Enum.join(messages, ", ")}"
                  end)
                  |> Enum.join("\n")

                "Validation errors:\n#{errors_list}"

              {:ok, decoded} ->
                # If there's a message in the response, use it
                decoded["title"] || decoded["message"] ||
                  "API returned error status code: #{status_code}"

              _ ->
                "API returned error status code: #{status_code}"
            end
          rescue
            _ -> "API returned error status code: #{status_code}"
          end

        {:error, error_message}

      {:error, %HTTPoison.Error{reason: reason}} ->
        Logger.error("HTTP request to NRB verification API failed: #{inspect(reason)}")
        {:error, "HTTP request failed: #{inspect(reason)}"}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp notify_parent, do: send(self(), {:upgrade_complete})

  # Image handling helpers
  defp ensure_upload_directory do
    Logger.info("Ensuring upload directory exists: #{@upload_directory}")
    File.mkdir_p!(@upload_directory)
  end

  defp generate_image_filename(entry, user_id) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    extension = Path.extname(entry.client_name)
    "#{timestamp}_#{user_id}#{extension}"
  end

  defp handle_image_upload(socket, entry, user_id) do
    Logger.info("Starting image upload for user #{user_id}")
    ensure_upload_directory()

    filename = generate_image_filename(entry, user_id)
    path = Path.join(@upload_directory, filename)
    public_path = Path.join(@public_path, filename)

    Logger.info("""
    Image paths:
    - Upload directory: #{@upload_directory}
    - Full path: #{path}
    - Public path: #{public_path}
    - CWD: #{@cwd}
    """)

    # consume_uploaded_entry requires {:ok, value} return
    result =
      consume_uploaded_entry(socket, entry, fn %{path: temp_path} ->
        Logger.info("Copying file from #{temp_path} to #{path}")

        case File.cp(temp_path, path) do
          :ok ->
            Logger.info("File copied successfully to #{path}")
            # Return proper {:ok, value} tuple
            {:ok, public_path}

          {:error, reason} ->
            Logger.error("Failed to copy file: #{inspect(reason)}")
            # Still return {:ok, value} but with error info
            {:ok, {:error, reason}}
        end
      end)

    case result do
      # Matches nested {:ok, {:ok, path}}
      path ->
        Logger.info("Successfully processed image at #{path}")
        {:ok, path}

      {:ok, {:error, reason}} ->
        Logger.error("Failed during file copy: #{inspect(reason)}")
        {:error, reason}

      {:error, reason} ->
        Logger.error("Failed to process upload: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
