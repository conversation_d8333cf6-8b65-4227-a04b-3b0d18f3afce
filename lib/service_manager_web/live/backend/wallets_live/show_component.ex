defmodule ServiceManagerWeb.Backend.WalletsLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Wallet User Details</h2>
          <p class="text-sm text-gray-500">ID: <%= @data.id %></p>
        </div>
        <div class="w-48 h-48 bg-gray-100 rounded-lg overflow-hidden">
          <%= if @data.id_image do %>
            <img
              src={"data:image/png;base64,#{@data.id_image}"}
              alt="ID Image"
              class="w-full h-full object-cover"
            />
          <% else %>
            <div class="w-full h-full flex items-center justify-center text-gray-400">
              No ID Image
            </div>
          <% end %>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- NRB Validation Information -->
        <%= if @data.nrb_status do %>
          <div class="space-y-6 col-span-2">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">
              NRB Validation Information
            </h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-500">NRB Status</label>
                <p class="mt-1">
                  <%= cond do %>
                    <% @data.nrb_status == "VALID" -> %>
                      <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        <%= @data.nrb_status %>
                      </span>
                    <% @data.nrb_status == "INVALID" -> %>
                      <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                        <%= @data.nrb_status %>
                      </span>
                    <% true -> %>
                      <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                        <%= @data.nrb_status %>
                      </span>
                  <% end %>
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">NRB Status Code</label>
                <p class="mt-1"><%= @data.nrb_status_code || "--" %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">NRB Status Reason</label>
                <p class="mt-1"><%= @data.nrb_status_reason || "--" %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">NRB Response Code</label>
                <p class="mt-1"><%= @data.nrb_response_code || "--" %></p>
              </div>
              <div class="col-span-2">
                <label class="text-sm font-medium text-gray-500">NRB Response Message</label>
                <p class="mt-1"><%= @data.nrb_response_message || "--" %></p>
              </div>
            </div>
          </div>
        <% end %>
        <!-- Personal Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Personal Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Full Name</label>
              <p class="mt-1"><%= "#{@data.first_name} #{@data.last_name}" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Email</label>
              <p class="mt-1"><%= @data.email || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Phone Number</label>
              <p class="mt-1"><%= @data.mobile_number || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">ID Number</label>
              <p class="mt-1"><%= @data.id_number || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Account Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Account Number</label>
              <p class="mt-1"><%= @data.account_number || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Account Type</label>
              <p class="mt-1"><%= @data.account_type || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Balance</label>
              <p class="mt-1">
                <%= if @data.balance, do: "#{@data.balance} #{@data.currency || "MWK"}", else: "--" %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Transaction</label>
              <p class="mt-1">
                <%= if @data.last_transaction_date do
                  @data.last_transaction_date
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "")
                else
                  "--"
                end %>
              </p>
            </div>
          </div>
        </div>
        <!-- System Status -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">System Status</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Account Status</label>
              <div class="mt-2 space-y-2">
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @data.frozen, do: "bg-red-500", else: "bg-green-500"}"} />
                  <span class="text-sm">
                    <%= if @data.frozen, do: "Frozen", else: "Not Frozen" %>
                  </span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @data.locked, do: "bg-red-500", else: "bg-green-500"}"} />
                  <span class="text-sm">
                    <%= if @data.locked, do: "Locked", else: "Not Locked" %>
                  </span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class={"w-2 h-2 rounded-full #{if @data.blocked, do: "bg-red-500", else: "bg-green-500"}"} />
                  <span class="text-sm">
                    <%= if @data.blocked, do: "Blocked", else: "Not Blocked" %>
                  </span>
                </div>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">First Time Login</label>
              <p class="mt-1"><%= if @data.first_time_login, do: "Yes", else: "No" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Account Confirmed</label>
              <p class="mt-1"><%= if @data.confirmed_at, do: "Yes", else: "No" %></p>
            </div>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @data.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @data.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Confirmed At</label>
              <p class="mt-1">
                <%= if @data.confirmed_at do
                  @data.confirmed_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "")
                else
                  "--"
                end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
