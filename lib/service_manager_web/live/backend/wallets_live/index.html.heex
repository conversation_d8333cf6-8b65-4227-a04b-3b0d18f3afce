<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="wallet-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />

<.header>
  Wallets Overview
</.header>

<div id="wallets-container" phx-hook="WalletData">
  <.table
    id="wallets"
    filter_params={@filter_params}
    pagination={@pagination}
    selected_column={@selected_column}
    filter_url={~p"/mobileBanking/WalletsOverview/filter"}
    export_url={~p"/mobileBanking/WalletsOverview/ExcelExportFilter"}
    show_filter={true}
    show_export={true}
    rows={@streams.data}
    row_click={fn {_id, data} -> JS.patch(~p"/mobileBanking/WalletsOverview/#{data}") end}
    class="max-w-full"
  >
    <!-- Primary Information -->
    <:col :let={{_id, data}} filter_item="id" label="ID">
      <div class="text-[12px]"><%= data.id || "--" %></div>
    </:col>

    <:col :let={{_id, data}} filter_item="full_name" label="Full Name">
      <div class="text-[12px]">
        <%= cond do
          is_nil(data.first_name) && is_nil(data.last_name) ->
            "--"

          String.trim(data.first_name || "") == "" && String.trim(data.last_name || "") == "" ->
            "--"

          true ->
            "#{data.first_name} #{data.last_name}"
        end %>
      </div>
    </:col>
    <:col :let={{_id, data}} filter_item="mobile_number" label="Phone Number">
      <div class="text-[12px]">
        <%= if is_nil(data.mobile_number) || String.trim(data.mobile_number) == "",
          do: "--",
          else: data.mobile_number %>
      </div>
    </:col>
    <:col :let={{_id, data}} filter_item="id_number" label="ID Number">
      <div class="text-[12px]">
        <%= if is_nil(data.id_number) || String.trim(data.id_number) == "",
          do: "--",
          else: data.id_number %>
      </div>
    </:col>
    <!-- Account Information -->
    <:col :let={{_id, data}} filter_item="wallet_tier" label="Account Type">
      <div class="text-[12px]">
        <%= if data.wallet_tier do %>
          <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
            <%= data.wallet_tier.name %>
          </span>
        <% else %>
          <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
            Basic
          </span>
        <% end %>
      </div>
    </:col>

    <:col :let={{_id, data}} filter_item="balance" label="Balance">
      <div class="text-[12px] text-right font-medium">
        <%= cond do
          is_nil(data.balance) ->
            "--"

          true ->
            "#{data.balance} #{if is_nil(data.currency) || String.trim(data.currency) == "", do: "MWK", else: data.currency}"
        end %>
      </div>
    </:col>
    <!-- System Status -->
    <:col :let={{_id, data}} filter_item="frozen" label="Account Status">
      <.status_pill status={!data.frozen} text={handle_status(data.frozen, "frozen")} />
    </:col>
    <:col :let={{_id, data}} filter_item="locked" label="Lock Status">
      <.status_pill status={!data.locked} text={handle_status(data.locked, "locked")} />
    </:col>
    <:col :let={{_id, data}} filter_item="blocked" label="Block Status">
      <.status_pill status={!data.blocked} text={handle_status(data.blocked, "blocked")} />
    </:col>

    <:col :let={{_id, data}} filter_item="nrb_status" label="NRB Status">
      <div class="text-[12px]">
        <%= if is_nil(data.nrb_status) do %>
          --
        <% else %>
          <%= cond do %>
            <% data.nrb_status == "VALID" -> %>
              <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                <%= data.nrb_status %>
              </span>
            <% data.nrb_status == "INVALID" -> %>
              <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                <%= data.nrb_status %>
              </span>
            <% true -> %>
              <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                <%= data.nrb_status %>
              </span>
          <% end %>
        <% end %>
      </div>
    </:col>

    <:action :let={{id, data}}>
      <%= if has_any_permission?(@current_user, [:view, :update, :activate], :wallets) do %>
        <.dropdown id={"dropdown-#{id}"} label="Options">
          <%= if can_update?(@current_user, :wallets) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              patch={~p"/mobileBanking/WalletsOverview/#{data}/edit"}
            >
              Edit
            </.link>
          <% end %>
          <%= if can_view?(@current_user, :wallets) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              patch={~p"/mobileBanking/WalletsOverview/#{data}"}
            >
              View Details
            </.link>
          <% end %>
          <%= if can_update?(@current_user, :wallets) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("reset_password", value: %{id: data.id, email: data.email})}
              data-confirm="Are you sure you want to reset this user's password?"
            >
              Reset Password
            </.link>
          <% end %>
          <!-- Account Status Management -->
          <%= if can_activate?(@current_user, :wallets) do %>
            <%= if data.frozen do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "unfreeze"})}
              >
                Unfreeze Account
              </.link>
            <% else %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "freeze"})}
              >
                Freeze Account
              </.link>
            <% end %>

            <%= if data.locked do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "unlock"})}
              >
                Unlock Account
              </.link>
            <% else %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "lock"})}
              >
                Lock Account
              </.link>
            <% end %>

            <%= if data.blocked do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "unblock"})}
              >
                Unblock Account
              </.link>
            <% else %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("open_reason_modal", value: %{id: data.id, action: "block"})}
              >
                Block Account
              </.link>
            <% end %>
          <% end %>

          <%= if can_approve?(@current_user, :wallets) && !data.blocked && !data.frozen && !data.locked do %>
            <.link
              class="block px-4 py-2 text-sm text-green-700 hover:bg-gray-100 font-medium"
              patch={~p"/mobileBanking/WalletsOverview/#{data}/upgrade"}
            >
              Upgrade Account
            </.link>
          <% end %>
        </.dropdown>
      <% end %>
    </:action>
  </.table>

  <.modal
    :if={@live_action in [:new, :edit]}
    id="data-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.WalletsLive.FormComponent}
      id={@data.id || :new}
      title={@page_title}
      action={@live_action}
      data={@data}
      current_user={@current_user}
      patch={~p"/mobileBanking/WalletsOverview"}
    />
  </.modal>
</div>
<!-- Reason Modal -->
<.modal :if={@show_reason_modal} id="reason-modal" show on_cancel={JS.push("close_reason_modal")}>
  <.header>
    <%= case @action_type do %>
      <% "freeze" -> %>
        Freeze Account
      <% "unfreeze" -> %>
        Unfreeze Account
      <% "lock" -> %>
        Lock Account
      <% "unlock" -> %>
        Unlock Account
      <% "block" -> %>
        Block Account
      <% "unblock" -> %>
        Unblock Account
    <% end %>
  </.header>

  <form phx-submit="save_with_reason">
    <div class="mt-6 space-y-4">
      <.input
        type="textarea"
        name="reason"
        label="Reason"
        value={@reason}
        phx-change="update_reason"
        required
      />
    </div>

    <div class="mt-6 flex justify-between">
      <.button type="button" phx-click="close_reason_modal">Cancel</.button>
      <.button
        type="submit"
        class="ml-3"
        disabled={@reason == ""}
        data-confirm={
          case @action_type do
            "freeze" -> "Are you sure you want to freeze this account?"
            "unfreeze" -> "Are you sure you want to unfreeze this account?"
            "lock" -> "Are you sure you want to lock this account?"
            "unlock" -> "Are you sure you want to unlock this account?"
            "block" -> "Are you sure you want to block this account?"
            "unblock" -> "Are you sure you want to unblock this account?"
            _ -> "Are you sure you want to proceed?"
          end
        }
      >
        <%= case @action_type do
          "freeze" -> "Freeze"
          "unfreeze" -> "Unfreeze"
          "lock" -> "Lock"
          "unlock" -> "Unlock"
          "block" -> "Block"
          "unblock" -> "Unblock"
          _ -> "Save"
        end %>
      </.button>
    </div>
  </form>
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/WalletsOverview"}
  />
</.modal>

<.modal
  :if={@live_action == :upgrade}
  id="upgrade-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletsLive.UpgradeFormComponent}
    id={@data.id}
    title="Upgrade Wallet Account"
    action={@live_action}
    data={@data}
    patch={~p"/mobileBanking/WalletsOverview"}
  />
</.modal>

<.modal
  :if={@live_action == :show}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview")}
  max-width="max-w-[95%]"
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
    patch={~p"/mobileBanking/WalletsOverview"}
  />
</.modal>
