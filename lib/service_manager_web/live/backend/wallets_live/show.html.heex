<.header>
  Wallet <%= @data.id %>
  <:subtitle>This is a Wallet record from your database.</:subtitle>
  <:actions></:actions>
</.header>

<.list>
  <:item title="Email"><%= @data.email %></:item>
  <:item title="First Name"><%= @data.first_name %></:item>
  <:item title="Last Name"><%= @data.last_name %></:item>
  <:item title="ID Number"><%= @data.id_number %></:item>
  <:item title="Image ID"><%= @data.id_image %></:item>
  <:item title="Mobile Number"><%= @data.mobile_number %></:item>
  <:item title="Currency"><%= @data.currency %></:item>
  <:item title="Balance"><%= @data.balance %></:item>
  <:item title="Status"><%= @data.status %></:item>
</.list>

<.back navigate={~p"/mobileBanking/WalletsOverview"}>Go Back</.back>

<.modal
  :if={@live_action == :edit}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview/#{@data}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletsLive.FormComponent}
    id={@data.id}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/WalletsOverview/#{@data}"}
  />
</.modal>
