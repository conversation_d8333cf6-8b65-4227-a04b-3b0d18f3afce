defmodule ServiceManagerWeb.Backend.WalletsLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.BeneficieriesContext, as: MainContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage Beneficiary records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="main-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <.input field={@form[:name]} type="text" label="Name" />
          <.input field={@form[:account_number]} type="text" label="Account Number" />
          <.input field={@form[:bank_code]} type="text" label="Bank Code" />
          <.input field={@form[:currency]} type="text" label="Currency" />
          <.input field={@form[:description]} type="text" label="Description" />
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Beneficiary</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: data} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(MainContext.change_data(data))
     end)}
  end

  @impl true
  def handle_event("validate", %{"schema_beneficiary" => params}, socket) do
    changeset = MainContext.change_data(socket.assigns.data, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"schema_beneficiary" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, :edit, params) do
    MainContext.update_data(socket.assigns.data, params, socket.assigns.current_user)
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "updated successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_data(socket, :new, params) do
    MainContext.insert_data(params, socket.assigns.current_user)
    |> IO.inspect(label: "=================")
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "created successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
