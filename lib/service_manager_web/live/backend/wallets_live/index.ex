defmodule ServiceManagerWeb.Backend.WalletsLive.Index do
  use ServiceManagerWeb, :live_view
  import ServiceManagerWeb.Utilities.Sorting
  import ServiceManagerWeb.Utilities.Utils, only: [generate_pagination_details: 1]

  alias ServiceManager.WalletAccounts.WalletUser, as: MainSchema
  alias ServiceManager.WalletAccounts, as: MainContext
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.WalletAccounts.WalletTier
  alias ServiceManager.Repo

  @url "/mobileBanking/WalletsOverview"

  import Ecto.Query
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> stream(:data, [])
      |> assign(:stats, get_wallet_stats())
      |> assign(:filter_params, %{})
      |> assign(:selected_column, nil)
      |> assign(:live_action, :index)
      |> assign(:url, @url)
      |> assign(:show_reason_modal, false)
      |> assign(:action_type, nil)
      |> assign(:selected_user_id, nil)
      |> assign(:reason, "")

    {:ok, socket}
  end

  def handle_event("open_reason_modal", %{"id" => id, "action" => action}, socket) do
    {:noreply,
     socket
     |> assign(:show_reason_modal, true)
     |> assign(:action_type, action)
     |> assign(:selected_user_id, id)
     |> assign(:reason, "")}
  end

  def handle_event("close_reason_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_reason_modal, false)
     |> assign(:action_type, nil)
     |> assign(:selected_user_id, nil)
     |> assign(:reason, "")}
  end

  def handle_event("update_reason", %{"reason" => reason}, socket) do
    {:noreply, assign(socket, :reason, reason)}
  end

  def handle_event("save_with_reason", _params, socket) do
    reason = socket.assigns.reason
    action = socket.assigns.action_type
    id = socket.assigns.selected_user_id

    case action do
      "freeze" -> handle_freeze(id, reason, socket)
      "unfreeze" -> handle_unfreeze(id, reason, socket)
      "lock" -> handle_lock(id, reason, socket)
      "unlock" -> handle_unlock(id, reason, socket)
      "block" -> handle_block(id, reason, socket)
      "unblock" -> handle_unblock(id, reason, socket)
      _ -> {:noreply, socket}
    end
  end

  defp get_wallet_stats do
    total_users = Repo.aggregate(MainSchema, :count, :id)
    frozen_users = Repo.aggregate(from(u in MainSchema, where: u.frozen == true), :count, :id)
    locked_users = Repo.aggregate(from(u in MainSchema, where: u.locked == true), :count, :id)
    blocked_users = Repo.aggregate(from(u in MainSchema, where: u.blocked == true), :count, :id)

    # Get count of users with any tier assigned (upgraded users)
    upgraded_users =
      Repo.aggregate(
        from(u in MainSchema,
          where: not is_nil(u.wallet_tier_id)
        ),
        :count,
        :id
      )

    [
      %{
        title: "Total Users",
        value: total_users
      },
      %{
        title: "Upgraded Accounts",
        value: upgraded_users,
        comparison: "users"
      },
      %{
        title: "Frozen Accounts",
        value: frozen_users,
        comparison: "users"
      },
      %{
        title: "Locked Accounts",
        value: locked_users,
        comparison: "users"
      },
      %{
        title: "Blocked Accounts",
        value: blocked_users,
        comparison: "users"
      }
    ]
  end

  @impl true
  def handle_params(%{"page" => _page} = params, _url, socket) do
    data =
      MainContext.retrieve(params)
      |> IO.inspect(label: "data ======================")

    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:data)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    socket =
      apply_action(socket, socket.assigns.live_action, params)
      |> assign(:filter_params, params)
      |> assign(pagination: pagination)
      |> stream(:data, data)

    {:noreply, socket}
  end

  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = MainContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:data)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    socket =
      apply_action(socket, socket.assigns.live_action, params)
      |> assign(:selected_column, sort_field)
      |> assign(:filter_params, params)
      |> assign(pagination: pagination)
      |> stream(:data, data, reset: true)

    {:noreply, socket}
  end

  def handle_params(params, _url, socket) do
    data = MainContext.retrieve()
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:data)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    socket =
      apply_action(socket, socket.assigns.live_action, params)
      |> assign(:filter_params, params)
      |> assign(pagination: pagination)
      |> stream(:data, data, reset: true)

    {:noreply, socket}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Wallet")
    |> assign(:data, MainContext.get_wallet_user!(id, false))
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Wallet Details")
    |> assign(:data, MainContext.get_wallet_user!(id, true))
  end

  defp apply_action(socket, :upgrade, %{"id" => id}) do
    socket
    |> assign(:page_title, "Upgrade Wallet Account")
    |> assign(:data, MainContext.get_wallet_user!(id, false) |> IO.inspect())
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Wallet")
    |> assign(:data, %MainSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Wallets")
    |> assign(:data, [])
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Wallets")
    |> assign(:data, [])
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page_title, "Export Wallets")
    |> assign(:data, [])
  end

  @impl true
  def handle_info({:upgrade_complete}, socket) do
    socket =
      socket
      |> assign(:stats, get_wallet_stats())

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_frozen", %{"id" => id}, socket) do
    case MainContext.get_wallet_user!(id, false) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "frozen" => !user.frozen
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(
               :info,
               "Account #{if user.frozen, do: "unfrozen", else: "frozen"} successfully"
             )
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to update account status")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("toggle_locked", %{"id" => id}, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "locked" => !user.locked
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(
               :info,
               "Account #{if user.locked, do: "unlocked", else: "locked"} successfully"
             )
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to update account status")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("toggle_blocked", %{"id" => id}, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "blocked" => !user.blocked
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(
               :info,
               "Account #{if user.blocked, do: "unblocked", else: "blocked"} successfully"
             )
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to update account status")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("reset_password", %{"id" => id, "email" => email}, socket) do
    new_password = generate_secure_password()

    user = MainContext.get_wallet_user!(id, false)

    changeset =
      MainSchema.update_password_changeset(user, %{
        "password" => new_password,
        "first_time_login" => true
      })

    case Repo.update(changeset) do
      {:ok, updated_user} ->
        message =
          "Your FDH Mobile Banking password has been reset. Your new password is: #{new_password}. Please change this password when you next login."

        Repo.insert(%SMSNotification{
          msisdn: updated_user.mobile_number,
          message: message
        })

        {:noreply,
         socket
         |> put_flash(:info, "Password has been reset and sent to user's phone number")
         |> push_navigate(to: @url, replace: true)}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reset password")
         |> push_navigate(to: @url, replace: true)}
    end
  end

  def handle_event("disable", %{"id" => id}, socket) do
    case MainContext.get_wallet_user!(id, false) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "blocked" => true
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(:info, "Profile has been disabled")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to disable profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("enable", %{"id" => id}, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "blocked" => false
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> put_flash(:info, "Profile has been enabled")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Failed to enable profile")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_status(status, type \\ nil) do
    case type do
      "frozen" ->
        if status, do: "Account Frozen", else: "Account Active"

      "locked" ->
        if status, do: "Account Locked", else: "Account Active"

      "blocked" ->
        if status, do: "Account Blocked", else: "Account Active"

      _ ->
        if status, do: "Yes", else: "No"
    end
  end

  defp handle_freeze(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "frozen" => true,
            "frozen_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been frozen")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to freeze account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_unfreeze(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "frozen" => false,
            "unfreeze_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been unfrozen")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to unfreeze account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_lock(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "locked" => true,
            "lock_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been locked")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to lock account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_unlock(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "locked" => false,
            "unlock_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been unlocked")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to unlock account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_block(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "blocked" => true,
            "block_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been blocked")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to block account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  defp handle_unblock(id, reason, socket) do
    case MainContext.get_wallet_user!(id) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "User not found")
         |> push_navigate(to: @url, replace: true)}

      user ->
        changeset =
          MainSchema.changeset(user, %{
            "blocked" => false,
            "unblock_reason" => reason
          })

        case Repo.update(changeset) do
          {:ok, _updated_user} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> assign(:stats, get_wallet_stats())
             |> put_flash(:info, "Account has been unblocked")
             |> push_navigate(to: @url, replace: true)}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> assign(:show_reason_modal, false)
             |> put_flash(:error, "Failed to unblock account")
             |> push_navigate(to: @url, replace: true)}
        end
    end
  end

  # Generate a secure password that meets the policy requirements
  defp generate_secure_password do
    # Define character sets
    # Removed confusing I,O
    uppercase = "ABCDEFGHJKLMNPQRSTUVWXYZ"
    # Removed confusing l,o
    lowercase = "abcdefghijkmnpqrstuvwxyz"
    # Removed confusing 0,1
    numbers = "********"
    special = "!@#$%^&*"

    # Ensure at least one of each type
    initial = [
      String.at(uppercase, :rand.uniform(String.length(uppercase)) - 1),
      String.at(lowercase, :rand.uniform(String.length(lowercase)) - 1),
      String.at(numbers, :rand.uniform(String.length(numbers)) - 1),
      String.at(special, :rand.uniform(String.length(special)) - 1)
    ]

    # Add 8 more random characters for a total of 12
    all_chars = uppercase <> lowercase <> numbers <> special

    remaining =
      for _ <- 1..8 do
        String.at(all_chars, :rand.uniform(String.length(all_chars)) - 1)
      end

    # Combine and shuffle
    (initial ++ remaining)
    |> Enum.shuffle()
    |> Enum.join()
  end
end
