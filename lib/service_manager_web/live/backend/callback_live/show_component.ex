defmodule ServiceManagerWeb.Backend.CallbackLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Callback Details</h2>
          <div class="space-y-1">
            <p class="text-sm text-gray-500">ID: <%= @callback.id %></p>
            <p class="text-sm text-gray-500">URL: <%= @callback.callback_url %></p>
          </div>
        </div>
        <div class="text-right">
          <div class="inline-flex items-center px-4 py-2 rounded-md bg-gray-100">
            <div class={
              "w-2 h-2 rounded-full mr-2 #{
                case @callback.status do
                  "pending" -> "bg-yellow-500"
                  "success" -> "bg-green-500"
                  "failed" -> "bg-red-500"
                end
              }"
            } />
            <span class="text-sm font-medium text-gray-700">
              <%= String.capitalize(@callback.status) %>
            </span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Basic Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Type</label>
              <p class="mt-1"><%= @callback.callback_type %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Duration</label>
              <p class="mt-1">
                <%= if @callback.duration_ms, do: "#{@callback.duration_ms}ms", else: "--" %>
              </p>
            </div>
          </div>
        </div>
        <!-- Status Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Status Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Response Status</label>
              <p class="mt-1"><%= @callback.response_status || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Retry Count</label>
              <p class="mt-1"><%= @callback.retry_count || "0" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Next Retry At</label>
              <p class="mt-1">
                <%= if @callback.next_retry_at do %>
                  <%= Calendar.strftime(@callback.next_retry_at, "%Y-%m-%d %H:%M:%S") %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
          </div>
        </div>
        <!-- Request Details -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Request Details</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Headers</label>
              <div class="mt-1 bg-gray-50 rounded p-3 overflow-x-auto">
                <pre class="text-xs"><code class="language-json"><%= Jason.encode!(@callback.request_headers, pretty: true) %></code></pre>
              </div>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Body</label>
              <div class="mt-1 bg-gray-50 rounded p-3 overflow-x-auto">
                <pre class="text-xs"><code class="language-json"><%= Jason.encode!(@callback.request_body, pretty: true) %></code></pre>
              </div>
            </div>
          </div>
        </div>
        <!-- Response Details -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Response Details</h3>
          <div class="space-y-4">
            <%= if @callback.response_headers do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Headers</label>
                <div class="mt-1 bg-gray-50 rounded p-3 overflow-x-auto">
                  <pre class="text-xs"><code class="language-json"><%= Jason.encode!(@callback.response_headers, pretty: true) %></code></pre>
                </div>
              </div>
            <% end %>
            <%= if @callback.response_body do %>
              <div>
                <label class="text-sm font-medium text-gray-500">Body</label>
                <div class="mt-1 bg-gray-50 rounded p-3 overflow-x-auto">
                  <pre class="text-xs"><code class="language-json"><%= Jason.encode!(@callback.response_body, pretty: true) %></code></pre>
                </div>
              </div>
            <% end %>
          </div>
        </div>
        <!-- Error Information -->
        <%= if @callback.error_message do %>
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Error Information</h3>
            <div class="bg-red-50 text-red-700 p-3 rounded text-sm">
              <%= @callback.error_message %>
            </div>
          </div>
        <% end %>
        <!-- Metadata -->
        <%= if map_size(@callback.metadata) > 0 do %>
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Metadata</h3>
            <div class="mt-1 bg-gray-50 rounded p-3 overflow-x-auto">
              <pre class="text-xs"><code class="language-json"><%= Jason.encode!(@callback.metadata, pretty: true) %></code></pre>
            </div>
          </div>
        <% end %>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1"><%= Calendar.strftime(@callback.inserted_at, "%Y-%m-%d %H:%M:%S") %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Updated At</label>
              <p class="mt-1"><%= Calendar.strftime(@callback.updated_at, "%Y-%m-%d %H:%M:%S") %></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
