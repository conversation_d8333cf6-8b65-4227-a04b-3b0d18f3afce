defmodule ServiceManagerWeb.Backend.CallbackLive.Index do
  use ServiceManagerWeb, :live_view
  import Ecto.Query
  import ServiceManagerWeb.Utilities.Utils, only: [generate_pagination_details: 1]
  import ServiceManagerWeb.Utilities.PermissionHelpers

  alias ServiceManager.Contexts.CallbackContext
  alias ServiceManager.Schemas.Callback
  alias ServiceManager.Repo

  @url "/mobileBanking/callbacks"

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, "callbacks")
    end

    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:stats, get_stats())

    {:ok, socket}
  end

  defp get_stats do
    %{
      total: Repo.aggregate(Callback, :count, :id),
      pending: Repo.aggregate(from(c in Callback, where: c.status == "pending"), :count, :id),
      success: Repo.aggregate(from(c in Callback, where: c.status == "success"), :count, :id),
      failed: Repo.aggregate(from(c in Callback, where: c.status == "failed"), :count, :id)
    }
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = CallbackContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:callbacks, data)}
  end

  def handle_params(params, url, socket) do
    data = CallbackContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:callbacks, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Callback Details")
    |> assign(:callback, CallbackContext.get_data!(id))
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Callbacks")
    |> assign(:callback, nil)
  end

  @impl true
  def handle_info({:callback_updated, _callback}, socket) do
    data = CallbackContext.retrieve()
    stats = get_stats()
    {:noreply, socket |> stream(:callbacks, data) |> assign(:stats, stats)}
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
