<!-- Statistics Cards -->
<div class="grid grid-cols-4 gap-6 mb-8">
  <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-gray-500">
    <div class="p-4">
      <div class="text-sm font-medium text-gray-600 mb-2">Total Callbacks</div>
      <div class="text-3xl font-bold text-gray-900"><%= @stats.total %></div>
    </div>
    <div class="bg-gray-100 px-4 py-2">
      <div class="text-xs text-gray-600">All Time</div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-green-500">
    <div class="p-4">
      <div class="text-sm font-medium text-green-600 mb-2">Success</div>
      <div class="text-3xl font-bold text-green-900"><%= @stats.success %></div>
    </div>
    <div class="bg-green-100 px-4 py-2">
      <div class="text-xs text-green-600">Completed</div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-yellow-500">
    <div class="p-4">
      <div class="text-sm font-medium text-yellow-600 mb-2">Pending</div>
      <div class="text-3xl font-bold text-yellow-900"><%= @stats.pending %></div>
    </div>
    <div class="bg-yellow-100 px-4 py-2">
      <div class="text-xs text-yellow-600">In Progress</div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-red-500">
    <div class="p-4">
      <div class="text-sm font-medium text-red-600 mb-2">Failed</div>
      <div class="text-3xl font-bold text-red-900"><%= @stats.failed %></div>
    </div>
    <div class="bg-red-100 px-4 py-2">
      <div class="text-xs text-red-600">Error</div>
    </div>
  </div>
</div>

<.header>
  Callbacks List
</.header>

<.table
  id="callbacks"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.callbacks}
  row_click={fn {_id, callback} -> JS.patch(~p"/mobileBanking/callbacks/#{callback}") end}
>
  <:col :let={{_id, callback}} filter_item="id" label="ID"><%= callback.id || "--" %></:col>
  <:col :let={{_id, callback}} filter_item="callback_type" label="Type">
    <%= callback.callback_type || "--" %>
  </:col>
  <:col :let={{_id, callback}} filter_item="callback_url" label="URL">
    <%= callback.callback_url || "--" %>
  </:col>
  <:col :let={{_id, callback}} filter_item="status" label="Status">
    <.status_pill status={callback.status == "success"} text={String.capitalize(callback.status)} />
  </:col>
  <:col :let={{_id, callback}} filter_item="duration_ms" label="Duration">
    <%= if callback.duration_ms, do: "#{callback.duration_ms}ms", else: "--" %>
  </:col>
  <:col :let={{_id, callback}} filter_item="retry_count" label="Retries">
    <%= callback.retry_count || "0" %>
  </:col>
  <:col :let={{_id, callback}} filter_item="inserted_at" label="Created At">
    <%= Calendar.strftime(callback.inserted_at, "%Y-%m-%d %H:%M:%S") %>
  </:col>

  <:action :let={{_id, callback}}>
    <%= if can_view?(@current_user, :callbacks) do %>
      <.dropdown id={"dropdown-#{callback.id}"} label="Options">
        <.link
          class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          patch={~p"/mobileBanking/callbacks/#{callback}"}
        >
          View Details
        </.link>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action == :show}
  id="callback-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/callbacks")}
  max-width="max-w-[95%]"
>
  <.live_component
    module={ServiceManagerWeb.Backend.CallbackLive.ShowComponent}
    id={@callback.id}
    title={@page_title}
    callback={@callback}
    patch={~p"/mobileBanking/callbacks"}
  />
</.modal>
