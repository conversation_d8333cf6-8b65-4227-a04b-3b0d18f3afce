defmodule ServiceManagerWeb.Backend.CardlessWithdrawsLive.Index do
  use ServiceManagerWeb, :live_view

  import ServiceManagerWeb.Utilities.Utils,
    only: [
      generate_pagination_details: 1
    ]

  alias ServiceManager.Contexts.CardlessWithdrawsContext, as: MainContext
  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManager.Schemas.Withdraws.CardlessWithdraw.CardlessWithdrawSchema, as: MainSchema
  import Ecto.Query
  import ServiceManagerWeb.Utilities.PermissionHelpers
  alias ServiceManager.Repo

  @url "/mobileBanking/cardless-withdraws"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:stats, get_request_stats())
      |> assign(:chart_data, get_chart_data())

    {:ok, socket}
  end

  defp get_chart_data do
    total_requests = Repo.aggregate(MainSchema, :count, :id)

    # Volume over time (last 7 days)
    volume_query =
      from t in MainSchema,
        group_by: fragment("DATE(inserted_at)"),
        order_by: fragment("DATE(inserted_at) DESC"),
        limit: 7,
        select: %{
          date: fragment("DATE(inserted_at)"),
          count: count(t.id)
        }

    volume_data = Repo.all(volume_query)

    # Status distribution
    status_query =
      from t in MainSchema,
        group_by: t.status,
        select: %{
          status: t.status,
          count: count(t.id)
        }

    status_data = Repo.all(status_query)

    # Amount distribution
    amount_ranges = [
      {0, 1000, "<1K"},
      {1000, 5000, "1K-5K"},
      {5000, 10000, "5K-10K"},
      {10000, 50000, "10K-50K"},
      {50000, 100_000, "50K-100K"},
      {100_000, 500_000, "100K-500K"},
      {500_000, 999_999_999, ">500K"}
    ]

    amount_data =
      Enum.map(amount_ranges, fn {min, max, label} ->
        count =
          Repo.one(
            from t in MainSchema,
              where: t.amount >= ^min and t.amount < ^max,
              select: count(t.id)
          )

        %{range: label, count: count}
      end)

    # Type distribution
    type_query =
      from t in MainSchema,
        select: %{
          type: fragment("'cardless_withdraw'"),
          count: count(t.id)
        }

    type_data = Repo.all(type_query)

    %{
      volume_data: volume_data |> Enum.reverse(),
      status_data: status_data,
      amount_data: amount_data,
      type_data: type_data
    }
  end

  defp get_request_stats do
    total_requests = Repo.aggregate(MainSchema, :count, :id)

    rejected_query = from(t in MainSchema, where: t.status == "rejected")
    rejected_count = Repo.aggregate(rejected_query, :count, :id)
    rejected_amount = Repo.aggregate(rejected_query, :sum, :amount) || Decimal.new(0)

    approved_query = from(t in MainSchema, where: t.status == "approved")
    approved_count = Repo.aggregate(approved_query, :count, :id)
    approved_amount = Repo.aggregate(approved_query, :sum, :amount) || Decimal.new(0)

    pending_query = from(t in MainSchema, where: t.status == "pending")
    pending_count = Repo.aggregate(pending_query, :count, :id)
    pending_amount = Repo.aggregate(pending_query, :sum, :amount) || Decimal.new(0)

    [
      %{
        title: "Total Requests",
        value: total_requests
      },
      %{
        title: "Rejected Requests",
        value: rejected_count,
        comparison: "MWK #{rejected_amount}"
      },
      %{
        title: "Approved Requests",
        value: approved_count,
        comparison: "MWK #{approved_amount}"
      },
      %{
        title: "Pending Requests",
        value: pending_count,
        comparison: "MWK #{pending_amount}"
      }
    ]
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = MainContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:data, data)}
  end

  def handle_params(params, _url, socket) do
    data = MainContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:data, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Cardless Withdraws")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Cardless Withdraw Details")
    |> assign(:data, MainContext.get_data!(id))
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Cardless Withdraws")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, nil)
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page_title, "Excel Export Cardless Withdraws")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, "/mobileBankingReporting/cardless-withdraws/ExcelExport")
  end

  defp apply_action(socket, _action, _params) do
    socket
    |> assign(:page_title, "Listing Cardless Withdraws")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  @impl true
  def handle_event("toggle_status", %{"id" => id, "status" => status}, socket) do
    data = MainContext.get_data!(id)

    result =
      case status do
        "pending" ->
          MainContext.approve_request(data, socket.assigns.current_user)

        "approved" ->
          MainContext.reject_request(data, socket.assigns.current_user)

        "rejected" ->
          MainContext.approve_request(data, socket.assigns.current_user)

        "completed" ->
          # No action needed for completed status
          {:ok, data}

        _ ->
          {:error, "Invalid status"}
      end

    result
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> assign(:stats, get_request_stats())
         |> assign(:chart_data, get_chart_data())
         |> put_flash(:info, "Cardless withdraw status updated successfully")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: @url, replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
