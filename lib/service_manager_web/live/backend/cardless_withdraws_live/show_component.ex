defmodule ServiceManagerWeb.Backend.CardlessWithdrawsLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto" style="min-height: 29.7cm;">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Cardless Withdraw Details</h2>
          <p class="text-sm text-gray-500">ID: <%= @data.id %></p>
        </div>
        <div>
          <.status_pill status={@data.status} text={@data.status} />
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Request Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Request Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Reference Number</label>
              <p class="mt-1"><%= @data.reference_number %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Amount</label>
              <p class="mt-1 font-medium">MWK <%= @data.amount %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Narration</label>
              <p class="mt-1"><%= @data.narration || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Account Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Source Account</label>
              <p class="mt-1"><%= @data.source_account %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Suspense Account</label>
              <p class="mt-1"><%= @data.suspense_account || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Beneficiary Name</label>
              <p class="mt-1"><%= @data.beneficiary_name %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Beneficiary Phone</label>
              <p class="mt-1"><%= @data.beneficiary_phone %></p>
            </div>
          </div>
        </div>
        <!-- Status Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Status Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Status</label>
              <p class="mt-1"><%= @data.status %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">OTP Verified</label>
              <p class="mt-1"><%= @data.otp_verified %></p>
            </div>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @data.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @data.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
