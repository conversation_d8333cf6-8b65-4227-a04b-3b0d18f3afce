<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="cardless-withdraw-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />
<br />

<.header>
  Listing Cardless Withdraws
</.header>

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/cardless-withdraws/filter"}
  export_url={~p"/mobileBanking/cardless-withdraws/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.patch(~p"/mobileBanking/cardless-withdraws/#{data}/show") end}
>
  <:col :let={{_id, data}} filter_item="id" label="ID">
    <div class="text-[12px]"><%= data.id %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="reference_number" label="Reference">
    <div class="text-[12px] whitespace-nowrap"><%= data.reference_number %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="amount" label="Amount">
    <div class="text-[12px] text-right font-medium">MWK <%= data.amount %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="source_account" label="From Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.source_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="beneficiary_phone" label="Beneficiary Phone">
    <div class="text-[12px] whitespace-nowrap"><%= data.beneficiary_phone %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="beneficiary_name" label="Beneficiary Name">
    <div class="text-[12px] whitespace-nowrap"><%= data.beneficiary_name %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="status" label="Status">
    <.status_pill status={data.status} text={data.status} />
  </:col>
  <:col :let={{_id, data}} filter_item="inserted_at" label="Created At">
    <div class="text-[12px] whitespace-nowrap"><%= data.inserted_at %></div>
  </:col>
  <:action :let={{_id, data}}>
    <%= if can_approve?(@current_user, :cardless_withdraws) do %>
      <div class="sr-only">
        <.link navigate={~p"/mobileBanking/cardless-withdraws/#{data}/show"}>Show</.link>
      </div>
      <.link
        phx-click="toggle_status"
        phx-value-id={data.id}
        phx-value-status={data.status}
        class="text-[12px] text-blue-600 hover:text-blue-900"
      >
        <%= case data.status do %>
          <% "pending" -> %>
            Approve
          <% "approved" -> %>
            Reject
          <% "rejected" -> %>
            Approve
          <% "completed" -> %>
            Completed
          <% _ -> %>
            <%= data.status %>
        <% end %>
      </.link>
    <% end %>
  </:action>
</.table>
<!-- Charts Section -->
<div
  class="mt-8 grid grid-cols-2 gap-6"
  id="charts"
  phx-hook="Charts"
  data-chart-data={Jason.encode!(@chart_data)}
>
  <!-- Request Volume Over Time -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Request Volume Over Time</h3>
    <div id="volumeChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Status Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
    <div id="statusChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Amount Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Amount Distribution</h3>
    <div id="amountChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Request Type Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Request Types</h3>
    <div id="typeChart" class="w-full h-[300px]"></div>
  </div>
</div>
<!-- ApexCharts Script -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts">
</script>

<.modal
  :if={@live_action == :show}
  id="cardless-withdraw-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cardless-withdraws")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CardlessWithdrawsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cardless-withdraws")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.TransactionsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/cardless-withdraws"}
  />
</.modal>
