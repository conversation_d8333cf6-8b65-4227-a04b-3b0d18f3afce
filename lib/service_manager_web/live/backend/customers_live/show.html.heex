<.header>
  User management <%= @user_management.id %>
  <:subtitle>This is a User record from your database.</:subtitle>
  <:actions>
    <.back navigate={~p"/mobileBanking/Customers"}>Back to Customers</.back>
  </:actions>
</.header>

<.list>
  <:item title="Email"><%= @user_management.email %></:item>
  <:item title="Name"><%= @user_management.name %></:item>
  <:item title="Nickname"><%= @user_management.nickname %></:item>
  <:item title="First name"><%= @user_management.first_name %></:item>
  <:item title="Last name"><%= @user_management.last_name %></:item>
  <:item title="Phone number"><%= @user_management.phone_number %></:item>
  <:item title="Date of birth"><%= @user_management.date_of_birth %></:item>
  <:item title="Address"><%= @user_management.address %></:item>
  <:item title="City"><%= @user_management.city %></:item>
  <:item title="State"><%= @user_management.state %></:item>
  <:item title="Zip"><%= @user_management.zip %></:item>
  <:item title="Country"><%= @user_management.country %></:item>
</.list>

<.back navigate={~p"/mobileBanking/Customers"}>Back to Customers</.back>

<.modal
  :if={@live_action == :edit}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/user_managements/#{@user_management}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CustomersLiveLive.FormComponent}
    id={@user_management.id}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    patch={~p"/mobileBanking/user_managements/#{@user_management}"}
  />
</.modal>
