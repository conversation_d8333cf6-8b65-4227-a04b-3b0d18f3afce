defmodule ServiceManagerWeb.Backend.CustomersLiveLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.UserManagementContext
  alias ServiceManagerWeb.Api.Services.ProfileServiceController

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket, :current_path, Phoenix.LiveView.get_connect_params(socket)["current_path"])

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:user_management, UserManagementContext.get_data!(id))}
  end

  defp page_title(:show), do: "Show User management"
  defp page_title(:edit), do: "Edit User management"

  @impl true
  def handle_event("approve", %{"id" => id} = params, socket) do
    ProfileServiceController.approve(params)
    |> case do
      %{"data" => %{"errors" => error}} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}

      %{"message" => message} ->
        {:noreply,
         socket
         |> put_flash(:info, message)
         |> push_navigate(to: "/mobileBanking/user_managements/#{id}", replace: true)}
    end
  end

  def handle_event("disable", %{"id" => id} = params, socket) do
    ProfileServiceController.disable(params)
    |> case do
      %{"data" => %{"errors" => error}} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}

      %{"message" => message} ->
        {:noreply,
         socket
         |> put_flash(:info, message)
         |> push_navigate(to: "/mobileBanking/user_managements/#{id}", replace: true)}
    end
  end
end
