defmodule ServiceManagerWeb.Backend.CustomersLiveLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.CustomersContext, as: UserManagementContext
  alias ServiceManager.Schemas.AdminUsers, as: UserManagement
  alias ServiceManager.Schemas.Embedded.EmbeddedForm

  @url "/mobileBanking/Customers"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = UserManagementContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:user_managements, data)}
  end

  def handle_params(params, _url, socket) do
    data = UserManagementContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:user_managements, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Customers")
    |> assign(:user_management, UserManagementContext.get_data!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Customers")
    |> assign(:user_management, %UserManagement{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Customers")
    |> assign(:user_management, nil)
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Customers")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, nil)
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page_title, "Excel Export Customers")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, "/mobileBankingReporting/Customers/ExcelExport")
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    user_management = UserManagementContext.get_data!(id)
    {:ok, resp} = UserManagementContext.delete_data(user_management)

    Task.start(fn ->
      LogsContext.insert_log(
        %{
          message: "Customer deleted successfully",
          details: inspect(resp)
        },
        socket
      )
    end)

    {:noreply, stream_delete(socket, :user_managements, user_management)}
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_approval(is_approved?) do
    if is_approved? do
      "Approved"
    else
      "Not Approved"
    end
  end
end
