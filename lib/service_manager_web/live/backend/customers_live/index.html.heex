<.header>
  Customer Management Dashboard
</.header>

<.table
  id="user_managements"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/Customers/filter"}
  export_url={~p"/mobileBanking/Customers/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.user_managements}
  row_click={
    fn {_id, user_management} -> JS.navigate(~p"/mobileBanking/Customers/#{user_management}") end
  }
>
  <:col :let={{_id, user_management}} filter_item="id" label="ID"><%= user_management.id %></:col>
  <:col :let={{_id, user_management}} filter_item="email" label="Email">
    <%= user_management.email %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="name" label="Name">
    <%= user_management.name %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="nickname" label="Nickname">
    <%= user_management.nickname %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="first_name" label="First name">
    <%= user_management.first_name %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="last_name" label="Last name">
    <%= user_management.last_name %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="phone_number" label="Phone number">
    <%= user_management.phone_number %>
  </:col>
  <:col :let={{_id, user_management}} filter_item="approved" label="Approval Status">
    <.status_pill
      status={user_management.approved}
      text={handle_approval(user_management.approved)}
    />
  </:col>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/Customers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CustomersLiveLive.FormComponent}
    id={@user_management.id || :new}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    current_user={@current_user}
    patch={~p"/mobileBanking/Customers"}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/Customers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.CustomersLiveLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/Customers"}
  />
</.modal>
