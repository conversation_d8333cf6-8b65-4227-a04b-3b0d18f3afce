<.header>
  Wallet <%= @data.id %>
  <:subtitle>This is a Wallet record from your database.</:subtitle>
  <:actions></:actions>
</.header>

<.list>
  <:item title="Tag"><%= @data.tag %></:item>
  <:item title="Name"><%= @data.name %></:item>
  <:item title="Last Name"><%= @data.number %></:item>
  <:item title="Type"><%= @data.type %></:item>
  <:item title="Currency"><%= @data.currency %></:item>
  <:item title="Balance"><%= @data.balance %></:item>
</.list>

<.back navigate={~p"/mobileBanking/StandardAccounts"}>Go Back</.back>

<.modal
  :if={@live_action == :edit}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletsOverview/#{@data}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.AccountsLive.FormComponent}
    id={@data.id}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/StandardAccounts/#{@data}"}
  />
</.modal>
