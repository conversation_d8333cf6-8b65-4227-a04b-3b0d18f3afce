<.header>
  Wellets Overview
</.header>

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.navigate(~p"/mobileBanking/StandardAccounts/#{data}") end}
>
  <:col :let={{_id, data}} filter_item="tag" label="Tag"><%= data.tag %></:col>
  <:col :let={{_id, data}} filter_item="name" label="Name"><%= data.name %></:col>
  <:col :let={{_id, data}} filter_item="number" label="Account Number"><%= data.number %></:col>
  <:col :let={{_id, data}} filter_item="type" label="Type"><%= data.type %></:col>
  <:col :let={{_id, data}} filter_item="currency" label="Currency"><%= data.currency %></:col>
  <:col :let={{_id, data}} filter_item="balance" label="Balance"><%= data.balance %></:col>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/StandardAccounts")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.AccountsLive.FormComponent}
    id={@data.id || :new}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/StandardAccounts"}
  />
</.modal>
