defmodule ServiceManagerWeb.Backend.VirtualCardsApiConfigLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.VirtualCardsAContexts, as: Configuration

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>
          Use this form to manage Virtual card api config records in your database.
        </:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="api_config-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
          <.input field={@form[:provider_name]} type="text" label="Provider name" />
          <div class="col-span-2">
            <.input field={@form[:base_url]} type="text" label="Base url" />
          </div>
          <div class="col-span-2">
            <.input field={@form[:auth_token]} type="text" label="Auth token" />
          </div>
          <.input field={@form[:api_key]} type="text" label="Api key" />
          <.input field={@form[:api_secret]} type="text" label="Api secret" />
          <.input field={@form[:version]} type="text" label="Version" />
          <.input field={@form[:timeout]} type="number" label="Timeout" />
          <div class="col-span-3">
            <.input field={@form[:notes]} type="textarea" label="Notes" />
          </div>
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Save Api config</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{api_config: api_config} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(Configuration.change_api_config(api_config))
     end)}
  end

  @impl true
  def handle_event("validate", %{"virtual_cards_api_config" => api_config_params}, socket) do
    changeset = Configuration.change_api_config(socket.assigns.api_config, api_config_params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"virtual_cards_api_config" => api_config_params}, socket) do
    save_api_config(socket, socket.assigns.action, api_config_params)
  end

  defp save_api_config(socket, :edit, api_config_params) do
    case Configuration.update_api_config(socket.assigns.api_config, api_config_params) do
      {:ok, api_config} ->
        notify_parent({:saved, api_config})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Api config updated successfully",
              details: inspect(api_config)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Api config updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Api config update failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_api_config(socket, :new, api_config_params) do
    case Configuration.create_api_config(api_config_params) do
      {:ok, api_config} ->
        notify_parent({:saved, api_config})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Api config created successfully",
              details: inspect(api_config)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Api config created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Api config create failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end
