<.header>
  Virtual Cards API Configuration
  <:subtitle>Configure and manage virtual card provider API settings.</:subtitle>
  <:actions>
    <%= if can_create?(@current_user, :virtual_cards_api_configs) do %>
      <.link patch={~p"/mobileBanking/VirtualCardsAPIConfigs/new"}>
        <.button>New Virtual card Api config</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="api_configs"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.api_configs}
  row_click={
    fn {_id, api_config} ->
      JS.navigate(~p"/mobileBanking/VirtualCardsAPIConfigs/#{api_config}")
    end
  }
>
  <:col :let={{_id, api_config}} filter_item="provider_name" label="Provider name">
    <%= api_config.provider_name %>
  </:col>
  <:col :let={{_id, api_config}} filter_item="base_url" label="Base url">
    <%= api_config.base_url %>
  </:col>
  <:col :let={{_id, api_config}} filter_item="api_key" label="Api key">
    <%= api_config.api_key %>
  </:col>
  <:col :let={{_id, api_config}} filter_item="version" label="Version">
    <%= api_config.version %>
  </:col>
  <:col :let={{_id, api_config}} filter_item="timeout" label="Timeout">
    <%= api_config.timeout %>
  </:col>
  <:col :let={{_id, api_config}} filter_item="status" label="Status">
    <.status_pill status={api_config.status} text={api_config.status} />
  </:col>

  <:action :let={{id, dataset}}>
    <%= if has_any_permission?(@current_user, [:update, :delete, :activate], :virtual_cards_api_configs) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_activate?(@current_user, :virtual_cards_api_configs) do %>
          <%= if dataset.status == "active" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("deactivate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Deactivate
            </.link>
          <% else %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("activate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Activate
            </.link>
          <% end %>
        <% end %>

        <%= if can_update?(@current_user, :virtual_cards_api_configs) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/VirtualCardsAPIConfigs/#{dataset}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :virtual_cards_api_configs) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: dataset.id}) |> hide("##{id}")}
            data-confirm="Are you sure?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="api_config-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/VirtualCardsAPIConfigs")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.VirtualCardsApiConfigLive.FormComponent}
    id={@api_config.id || :new}
    title={@page_title}
    action={@live_action}
    api_config={@api_config}
    patch={~p"/mobileBanking/VirtualCardsAPIConfigs"}
  />
</.modal>
