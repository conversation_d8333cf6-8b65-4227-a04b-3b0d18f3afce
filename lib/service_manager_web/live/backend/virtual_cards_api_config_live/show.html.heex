<.header>
  Api config <%= @api_config.id %>
  <:subtitle>This is a api_config record from your database.</:subtitle>
  <:actions>
    <.link
      patch={~p"/mobileBanking/VirtualCardsAPIConfigs/#{@api_config}/show/edit"}
      phx-click={JS.push_focus()}
    >
      <.button>Edit api_config</.button>
    </.link>
  </:actions>
</.header>

<.list>
  <:item title="Provider name"><%= @api_config.provider_name %></:item>
  <:item title="Base url"><%= @api_config.base_url %></:item>
  <:item title="Auth token"><%= @api_config.auth_token %></:item>
  <:item title="Api key"><%= @api_config.api_key %></:item>
  <:item title="Api secret"><%= @api_config.api_secret %></:item>
  <:item title="Version"><%= @api_config.version %></:item>
  <:item title="Timeout"><%= @api_config.timeout %></:item>
  <:item title="Status"><%= @api_config.status %></:item>
  <:item title="Notes"><%= @api_config.notes %></:item>
</.list>

<.back navigate={~p"/mobileBanking/VirtualCardsAPIConfigs"}>Back to Virtual Cards APIs</.back>

<.modal
  :if={@live_action == :edit}
  id="api_config-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/VirtualCardsAPIConfigs/#{@api_config}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.VirtualCardsApiConfigLive.FormComponent}
    id={@api_config.id}
    title={@page_title}
    action={@live_action}
    api_config={@api_config}
    patch={~p"/mobileBanking/VirtualCardsAPIConfigs/#{@api_config}"}
  />
</.modal>
