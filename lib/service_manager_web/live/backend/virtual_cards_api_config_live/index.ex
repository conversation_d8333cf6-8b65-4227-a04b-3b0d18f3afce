defmodule ServiceManagerWeb.Backend.VirtualCardsApiConfigLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.VirtualCardsAContexts, as: Configuration
  alias ServiceManager.Schemas.VirtualCardsApiConfig, as: ApiConfig
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @url "/mobileBanking/VirtualCardsAPIConfigs"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = Configuration.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:api_configs, data)}
  end

  def handle_params(params, _url, socket) do
    data = Configuration.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:api_configs, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Virtual card Api config")
    |> assign(:api_config, Configuration.get_api_config!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Virtual card Api config")
    |> assign(:api_config, %ApiConfig{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Virtual card Api configs")
    |> assign(:api_config, nil)
  end

  @impl true
  def handle_info(
        {ServiceManagerWeb.Backend.VirtualCardsApiConfigLive.FormComponent, {:saved, api_config}},
        socket
      ) do
    {:noreply, stream_insert(socket, :api_configs, api_config)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    api_config = Configuration.get_api_config!(id)
    {:ok, _} = Configuration.delete_api_config(api_config)

    {:noreply, stream_delete(socket, :api_configs, api_config)}
  end

  def handle_event("activate", %{"id" => id}, socket) do
    data = Configuration.get_api_config!(id)

    Configuration.update_data(data, %{"status" => "active"}, socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Virtual Card API activated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Virtual Card API activation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("deactivate", %{"id" => id}, socket) do
    data = Configuration.get_api_config!(id)

    Configuration.update_data(data, %{"status" => "inactive"}, socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Virtual Card API inactivated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Virtual Card API inactivation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
