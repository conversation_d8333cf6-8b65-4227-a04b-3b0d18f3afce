<.header>
  SMS Logs Management
  <:subtitle>Configure and manage SMS logs.</:subtitle>
</.header>

<.table
  id="dataSmsLogs"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.data}
>
  <:col :let={{_id, data}} filter_item="id" label="Reference"><%= data.id %></:col>
  <:col :let={{_id, data}} filter_item="msisdn" label="MSISDN"><%= data.msisdn %></:col>
  <:col :let={{_id, data}} filter_item="status" label="Satus">
    <.status_pill status={data.status} text={data.status} />
  </:col>
  <:col :let={{_id, data}} filter_item="attempt_count" label="Attempt Count">
    <%= data.attempt_count %>
  </:col>
  <:col :let={{_id, data}} filter_item="inserted_at" label="Inserted At">
    <%= data.inserted_at %>
  </:col>
  <:col :let={{_id, data}} filter_item="sent_at" label="Sent At"><%= data.sent_at %></:col>
  <:col :let={{_id, data}} filter_item="message" label="Message"><%= data.message %></:col>
  <:action :let={{id, data}}>
    <%= if can_view?(@current_user, :sms_logs) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <.link
          class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          patch={~p"/mobileBanking/sms_logs/#{data}/details"}
        >
          Details
        </.link>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:show]}
  id="sms-notification-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/sms_logs")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.SmsLogsLive.FormComponent}
    id={@data.id || :new}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/sms_logs"}
  />
</.modal>
