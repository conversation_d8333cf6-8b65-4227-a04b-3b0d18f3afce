defmodule ServiceManagerWeb.Backend.SmsLogsLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.SmsNotificationsContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage currency records in your database.</:subtitle>
      </.header>

      <.simple_form for={@form} id="sms-notification-form" phx-target={@myself}>
        <div class="grid grid-cols-2 gap-5">
          <.input field={@form[:id]} type="text" label="ID" disabled />
          <.input field={@form[:msisdn]} type="text" label="MSISDN" disabled />
          <.input field={@form[:status]} type="text" label="Status" disabled />
          <.input field={@form[:attempt_count]} type="text" label="Attempt Count" disabled />
          <.input field={@form[:sent_at]} type="number" label="Sent At" step="any" disabled />
          <.input field={@form[:inserted_at]} type="number" label="Inserted At" disabled />
          <.input field={@form[:message]} type="textarea" label="Message" disabled />
          <.input
            field={@form[:details]}
            type="textarea"
            label="Details"
            value={inspect(@form[:details].value, pretty: true)}
            disabled
          />
        </div>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: data} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(SmsNotificationsContext.change_data(data))
     end)}
  end

  @impl true
  def handle_event("validate", %{"sms_notification" => sms_notification_params}, socket) do
    changeset = SmsNotificationsContext.change_data(socket.assigns.data, sms_notification_params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end
end
