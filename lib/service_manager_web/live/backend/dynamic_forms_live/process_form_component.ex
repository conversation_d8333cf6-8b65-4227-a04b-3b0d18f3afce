defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessFormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess

  @impl true
  def update(%{process: process} = assigns, socket) do
    # Convert tags array to comma-separated string for form display
    process_with_tags_string = case process.tags do
      nil -> process
      [] -> process
      tags when is_list(tags) ->
        %{process | tags: Enum.join(tags, ", ")}
      _ -> process
    end

    changeset = DynamicProcess.changeset(process_with_tags_string, %{})

    # Sample Elixir code template for new processes
    default_code = """
def process(input) do
  # Your Elixir code here
  # Input contains the data from the previous process or API request
  # Return {:ok, result} on success or {:error, reason} on failure

  # Example: Add a timestamp to the input
  result = Map.put(input, "processed_at", DateTime.utc_now() |> to_string())

  {:ok, result}
end

# Helper functions can be defined here
# defp helper_function(data) do
#   # your helper code
# end
"""

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:changeset, changeset)
     |> assign(:default_code, default_code)
     |> assign(:code_editor_id, "process-code-editor-#{:rand.uniform(1000)}")
     |> assign(:expected_params_editor_id, "expected-params-editor-#{:rand.uniform(1000)}")}
  end

  @impl true
  def handle_event("validate", %{"dynamic_process" => process_params}, socket) do
    # Parse the expected_params JSON string into a map
    process_params = parse_expected_params(process_params)
    # Parse tags from comma-separated string to array
    process_params = parse_tags(process_params)

    changeset =
      socket.assigns.process
      |> DynamicProcess.changeset(process_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"dynamic_process" => process_params} = _params, socket) do
    # Parse the expected_params JSON string into a map
    process_params = parse_expected_params(process_params)
    # Parse tags from comma-separated string to array
    process_params = parse_tags(process_params)

    save_process(socket, socket.assigns.action, process_params)
  end

  # Parse the expected_params JSON string into a map
  defp parse_expected_params(process_params) do
    case process_params["expected_params"] do
      nil -> process_params
      "" -> Map.put(process_params, "expected_params", %{})
      json_string ->
        case Jason.decode(json_string) do
          {:ok, parsed_map} ->
            Map.put(process_params, "expected_params", parsed_map)
          {:error, _} ->
            # If JSON parsing fails, keep the original string
            process_params
        end
    end
  end

  # Parse tags from comma-separated string to array
  defp parse_tags(process_params) do
    case process_params["tags"] do
      nil -> process_params
      "" -> Map.put(process_params, "tags", [])
      tags_string when is_binary(tags_string) ->
        tags =
          tags_string
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.reject(&(&1 == ""))
        Map.put(process_params, "tags", tags)
      _ -> process_params
    end
  end

  defp save_process(socket, :new, process_params) do
    # Add the current user ID to the process params
    process_params = Map.put(process_params, "created_by_id", socket.assigns.current_user.id)

    case ProcessManager.create_process(process_params) do
      {:ok, _process} ->
        {:noreply,
         socket
         |> put_flash(:info, "Process created successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_process(socket, :edit, process_params) do
    # Add the current user ID to the process params
    process_params = Map.put(process_params, "created_by_id", socket.assigns.current_user.id)

    case ProcessManager.update_process(socket.assigns.process, process_params) do
      {:ok, _process} ->
        {:noreply,
         socket
         |> put_flash(:info, "Process updated successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-5xl mx-auto">
      <.header>
        <%= @title %>
        <:subtitle>
          <%= if @action == :edit do %>
            Edit process details and code
          <% else %>
            Create a new dynamic process with custom Elixir code
          <% end %>
        </:subtitle>
      </.header>

      <.simple_form
        :let={f}
        for={@changeset}
        id="process-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
        class="mt-6"
      >
        <!-- Basic Information -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <.input field={f[:name]} type="text" label="Plugin Name" required placeholder="e.g., Data Transformer" />
          <.input field={f[:description]} type="textarea" label="Description" placeholder="What does this plugin do?" rows="3" />
        </div>

        <!-- Plugin Library Information -->
        <div class="mt-8">
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-sm font-medium text-gray-900 flex items-center">
                <.icon name="hero-puzzle-piece" class="h-4 w-4 mr-2 text-blue-600" />
                Plugin Library Information
              </h3>
              <p class="text-xs text-gray-600 mt-1">Configure how this plugin appears in the library</p>
            </div>
            <div class="p-6 bg-white space-y-6">
              <!-- Row 1: Category, Group, Type -->
              <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
                <.input
                  field={f[:category]}
                  type="text"
                  label="Category"
                  placeholder="e.g., Data Processing"
                  help="Functional category (Data Processing, Authentication, etc.)"
                />
                <.input
                  field={f[:group]}
                  type="text"
                  label="Group"
                  placeholder="e.g., User Plugins"
                  help="Organization group or team"
                />
                <.input
                  field={f[:plugin_type]}
                  type="select"
                  label="Plugin Type"
                  options={[
                    {"Public - Available to all users", "public"},
                    {"Protected - Requires permissions", "protected"},
                    {"Private - Only visible to creator", "private"},
                    {"System - Core system plugins", "system"},
                    {"Enterprise - Organization-wide", "enterprise"}
                  ]}
                  help="Controls plugin visibility and access"
                />
              </div>

              <!-- Row 2: Version, Author, License -->
              <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
                <.input
                  field={f[:version]}
                  type="text"
                  label="Version"
                  placeholder="1.0.0"
                  help="Semantic version (major.minor.patch)"
                />
                <.input
                  field={f[:author]}
                  type="text"
                  label="Author"
                  placeholder="Your Name"
                  help="Plugin author or maintainer"
                />
                <.input
                  field={f[:license]}
                  type="text"
                  label="License"
                  placeholder="MIT"
                  help="Software license (MIT, Apache, etc.)"
                />
              </div>

              <!-- Row 3: URLs -->
              <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <.input
                  field={f[:repository_url]}
                  type="url"
                  label="Repository URL"
                  placeholder="https://github.com/user/repo"
                  help="Link to source code repository"
                />
                <.input
                  field={f[:documentation_url]}
                  type="url"
                  label="Documentation URL"
                  placeholder="https://docs.example.com"
                  help="Link to plugin documentation"
                />
              </div>

              <!-- Row 4: Tags -->
              <div>
                <.input
                  field={f[:tags]}
                  type="text"
                  label="Tags"
                  placeholder="api, transformer, data, json"
                  help="Comma-separated tags for search and discovery"
                />
                <p class="text-xs text-gray-500 mt-1">
                  Enter tags separated by commas. Tags help users discover your plugin.
                </p>
              </div>

              <!-- Row 5: Settings -->
              <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div class="space-y-4">
                  <.input
                    field={f[:featured]}
                    type="checkbox"
                    label="Featured Plugin"
                    help="Highlight this plugin in the library"
                  />
                  <.input
                    field={f[:verified]}
                    type="checkbox"
                    label="Verified Plugin"
                    help="Mark as verified/trusted plugin"
                  />
                </div>
                <div class="space-y-4">
                  <.input
                    field={f[:rating]}
                    type="number"
                    label="Rating"
                    placeholder="4.5"
                    step="0.1"
                    min="0"
                    max="5"
                    help="Plugin rating (0-5 stars)"
                  />
                  <.input
                    field={f[:downloads]}
                    type="number"
                    label="Download Count"
                    placeholder="0"
                    min="0"
                    help="Number of times downloaded/used"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Expected Parameters Section -->
        <div class="mt-8">
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-sm font-medium text-gray-900 flex items-center">
                <.icon name="hero-cog-6-tooth" class="h-4 w-4 mr-2 text-gray-600" />
                Expected Parameters
              </h3>
              <p class="text-xs text-gray-600 mt-1">Define the JSON schema for parameters this process expects</p>
            </div>
            <div class="p-4 bg-white">
              <div class="relative">
                <textarea
                  name="dynamic_process[expected_params]"
                  id={@expected_params_editor_id}
                  class="w-full h-32 p-3 text-sm font-mono border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 bg-gray-50"
                  placeholder='{"param1": "string", "param2": "number"}'
                >
                  <%= Jason.encode!(Map.get(@changeset.data, :expected_params, %{}), pretty: true) %>
                </textarea>
                <div class="absolute top-2 right-2">
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    JSON
                  </span>
                </div>
              </div>
              <div class="mt-2 flex items-start space-x-2">
                <.icon name="hero-information-circle" class="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <p class="text-xs text-gray-600">
                  This defines what parameters your process expects to receive. Leave empty if no specific parameters are required.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Code Editor Section -->
        <div class="mt-8">
          <div class="border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 px-4 py-3 border-b border-gray-200">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-sm font-medium text-gray-900 flex items-center">
                    <.icon name="hero-code-bracket" class="h-4 w-4 mr-2 text-purple-600" />
                    Elixir Process Code
                  </h3>
                  <p class="text-xs text-gray-600 mt-1">Write your process logic in Elixir. Must include a process/1 function.</p>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                    Elixir
                  </span>
                  <%= if @action == :new do %>
                    <button
                      type="button"
                      onclick={"document.getElementById('#{@code_editor_id}').value = `#{String.replace(@default_code, "`", "\\`")}`"}
                      class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                    >
                      Use Template
                    </button>
                  <% end %>
                </div>
              </div>
            </div>
            <div class="bg-white">
              <!-- Code Editor Toolbar -->
              <div class="flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center space-x-4 text-xs text-gray-600">
                  <span class="flex items-center">
                    <.icon name="hero-play" class="h-3 w-3 mr-1" />
                    Required: process/1 function
                  </span>
                  <span class="flex items-center">
                    <.icon name="hero-arrow-right" class="h-3 w-3 mr-1" />
                    Return: ok-result or error-reason
                  </span>
                </div>
                <div class="flex items-center space-x-2 text-xs">
                  <span class="text-gray-500">Lines:</span>
                  <span class="font-mono text-gray-700" id={"#{@code_editor_id}-lines"}>1</span>
                </div>
              </div>

              <!-- Code Editor -->
              <div class="relative">
                <textarea
                  name="dynamic_process[code]"
                  id={@code_editor_id}
                  class="w-full h-96 p-4 text-sm font-mono border-0 focus:ring-0 focus:outline-none resize-none bg-white"
                  style="tab-size: 2;"
                  spellcheck="false"
                  placeholder={@default_code}
                  onKeyDown="handleCodeEditorKeyDown(event)"
                  onInput={"updateLineCount('#{@code_editor_id}')"}
                >
                  <%= Map.get(@changeset.data, :code) || (@action == :new && @default_code) || "" %>
                </textarea>

                <!-- Line numbers (simplified) -->
                <div class="absolute left-0 top-0 bottom-0 w-12 bg-gray-50 border-r border-gray-200 flex flex-col text-xs text-gray-400 font-mono pt-4">
                  <div class="flex-1 space-y-0" id={"#{@code_editor_id}-line-numbers"}>
                    <!-- Line numbers will be populated by JavaScript -->
                  </div>
                </div>
                <style>
                  #<%= @code_editor_id %> {
                    padding-left: 3.5rem;
                  }
                </style>
              </div>

              <!-- Code Help -->
              <div class="px-4 py-3 bg-blue-50 border-t border-blue-200">
                <details class="group">
                  <summary class="flex items-center cursor-pointer text-sm font-medium text-blue-900 hover:text-blue-700">
                    <.icon name="hero-question-mark-circle" class="h-4 w-4 mr-2" />
                    Code Examples & Help
                    <.icon name="hero-chevron-down" class="h-3 w-3 ml-auto group-open:rotate-180 transition-transform" />
                  </summary>
                  <div class="mt-3 space-y-3">
                    <div class="bg-white rounded border p-3">
                      <h4 class="text-xs font-semibold text-gray-900 mb-2">Basic Structure:</h4>
                      <pre class="text-xs text-gray-700 bg-gray-50 p-2 rounded overflow-x-auto"><code>def process(input) do
  # Your logic here
  ok-modified_input
end</code></pre>
                    </div>
                    <div class="bg-white rounded border p-3">
                      <h4 class="text-xs font-semibold text-gray-900 mb-2">Available Functions:</h4>
                      <ul class="text-xs text-gray-700 space-y-1">
                        <li><code>Map.get/2</code>, <code>Map.put/3</code> - Map operations</li>
                        <li><code>DateTime.utc_now/0</code> - Current timestamp</li>
                        <li><code>Jason.encode!/1</code>, <code>Jason.decode!/1</code> - JSON operations</li>
                        <li><code>String</code> module functions - String manipulation</li>
                      </ul>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        <:actions>
          <div class="flex items-center justify-between">
            <.link
              patch={@patch}
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </.link>
            <.button type="submit" phx-disable-with="Saving..." class="ml-3">
              <%= if @action == :edit, do: "Update Process", else: "Create Process" %>
            </.button>
          </div>
        </:actions>
      </.simple_form>

      <script>
        // Code editor enhancements
        function handleCodeEditorKeyDown(event) {
          if (event.key === 'Tab') {
            event.preventDefault();
            const textarea = event.target;
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;

            // Insert tab (2 spaces)
            textarea.value = textarea.value.substring(0, start) + '  ' + textarea.value.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + 2;
          }
        }

        function updateLineCount(editorId) {
          const textarea = document.getElementById(editorId);
          const lines = textarea.value.split('\n').length;
          document.getElementById(editorId + '-lines').textContent = lines;

          // Update line numbers
          const lineNumbers = document.getElementById(editorId + '-line-numbers');
          lineNumbers.innerHTML = '';
          for (let i = 1; i <= lines; i++) {
            const div = document.createElement('div');
            div.className = 'text-right pr-2 leading-5';
            div.textContent = i;
            lineNumbers.appendChild(div);
          }
        }

        // Initialize line numbers
        document.addEventListener('DOMContentLoaded', function() {
          updateLineCount('<%= @code_editor_id %>');
        });
      </script>
    </div>
    """
  end

  defp default_process_code do
    """
def process(input) do
  # Default process implementation
  {:ok, input}
end
"""
  end
end
