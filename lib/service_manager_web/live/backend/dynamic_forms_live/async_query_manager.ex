defmodule ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager do
  @moduledoc """
  Central module for managing background database queries in Dynamic Forms LiveViews.
  
  This module provides a consistent API for:
  - Running expensive database queries in background tasks
  - Managing task lifecycle (creation, monitoring, cleanup)
  - Handling task completion and errors
  - Providing loading states and progressive updates
  """

  @doc """
  Starts an async task for a single query.
  
  ## Parameters
  - `query_key` - Unique identifier for the query (atom or string)
  - `query_fun` - Function that performs the database query
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with task tracking
  """
  def start_async_query(query_key, query_fun, socket) do
    # Cancel existing task if any
    cancel_task(socket, query_key)
    
    # Start new task
    task = Task.async(fn ->
      try do
        result = query_fun.()
        {query_key, {:ok, result}}
      rescue
        error ->
          {query_key, {:error, error}}
      end
    end)
    
    # Track the task
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    updated_tasks = Map.put(current_tasks, query_key, task)
    
    Phoenix.Component.assign(socket, :async_tasks, updated_tasks)
  end

  @doc """
  Starts multiple async queries in parallel.
  
  ## Parameters
  - `queries` - List of {query_key, query_fun} tuples
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with all tasks tracking
  """
  def start_async_queries(queries, socket) do
    Enum.reduce(queries, socket, fn {query_key, query_fun}, acc_socket ->
      start_async_query(query_key, query_fun, acc_socket)
    end)
  end

  @doc """
  Starts async queries for multiple items (like processes, routes, etc.).
  
  ## Parameters
  - `items` - List of items to process
  - `query_prefix` - Prefix for query keys (e.g., "process_stats")
  - `query_fun` - Function that takes an item and returns query result
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with all item tasks tracking
  """
  def start_async_item_queries(items, query_prefix, query_fun, socket) do
    # Cancel existing tasks with this prefix
    cancel_tasks_with_prefix(socket, query_prefix)
    
    # Start tasks for each item
    tasks = items
    |> Enum.map(fn item ->
      query_key = "#{query_prefix}_#{item.id}"
      task = Task.async(fn ->
        try do
          result = query_fun.(item)
          {query_key, item.id, {:ok, result}}
        rescue
          error ->
            {query_key, item.id, {:error, error}}
        end
      end)
      {query_key, task}
    end)
    |> Enum.into(%{})
    
    # Merge with existing tasks
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    updated_tasks = Map.merge(current_tasks, tasks)
    
    Phoenix.Component.assign(socket, :async_tasks, updated_tasks)
  end

  @doc """
  Handles async task completion message.
  
  ## Parameters
  - `ref` - Task reference from message
  - `result` - Task result
  - `socket` - LiveView socket
  - `handler_fun` - Function to handle the result: `fn socket, query_key, result -> updated_socket end`
  
  ## Returns
  Updated socket with task removed and result processed
  """
  def handle_async_result(ref, result, socket, handler_fun) when is_reference(ref) do
    Process.demonitor(ref, [:flush])
    
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    
    # Find and remove the completed task
    {completed_task, remaining_tasks} = find_and_remove_task_by_ref(current_tasks, ref)
    
    updated_socket = Phoenix.Component.assign(socket, :async_tasks, remaining_tasks)
    
    case {completed_task, result} do
      {nil, _} ->
        # Task not found, just return socket
        updated_socket
        
      {query_key, {query_key, query_result}} ->
        # Single query result
        handler_fun.(updated_socket, query_key, query_result)
        
      {query_key, {query_key, item_id, query_result}} ->
        # Item query result
        handler_fun.(updated_socket, query_key, item_id, query_result)
        
      _ ->
        # Unexpected result format
        updated_socket
    end
  end

  @doc """
  Handles async task failure (DOWN message).
  
  ## Parameters
  - `ref` - Task reference
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with failed task removed
  """
  def handle_async_failure(ref, socket) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    {_completed_task, remaining_tasks} = find_and_remove_task_by_ref(current_tasks, ref)
    Phoenix.Component.assign(socket, :async_tasks, remaining_tasks)
  end

  @doc """
  Cancels all async tasks.
  
  ## Parameters
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with all tasks cancelled
  """
  def cancel_all_tasks(socket) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    
    Enum.each(current_tasks, fn {_key, task} ->
      Task.shutdown(task, :brutal_kill)
    end)
    
    Phoenix.Component.assign(socket, :async_tasks, %{})
  end

  @doc """
  Cancels a specific async task.
  
  ## Parameters
  - `socket` - LiveView socket
  - `query_key` - Key of the task to cancel
  
  ## Returns
  Updated socket with task cancelled
  """
  def cancel_task(socket, query_key) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    
    case Map.get(current_tasks, query_key) do
      nil ->
        socket
      task ->
        Task.shutdown(task, :brutal_kill)
        updated_tasks = Map.delete(current_tasks, query_key)
        Phoenix.Component.assign(socket, :async_tasks, updated_tasks)
    end
  end

  @doc """
  Cancels all tasks with a specific prefix.
  
  ## Parameters
  - `socket` - LiveView socket
  - `prefix` - Prefix to match
  
  ## Returns
  Updated socket with matching tasks cancelled
  """
  def cancel_tasks_with_prefix(socket, prefix) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    
    {to_cancel, to_keep} = Enum.split_with(current_tasks, fn {key, _task} ->
      String.starts_with?(to_string(key), prefix)
    end)
    
    # Cancel the matching tasks
    Enum.each(to_cancel, fn {_key, task} ->
      Task.shutdown(task, :brutal_kill)
    end)
    
    Phoenix.Component.assign(socket, :async_tasks, Map.new(to_keep))
  end

  @doc """
  Checks if there are any pending async tasks.
  
  ## Parameters
  - `socket` - LiveView socket
  
  ## Returns
  Boolean indicating if any tasks are still running
  """
  def has_pending_tasks?(socket) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    not Enum.empty?(current_tasks)
  end

  @doc """
  Checks if there are any pending async tasks with a specific prefix.
  
  ## Parameters
  - `socket` - LiveView socket
  - `prefix` - Prefix to check
  
  ## Returns
  Boolean indicating if any matching tasks are still running
  """
  def has_pending_tasks_with_prefix?(socket, prefix) do
    current_tasks = Map.get(socket.assigns, :async_tasks, %{})
    
    Enum.any?(current_tasks, fn {key, _task} ->
      String.starts_with?(to_string(key), prefix)
    end)
  end

  @doc """
  Initializes async task tracking in socket assigns.
  
  ## Parameters
  - `socket` - LiveView socket
  
  ## Returns
  Socket with async_tasks assign initialized
  """
  def init_async_tracking(socket) do
    Phoenix.Component.assign(socket, :async_tasks, %{})
  end

  @doc """
  Starts async loading of process steps with background processing.
  
  ## Parameters
  - `process_id` - ID of the process to load steps for
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with step loading task
  """
  def load_process_steps_async(process_id, socket) do
    start_async_query("load_process_steps", fn ->
      alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager
      CodeStepManager.get_process_steps(process_id)
    end, socket)
  end

  @doc """
  Starts async code generation from step tree.
  
  ## Parameters
  - `process_id` - ID of the process to generate code for
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with code generation task
  """
  def generate_code_async(process_id, socket) do
    start_async_query("generate_code", fn ->
      alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager
      CodeStepManager.generate_code_from_steps(process_id)
    end, socket)
  end

  @doc """
  Starts async validation of step hierarchy.
  
  ## Parameters
  - `process_id` - ID of the process to validate
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with validation task
  """
  def validate_steps_async(process_id, socket) do
    start_async_query("validate_steps", fn ->
      alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager
      CodeStepManager.validate_step_hierarchy(process_id)
    end, socket)
  end

  @doc """
  Starts async batch update of multiple steps.
  
  ## Parameters
  - `steps_data` - List of {step_id, attrs} tuples
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with batch update task
  """
  def batch_update_steps_async(steps_data, socket) do
    start_async_query("batch_update_steps", fn ->
      alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager
      CodeStepManager.batch_update_steps(steps_data)
    end, socket)
  end

  @doc """
  Starts async loading of step templates.
  
  ## Parameters
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with template loading task
  """
  def load_step_templates_async(socket) do
    start_async_query("load_step_templates", fn ->
      # This will be implemented when we create the step templates module
      {:ok, [
        %{type: "function_open", label: "Function Definition", template: "def %{name}(%{params}) do"},
        %{type: "function_close", label: "End Function", template: "end"},
        %{type: "line", label: "Variable Assignment", template: "%{var} = %{value}"},
        %{type: "block_open", label: "If Statement", template: "if %{condition} do"},
        %{type: "comment", label: "Comment", template: "# %{comment}"},
        %{type: "import", label: "Import Module", template: "import %{module}"}
      ]}
    end, socket)
  end

  @doc """
  Starts async import of code string into steps.
  
  ## Parameters
  - `process_id` - ID of the process
  - `code_string` - Code to parse into steps
  - `user_id` - ID of the user performing the import
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with import task
  """
  def import_code_to_steps_async(process_id, code_string, user_id, socket) do
    start_async_query("import_code_to_steps", fn ->
      alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager
      CodeStepManager.import_code_to_steps(process_id, code_string, user_id)
    end, socket)
  end

  @doc """
  Starts async loading of routes that use a specific process.
  
  ## Parameters
  - `process_id` - ID of the process to find routes for
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with routes loading task
  """
  def load_routes_using_process_async(process_id, socket) do
    start_async_query("routes_using_process", fn ->
      get_routes_using_process(process_id)
    end, socket)
  end

  @doc """
  Starts async loading of plugins that use a specific process.
  
  ## Parameters
  - `process_id` - ID of the process to find plugins for
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with plugins loading task
  """
  def load_plugins_using_process_async(process_id, socket) do
    start_async_query("plugins_using_process", fn ->
      get_plugins_using_process(process_id)
    end, socket)
  end

  @doc """
  Starts async loading of both routes and plugins that use a specific process.
  
  ## Parameters
  - `process_id` - ID of the process to find dependencies for
  - `socket` - LiveView socket
  
  ## Returns
  Updated socket with both dependency loading tasks
  """
  def load_process_dependencies_async(process_id, socket) do
    start_async_queries([
      {"routes_using_process", fn -> get_routes_using_process(process_id) end},
      {"plugins_using_process", fn -> get_plugins_using_process(process_id) end}
    ], socket)
  end

  # Database query functions (moved from process_edit_live.ex)
  
  defp get_routes_using_process(process_id) do
    import Ecto.Query
    alias ServiceManager.Routing.DynamicRoute
    alias ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink

    query = from r in DynamicRoute,
            join: rpl in RouteProcessLink, on: rpl.route_id == r.id,
            where: rpl.initial_process_id == ^process_id,
            select: r

    ServiceManager.Repo.all(query)
  end

  defp get_plugins_using_process(process_id) do
    import Ecto.Query
    alias ServiceManager.Schemas.Dynamic.Processes.{ProcessChainLink, ProcessManager}

    query = from pcl in ProcessChainLink,
            where: pcl.target_process_id == ^process_id and not pcl.is_root,
            select: pcl.source_process_id

    source_ids = ServiceManager.Repo.all(query)
    all_processes = ProcessManager.list_processes()
    Enum.filter(all_processes, fn p -> p.id in source_ids end)
  end

  # Private helper functions

  defp find_and_remove_task_by_ref(tasks, ref) do
    case Enum.find(tasks, fn {_key, task} -> task.ref == ref end) do
      nil ->
        {nil, tasks}
      {key, _task} ->
        {key, Map.delete(tasks, key)}
    end
  end
end