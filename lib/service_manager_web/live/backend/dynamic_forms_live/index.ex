defmodule ServiceManagerWeb.Backend.DynamicFormsLive.Index do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Routing.DynamicRoute
  alias ServiceManager.Forms.DynamicForm
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess
  import ServiceManagerWeb.Utilities.PermissionHelpers

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms")

    if connected?(socket) do
      :timer.send_interval(30000, :refresh)
    end

    routes = list_routes()
    forms = list_forms()
    processes = list_processes()

    socket =
      socket
      |> assign(:routes, routes)
      |> assign(:forms, forms)
      |> assign(:processes, processes)
      |> assign(:route_forms, list_route_forms())
      |> assign(:filter_params, %{
        "http_method" => "",
        "status" => "",
        "search" => "",
        "page" => 1,
        "page_size" => 10,
        "sort_field" => "inserted_at",
        "sort_order" => "desc"
      })
      |> assign(:pagination, %{
        page_number: 1,
        page_size: 10,
        total_entries: length(routes),
        total_pages: ceil(length(routes) / 10)
      })
      |> assign(:selected_column, "inserted_at")
      |> assign(:active_tab, "routes")

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Dynamic Routes & Forms")
    |> assign(:route, nil)
    |> assign(:form, nil)
    |> assign(:route_form, nil)
  end

  defp apply_action(socket, :new_route, _params) do
    socket
    |> assign(:page_title, "New Dynamic Route")
    |> assign(:route, %DynamicRoute{})
  end

  defp apply_action(socket, :edit_route, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Dynamic Route")
    |> assign(:route, DynamicRouteManager.get_route(id))
  end

  defp apply_action(socket, :new_form, _params) do
    socket
    |> assign(:page_title, "New Dynamic Form")
    |> assign(:form, %DynamicForm{})
  end

  defp apply_action(socket, :edit_form, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Dynamic Form")
    |> assign(:form, DynamicFormsManager.get_form(id))
  end

  defp apply_action(socket, :link_form, _params) do
    socket
    |> assign(:page_title, "Link Form to Route")
    |> assign(:route_form, %{route_id: nil, form_id: nil})
  end
  
  defp apply_action(socket, :new_process, _params) do
    socket
    |> assign(:page_title, "New Dynamic Process")
    |> assign(:process, %DynamicProcess{})
  end

  defp apply_action(socket, :edit_process, %{"id" => id}) do
    case ProcessManager.get_process(id) do
      {:ok, process} ->
        socket
        |> assign(:page_title, "Edit Dynamic Process")
        |> assign(:process, process)
      
      {:error, _} ->
        socket
        |> put_flash(:error, "Process not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms")
    end
  end

  defp apply_action(socket, :chain_process, %{"id" => id}) do
    case ProcessManager.get_process(id) do
      {:ok, process} ->
        socket
        |> assign(:page_title, "Configure Process Chain")
        |> assign(:source_process, process)
      
      {:error, _} ->
        socket
        |> put_flash(:error, "Process not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms")
    end
  end

  defp apply_action(socket, :link_process, _params) do
    socket
    |> assign(:page_title, "Link Process to Route")
    |> assign(:route_id, nil)
  end

  @impl true
  def handle_info(:refresh, socket) do
    routes = list_routes()
    forms = list_forms()
    processes = list_processes()

    socket =
      socket
      |> assign(:routes, routes)
      |> assign(:forms, forms)
      |> assign(:processes, processes)
      |> assign(:route_forms, list_route_forms())

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter", params, socket) do
    filtered_items = 
      case socket.assigns.active_tab do
        "routes" -> apply_route_filters(params)
        "forms" -> apply_form_filters(params)
        _ -> []
      end
    
    total_entries = length(filtered_items)

    socket =
      socket
      |> assign(
        case socket.assigns.active_tab do
          "routes" -> :routes
          "forms" -> :forms
          _ -> :routes
        end,
        filtered_items
      )
      |> assign(:filter_params, params)
      |> assign(:pagination, %{
        page_number: 1,
        page_size: socket.assigns.pagination.page_size,
        total_entries: total_entries,
        total_pages: ceil(total_entries / socket.assigns.pagination.page_size)
      })

    {:noreply, socket}
  end

  @impl true
  def handle_event("change-tab", %{"tab" => tab}, socket) do
    items = 
      case tab do
        "routes" -> list_routes()
        "forms" -> list_forms()
        "route_forms" -> list_route_forms()
        "processes" -> list_processes()
        _ -> []
      end
    
    total_entries = length(items)

    socket =
      socket
      |> assign(:active_tab, tab)
      |> assign(:pagination, %{
        page_number: 1,
        page_size: socket.assigns.pagination.page_size,
        total_entries: total_entries,
        total_pages: ceil(total_entries / socket.assigns.pagination.page_size)
      })

    {:noreply, socket}
  end

  @impl true
  def handle_event("unlink-form", %{"route-id" => route_id, "form-id" => form_id}, socket) do
    route_id = String.to_integer(route_id)
    form_id = String.to_integer(form_id)
    
    # Unlink the form from the route
    DynamicFormsManager.unlink_form_from_route(route_id, form_id)
    
    # Refresh the route_forms list
    route_forms = list_route_forms()
    
    socket =
      socket
      |> assign(:route_forms, route_forms)
      |> put_flash(:info, "Form unlinked from route successfully")
    
    {:noreply, socket}
  end

  defp list_routes do
    DynamicRouteManager.list_routes()
  end

  defp list_forms do
    DynamicFormsManager.list_forms()
  end

  defp list_route_forms do
    # Get all routes with their associated forms
    routes = DynamicRouteManager.list_routes()
    
    Enum.flat_map(routes, fn route ->
      forms = DynamicFormsManager.get_route_forms(route.id)
      
      if Enum.empty?(forms) do
        [%{route: route, form: nil}]
      else
        Enum.map(forms, fn form ->
          %{route: route, form: form}
        end)
      end
    end)
  end
  
  defp list_processes do
    ProcessManager.list_processes()
  end

  defp apply_route_filters(params) do
    routes = list_routes()
    
    routes
    |> filter_by_method(params["http_method"])
    |> filter_by_status(params["status"])
    |> filter_by_search(params["search"])
  end

  defp apply_form_filters(params) do
    forms = list_forms()
    
    forms
    |> filter_forms_by_method(params["http_method"])
    |> filter_forms_by_required(params["status"])
    |> filter_forms_by_search(params["search"])
  end

  defp filter_by_method(routes, ""), do: routes
  defp filter_by_method(routes, nil), do: routes

  defp filter_by_method(routes, method) do
    Enum.filter(routes, fn route -> route.method == method end)
  end

  defp filter_by_status(routes, ""), do: routes
  defp filter_by_status(routes, nil), do: routes

  defp filter_by_status(routes, "enabled") do
    Enum.filter(routes, fn route -> route.enabled end)
  end

  defp filter_by_status(routes, "disabled") do
    Enum.filter(routes, fn route -> !route.enabled end)
  end

  defp filter_by_search(routes, ""), do: routes
  defp filter_by_search(routes, nil), do: routes

  defp filter_by_search(routes, search) do
    search = "%#{search}%"
    
    Enum.filter(routes, fn route ->
      String.contains?(String.downcase(route.name), String.downcase(search)) ||
      String.contains?(String.downcase(route.path), String.downcase(search))
    end)
  end

  defp filter_forms_by_method(forms, ""), do: forms
  defp filter_forms_by_method(forms, nil), do: forms

  defp filter_forms_by_method(forms, method) do
    Enum.filter(forms, fn form -> form.http_method == method end)
  end

  defp filter_forms_by_required(forms, ""), do: forms
  defp filter_forms_by_required(forms, nil), do: forms

  defp filter_forms_by_required(forms, "required") do
    Enum.filter(forms, fn form -> form.required end)
  end

  defp filter_forms_by_required(forms, "optional") do
    Enum.filter(forms, fn form -> !form.required end)
  end

  defp filter_forms_by_search(forms, ""), do: forms
  defp filter_forms_by_search(forms, nil), do: forms

  defp filter_forms_by_search(forms, search) do
    search = String.downcase(search)
    
    Enum.filter(forms, fn form ->
      String.contains?(String.downcase(form.name), search) ||
      String.contains?(String.downcase(form.description || ""), search)
    end)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.header>
      Dynamic Routes & Forms Management
      <:actions>
        <div class="flex space-x-2">
          <.link
            :if={@active_tab == "routes"}
            patch={~p"/mobileBanking/dynamic-forms/routes/new"}
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            New Route
          </.link>
          <.link
            :if={@active_tab == "forms"}
            patch={~p"/mobileBanking/dynamic-forms/forms/new"}
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
          >
            New Form
          </.link>
          <.link
            :if={@active_tab == "route_forms"}
            patch={~p"/mobileBanking/dynamic-forms/link"}
            class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
          >
            Link Form to Route
          </.link>
          <.link
            :if={@active_tab == "processes"}
            patch={~p"/mobileBanking/dynamic-forms/processes/new"}
            class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
          >
            New Plugin
          </.link>
        </div>
      </:actions>
    </.header>

    <div class="mb-4 border-b border-gray-200">
      <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
        <li class="mr-2">
          <a 
            href="#" 
            phx-click="change-tab" 
            phx-value-tab="routes"
            class={[
              "inline-block p-4 rounded-t-lg",
              @active_tab == "routes" && "text-blue-600 border-b-2 border-blue-600 active",
              @active_tab != "routes" && "hover:text-gray-600 hover:border-gray-300"
            ]}
          >
            Routes
          </a>
        </li>
        <li class="mr-2">
          <a 
            href="#" 
            phx-click="change-tab" 
            phx-value-tab="forms"
            class={[
              "inline-block p-4 rounded-t-lg",
              @active_tab == "forms" && "text-blue-600 border-b-2 border-blue-600 active",
              @active_tab != "forms" && "hover:text-gray-600 hover:border-gray-300"
            ]}
          >
            Forms
          </a>
        </li>
        <li class="mr-2">
          <a 
            href="#" 
            phx-click="change-tab" 
            phx-value-tab="route_forms"
            class={[
              "inline-block p-4 rounded-t-lg",
              @active_tab == "route_forms" && "text-blue-600 border-b-2 border-blue-600 active",
              @active_tab != "route_forms" && "hover:text-gray-600 hover:border-gray-300"
            ]}
          >
            Route-Form Links
          </a>
        </li>
        <li class="mr-2">
          <a 
            href="#" 
            phx-click="change-tab" 
            phx-value-tab="processes"
            class={[
              "inline-block p-4 rounded-t-lg",
              @active_tab == "processes" && "text-blue-600 border-b-2 border-blue-600 active",
              @active_tab != "processes" && "hover:text-gray-600 hover:border-gray-300"
            ]}
          >
            Plugins
          </a>
        </li>
      </ul>
    </div>

    <div :if={@active_tab == "routes"} id="routes-tab" class="p-4">
      <.table
        id="routes"
        rows={@routes}
        filter_params={@filter_params}
        pagination={@pagination}
        selected_column={@selected_column}
      >
        <:col :let={route} label="Name">
          <div class="text-sm font-medium"><%= route.name %></div>
        </:col>

        <:col :let={route} label="Method">
          <span class={method_color(route.method)}>
            <%= route.method %>
          </span>
        </:col>

        <:col :let={route} label="Path">
          <div class="text-sm font-mono"><%= route.path %></div>
        </:col>

        <:col :let={route} label="Status">
          <span class={status_color(route.enabled)}>
            <%= if route.enabled, do: "Enabled", else: "Disabled" %>
          </span>
        </:col>

        <:col :let={route} label="Created">
          <div class="text-sm whitespace-nowrap">
            <%= Calendar.strftime(route.inserted_at, "%Y-%m-%d %H:%M:%S") %>
          </div>
        </:col>

        <:action :let={route}>
          <div class="flex space-x-2">
            <.link
              patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
              class="text-blue-600 hover:text-blue-900"
            >
              Edit
            </.link>
          </div>
        </:action>
      </.table>
    </div>

    <div :if={@active_tab == "forms"} id="forms-tab" class="p-4">
      <.table
        id="forms"
        rows={@forms}
        filter_params={@filter_params}
        pagination={@pagination}
        selected_column={@selected_column}
      >
        <:col :let={form} label="Name">
          <div class="text-sm font-medium"><%= form.name %></div>
        </:col>

        <:col :let={form} label="Description">
          <div class="text-sm"><%= form.description %></div>
        </:col>

        <:col :let={form} label="HTTP Method">
          <span class={method_color(form.http_method)}>
            <%= form.http_method %>
          </span>
        </:col>

        <:col :let={form} label="Required">
          <span class={required_color(form.required)}>
            <%= if form.required, do: "Required", else: "Optional" %>
          </span>
        </:col>

        <:col :let={form} label="Fields">
          <div class="text-sm">
            <%= length(form.form["fields"]) %> fields
          </div>
        </:col>

        <:col :let={form} label="Created">
          <div class="text-sm whitespace-nowrap">
            <%= Calendar.strftime(form.inserted_at, "%Y-%m-%d %H:%M:%S") %>
          </div>
        </:col>

        <:action :let={form}>
          <div class="flex space-x-2">
            <.link
              patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
              class="text-blue-600 hover:text-blue-900"
            >
              Edit
            </.link>
          </div>
        </:action>
      </.table>
    </div>

    <div :if={@active_tab == "route_forms"} id="route-forms-tab" class="p-4">
      <.table
        id="route_forms"
        rows={@route_forms}
        filter_params={@filter_params}
        pagination={@pagination}
        selected_column={@selected_column}
      >
        <:col :let={link} label="Route">
          <div class="text-sm font-medium"><%= link.route.name %></div>
          <div class="text-xs font-mono text-gray-500"><%= link.route.path %></div>
        </:col>

        <:col :let={link} label="Method">
          <span class={method_color(link.route.method)}>
            <%= link.route.method %>
          </span>
        </:col>

        <:col :let={link} label="Form">
          <%= if link.form do %>
            <div class="text-sm font-medium"><%= link.form.name %></div>
            <div class="text-xs text-gray-500"><%= link.form.description %></div>
          <% else %>
            <span class="text-gray-500">No form linked</span>
          <% end %>
        </:col>

        <:col :let={link} label="Form Method">
          <%= if link.form do %>
            <span class={method_color(link.form.http_method)}>
              <%= link.form.http_method %>
            </span>
          <% else %>
            <span class="text-gray-500">-</span>
          <% end %>
        </:col>

        <:col :let={link} label="Required">
          <%= if link.form do %>
            <span class={required_color(link.form.required)}>
              <%= if link.form.required, do: "Required", else: "Optional" %>
            </span>
          <% else %>
            <span class="text-gray-500">-</span>
          <% end %>
        </:col>

        <:action :let={link}>
          <%= if link.form do %>
            <div class="flex space-x-2">
              <button
                phx-click="unlink-form"
                phx-value-route-id={link.route.id}
                phx-value-form-id={link.form.id}
                class="text-red-600 hover:text-red-900"
                data-confirm="Are you sure you want to unlink this form from the route?"
              >
                Unlink
              </button>
            </div>
          <% else %>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/link?route_id=#{link.route.id}"}
              class="text-blue-600 hover:text-blue-900"
            >
              Link Form
            </.link>
          <% end %>
        </:action>
      </.table>
    </div>

    <div :if={@active_tab == "processes"} id="processes-tab" class="p-4">
      <.table
        id="processes"
        rows={@processes}
        filter_params={@filter_params}
        pagination={@pagination}
        selected_column={@selected_column}
      >
        <:col :let={process} label="Name">
          <div class="text-sm font-medium"><%= process.name %></div>
        </:col>

        <:col :let={process} label="Description">
          <div class="text-sm"><%= process.description %></div>
        </:col>

        <:col :let={process} label="Expected Params">
          <div class="text-sm font-mono">
            <%= if map_size(process.expected_params) > 0 do %>
              <%= Enum.map_join(Map.keys(process.expected_params), ", ", & &1) %>
            <% else %>
              <span class="text-gray-500">None</span>
            <% end %>
          </div>
        </:col>

        <:col :let={process} label="Created">
          <div class="text-sm whitespace-nowrap">
            <%= Calendar.strftime(process.inserted_at, "%Y-%m-%d %H:%M:%S") %>
          </div>
        </:col>

        <:action :let={process}>
          <%= if has_any_permission?(@current_user, [:update, :view], :dynamic_forms) do %>
            <div class="flex space-x-2">
              <%= if can_update?(@current_user, :dynamic_forms) do %>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/processes/#{process}/edit"}
                  class="text-blue-600 hover:text-blue-900"
                >
                  Edit
                </.link>
              <% end %>
              <%= if can_view?(@current_user, :dynamic_forms) do %>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/processes/#{process}/chain"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  Chain
                </.link>
              <% end %>
            </div>
          <% end %>
        </:action>
      </.table>
    </div>

    <.modal
      :if={@live_action in [:new_route, :edit_route]}
      id="route-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.RouteFormComponent}
        id={@route.id || :new}
        title={@page_title}
        action={@live_action}
        route={@route}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>

    <.modal
      :if={@live_action in [:new_form, :edit_form]}
      id="form-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.FormComponent}
        id={@form.id || :new}
        title={@page_title}
        action={@live_action}
        form={@form}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>

    <.modal
      :if={@live_action in [:link_form]}
      id="link-form-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.LinkFormComponent}
        id={:link}
        title={@page_title}
        action={@live_action}
        route_form={@route_form}
        routes={@routes}
        forms={@forms}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>

    <.modal
      :if={@live_action in [:new_process, :edit_process]}
      id="process-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.ProcessFormComponent}
        id={if assigns[:process], do: @process.id || :new, else: :new}
        title={@page_title}
        action={@live_action}
        process={assigns[:process] || %DynamicProcess{}}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>

    <.modal
      :if={@live_action in [:chain_process]}
      id="process-chain-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.ProcessChainComponent}
        id={@source_process.id}
        title={@page_title}
        source_process={@source_process}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>

    <.modal
      :if={@live_action in [:link_process]}
      id="process-link-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.ProcessLinkComponent}
        id={:process_link}
        title={@page_title}
        route_id={@route_id}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms"}
      />
    </.modal>
    """
  end

  defp method_color("GET"),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"

  defp method_color("POST"),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"

  defp method_color("PUT"),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"

  defp method_color("PATCH"),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800"

  defp method_color("DELETE"),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"

  defp method_color(_),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp status_color(true),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"

  defp status_color(false),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"

  defp required_color(true),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"

  defp required_color(false),
    do:
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
end
