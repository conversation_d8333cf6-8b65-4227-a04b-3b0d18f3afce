defmodule ServiceManagerWeb.Backend.DynamicFormsLive.FormsIndex do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Forms.{DynamicForm, DynamicFormsManager, FormWizard}
  alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service, as: V2

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/forms")
      |> assign(:view_mode, "grid")
      |> assign(:tree_view, true)
      |> assign(:expanded_methods, MapSet.new())
      |> assign(:expanded_required, MapSet.new())
      |> assign(:active_tab, "forms")
      |> assign(:selected_forms, [])
      |> assign(:wizard_builder_step, :select_forms)
      |> assign(:validation_assignments, %{})  # mobile_form_id => dynamic_form_id
      |> assign(:preview_device, "mobile")
      |> assign(:current_preview_wizard, nil)
      |> assign(:current_step_number, 1)
      |> assign(:wizard_form_data, %{})
      |> assign(:wizard_selected_screen_id, nil)
      |> assign(:wizard_selected_page_id, nil)
      |> assign(:wizard_screens, [])
      |> assign(:wizard_pages, [])
      |> assign(:wizard_filtered_mobile_forms, [])
      |> load_forms()
      |> load_mobile_forms()
      |> load_wizard_data()
      |> load_wizards()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case DynamicFormsManager.get_form(id) do
      nil ->
        socket
        |> put_flash(:error, "Form not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/forms")
      
      form ->
        socket
        |> assign(:page_title, "Edit Form")
        |> assign(:form, form)
    end
  end

  defp apply_action(socket, :edit_wizard, %{"id" => id}) do
    case DynamicFormsManager.get_wizard(id) do
      nil ->
        socket
        |> put_flash(:error, "Wizard not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/forms")
      
      wizard ->
        socket
        |> assign(:page_title, "Edit Wizard")
        |> assign(:wizard, wizard)
        |> assign(:wizard_builder_step, :edit_wizard)
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Form")
    |> assign(:form, %DynamicForm{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Forms")
    |> assign(:form, nil)
    |> assign(:wizard, nil)
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case DynamicFormsManager.get_form(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Form not found")
         |> load_forms()}
      
      form ->
        {:ok, _} = DynamicFormsManager.delete_form(form)
        {:noreply, 
         socket
         |> put_flash(:info, "Form deleted successfully")
         |> load_forms()}
    end
  end

  @impl true
  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  @impl true
  def handle_event("toggle_tree_view", _params, socket) do
    {:noreply, assign(socket, :tree_view, !socket.assigns.tree_view)}
  end

  @impl true
  def handle_event("toggle_method", %{"method" => method}, socket) do
    expanded_methods = socket.assigns.expanded_methods
    
    updated_expanded = if MapSet.member?(expanded_methods, method) do
      MapSet.delete(expanded_methods, method)
    else
      MapSet.put(expanded_methods, method)
    end
    
    {:noreply, assign(socket, :expanded_methods, updated_expanded)}
  end

  @impl true
  def handle_event("toggle_required", %{"required" => required, "method" => method}, socket) do
    required_key = "#{method}:#{required}"
    expanded_required = socket.assigns.expanded_required
    
    updated_expanded = if MapSet.member?(expanded_required, required_key) do
      MapSet.delete(expanded_required, required_key)
    else
      MapSet.put(expanded_required, required_key)
    end
    
    {:noreply, assign(socket, :expanded_required, updated_expanded)}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  @impl true
  def handle_event("toggle_form_selection", %{"form_id" => form_id}, socket) do
    # Keep form_id as string since mobile forms use UUIDs
    selected_forms = socket.assigns.selected_forms
    
    updated_selected = if form_id in selected_forms do
      List.delete(selected_forms, form_id)
    else
      [form_id | selected_forms]
    end
    
    {:noreply, assign(socket, :selected_forms, updated_selected)}
  end

  @impl true
  def handle_event("create_wizard_from_selection", _params, socket) do
    case socket.assigns.selected_forms do
      [] -> 
        {:noreply, put_flash(socket, :error, "Please select at least one mobile form to create a wizard")}
      selected_forms ->
        socket =
          socket
          |> assign(:wizard_builder_step, :assign_validations)
          |> assign(:wizard_forms, get_selected_mobile_form_data(selected_forms, socket.assigns.mobile_forms))
        
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_wizard", %{"wizard" => wizard_params}, socket) do
    wizard_attrs = Map.put(wizard_params, "created_by_id", socket.assigns.current_user.id)
    
    case DynamicFormsManager.create_wizard(wizard_attrs) do
      {:ok, wizard} ->
        # Add steps for selected mobile forms and create validation links
        socket.assigns.wizard_forms
        |> Enum.with_index(1)
        |> Enum.each(fn {form, step_number} ->
          DynamicFormsManager.add_mobile_wizard_step(wizard.id, form.id, step_number)
          
          # Create validation link if assigned
          case Map.get(form, :assigned_validation) do
            nil -> :ok
            assigned_validation ->
              DynamicFormsManager.link_mobile_form_to_validation(form.id, assigned_validation.id)
          end
        end)

        socket =
          socket
          |> put_flash(:info, "Wizard created successfully")
          |> assign(:wizard_builder_step, :select_forms)
          |> assign(:selected_forms, [])
          |> assign(:validation_assignments, %{})
          |> load_wizards()

        {:noreply, socket}
      
      {:error, changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to create wizard")}
    end
  end

  @impl true
  def handle_event("assign_validation", %{"mobile_form_id" => mobile_form_id, "dynamic_form_id" => dynamic_form_id}, socket) do
    current_assignments = socket.assigns.validation_assignments
    
    # Convert empty string to nil for removal
    new_dynamic_form_id = if dynamic_form_id == "", do: nil, else: String.to_integer(dynamic_form_id)
    
    updated_assignments = if new_dynamic_form_id do
      Map.put(current_assignments, mobile_form_id, new_dynamic_form_id)
    else
      Map.delete(current_assignments, mobile_form_id)
    end
    
    {:noreply, assign(socket, :validation_assignments, updated_assignments)}
  end

  @impl true
  def handle_event("proceed_to_wizard_config", _params, socket) do
    # Apply validation assignments to the wizard forms
    wizard_forms_with_validation = Enum.map(socket.assigns.wizard_forms, fn form ->
      case Map.get(socket.assigns.validation_assignments, to_string(form.id)) do
        nil -> form
        dynamic_form_id ->
          dynamic_form = Enum.find(socket.assigns.forms, &(&1.id == dynamic_form_id))
          Map.put(form, :assigned_validation, dynamic_form)
      end
    end)
    
    socket =
      socket
      |> assign(:wizard_builder_step, :configure_wizard)
      |> assign(:wizard_forms, wizard_forms_with_validation)
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_wizard_builder", _params, socket) do
    socket =
      socket
      |> assign(:wizard_builder_step, :select_forms)
      |> assign(:selected_forms, [])
      |> assign(:validation_assignments, %{})
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("preview_wizard", %{"id" => id}, socket) do
    case DynamicFormsManager.get_wizard(id) do
      nil ->
        {:noreply, put_flash(socket, :error, "Wizard not found")}
      
      wizard ->
        socket =
          socket
          |> assign(:current_preview_wizard, wizard)
          |> assign(:current_step_number, 1)
          |> assign(:wizard_form_data, %{})
          |> assign(:wizard_builder_step, :preview_wizard)
        
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("change_preview_device", %{"device" => device}, socket) do
    {:noreply, assign(socket, :preview_device, device)}
  end

  @impl true
  def handle_event("preview_next_step", _params, socket) do
    wizard = socket.assigns.current_preview_wizard
    current_step = socket.assigns.current_step_number
    total_steps = length(wizard.steps || [])
    
    next_step = if current_step < total_steps, do: current_step + 1, else: current_step
    
    {:noreply, assign(socket, :current_step_number, next_step)}
  end

  @impl true
  def handle_event("preview_prev_step", _params, socket) do
    current_step = socket.assigns.current_step_number
    prev_step = if current_step > 1, do: current_step - 1, else: 1
    
    {:noreply, assign(socket, :current_step_number, prev_step)}
  end

  @impl true
  def handle_event("close_preview", _params, socket) do
    socket =
      socket
      |> assign(:wizard_builder_step, :select_forms)
      |> assign(:current_preview_wizard, nil)
      |> assign(:current_step_number, 1)
      |> assign(:wizard_form_data, %{})
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_wizard_form_data", %{"form_data" => form_data}, socket) do
    {:noreply, assign(socket, :wizard_form_data, form_data)}
  end

  @impl true
  def handle_info({:change_preview_device, device}, socket) do
    {:noreply, assign(socket, :preview_device, device)}
  end

  @impl true
  def handle_info(:close_preview, socket) do
    socket =
      socket
      |> assign(:wizard_builder_step, :select_forms)
      |> assign(:current_preview_wizard, nil)
      |> assign(:current_step_number, 1)
      |> assign(:wizard_form_data, %{})
    
    {:noreply, socket}
  end

  @impl true
  def handle_info(:preview_prev_step, socket) do
    current_step = socket.assigns.current_step_number
    prev_step = if current_step > 1, do: current_step - 1, else: 1
    
    {:noreply, assign(socket, :current_step_number, prev_step)}
  end

  @impl true
  def handle_info(:preview_next_step, socket) do
    wizard = socket.assigns.current_preview_wizard
    current_step = socket.assigns.current_step_number
    total_steps = length(wizard.steps || [])
    
    next_step = if current_step < total_steps, do: current_step + 1, else: current_step
    
    {:noreply, assign(socket, :current_step_number, next_step)}
  end

  @impl true
  def handle_info(:refresh_wizard_data, socket) do
    # Reload wizards to get updated data
    {:noreply, load_wizards(socket)}
  end

  @impl true
  def handle_event("delete_wizard", %{"id" => id}, socket) do
    case DynamicFormsManager.get_wizard(id) do
      nil ->
        {:noreply, put_flash(socket, :error, "Wizard not found")}
      
      wizard ->
        {:ok, _} = DynamicFormsManager.delete_wizard(wizard)
        {:noreply, 
         socket
         |> put_flash(:info, "Wizard deleted successfully")
         |> load_wizards()}
    end
  end

  @impl true
  def handle_event("edit_wizard", %{"id" => id}, socket) do
    {:noreply, push_patch(socket, to: ~p"/mobileBanking/dynamic-forms/forms/wizard/#{id}/edit")}
  end

  @impl true
  def handle_event("wizard_filter_by_screen", %{"screen_id" => screen_id}, socket) do
    selected_screen_id = if screen_id == "", do: nil, else: screen_id
    
    # Get pages for selected screen
    pages = if selected_screen_id do
      case V2.list_pages(%{screen_id: selected_screen_id}) do
        {:ok, pages} -> pages
        _ -> []
      end
    else
      socket.assigns.wizard_pages
    end
    
    # Filter mobile forms
    filtered_forms = filter_mobile_forms(socket.assigns.mobile_forms, selected_screen_id, nil)
    
    socket =
      socket
      |> assign(:wizard_selected_screen_id, selected_screen_id)
      |> assign(:wizard_selected_page_id, nil)
      |> assign(:wizard_pages, pages)
      |> assign(:wizard_filtered_mobile_forms, filtered_forms)
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("wizard_filter_by_page", %{"page_id" => page_id}, socket) do
    selected_page_id = if page_id == "", do: nil, else: page_id
    
    # Filter mobile forms
    filtered_forms = filter_mobile_forms(socket.assigns.mobile_forms, socket.assigns.wizard_selected_screen_id, selected_page_id)
    
    socket =
      socket
      |> assign(:wizard_selected_page_id, selected_page_id)
      |> assign(:wizard_filtered_mobile_forms, filtered_forms)
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("wizard_clear_filters", _params, socket) do
    # Reset all wizard pages and clear filters
    with {:ok, pages} <- V2.list_pages(%{screen_id: nil}) do
      socket =
        socket
        |> assign(:wizard_selected_screen_id, nil)
        |> assign(:wizard_selected_page_id, nil)
        |> assign(:wizard_pages, pages)
        |> assign(:wizard_filtered_mobile_forms, socket.assigns.mobile_forms)
      
      {:noreply, socket}
    else
      _ ->
        socket =
          socket
          |> assign(:wizard_selected_screen_id, nil)
          |> assign(:wizard_selected_page_id, nil)
          |> assign(:wizard_pages, [])
          |> assign(:wizard_filtered_mobile_forms, socket.assigns.mobile_forms)
        
        {:noreply, socket}
    end
  end

  defp load_forms(socket) do
    forms = DynamicFormsManager.list_forms()
    assign(socket, :forms, forms)
  end

  defp load_mobile_forms(socket) do
    case DynamicFormsManager.list_mobile_forms_for_wizard() do
      {:ok, mobile_forms} ->
        assign(socket, :mobile_forms, mobile_forms)
      {:error, _reason} ->
        socket
        |> assign(:mobile_forms, [])
        |> put_flash(:error, "Failed to load mobile forms")
    end
  end

  defp load_wizards(socket) do
    wizards = DynamicFormsManager.list_wizards()
    assign(socket, :wizards, wizards)
  end

  defp load_wizard_data(socket) do
    with {:ok, screens} <- V2.list_screens(%{}),
         {:ok, pages} <- V2.list_pages(%{screen_id: nil}) do
      mobile_forms = Map.get(socket.assigns, :mobile_forms, [])
      
      socket
      |> assign(:wizard_screens, screens)
      |> assign(:wizard_pages, pages)
      |> assign(:wizard_filtered_mobile_forms, mobile_forms)
    else
      _ ->
        mobile_forms = Map.get(socket.assigns, :mobile_forms, [])
        
        socket
        |> assign(:wizard_screens, [])
        |> assign(:wizard_pages, [])
        |> assign(:wizard_filtered_mobile_forms, mobile_forms)
    end
  end

  defp get_selected_form_data(form_ids, all_forms) do
    all_forms
    |> Enum.filter(&(&1.id in form_ids))
    |> Enum.sort_by(&(&1.name))
  end

  defp get_selected_mobile_form_data(form_ids, all_mobile_forms) do
    # Convert string IDs to match mobile form UUIDs
    uuid_form_ids = Enum.map(form_ids, fn id ->
      if is_binary(id), do: id, else: to_string(id)
    end)
    
    all_mobile_forms
    |> Enum.filter(&(to_string(&1.id) in uuid_form_ids))
    |> Enum.sort_by(&(&1.name))
  end

  defp filter_mobile_forms(mobile_forms, selected_screen_id, selected_page_id) do
    mobile_forms
    |> Enum.filter(fn form ->
      cond do
        # No filters applied - show all forms
        is_nil(selected_screen_id) && is_nil(selected_page_id) ->
          true
        
        # Only screen filter applied
        !is_nil(selected_screen_id) && is_nil(selected_page_id) ->
          form.page && form.page.screen_id == selected_screen_id
        
        # Both screen and page filters applied
        !is_nil(selected_screen_id) && !is_nil(selected_page_id) ->
          form.page && form.page.screen_id == selected_screen_id && form.page.id == selected_page_id
        
        # Only page filter applied (shouldn't normally happen, but handle gracefully)
        is_nil(selected_screen_id) && !is_nil(selected_page_id) ->
          form.page && form.page.id == selected_page_id
      end
    end)
    |> Enum.sort_by(&(&1.name))
  end

  defp dynamic_grid_classes(form_count) do
    cond do
      form_count == 1 -> "grid grid-cols-1 gap-4 max-w-md mx-auto"
      form_count == 2 -> "grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-2xl mx-auto"
      form_count == 3 -> "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto"
      form_count == 4 -> "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-w-5xl mx-auto"
      form_count <= 6 -> "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 max-w-6xl mx-auto"
      form_count <= 8 -> "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-w-7xl mx-auto"
      true -> "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
    end
  end

  defp render_tree_structure(forms, expanded_methods, expanded_required) do
    # Create a hierarchical tree structure: HTTP Method > Required Status > Forms
    tree_data = forms
    |> Enum.group_by(fn form -> form.http_method || "Unknown" end)
    |> Enum.map(fn {method, method_forms} ->
      # Group forms within each method by required status
      required_groups = method_forms
      |> Enum.group_by(fn form -> if form.required, do: "Required", else: "Optional" end)
      |> Enum.map(fn {required_status, required_forms} ->
        %{
          name: required_status,
          type: :required_status,
          forms: required_forms,
          count: length(required_forms)
        }
      end)
      |> Enum.sort_by(fn group -> 
        # Sort so "Required" comes first
        if group.name == "Required", do: "0", else: "1"
      end)
      
      %{
        name: method,
        type: :method,
        required_groups: required_groups,
        forms: method_forms,
        count: length(method_forms)
      }
    end)
    |> Enum.sort_by(fn method ->
      # Sort methods in logical order
      case method.name do
        "GET" -> "1"
        "POST" -> "2"
        "PUT" -> "3"
        "PATCH" -> "4"
        "DELETE" -> "5"
        _ -> "9"
      end
    end)
    
    assigns = %{
      tree_data: tree_data,
      expanded_methods: expanded_methods,
      expanded_required: expanded_required
    }
    
    ~H"""
    <div class="space-y-2">
      <%= for method <- @tree_data do %>
        <div class="border border-gray-200 rounded-lg bg-white shadow-sm">
          <!-- Method Header (Always visible) -->
          <div 
            class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            phx-click="toggle_method"
            phx-value-method={method.name}
          >
            <div class="flex items-center space-x-3">
              <!-- Expand/Collapse Icon -->
              <%= if MapSet.member?(@expanded_methods, method.name) do %>
                <.icon name="hero-chevron-down" class="h-4 w-4 text-gray-500 transition-transform" />
              <% else %>
                <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-500 transition-transform" />
              <% end %>
              
              <!-- Method Icon and Name -->
              <span class={[
                "inline-flex items-center px-3 py-1 rounded text-sm font-medium",
                case method.name do
                  "GET" -> "bg-green-100 text-green-800"
                  "POST" -> "bg-blue-100 text-blue-800"
                  "PUT" -> "bg-yellow-100 text-yellow-800"
                  "PATCH" -> "bg-orange-100 text-orange-800"
                  "DELETE" -> "bg-red-100 text-red-800"
                  _ -> "bg-gray-100 text-gray-800"
                end
              ]}>
                <%= method.name %>
              </span>
            </div>
            
            <!-- Method Stats -->
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span><%= length(method.required_groups) %> groups</span>
              <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-700">
                <%= method.count %> form<%= if method.count != 1, do: "s" %>
              </span>
            </div>
          </div>
          
          <!-- Method Content (Expandable) -->
          <%= if MapSet.member?(@expanded_methods, method.name) do %>
            <div class="border-t border-gray-200 bg-gray-50">
              <%= for required_group <- method.required_groups do %>
                <div class="ml-6 border-l border-gray-200">
                  <!-- Required Status Header -->
                  <div 
                    class="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
                    phx-click="toggle_required"
                    phx-value-required={required_group.name}
                    phx-value-method={method.name}
                  >
                    <!-- Connection line -->
                    <div class="absolute left-0 top-1/2 w-4 h-px bg-gray-200"></div>
                    
                    <div class="flex items-center space-x-3 ml-4">
                      <!-- Expand/Collapse Icon for Required Group -->
                      <%= if MapSet.member?(@expanded_required, "#{method.name}:#{required_group.name}") do %>
                        <.icon name="hero-chevron-down" class="h-3 w-3 text-gray-400" />
                      <% else %>
                        <.icon name="hero-chevron-right" class="h-3 w-3 text-gray-400" />
                      <% end %>
                      
                      <!-- Required Status Icon and Name -->
                      <%= if required_group.name == "Required" do %>
                        <.icon name="hero-lock-closed" class="h-4 w-4 text-red-500" />
                        <span class="text-sm font-medium text-red-700"><%= required_group.name %></span>
                      <% else %>
                        <.icon name="hero-lock-open" class="h-4 w-4 text-green-500" />
                        <span class="text-sm font-medium text-green-700"><%= required_group.name %></span>
                      <% end %>
                    </div>
                    
                    <!-- Required Group Stats -->
                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                      <span class="bg-gray-200 px-2 py-1 rounded">
                        <%= required_group.count %> form<%= if required_group.count != 1, do: "s" %>
                      </span>
                    </div>
                  </div>
                  
                  <!-- Required Group Content (Forms List) -->
                  <%= if MapSet.member?(@expanded_required, "#{method.name}:#{required_group.name}") do %>
                    <div class="ml-8 pb-4">
                      <div class="space-y-2">
                        <%= for form <- required_group.forms do %>
                          <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md hover:shadow-sm transition-shadow relative">
                            <!-- Connection line -->
                            <div class="absolute -left-8 top-1/2 w-4 h-px bg-gray-200"></div>
                            
                            <div class="flex items-center space-x-3">
                              <!-- Form Icon -->
                              <.icon name="hero-document-text" class="h-4 w-4 text-indigo-500" />
                              
                              <!-- Form Info -->
                              <div>
                                <div class="flex items-center space-x-2">
                                  <span class="text-sm font-medium text-gray-900"><%= form.name %></span>
                                  <%= if form.required do %>
                                    <.icon name="hero-lock-closed" class="h-3 w-3 text-red-500" />
                                  <% end %>
                                </div>
                                <%= if form.description do %>
                                  <p class="text-xs text-gray-500 mt-1"><%= form.description %></p>
                                <% end %>
                              </div>
                            </div>
                            
                            <!-- Form Stats and Actions -->
                            <div class="flex items-center space-x-4">
                              <!-- Form Stats -->
                              <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                  <.icon name="hero-document" class="h-3 w-3 mr-1" />
                                  <%= length(form.form["fields"] || []) %> fields
                                </span>
                                <span class="flex items-center">
                                  <.icon name={form.required && "hero-lock-closed" || "hero-lock-open"} class="h-3 w-3 mr-1" />
                                  <%= form.required && "Required" || "Optional" %>
                                </span>
                              </div>
                              
                              <!-- Action Buttons -->
                              <div class="flex items-center space-x-2">
                                <.link
                                  patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
                                  class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs rounded text-gray-700 bg-white hover:bg-gray-50"
                                  title="Edit Form"
                                >
                                  <.icon name="hero-pencil" class="h-3 w-3" />
                                </.link>
                                
                                <button
                                  phx-click="delete"
                                  phx-value-id={form.id}
                                  data-confirm="Are you sure you want to delete this form?"
                                  class="inline-flex items-center px-2 py-1 border border-red-300 text-xs rounded text-red-700 bg-white hover:bg-red-50"
                                  title="Delete Form"
                                >
                                  <.icon name="hero-trash" class="h-3 w-3" />
                                </button>
                              </div>
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Forms"
        subtitle="Manage API forms and validation schemas"
        current_page={:forms}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Forms"}
        ]}
      />

      <!-- Tab Navigation -->
      <div class="bg-white border-b border-gray-200 shadow-sm">
        <div class="px-6">
          <nav class="-mb-px flex space-x-8">
            <button
              phx-click="switch_tab"
              phx-value-tab="forms"
              class={[
                "py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200",
                if(@active_tab == "forms", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
              ]}
            >
              <.icon name="hero-document-text" class="h-5 w-5 inline mr-2" />
              Forms
              <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
                <%= length(@forms) %>
              </span>
            </button>
            
            <button
              phx-click="switch_tab"
              phx-value-tab="wizard_builder"
              class={[
                "py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200",
                if(@active_tab == "wizard_builder", do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
              ]}
            >
              <.icon name="hero-squares-plus" class="h-5 w-5 inline mr-2" />
              Wizard Builder
              <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
                <%= length(assigns[:wizards] || []) %>
              </span>
            </button>
          </nav>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="px-6 py-6">
        <%= if @active_tab == "forms" do %>
          <%= render_forms_tab(assigns) %>
        <% else %>
          <%= render_wizard_builder_tab(assigns) %>
        <% end %>
      </div>
    </div>

    <.modal :if={@live_action in [:new, :edit]} id="form-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/forms")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.FormComponent}
        id={@form.id || :new}
        title={@page_title}
        action={@live_action}
        form={@form}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms/forms"}
      />
    </.modal>
    """
  end

  defp render_forms_tab(assigns) do
    ~H"""
    <%= if Enum.empty?(@forms) do %>
          <!-- Empty State -->
          <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <.icon name="hero-document-text" class="h-12 w-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No forms yet</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first API form</p>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/forms/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Create Form
            </.link>
          </div>
        <% else %>
          <!-- Header with View Controls -->
          <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-6">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">All Forms</h2>
                  <p class="text-sm text-gray-600">Manage your API forms and validation schemas</p>
                </div>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/forms/new"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Create Form
                </.link>
              </div>

              <!-- Controls -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-600"><%= length(@forms) %> forms</span>
                  
                  <!-- Tree View Toggle -->
                  <%= if @view_mode == "grid" do %>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-500">Layout:</span>
                      <button
                        phx-click="toggle_tree_view"
                        class={[
                          "inline-flex items-center px-2 py-1 rounded text-xs font-medium transition-colors border",
                          if @tree_view do
                            "border-indigo-300 bg-indigo-50 text-indigo-700"
                          else
                            "border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                          end
                        ]}
                        title="Toggle Tree View"
                      >
                        <.icon name="hero-bars-3" class="h-3 w-3 mr-1" />
                        <%= if @tree_view, do: "Tree", else: "Cards" %>
                      </button>
                    </div>
                  <% end %>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    phx-click="toggle_view"
                    phx-value-view="grid"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-squares-2x2" class="h-4 w-4" />
                  </button>
                  <button
                    phx-click="toggle_view"
                    phx-value-view="list"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-list-bullet" class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-6">
            <!-- Grid View -->
            <div :if={@view_mode == "grid"}>
              <%= if @tree_view do %>
                <%= render_tree_structure(@forms, @expanded_methods, @expanded_required) %>
              <% else %>
                <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              <%= for form <- @forms do %>
                <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  <!-- Header -->
                  <div class="p-3 border-b border-gray-100">
                    <div class="flex items-start justify-between mb-1">
                      <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= form.name %></h3>
                      <div class="flex space-x-1 flex-shrink-0">
                        <%= if form.required do %>
                          <.icon name="hero-lock-closed" class="h-3 w-3 text-red-500" />
                        <% end %>
                      </div>
                    </div>

                    <div class="flex items-center space-x-1 mb-2">
                      <span class={[
                        "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                        case form.http_method do
                          "GET" -> "bg-green-100 text-green-800"
                          "POST" -> "bg-blue-100 text-blue-800"
                          "PUT" -> "bg-yellow-100 text-yellow-800"
                          "PATCH" -> "bg-orange-100 text-orange-800"
                          "DELETE" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= form.http_method %>
                      </span>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                        Form
                      </span>
                    </div>

                    <p class="text-xs text-gray-600 line-clamp-2 mb-2">
                      <%= form.description || "No description provided" %>
                    </p>

                    <!-- Form Stats -->
                    <div class="flex items-center justify-between text-xs">
                      <div class="flex items-center space-x-2">
                        <span class="flex items-center text-gray-500">
                          <.icon name="hero-document" class="h-3 w-3 mr-1" />
                          <%= length(form.form["fields"] || []) %> fields
                        </span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class="flex items-center text-gray-500">
                          <.icon name={form.required && "hero-lock-closed" || "hero-lock-open"} class="h-3 w-3 mr-1" />
                          <%= form.required && "Required" || "Optional" %>
                        </span>
                        <span class="text-gray-400"><%= Calendar.strftime(form.inserted_at, "%b %d") %></span>
                      </div>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="p-2">
                    <div class="flex space-x-1">
                      <.link
                        patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
                        class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                        Edit
                      </.link>
                      <button
                        phx-click="delete"
                        phx-value-id={form.id}
                        data-confirm="Are you sure you want to delete this form?"
                        class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                      >
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </button>
                    </div>
                    </div>
                  </div>
                <% end %>
                </div>
              <% end %>
            </div>

            <!-- List View - Compact Table -->
            <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Form</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Method</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Fields</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Required</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for form <- @forms do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="min-w-0 flex-1">
                            <span class="text-sm font-medium text-gray-900 truncate max-w-32 block" title={form.name}>
                              <%= form.name %>
                            </span>
                            <p class="text-xs text-gray-500 truncate max-w-32" title={form.description}>
                              <%= form.description || "No description" %>
                            </p>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2 py-1 rounded text-sm font-medium",
                            case form.http_method do
                              "GET" -> "bg-green-100 text-green-800"
                              "POST" -> "bg-blue-100 text-blue-800"
                              "PUT" -> "bg-yellow-100 text-yellow-800"
                              "PATCH" -> "bg-orange-100 text-orange-800"
                              "DELETE" -> "bg-red-100 text-red-800"
                              _ -> "bg-gray-100 text-gray-800"
                            end
                          ]}>
                            <%= form.http_method %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <.icon name="hero-document" class="h-4 w-4 mr-1 text-gray-400" />
                            <span class="text-sm text-gray-900">
                              <%= length(form.form["fields"] || []) %> fields
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <.icon name={form.required && "hero-lock-closed" || "hero-lock-open"} class="h-4 w-4 mr-1 text-gray-400" />
                            <span class={[
                              "text-sm font-medium",
                              if(form.required, do: "text-red-800", else: "text-green-800")
                            ]}>
                              <%= form.required && "Required" || "Optional" %>
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class="text-sm text-gray-500">
                            <%= Calendar.strftime(form.inserted_at, "%b %d, %Y") %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center space-x-2">
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/forms/#{form}/edit"}
                              class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                              title="Edit"
                            >
                              <.icon name="hero-pencil" class="h-4 w-4" />
                            </.link>
                            <button
                              phx-click="delete"
                              phx-value-id={form.id}
                              data-confirm="Are you sure you want to delete this form?"
                              class="inline-flex items-center px-2 py-1 border border-red-300 text-sm rounded text-red-700 bg-white hover:bg-red-50"
                              title="Delete"
                            >
                              <.icon name="hero-trash" class="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        <% end %>
    """
  end

  defp render_wizard_builder_tab(assigns) do
    ~H"""
    <%= case @wizard_builder_step do %>
      <% :edit_wizard -> %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Edit Wizard: <%= @wizard.name %></h3>
                <p class="text-sm text-gray-600">Manage wizard steps and reorder forms</p>
              </div>
              <button
                phx-click="cancel_wizard_builder"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <.icon name="hero-arrow-left" class="h-4 w-4 mr-2" />
                Back to Wizards
              </button>
            </div>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Wizard Details -->
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-4">Wizard Information</h4>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <p class="text-sm text-gray-900 p-2 bg-gray-50 rounded-md"><%= @wizard.name %></p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <p class="text-sm text-gray-600 p-2 bg-gray-50 rounded-md"><%= @wizard.description || "No description" %></p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <span class={[
                      "inline-flex items-center px-2 py-1 rounded text-xs font-medium",
                      if(@wizard.active, do: "bg-green-100 text-green-800", else: "bg-gray-100 text-gray-800")
                    ]}>
                      <%= if @wizard.active, do: "Active", else: "Inactive" %>
                    </span>
                  </div>
                </div>

                <!-- Preview Button -->
                <div class="mt-6">
                  <button
                    phx-click="preview_wizard"
                    phx-value-id={@wizard.id}
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <.icon name="hero-eye" class="h-4 w-4 mr-2" />
                    Preview Wizard
                  </button>
                </div>
              </div>

              <!-- Wizard Steps Management -->
              <div>
                <.live_component
                  module={ServiceManagerWeb.Backend.DynamicFormsLive.WizardStepsManagerComponent}
                  id="wizard_steps_manager"
                  wizard={@wizard}
                />
              </div>
            </div>
          </div>
        </div>
      <% :preview_wizard -> %>
        <.live_component
          module={ServiceManagerWeb.Backend.DynamicFormsLive.WizardPreviewComponent}
          id="wizard_preview"
          wizard={@current_preview_wizard}
          current_step_number={@current_step_number}
          preview_device={@preview_device}
          wizard_form_data={@wizard_form_data}
        />
      <% :select_forms -> %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Select Mobile Forms for Wizard</h3>
                <p class="text-sm text-gray-600">Choose the mobile forms you want to connect into a wizard flow</p>
              </div>
              <%= if length(@selected_forms) > 0 do %>
                <button
                  phx-click="create_wizard_from_selection"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-arrows-right-left" class="h-4 w-4 mr-2" />
                  Create Wizard (<%= length(@selected_forms) %> forms)
                </button>
              <% end %>
            </div>
          </div>

          <div class="p-6">
            <!-- Filter Controls -->
            <%= if !Enum.empty?(assigns[:mobile_forms] || []) do %>
              <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">Filter Forms</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Screen Filter -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Screen</label>
                    <.simple_form
                      for={%{}}
                      id="screen-filter-form"
                      phx-change="wizard_filter_by_screen"
                    >
                      <select
                        name="screen_id"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      >
                        <option value="">All Screens</option>
                        <%= for screen <- @wizard_screens do %>
                          <option 
                            value={screen.id} 
                            selected={@wizard_selected_screen_id == screen.id}
                          >
                            <%= screen.name %>
                          </option>
                        <% end %>
                      </select>
                    </.simple_form>
                  </div>
                  
                  <!-- Page Filter -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Page</label>
                    <.simple_form
                      for={%{}}
                      id="page-filter-form"
                      phx-change="wizard_filter_by_page"
                    >
                      <select
                        name="page_id"
                        class={[
                          "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm",
                          if(is_nil(@wizard_selected_screen_id), do: "bg-gray-100", else: "")
                        ]}
                        disabled={is_nil(@wizard_selected_screen_id)}
                      >
                        <option value="">All Pages</option>
                        <%= for page <- @wizard_pages do %>
                          <%= if is_nil(@wizard_selected_screen_id) || page.screen_id == @wizard_selected_screen_id do %>
                            <option 
                              value={page.id} 
                              selected={@wizard_selected_page_id == page.id}
                            >
                              <%= page.name %>
                            </option>
                          <% end %>
                        <% end %>
                      </select>
                    </.simple_form>
                  </div>
                </div>
                
                <!-- Filter Stats -->
                <div class="mt-3 flex items-center justify-between">
                  <div class="text-sm text-gray-600">
                    <%= if @wizard_selected_screen_id || @wizard_selected_page_id do %>
                      Showing <%= length(@wizard_filtered_mobile_forms) %> of <%= length(@mobile_forms) %> forms
                    <% else %>
                      Showing all <%= length(@mobile_forms) %> forms
                    <% end %>
                  </div>
                  <%= if @wizard_selected_screen_id || @wizard_selected_page_id do %>
                    <button
                      phx-click="wizard_clear_filters"
                      class="text-xs text-indigo-600 hover:text-indigo-800"
                    >
                      Clear filters
                    </button>
                  <% end %>
                </div>
              </div>
            <% end %>
            
            <%= if Enum.empty?(assigns[:mobile_forms] || []) do %>
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <.icon name="hero-document-text" class="h-8 w-8 text-gray-400" />
                </div>
                <h4 class="text-md font-medium text-gray-900 mb-2">No mobile forms available</h4>
                <p class="text-gray-600 mb-4">Create some mobile forms first before building wizards</p>
                <.link
                  navigate={~p"/mobileBanking/mobile-forms"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Create Mobile Forms
                </.link>
              </div>
            <% else %>
              <%= if Enum.empty?(@wizard_filtered_mobile_forms) do %>
                <div class="text-center py-12">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <.icon name="hero-funnel" class="h-8 w-8 text-gray-400" />
                  </div>
                  <h4 class="text-md font-medium text-gray-900 mb-2">No forms match current filters</h4>
                  <p class="text-gray-600 mb-4">Try adjusting your screen or page filters</p>
                  <button
                    phx-click="wizard_clear_filters"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <.icon name="hero-x-mark" class="h-4 w-4 mr-2" />
                    Clear Filters
                  </button>
                </div>
              <% else %>
                <div class={dynamic_grid_classes(length(@wizard_filtered_mobile_forms))}>
                  <%= for form <- @wizard_filtered_mobile_forms do %>
                  <div 
                    class={[
                      "relative rounded-lg border-2 p-4 cursor-pointer transition-all duration-200",
                      if(to_string(form.id) in @selected_forms, do: "border-indigo-500 bg-indigo-50", else: "border-gray-200 hover:border-gray-300 bg-white")
                    ]}
                    phx-click="toggle_form_selection"
                    phx-value-form_id={form.id}
                  >
                    <%= if to_string(form.id) in @selected_forms do %>
                      <div class="absolute top-2 right-2">
                        <div class="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-600">
                          <.icon name="hero-check" class="h-4 w-4 text-white" />
                        </div>
                      </div>
                    <% end %>

                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0">
                        <.icon name="hero-device-phone-mobile" class="h-6 w-6 text-blue-500" />
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= form.name %>
                        </p>
                        <div class="text-xs text-gray-500 mt-1">
                          <%= if form.page do %>
                            <span class="inline-flex items-center space-x-1">
                              <span><%= form.page.screen.name %></span>
                              <.icon name="hero-chevron-right" class="h-3 w-3" />
                              <span><%= form.page.name %></span>
                            </span>
                          <% else %>
                            No hierarchy info
                          <% end %>
                        </div>
                        <div class="flex items-center space-x-2 mt-2">
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Mobile Form
                          </span>
                          <%= if form.validation_schema do %>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              <.icon name="hero-shield-check" class="h-3 w-3 mr-1" />
                              Validated
                            </span>
                          <% else %>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                              <.icon name="hero-exclamation-triangle" class="h-3 w-3 mr-1" />
                              No Validation
                            </span>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                <% end %>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>

      <% :assign_validations -> %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Assign Validation Schemas</h3>
                <p class="text-sm text-gray-600">Optionally assign dynamic form validation schemas to your mobile forms</p>
              </div>
              <div class="flex items-center space-x-3">
                <button
                  phx-click="cancel_wizard_builder"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  phx-click="proceed_to_wizard_config"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-arrow-right" class="h-4 w-4 mr-2" />
                  Configure Wizard
                </button>
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <%= for form <- @wizard_forms do %>
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-3">
                        <.icon name="hero-device-phone-mobile" class="h-5 w-5 text-blue-500" />
                        <div>
                          <h4 class="text-sm font-medium text-gray-900"><%= form.name %></h4>
                          <%= if form.page do %>
                            <p class="text-xs text-gray-500"><%= form.page.screen.name %> → <%= form.page.name %></p>
                          <% end %>
                        </div>
                      </div>
                    </div>
                    <div class="ml-4 min-w-0 flex-1 max-w-xs">
                      <label class="block text-xs font-medium text-gray-700 mb-1">Validation Schema</label>
                      <select
                        phx-change="assign_validation"
                        phx-value-mobile_form_id={form.id}
                        name="dynamic_form_id"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      >
                        <option value="">No validation</option>
                        <%= for dynamic_form <- @forms do %>
                          <option 
                            value={dynamic_form.id} 
                            selected={Map.get(@validation_assignments, to_string(form.id)) == dynamic_form.id}
                          >
                            <%= dynamic_form.name %> (<%= dynamic_form.http_method %>)
                          </option>
                        <% end %>
                      </select>
                      <%= if Map.get(@validation_assignments, to_string(form.id)) do %>
                        <p class="mt-1 text-xs text-green-600">
                          <.icon name="hero-check-circle" class="h-3 w-3 inline mr-1" />
                          Validation assigned
                        </p>
                      <% else %>
                        <p class="mt-1 text-xs text-yellow-600">
                          <.icon name="hero-exclamation-triangle" class="h-3 w-3 inline mr-1" />
                          No validation
                        </p>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

      <% :configure_wizard -> %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Configure Wizard</h3>
            <p class="text-sm text-gray-600">Set up your wizard details and step flow</p>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Wizard Configuration Form -->
              <div>
                <.form
                  for={%{}}
                  as={:wizard}
                  phx-submit="save_wizard"
                  class="space-y-6"
                >
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Wizard Name
                    </label>
                    <input
                      name="wizard[name]"
                      type="text"
                      required
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      placeholder="Enter wizard name..."
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      name="wizard[description]"
                      rows={3}
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      placeholder="Describe what this wizard does..."
                    ></textarea>
                  </div>

                  <div class="flex items-center space-x-3">
                    <button
                      type="submit"
                      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      <.icon name="hero-check" class="h-4 w-4 mr-2" />
                      Create Wizard
                    </button>
                    <button
                      type="button"
                      phx-click="cancel_wizard_builder"
                      class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  </div>
                </.form>
              </div>

              <!-- Step Preview -->
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-4">Wizard Steps Preview</h4>
                <div class="space-y-3">
                  <%= for {form, index} <- Enum.with_index(@wizard_forms, 1) do %>
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-indigo-600"><%= index %></span>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900"><%= form.name %></p>
                        <div class="text-xs text-gray-500">
                          <%= if form.page do %>
                            <span><%= form.page.screen.name %> → <%= form.page.name %></span>
                          <% else %>
                            Mobile Form
                          <% end %>
                          <%= if form.validation_schema do %>
                            <span class="inline-flex items-center ml-2 px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-700">
                              <.icon name="hero-shield-check" class="h-3 w-3 mr-1" />
                              Validated
                            </span>
                          <% else %>
                            <span class="inline-flex items-center ml-2 px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-700">
                              <.icon name="hero-exclamation-triangle" class="h-3 w-3 mr-1" />
                              No Validation
                            </span>
                          <% end %>
                        </div>
                      </div>
                      <%= if index < length(@wizard_forms) do %>
                        <.icon name="hero-arrow-down" class="h-4 w-4 text-gray-400" />
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
    <% end %>

    <!-- Existing Wizards Section -->
    <%= if @wizard_builder_step == :select_forms and not Enum.empty?(assigns[:wizards] || []) do %>
      <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Existing Wizards</h3>
          <p class="text-sm text-gray-600">Manage your form wizards</p>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <%= for wizard <- @wizards do %>
              <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between">
                  <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-gray-900 truncate">
                      <%= wizard.name %>
                    </h4>
                    <p class="text-xs text-gray-500 mt-1 line-clamp-2">
                      <%= wizard.description || "No description" %>
                    </p>
                    <div class="mt-2 flex items-center space-x-2">
                      <span class={[
                        "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",
                        if(wizard.active, do: "bg-green-100 text-green-800", else: "bg-gray-100 text-gray-800")
                      ]}>
                        <%= if wizard.active, do: "Active", else: "Inactive" %>
                      </span>
                      <span class="text-xs text-gray-500">
                        <%= length(wizard.steps || []) %> steps
                      </span>
                    </div>
                  </div>
                  <div class="flex-shrink-0 ml-2 flex items-center space-x-1">
                    <button
                      phx-click="preview_wizard"
                      phx-value-id={wizard.id}
                      class="p-1 text-gray-400 hover:text-indigo-500 transition-colors"
                      title="Preview Wizard"
                    >
                      <.icon name="hero-eye" class="h-4 w-4" />
                    </button>
                    <button
                      phx-click="edit_wizard"
                      phx-value-id={wizard.id}
                      class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
                      title="Edit Wizard"
                    >
                      <.icon name="hero-pencil" class="h-4 w-4" />
                    </button>
                    <button
                      phx-click="delete_wizard"
                      phx-value-id={wizard.id}
                      data-confirm="Are you sure you want to delete this wizard?"
                      class="p-1 text-gray-400 hover:text-red-500 transition-colors"
                      title="Delete Wizard"
                    >
                      <.icon name="hero-trash" class="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    """
  end
end