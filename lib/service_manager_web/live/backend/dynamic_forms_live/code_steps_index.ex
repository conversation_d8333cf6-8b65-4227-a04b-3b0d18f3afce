defmodule ServiceManagerWeb.Backend.DynamicFormsLive.CodeStepsIndex do
  use ServiceManagerWeb, :live_view
  
  alias ServiceManager.Schemas.Dynamic.Processes.{CodeStep, CodeStepManager}
  alias ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/code-steps")
      |> AsyncQueryManager.init_async_tracking()
      |> assign(:page_title, "Code Steps Library")
      |> assign(:view_mode, :grid)
      |> assign(:search_query, "")
      |> assign(:selected_group, "")
      |> assign(:selected_category, "")
      |> assign(:selected_class, "")
      |> assign(:selected_type, "")
      |> assign(:sort_by, :popularity)
      |> assign(:sort_order, :desc)
      |> assign(:code_steps, [])
      |> assign(:loading, true)
      |> assign(:stats, %{
        total_steps: 0,
        featured_steps: 0,
        most_used_group: nil,
        total_usage: 0
      })

    socket = if connected?(socket) do
      load_code_steps_async(socket)
    else
      socket
    end

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  # Helper functions for code step operations
  defp list_template_steps(filters) do
    import Ecto.Query
    
    query = from s in CodeStep,
            where: s.is_template == true,
            order_by: [desc: s.popularity_score, desc: s.usage_count]
    
    query = apply_filters(query, filters)
    ServiceManager.Repo.all(query)
  end

  defp apply_filters(query, filters) do
    import Ecto.Query
    
    query
    |> filter_by_search(filters.search_query)
    |> filter_by_group(filters.group)
    |> filter_by_category(filters.category)
    |> filter_by_class(filters.class)
    |> filter_by_type(filters.type)
    |> apply_sorting(filters.sort_by, filters.sort_order)
  end

  defp filter_by_search(query, ""), do: query
  defp filter_by_search(query, search_query) do
    import Ecto.Query
    search_term = "%#{search_query}%"
    
    from s in query,
         where: ilike(s.content, ^search_term) or 
                ilike(s.description, ^search_term) or
                fragment("? @> ARRAY[?]::text[]", s.tags, ^search_query)
  end

  defp filter_by_group(query, ""), do: query
  defp filter_by_group(query, group) do
    import Ecto.Query
    from s in query, where: s.step_group == ^group
  end

  defp filter_by_category(query, ""), do: query
  defp filter_by_category(query, category) do
    import Ecto.Query
    from s in query, where: s.step_category == ^category
  end

  defp filter_by_class(query, ""), do: query
  defp filter_by_class(query, class) do
    import Ecto.Query
    from s in query, where: s.step_class == ^class
  end

  defp filter_by_type(query, ""), do: query
  defp filter_by_type(query, type) do
    import Ecto.Query
    from s in query, where: s.step_type == ^type
  end

  defp apply_sorting(query, sort_by, sort_order) do
    import Ecto.Query
    
    case {sort_by, sort_order} do
      {:popularity, :desc} -> from s in query, order_by: [desc: s.popularity_score, desc: s.usage_count]
      {:popularity, :asc} -> from s in query, order_by: [asc: s.popularity_score, asc: s.usage_count]
      {:usage_count, :desc} -> from s in query, order_by: [desc: s.usage_count]
      {:usage_count, :asc} -> from s in query, order_by: [asc: s.usage_count]
      {:inserted_at, :desc} -> from s in query, order_by: [desc: s.inserted_at]
      {:inserted_at, :asc} -> from s in query, order_by: [asc: s.inserted_at]
      {:step_type, :desc} -> from s in query, order_by: [desc: s.step_type]
      {:step_type, :asc} -> from s in query, order_by: [asc: s.step_type]
      _ -> from s in query, order_by: [desc: s.popularity_score, desc: s.usage_count]
    end
  end

  defp get_library_stats do
    import Ecto.Query
    
    # Get basic counts
    total_query = from s in CodeStep, where: s.is_template == true
    total_steps = ServiceManager.Repo.aggregate(total_query, :count, :id)
    
    featured_query = from s in CodeStep, where: s.is_template == true and s.is_featured == true
    featured_steps = ServiceManager.Repo.aggregate(featured_query, :count, :id)
    
    # Get total usage
    usage_query = from s in CodeStep, where: s.is_template == true, select: sum(s.usage_count)
    total_usage = ServiceManager.Repo.one(usage_query) || 0
    
    # Get most popular group
    group_query = from s in CodeStep, 
                  where: s.is_template == true and not is_nil(s.step_group),
                  group_by: s.step_group,
                  select: {s.step_group, count(s.id)},
                  order_by: [desc: count(s.id)],
                  limit: 1
    
    most_used_group = case ServiceManager.Repo.one(group_query) do
      {group, _count} -> group
      nil -> nil
    end
    
    %{
      total_steps: total_steps,
      featured_steps: featured_steps,
      total_usage: total_usage,
      most_used_group: most_used_group
    }
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case CodeStepManager.get_step(id) do
      {:ok, step} ->
        socket
        |> assign(:page_title, "Edit Code Step")
        |> assign(:code_step, step)
      {:error, :not_found} ->
        socket
        |> put_flash(:error, "Code step not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/code-steps")
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Code Step")
    |> assign(:code_step, %CodeStep{is_template: true})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Code Steps Library")
    |> assign(:code_step, nil)
  end

  @impl true
  def handle_event("search", %{"query" => query}, socket) do
    socket = 
      socket
      |> assign(:search_query, query)
      |> assign(:loading, true)
      |> load_code_steps_async()

    {:noreply, socket}
  end

  def handle_event("filter", %{"filter" => filter_params}, socket) do
    socket = 
      socket
      |> assign(:selected_group, Map.get(filter_params, "group", ""))
      |> assign(:selected_category, Map.get(filter_params, "category", ""))
      |> assign(:selected_class, Map.get(filter_params, "class", ""))
      |> assign(:selected_type, Map.get(filter_params, "type", ""))
      |> assign(:loading, true)
      |> load_code_steps_async()

    {:noreply, socket}
  end

  def handle_event("sort", %{"sort_by" => sort_by, "order" => order}, socket) do
    socket = 
      socket
      |> assign(:sort_by, String.to_atom(sort_by))
      |> assign(:sort_order, String.to_atom(order))
      |> assign(:loading, true)
      |> load_code_steps_async()

    {:noreply, socket}
  end

  def handle_event("toggle_view", %{"mode" => mode}, socket) do
    {:noreply, assign(socket, :view_mode, String.to_atom(mode))}
  end

  def handle_event("delete", %{"id" => id}, socket) do
    case CodeStepManager.get_step(id) do
      {:ok, code_step} ->
        {:ok, _} = CodeStepManager.delete_step(code_step)

        socket = 
          socket
          |> put_flash(:info, "Code step deleted successfully")
          |> load_code_steps_async()

        {:noreply, socket}
      
      {:error, :not_found} ->
        {:noreply, put_flash(socket, :error, "Code step not found")}
    end
  end

  def handle_event("clone", %{"id" => id}, socket) do
    case CodeStepManager.get_step(id) do
      {:ok, original_step} ->
        clone_attrs = %{
          step_type: original_step.step_type,
          content: original_step.content,
          step_category: original_step.step_category,
          step_group: original_step.step_group,
          step_class: original_step.step_class,
          template_params: original_step.template_params,
          description: "Copy of #{original_step.description || original_step.content}",
          author_id: socket.assigns.current_user.id,
          is_template: true
        }

        case CodeStepManager.create_step(clone_attrs) do
          {:ok, _step} ->
            socket = 
              socket
              |> put_flash(:info, "Code step cloned successfully")
              |> load_code_steps_async()

            {:noreply, socket}

          {:error, _changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to clone code step")}
        end
      
      {:error, :not_found} ->
        {:noreply, put_flash(socket, :error, "Code step not found")}
    end
  end

  def handle_event("toggle_featured", %{"id" => id}, socket) do
    case CodeStepManager.get_step(id) do
      {:ok, code_step} ->
        case CodeStepManager.update_step(code_step, %{is_featured: !code_step.is_featured}) do
          {:ok, _step} ->
            socket = 
              socket
              |> put_flash(:info, "Step #{if code_step.is_featured, do: "unfeatured", else: "featured"} successfully")
              |> load_code_steps_async()

            {:noreply, socket}

          {:error, _changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to update step")}
        end
      
      {:error, :not_found} ->
        {:noreply, put_flash(socket, :error, "Code step not found")}
    end
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    socket = AsyncQueryManager.handle_async_result(ref, result, socket, &handle_code_steps_result/3)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    socket = AsyncQueryManager.handle_async_failure(ref, socket)
    {:noreply, socket}
  end

  defp handle_code_steps_result(socket, query_key, result) do
    case {query_key, result} do
      {"load_code_steps", {:ok, %{steps: steps, stats: stats}}} ->
        socket
        |> assign(:code_steps, steps)
        |> assign(:stats, stats)
        |> assign(:loading, false)

      {_query_key, {:error, _error}} ->
        socket
        |> assign(:loading, false)
        |> put_flash(:error, "Failed to load code steps")

      _ ->
        socket
    end
  end

  defp load_code_steps_async(socket) do
    filters = %{
      search_query: socket.assigns.search_query,
      group: socket.assigns.selected_group,
      category: socket.assigns.selected_category,
      class: socket.assigns.selected_class,
      type: socket.assigns.selected_type,
      sort_by: socket.assigns.sort_by,
      sort_order: socket.assigns.sort_order
    }

    AsyncQueryManager.start_async_query("load_code_steps", fn ->
      steps = list_template_steps(filters)
      stats = get_library_stats()
      %{steps: steps, stats: stats}
    end, socket)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title={@page_title}
        subtitle="Browse and manage reusable code step templates"
        current_page={:code_steps}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Code Steps Library"}
        ]}
      />

      <div class="px-6 py-6">
        <!-- Header Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-squares-plus" class="h-6 w-6 text-amber-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Steps</p>
                <p class="text-2xl font-bold text-gray-900"><%= @stats.total_steps %></p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-star" class="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Featured</p>
                <p class="text-2xl font-bold text-gray-900"><%= @stats.featured_steps %></p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-chart-bar" class="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Usage</p>
                <p class="text-2xl font-bold text-gray-900"><%= @stats.total_usage %></p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-folder" class="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Popular Group</p>
                <p class="text-lg font-bold text-gray-900"><%= @stats.most_used_group || "None" %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Search -->
            <div class="flex-1 max-w-lg">
              <form phx-change="search">
                <div class="relative">
                  <.icon name="hero-magnifying-glass" class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <input 
                    type="text" 
                    name="query" 
                    value={@search_query}
                    placeholder="Search code steps..." 
                    class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>
              </form>
            </div>

            <!-- View Toggle and Actions -->
            <div class="flex items-center space-x-4">
              <!-- View Mode Toggle -->
              <div class="flex rounded-md shadow-sm">
                <button
                  phx-click="toggle_view"
                  phx-value-mode="grid"
                  class={[
                    "relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 rounded-l-md",
                    if(@view_mode == :grid, do: "bg-amber-50 text-amber-700 border-amber-300 z-10", else: "bg-white text-gray-700 hover:bg-gray-50")
                  ]}
                >
                  <.icon name="hero-squares-2x2" class="h-4 w-4" />
                </button>
                <button
                  phx-click="toggle_view"
                  phx-value-mode="list"
                  class={[
                    "relative -ml-px inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 rounded-r-md",
                    if(@view_mode == :list, do: "bg-amber-50 text-amber-700 border-amber-300 z-10", else: "bg-white text-gray-700 hover:bg-gray-50")
                  ]}
                >
                  <.icon name="hero-list-bullet" class="h-4 w-4" />
                </button>
              </div>

              <!-- Create New Step -->
              <.link
                patch={~p"/mobileBanking/dynamic-forms/code-steps/new"}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700"
              >
                <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                New Step
              </.link>
            </div>
          </div>

          <!-- Filters -->
          <div class="mt-4 pt-4 border-t border-gray-200">
            <form phx-change="filter">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Group</label>
                  <select name="filter[group]" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 rounded-md">
                    <option value="">All Groups</option>
                    <%= for group <- CodeStep.step_groups() do %>
                      <option value={group} selected={@selected_group == group}><%= group %></option>
                    <% end %>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                  <select name="filter[class]" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 rounded-md">
                    <option value="">All Classes</option>
                    <%= for class <- CodeStep.step_classes() do %>
                      <option value={class} selected={@selected_class == class}><%= class %></option>
                    <% end %>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <select name="filter[type]" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 rounded-md">
                    <option value="">All Types</option>
                    <%= for type <- CodeStep.step_types() do %>
                      <option value={type} selected={@selected_type == type}><%= String.capitalize(String.replace(type, "_", " ")) %></option>
                    <% end %>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                  <select name="sort[by]" phx-change="sort" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 rounded-md">
                    <option value={:popularity} selected={@sort_by == :popularity}>Popularity</option>
                    <option value={:usage_count} selected={@sort_by == :usage_count}>Usage Count</option>
                    <option value={:inserted_at} selected={@sort_by == :inserted_at}>Date Created</option>
                    <option value={:step_type} selected={@sort_by == :step_type}>Type</option>
                  </select>
                </div>
              </div>
            </form>
          </div>
        </div>

        <%= if @loading do %>
          <div class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
            <span class="ml-3 text-gray-600">Loading code steps...</span>
          </div>
        <% else %>
          <%= if Enum.empty?(@code_steps) do %>
            <div class="text-center py-12">
              <.icon name="hero-squares-plus" class="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">No code steps found</h3>
              <p class="text-gray-600 mb-4">Get started by creating your first code step template.</p>
              <.link
                patch={~p"/mobileBanking/dynamic-forms/code-steps/new"}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700"
              >
                <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                Create First Step
              </.link>
            </div>
          <% else %>
            <!-- Code Steps Display -->
            <%= if @view_mode == :grid do %>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <%= for step <- @code_steps do %>
                  <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                      <!-- Step Header -->
                      <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-2">
                          <span class={[
                            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                            step_type_color(step.step_type)
                          ]}>
                            <%= String.capitalize(String.replace(step.step_type, "_", " ")) %>
                          </span>
                          <%= if step.is_featured do %>
                            <.icon name="hero-star" class="h-4 w-4 text-yellow-500" />
                          <% end %>
                        </div>
                        <div class="flex items-center space-x-1">
                          <button
                            phx-click="clone"
                            phx-value-id={step.id}
                            class="p-1 text-gray-400 hover:text-gray-600"
                            title="Clone step"
                          >
                            <.icon name="hero-document-duplicate" class="h-4 w-4" />
                          </button>
                          <.link
                            patch={~p"/mobileBanking/dynamic-forms/code-steps/#{step}/edit"}
                            class="p-1 text-gray-400 hover:text-gray-600"
                            title="Edit step"
                          >
                            <.icon name="hero-pencil" class="h-4 w-4" />
                          </.link>
                        </div>
                      </div>

                      <!-- Step Content -->
                      <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-2">
                          <%= step.description || step.content %>
                        </h3>
                        <pre class="text-xs text-gray-600 bg-gray-100 rounded p-2 overflow-hidden"><%= String.slice(step.content, 0, 100) %><%= if String.length(step.content) > 100, do: "..." %></pre>
                      </div>

                      <!-- Step Metadata -->
                      <div class="space-y-2">
                        <%= if step.step_group do %>
                          <div class="flex items-center text-xs text-gray-500">
                            <.icon name="hero-folder" class="h-3 w-3 mr-1" />
                            <%= step.step_group %>
                          </div>
                        <% end %>
                        <%= if step.step_class do %>
                          <div class="flex items-center text-xs text-gray-500">
                            <.icon name="hero-academic-cap" class="h-3 w-3 mr-1" />
                            <%= step.step_class %>
                          </div>
                        <% end %>
                        <div class="flex items-center text-xs text-gray-500">
                          <.icon name="hero-chart-bar" class="h-3 w-3 mr-1" />
                          Used <%= step.usage_count %> times
                        </div>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <!-- List View -->
              <div class="bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden">
                <ul class="divide-y divide-gray-200">
                  <%= for step <- @code_steps do %>
                    <li class="p-6 hover:bg-gray-50">
                      <div class="flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center space-x-3 mb-2">
                            <span class={[
                              "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
                              step_type_color(step.step_type)
                            ]}>
                              <%= String.capitalize(String.replace(step.step_type, "_", " ")) %>
                            </span>
                            <%= if step.is_featured do %>
                              <.icon name="hero-star" class="h-4 w-4 text-yellow-500" />
                            <% end %>
                            <%= if step.step_group do %>
                              <span class="text-xs text-gray-500">
                                <.icon name="hero-folder" class="h-3 w-3 inline mr-1" />
                                <%= step.step_group %>
                              </span>
                            <% end %>
                            <%= if step.step_class do %>
                              <span class="text-xs text-gray-500">
                                <.icon name="hero-academic-cap" class="h-3 w-3 inline mr-1" />
                                <%= step.step_class %>
                              </span>
                            <% end %>
                          </div>
                          <h3 class="text-sm font-medium text-gray-900 mb-1">
                            <%= step.description || step.content %>
                          </h3>
                          <pre class="text-xs text-gray-600 bg-gray-100 rounded p-2 overflow-hidden max-w-2xl"><%= String.slice(step.content, 0, 200) %><%= if String.length(step.content) > 200, do: "..." %></pre>
                          <div class="mt-2 flex items-center text-xs text-gray-500 space-x-4">
                            <span>
                              <.icon name="hero-chart-bar" class="h-3 w-3 inline mr-1" />
                              <%= step.usage_count %> uses
                            </span>
                            <span>
                              Score: <%= Float.round(step.popularity_score, 1) %>
                            </span>
                          </div>
                        </div>
                        <div class="flex items-center space-x-2 ml-4">
                          <button
                            phx-click="toggle_featured"
                            phx-value-id={step.id}
                            class={[
                              "p-2 rounded-md text-sm font-medium",
                              if(step.is_featured, do: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200", else: "bg-gray-100 text-gray-600 hover:bg-gray-200")
                            ]}
                            title={if step.is_featured, do: "Remove from featured", else: "Add to featured"}
                          >
                            <.icon name="hero-star" class="h-4 w-4" />
                          </button>
                          <button
                            phx-click="clone"
                            phx-value-id={step.id}
                            class="p-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200"
                            title="Clone step"
                          >
                            <.icon name="hero-document-duplicate" class="h-4 w-4" />
                          </button>
                          <.link
                            patch={~p"/mobileBanking/dynamic-forms/code-steps/#{step}/edit"}
                            class="p-2 bg-amber-100 text-amber-600 rounded-md hover:bg-amber-200"
                            title="Edit step"
                          >
                            <.icon name="hero-pencil" class="h-4 w-4" />
                          </.link>
                          <button
                            phx-click="delete"
                            phx-value-id={step.id}
                            data-confirm="Are you sure you want to delete this code step?"
                            class="p-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200"
                            title="Delete step"
                          >
                            <.icon name="hero-trash" class="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>

    <%= if @live_action in [:new, :edit] do %>
      <.modal id="code-step-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/code-steps")}>
        <.live_component
          module={ServiceManagerWeb.Backend.DynamicFormsLive.CodeStepFormComponent}
          id={@code_step.id || :new}
          title={@page_title}
          action={@live_action}
          code_step={@code_step}
          patch={~p"/mobileBanking/dynamic-forms/code-steps"}
        />
      </.modal>
    <% end %>
    """
  end

  defp step_type_color("line"), do: "bg-blue-100 text-blue-800"
  defp step_type_color("function_open"), do: "bg-green-100 text-green-800"
  defp step_type_color("function_close"), do: "bg-green-100 text-green-800"
  defp step_type_color("block_open"), do: "bg-purple-100 text-purple-800"
  defp step_type_color("block_close"), do: "bg-purple-100 text-purple-800"
  defp step_type_color("comment"), do: "bg-gray-100 text-gray-800"
  defp step_type_color("import"), do: "bg-yellow-100 text-yellow-800"
  defp step_type_color("custom"), do: "bg-red-100 text-red-800"
  defp step_type_color(_), do: "bg-gray-100 text-gray-800"
end