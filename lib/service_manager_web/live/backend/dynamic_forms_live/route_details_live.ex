defmodule ServiceManagerWeb.Backend.DynamicFormsLive.RouteDetailsLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Forms.DynamicFormsManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(%{"id" => route_id}, _session, socket) do
    case DynamicRouteManager.get_route(route_id) do
      nil ->
        {:ok,
         socket
         |> put_flash(:error, "Route not found")
         |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")}

      route ->
        socket =
          socket
          |> assign(:route, route)
          |> assign(:page_title, "Route Details - #{route.name}")
          |> load_route_details(route)

        {:ok, socket}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("show-connect-modal", _params, socket) do
    {:noreply, assign(socket, :show_connect_modal, true)}
  end

  def handle_event("hide-connect-modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_connect_modal, false)
     |> assign(:selected_process_id, nil)}
  end

  def handle_event("select-process", %{"process-id" => process_id}, socket) do
    process_id = case process_id do
      "" -> nil
      id -> String.to_integer(id)
    end

    socket = assign(socket, :selected_process_id, process_id)

    {:noreply, socket}
  end

  def handle_event("connect-process", _params, socket) do
    case socket.assigns.selected_process_id do
      nil ->
        {:noreply, put_flash(socket, :error, "Please select a process to connect")}

      process_id ->
        if socket.assigns.initial_process do
          # There's already an initial process, so add to chain
          source_process_id = if Enum.empty?(socket.assigns.chain_info.chain_processes) do
            socket.assigns.initial_process.id
          else
            List.last(socket.assigns.chain_info.chain_processes).id
          end

          position = length(socket.assigns.chain_info.chain_processes)
          user_id = get_user_id(socket)

          # Use the initial process as the source to ensure we add to the same chain
          case ProcessManager.link_processes(socket.assigns.initial_process.id, process_id, position, user_id) do
            {:ok, _link} ->
              {:noreply,
               socket
               |> put_flash(:info, "Process connected to chain successfully")
               |> load_route_details(socket.assigns.route)
               |> assign(:show_connect_modal, false)
               |> assign(:selected_process_id, nil)}

            {:error, _changeset} ->
              {:noreply, put_flash(socket, :error, "Failed to connect process to chain")}
          end
        else
          # No initial process, so link as initial process to route
          case ProcessManager.link_process_to_route(socket.assigns.route.id, process_id, get_user_id(socket)) do
            {:ok, _link} ->
              {:noreply,
               socket
               |> put_flash(:info, "Process linked to route successfully")
               |> load_route_details(socket.assigns.route)
               |> assign(:show_connect_modal, false)
               |> assign(:selected_process_id, nil)}

            {:error, _changeset} ->
              {:noreply, put_flash(socket, :error, "Failed to link process to route")}
          end
        end
    end
  end

  def handle_event("delete-process", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)

    if socket.assigns.initial_process && socket.assigns.initial_process.id == process_id do
      # Deleting the initial process
      case ProcessManager.unlink_process_from_route(socket.assigns.route.id) do
        {:ok, _} ->
          {:noreply,
           socket
           |> put_flash(:info, "Process unlinked from route successfully")
           |> load_route_details(socket.assigns.route)}

        {:error, _reason} ->
          {:noreply, put_flash(socket, :error, "Failed to unlink process from route")}
      end
    else
      # Deleting a process from the chain
      source_process_id = socket.assigns.initial_process.id

      case ProcessManager.unlink_processes(source_process_id, process_id) do
        {:ok, _} ->
          {:noreply,
           socket
           |> put_flash(:info, "Process removed from chain successfully")
           |> load_route_details(socket.assigns.route)}

        {:error, _reason} ->
          {:noreply, put_flash(socket, :error, "Failed to remove process from chain")}
      end
    end
  end

  def handle_event("move-process-up", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)

    case ProcessManager.reorder_chain_process(process_id, :up, get_user_id(socket)) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Process moved up successfully")
         |> load_route_details(socket.assigns.route)}

      {:error, :cannot_move_further} ->
        {:noreply, put_flash(socket, :error, "Cannot move process up further")}

      {:error, _reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move process")}
    end
  end

  def handle_event("move-process-down", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)

    case ProcessManager.reorder_chain_process(process_id, :down, get_user_id(socket)) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Process moved down successfully")
         |> load_route_details(socket.assigns.route)}

      {:error, :cannot_move_further} ->
        {:noreply, put_flash(socket, :error, "Cannot move process down further")}

      {:error, _reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move process")}
    end
  end

  defp get_user_id(socket) do
    case socket.assigns do
      %{current_user: %{id: id}} -> id
      _ -> nil
    end
  end

  defp load_route_details(socket, route) do
    # Get linked process
    initial_process = case ProcessManager.get_initial_process(route.id) do
      {:ok, process} -> process
      {:error, _} -> nil
    end

    # Get linked forms
    linked_forms = get_linked_forms(route.id)

    # Get process chain information if there's a linked process
    chain_info = if initial_process do
      case ProcessManager.get_complete_chain(initial_process.id) do
        {:ok, chain_data} ->
          %{
            root_process: Map.get(chain_data, :root),
            chain_processes: Map.get(chain_data, :chain, []),
            chain_id: Map.get(chain_data, :chain_id)
          }
        {:error, _} -> %{root_process: nil, chain_processes: [], chain_id: nil}
      end
    else
      %{root_process: nil, chain_processes: [], chain_id: nil}
    end

    # Get all available processes for linking
    all_processes = ProcessManager.list_processes()

    # Filter out processes already in the chain
    available_processes = if initial_process do
      chained_process_ids = [initial_process.id | Enum.map(chain_info.chain_processes, & &1.id)]
      Enum.filter(all_processes, fn p -> p.id not in chained_process_ids end)
    else
      all_processes
    end

    socket
    |> assign(:initial_process, initial_process)
    |> assign(:linked_forms, linked_forms)
    |> assign(:chain_info, chain_info)
    |> assign(:available_processes, available_processes)
    |> assign(:show_connect_modal, false)
    |> assign(:selected_process_id, nil)
  end

  defp get_linked_forms(route_id) do
    import Ecto.Query
    alias ServiceManager.Forms.DynamicForm
    alias ServiceManager.Forms.DynamicRouteForm

    query = from f in DynamicForm,
            join: drf in DynamicRouteForm, on: drf.form_id == f.id,
            where: drf.route_id == ^route_id,
            select: f

    ServiceManager.Repo.all(query)
  end

  defp method_color("GET"), do: "bg-green-100 text-green-800"
  defp method_color("POST"), do: "bg-blue-100 text-blue-800"
  defp method_color("PUT"), do: "bg-yellow-100 text-yellow-800"
  defp method_color("PATCH"), do: "bg-orange-100 text-orange-800"
  defp method_color("DELETE"), do: "bg-red-100 text-red-800"
  defp method_color(_), do: "bg-gray-100 text-gray-800"

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Route Details"
        subtitle={@route.name}
        current_page={:routes}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Routes", link: ~p"/mobileBanking/dynamic-forms/routes"},
          %{title: @route.name}
        ]}
      />

      <!-- Header -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center space-x-3 mb-2">
                <span class={["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium", method_color(@route.method)]}>
                  <%= @route.method %>
                </span>
                <h1 class="text-2xl font-bold text-gray-900"><%= @route.name %></h1>
                <%= if !@route.enabled do %>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    DISABLED
                  </span>
                <% end %>
              </div>
              <code class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                <%= @route.path %>
              </code>
            </div>
            <div class="flex space-x-3">
              <.link
                navigate={~p"/mobileBanking/dynamic-forms/routes"}
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <.icon name="hero-arrow-left" class="h-4 w-4 mr-2" />
                Back to Routes
              </.link>
              <%= if @route.enabled && @initial_process do %>
                <a
                  href={"/dynamic#{@route.path}"}
                  target="_blank"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700"
                >
                  <.icon name="hero-play" class="h-4 w-4 mr-2" />
                  Test Route
                </a>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <div class="px-6 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Route Information -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-globe-alt" class="h-5 w-5 mr-2 text-blue-600" />
              Route Information
            </h2>

            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Name</label>
                <p class="text-sm text-gray-900"><%= @route.name %></p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Method</label>
                <p class="text-sm">
                  <span class={["inline-flex items-center px-2 py-1 rounded text-xs font-medium", method_color(@route.method)]}>
                    <%= @route.method %>
                  </span>
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Path</label>
                <code class="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded block">
                  <%= @route.path %>
                </code>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Status</label>
                <p class="text-sm">
                  <%= if @route.enabled do %>
                    <span class="inline-flex items-center text-green-600">
                      <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                      Active
                    </span>
                  <% else %>
                    <span class="inline-flex items-center text-red-600">
                      <div class="w-2 h-2 bg-red-400 rounded-full mr-2"></div>
                      Inactive
                    </span>
                  <% end %>
                </p>
              </div>

              <div>
                <label class="text-sm font-medium text-gray-500">Created</label>
                <p class="text-sm text-gray-900"><%= Calendar.strftime(@route.inserted_at, "%B %d, %Y at %I:%M %p") %></p>
              </div>
            </div>
          </div>

          <!-- Linked Process -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-cog" class="h-5 w-5 mr-2 text-indigo-600" />
              Linked Process
            </h2>

            <%= if @initial_process do %>
              <div class="space-y-4">
                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                  <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium text-indigo-900"><%= @initial_process.name %></h3>
                    <.link
                      navigate={~p"/mobileBanking/dynamic-forms/processes/#{@initial_process.id}/chain"}
                      class="text-indigo-600 hover:text-indigo-800 text-sm"
                    >
                      Configure →
                    </.link>
                  </div>
                  <p class="text-sm text-indigo-700"><%= @initial_process.description || "No description" %></p>

                  <div class="mt-3 flex items-center space-x-4 text-xs text-indigo-600">
                    <span>Version: <%= @initial_process.version %></span>
                    <%= if @initial_process.category do %>
                      <span>Category: <%= @initial_process.category %></span>
                    <% end %>
                  </div>
                </div>

                <%= if @initial_process.expected_params && map_size(@initial_process.expected_params) > 0 do %>
                  <div>
                    <label class="text-sm font-medium text-gray-500 mb-2 block">Expected Parameters</label>
                    <div class="bg-gray-50 rounded p-3">
                      <pre class="text-xs text-gray-700 overflow-auto"><%= Jason.encode!(@initial_process.expected_params, pretty: true) %></pre>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-8">
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <.icon name="hero-x-mark" class="h-6 w-6 text-gray-400" />
                </div>
                <p class="text-gray-500 text-sm">No process linked to this route</p>
                <p class="text-gray-400 text-xs mt-1">This route will only serve static responses</p>
              </div>
            <% end %>
          </div>

          <!-- Linked Forms -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-document-text" class="h-5 w-5 mr-2 text-green-600" />
              Linked Forms
            </h2>

            <%= if Enum.empty?(@linked_forms) do %>
              <div class="text-center py-8">
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <.icon name="hero-document-plus" class="h-6 w-6 text-gray-400" />
                </div>
                <p class="text-gray-500 text-sm">No forms linked to this route</p>
                <p class="text-gray-400 text-xs mt-1">This route accepts any request format</p>
              </div>
            <% else %>
              <div class="space-y-3">
                <%= for form <- @linked_forms do %>
                  <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-1">
                      <h3 class="font-medium text-green-900"><%= form.name %></h3>
                      <.link
                        navigate={~p"/mobileBanking/dynamic-forms/forms/#{form.id}/edit"}
                        class="text-green-600 hover:text-green-800 text-sm"
                      >
                        Edit →
                      </.link>
                    </div>
                    <%= if form.description do %>
                      <p class="text-sm text-green-700"><%= form.description %></p>
                    <% end %>

                    <div class="mt-2 flex items-center space-x-4 text-xs text-green-600">
                      <span>Method: <%= form.http_method %></span>
                      <%= if form.required do %>
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                          Required
                        </span>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Process Chain Diagram -->
        <%= if @initial_process || !Enum.empty?(@available_processes) do %>
          <div class="mt-6 bg-white border border-gray-200 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-squares-plus" class="h-5 w-5 mr-2 text-purple-600" />
              Process Chain Flow
            </h2>

            <div class="flex items-center space-x-4 overflow-x-auto pb-4">
              <!-- Entry Process -->
              <%= if @initial_process do %>
                <div class="flex-shrink-0 bg-gradient-to-br from-green-100 to-green-200 rounded-lg p-4 border-2 border-green-300 min-w-[200px]">
                  <div class="flex items-center justify-between mb-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-white">
                      ENTRY
                    </span>
                    <button
                      phx-click="delete-process"
                      phx-value-process-id={@initial_process.id}
                      data-confirm="Are you sure you want to unlink this process from the route?"
                      class="p-1 rounded text-red-400 hover:text-red-600 hover:bg-red-50"
                      title="Remove from route"
                    >
                      <.icon name="hero-trash" class="h-3 w-3" />
                    </button>
                  </div>
                  <h5 class="font-medium text-gray-900 text-sm"><%= @initial_process.name %></h5>
                  <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                    <%= @initial_process.description || "No description" %>
                  </p>
                </div>
              <% end %>

              <!-- Chain Processes -->
              <%= if !Enum.empty?(@chain_info.chain_processes) do %>
                <%= for {process, index} <- Enum.with_index(@chain_info.chain_processes) do %>
                  <!-- Arrow -->
                  <div class="flex-shrink-0 text-gray-400">
                    <.icon name="hero-arrow-right" class="h-6 w-6" />
                  </div>

                  <!-- Process Card -->
                  <div class="flex-shrink-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 min-w-[200px]">
                    <div class="flex items-center justify-between mb-2">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                        #<%= index + 1 %>
                      </span>
                      <div class="flex items-center space-x-1">
                        <button
                          phx-click="move-process-up"
                          phx-value-process-id={process.id}
                          class={["p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100", if(index == 0, do: "opacity-50 cursor-not-allowed", else: "")]}
                          disabled={index == 0}
                          title="Move up"
                        >
                          <.icon name="hero-chevron-up" class="h-3 w-3" />
                        </button>
                        <button
                          phx-click="move-process-down"
                          phx-value-process-id={process.id}
                          class={["p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100", if(index == length(@chain_info.chain_processes) - 1, do: "opacity-50 cursor-not-allowed", else: "")]}
                          disabled={index == length(@chain_info.chain_processes) - 1}
                          title="Move down"
                        >
                          <.icon name="hero-chevron-down" class="h-3 w-3" />
                        </button>
                        <button
                          phx-click="delete-process"
                          phx-value-process-id={process.id}
                          data-confirm="Are you sure you want to remove this process from the chain?"
                          class="p-1 rounded text-red-400 hover:text-red-600 hover:bg-red-50"
                          title="Remove from chain"
                        >
                          <.icon name="hero-trash" class="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                    <h5 class="font-medium text-gray-900 text-sm"><%= process.name %></h5>
                    <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                      <%= process.description || "No description" %>
                    </p>
                  </div>
                <% end %>
              <% end %>

              <!-- Connect New Process Card -->
              <%= if !Enum.empty?(@available_processes) do %>
                <!-- Arrow -->
                <div class="flex-shrink-0 text-gray-400">
                  <.icon name="hero-arrow-right" class="h-6 w-6" />
                </div>

                <!-- Connect Card -->
                <button
                  phx-click="show-connect-modal"
                  class="flex-shrink-0 bg-white rounded-lg p-4 border-2 border-dashed border-indigo-300 min-w-[200px] hover:border-indigo-400 hover:bg-indigo-50 transition-colors duration-200"
                >
                  <div class="text-center">
                    <.icon name="hero-plus" class="h-8 w-8 text-indigo-400 mx-auto mb-2" />
                    <p class="text-sm font-medium text-indigo-600">Connect Plugin</p>
                    <p class="text-xs text-indigo-500 mt-1">Add to chain</p>
                  </div>
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Connect Plugin Modal -->
    <div
      :if={@show_connect_modal}
      class="fixed inset-0 z-50 overflow-y-auto"
      phx-key="escape"
      phx-window-keydown="hide-connect-modal"
    >
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" phx-click="hide-connect-modal"></div>

        <!-- Modal panel -->
        <div
          class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          phx-click-stop
        >
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                <.icon name="hero-link" class="h-6 w-6 text-indigo-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  Connect Plugin to Chain
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Select a plugin to add to the process chain. It will be executed after the current chain.
                  </p>
                </div>

                <div class="mt-4">
                  <.simple_form
                    for={%{}}
                    id="plugin-selection-form"
                    phx-change="select-process"
                  >
                    <label for="process-select" class="block text-sm font-medium text-gray-700 mb-2">
                      Available Plugins
                    </label>
                    <select
                      id="process-select"
                      name="process-id"
                      value={@selected_process_id || ""}
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    >
                      <option value="">-- Select a plugin --</option>
                      <%= for process <- @available_processes do %>
                        <option value={process.id} selected={@selected_process_id == process.id}>
                          <%= process.name %> - <%= process.description || "No description" %>
                        </option>
                      <% end %>
                    </select>
                  </.simple_form>

                  <%= if @selected_process_id do %>
                    <% selected_process = Enum.find(@available_processes, &(&1.id == @selected_process_id)) %>
                    <%= if selected_process do %>
                      <div class="mt-3 p-3 bg-indigo-50 border border-indigo-200 rounded-md">
                        <h4 class="text-sm font-medium text-indigo-900"><%= selected_process.name %></h4>
                        <p class="text-xs text-indigo-700 mt-1"><%= selected_process.description || "No description" %></p>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-indigo-600">
                          <span>Version: <%= selected_process.version %></span>
                          <%= if selected_process.category do %>
                            <span>Category: <%= selected_process.category %></span>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              phx-click="connect-process"
              disabled={is_nil(@selected_process_id)}
              class={[
                "w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm",
                if(@selected_process_id, do: "bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500", else: "bg-gray-300 cursor-not-allowed")
              ]}
            >
              Connect Plugin
            </button>
            <button
              phx-click="hide-connect-modal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
