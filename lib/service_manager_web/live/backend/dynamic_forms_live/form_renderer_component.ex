defmodule ServiceManagerWeb.Backend.DynamicFormsLive.FormRendererComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def update(assigns, socket) do
    form_fields = get_form_fields(assigns.form, assigns[:is_mobile_form] || false)
    form_data = assigns.form_data || %{}
    is_mobile_form = assigns[:is_mobile_form] || false
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form_fields, form_fields)
     |> assign(:form_data, form_data)
     |> assign(:is_mobile_form, is_mobile_form)
     |> assign(:validation_errors, %{})}
  end

  defp get_form_fields(form, is_mobile_form) do
    cond do
      # Mobile form - fields are already converted to render format
      is_mobile_form && form && form.form && form.form["fields"] ->
        # Sort by field order for mobile forms
        form.form["fields"]
        |> Enum.sort_by(fn field -> 
          get_in(field, ["mobile_field_data", "order"]) || 0 
        end)
      
      # Dynamic form - standard structure  
      form && form.form && form.form["fields"] ->
        form.form["fields"]
      
      # No fields
      true ->
        []
    end
  end

  defp get_field_classes(device, field_type \\ nil) do
    base_classes = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
    
    device_classes = case device do
      "mobile" -> "text-sm py-2 px-3"
      "tablet" -> "text-base py-2.5 px-3"
      "desktop" -> "text-base py-3 px-4"
      _ -> "text-sm py-2 px-3"
    end
    
    field_classes = case field_type do
      "textarea" -> "min-h-[100px] resize-vertical"
      "select" -> ""
      _ -> ""
    end
    
    "#{base_classes} #{device_classes} #{field_classes}"
  end

  defp get_label_classes(device) do
    base_classes = "block font-medium text-gray-700 mb-2"
    
    case device do
      "mobile" -> "#{base_classes} text-sm"
      "tablet" -> "#{base_classes} text-base"
      "desktop" -> "#{base_classes} text-base"
      _ -> "#{base_classes} text-sm"
    end
  end

  defp get_container_classes(device) do
    padding_classes = case device do
      "mobile" -> "px-4 py-2"
      "tablet" -> "px-6 py-4"
      "desktop" -> "px-8 py-6"
      _ -> "px-4 py-2"
    end
    
    "h-full bg-white overflow-y-auto #{padding_classes}"
  end

  defp get_button_classes(device, type \\ "primary") do
    base_classes = "inline-flex items-center justify-center border font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2"
    
    size_classes = case device do
      "mobile" -> "px-3 py-2 text-sm"
      "tablet" -> "px-4 py-2.5 text-base"
      "desktop" -> "px-6 py-3 text-base"
      _ -> "px-3 py-2 text-sm"
    end
    
    color_classes = case type do
      "primary" -> "border-transparent text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500"
      "secondary" -> "border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500"
    end
    
    "#{base_classes} #{size_classes} #{color_classes}"
  end

  defp get_field_label(field, mobile_field_data) do
    if mobile_field_data && mobile_field_data["label"] do
      mobile_field_data["label"]
    else
      field["name"] || field["description"] || "Unnamed Field"
    end
  end

  defp render_field(assigns, field) do
    field_type = field["type"] || "string"
    field_name = field["name"]
    field_value = Map.get(assigns.form_data, field_name, "")
    
    # Check if this is a mobile field with special handling
    mobile_field_data = field["mobile_field_data"]
    original_mobile_type = if mobile_field_data, do: mobile_field_data["original_type"], else: nil
    
    assigns = assigns
              |> assign(:field, field) 
              |> assign(:field_value, field_value)
              |> assign(:mobile_field_data, mobile_field_data)
              |> assign(:original_mobile_type, original_mobile_type)
    
    case {field_type, original_mobile_type} do
      # Special mobile field types
      {"string", "textarea"} -> render_mobile_textarea_field(assigns)
      {"string", "select"} -> render_mobile_select_field(assigns)
      {"string", "email"} -> render_mobile_email_field(assigns)
      {"string", "password"} -> render_mobile_password_field(assigns)
      {"string", "phone"} -> render_mobile_phone_field(assigns)
      {"string", "date"} -> render_mobile_date_field(assigns)
      {"string", "datetime"} -> render_mobile_datetime_field(assigns)
      {"array", "multiselect"} -> render_mobile_multiselect_field(assigns)
      
      # Standard field types
      {"string", _} -> render_string_field(assigns)
      {"number", _} -> render_number_field(assigns)
      {"integer", _} -> render_integer_field(assigns)
      {"boolean", _} -> render_boolean_field(assigns)
      {"array", _} -> render_array_field(assigns)
      {"object", _} -> render_object_field(assigns)
      _ -> render_string_field(assigns)
    end
  end

  defp render_string_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mb-2"><%= @field["description"] %></p>
      <% end %>
      
      <%= if @field["format"] == "textarea" do %>
        <textarea
          name={"form_data[#{@field["name"]}]"}
          rows="3"
          placeholder={@field["placeholder"] || ""}
          class={get_field_classes(@device, "textarea")}
          required={@field["required"] == true}
        ><%= @field_value %></textarea>
      <% else %>
        <input
          type="text"
          name={"form_data[#{@field["name"]}]"}
          value={@field_value}
          placeholder={@field["placeholder"] || ""}
          class={get_field_classes(@device)}
          required={@field["required"] == true}
          minlength={@field["minLength"]}
          maxlength={@field["maxLength"]}
        />
      <% end %>
    </div>
    """
  end

  defp render_number_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= @field["name"] %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mb-2"><%= @field["description"] %></p>
      <% end %>
      
      <input
        type="number"
        step="0.01"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        placeholder={@field["placeholder"] || ""}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        min={@field["minimum"]}
        max={@field["maximum"]}
      />
    </div>
    """
  end

  defp render_integer_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= @field["name"] %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mb-2"><%= @field["description"] %></p>
      <% end %>
      
      <input
        type="number"
        step="1"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        placeholder={@field["placeholder"] || ""}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        min={@field["minimum"]}
        max={@field["maximum"]}
      />
    </div>
    """
  end

  defp render_boolean_field(assigns) do
    ~H"""
    <div class="mb-4">
      <div class="flex items-center">
        <input
          type="checkbox"
          name={"form_data[#{@field["name"]}]"}
          checked={@field_value == true or @field_value == "true"}
          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
        />
        <label class="ml-2 block text-sm text-gray-900">
          <%= @field["name"] %>
          <%= if @field["required"] do %>
            <span class="text-red-500">*</span>
          <% end %>
        </label>
      </div>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mt-1 ml-6"><%= @field["description"] %></p>
      <% end %>
    </div>
    """
  end

  defp render_array_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= @field["name"] %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mb-2"><%= @field["description"] %></p>
      <% end %>
      
      <textarea
        name={"form_data[#{@field["name"]}]"}
        rows="3"
        placeholder="Enter items separated by commas..."
        class={get_field_classes(@device, "textarea")}
        required={@field["required"] == true}
      ><%= @field_value %></textarea>
      <p class="text-xs text-gray-500 mt-1">Separate multiple items with commas</p>
    </div>
    """
  end

  defp render_object_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= @field["name"] %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      
      <%= if @field["description"] do %>
        <p class="text-xs text-gray-500 mb-2"><%= @field["description"] %></p>
      <% end %>
      
      <textarea
        name={"form_data[#{@field["name"]}]"}
        rows="4"
        placeholder="Enter JSON object..."
        class={get_field_classes(@device, "textarea")}
        required={@field["required"] == true}
      ><%= @field_value %></textarea>
      <p class="text-xs text-gray-500 mt-1">Enter a valid JSON object</p>
    </div>
    """
  end

  # Mobile-specific field renderers
  defp render_mobile_textarea_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <textarea
        name={"form_data[#{@field["name"]}]"}
        rows="4"
        class={get_field_classes(@device, "textarea")}
        required={@field["required"] == true}
      ><%= @field_value %></textarea>
    </div>
    """
  end

  defp render_mobile_select_field(assigns) do
    options = parse_mobile_field_options(@mobile_field_data["options"])
    assigns = assign(assigns, :options, options)
    
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <select
        name={"form_data[#{@field["name"]}]"}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
      >
        <option value="">Choose an option...</option>
        <%= for option <- @options do %>
          <option value={option.value} selected={@field_value == option.value}>
            <%= option.label %>
          </option>
        <% end %>
      </select>
    </div>
    """
  end

  defp render_mobile_email_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <input
        type="email"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        placeholder="Enter email address"
      />
    </div>
    """
  end

  defp render_mobile_password_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <input
        type="password"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        placeholder="Enter password"
      />
    </div>
    """
  end

  defp render_mobile_phone_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <input
        type="tel"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        placeholder="Enter phone number"
      />
    </div>
    """
  end

  defp render_mobile_date_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <input
        type="date"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
      />
    </div>
    """
  end

  defp render_mobile_datetime_field(assigns) do
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <input
        type="datetime-local"
        name={"form_data[#{@field["name"]}]"}
        value={@field_value}
        class={get_field_classes(@device)}
        required={@field["required"] == true}
      />
    </div>
    """
  end

  defp render_mobile_multiselect_field(assigns) do
    options = parse_mobile_field_options(@mobile_field_data["options"])
    assigns = assign(assigns, :options, options)
    
    ~H"""
    <div class="mb-4">
      <label class={get_label_classes(@device)}>
        <%= get_field_label(@field, @mobile_field_data) %>
        <%= if @field["required"] do %>
          <span class="text-red-500">*</span>
        <% end %>
      </label>
      <select
        name={"form_data[#{@field["name"]}][]"}
        multiple
        class={get_field_classes(@device)}
        required={@field["required"] == true}
        size="4"
      >
        <%= for option <- @options do %>
          <option value={option.value}>
            <%= option.label %>
          </option>
        <% end %>
      </select>
      <p class="text-xs text-gray-500 mt-1">Hold Ctrl/Cmd to select multiple options</p>
    </div>
    """
  end

  defp parse_mobile_field_options(options_string) when is_binary(options_string) do
    try do
      case Jason.decode(options_string) do
        {:ok, options} when is_list(options) ->
          Enum.map(options, fn
            %{"value" => value, "label" => label} -> %{value: value, label: label}
            %{"value" => value} -> %{value: value, label: value}
            value when is_binary(value) -> %{value: value, label: value}
            _ -> %{value: "", label: "Invalid Option"}
          end)
        {:ok, _} -> []
        {:error, _} ->
          # Fallback: treat as comma-separated values
          options_string
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.filter(&(&1 != ""))
          |> Enum.map(fn opt -> %{value: opt, label: opt} end)
      end
    rescue
      _ -> []
    end
  end
  defp parse_mobile_field_options(_), do: []

  @impl true
  def render(assigns) do
    ~H"""
    <div class={get_container_classes(@device)}>
      <%= if @form do %>
        <!-- Form Card Container -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mx-2 my-4">
          <!-- Form Header -->
          <div class="px-4 py-4 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <h2 class={[
                "font-bold text-gray-900",
                case @device do
                  "mobile" -> "text-lg"
                  "tablet" -> "text-xl"
                  "desktop" -> "text-2xl"
                  _ -> "text-lg"
                end
              ]}>
                <%= @form.name %>
              </h2>
              
              <%= if @is_mobile_form do %>
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  <.icon name="hero-device-phone-mobile" class="h-3 w-3 mr-1" />
                  Mobile Form
                </span>
              <% else %>
                <span class={[
                  "inline-flex items-center px-2 py-1 rounded text-xs font-medium",
                  case @form.http_method do
                    "GET" -> "bg-green-100 text-green-800"
                    "POST" -> "bg-blue-100 text-blue-800"
                    "PUT" -> "bg-yellow-100 text-yellow-800"
                    "PATCH" -> "bg-orange-100 text-orange-800"
                    "DELETE" -> "bg-red-100 text-red-800"
                    _ -> "bg-gray-100 text-gray-800"
                  end
                ]}>
                  <%= @form.http_method %>
                </span>
              <% end %>
            </div>
            
            <%= if @is_mobile_form && @form.mobile_form_data && @form.mobile_form_data.page do %>
              <div class={[
                "text-gray-600 space-y-1",
                case @device do
                  "mobile" -> "text-sm"
                  "tablet" -> "text-base"
                  "desktop" -> "text-base"
                  _ -> "text-sm"
                end
              ]}>
                <%= if @form.mobile_form_data.page && @form.mobile_form_data.page.screen do %>
                  <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span>📱 <%= @form.mobile_form_data.page.screen.name %></span>
                    <span>→</span>
                    <span>📄 <%= @form.mobile_form_data.page.name %></span>
                  </div>
                <% end %>
                <%= if @form.mobile_form_data.submit_to do %>
                  <div class="text-xs text-gray-500">
                    Submits to: <code class="bg-gray-100 px-1 rounded"><%= @form.mobile_form_data.submit_to %></code>
                  </div>
                <% end %>
              </div>
            <% else %>
              <%= if @form.description do %>
                <p class={[
                  "text-gray-600",
                  case @device do
                    "mobile" -> "text-sm"
                    "tablet" -> "text-base"
                    "desktop" -> "text-base"
                    _ -> "text-sm"
                  end
                ]}>
                  <%= @form.description %>
                </p>
              <% end %>
            <% end %>
          </div>

          <!-- Form Fields -->
          <div class="px-4 py-4">
            <form class="space-y-4">
              <%= if Enum.empty?(@form_fields) do %>
                <div class="text-center py-8 text-gray-500">
                  <.icon name="hero-document-text" class="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p class="text-lg font-medium mb-2">No Fields Configured</p>
                  <p class="text-sm">This form doesn't have any fields defined yet.</p>
                </div>
              <% else %>
                <%= for field <- @form_fields do %>
                  <%= render_field(assigns, field) %>
                <% end %>
              <% end %>
            </form>
          </div>
          
          <!-- Form Actions -->
          <div class="px-4 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
            <div class={[
              "flex space-x-3",
              case @device do
                "mobile" -> "flex-col space-y-3 space-x-0"
                _ -> "items-center justify-between"
              end
            ]}>
              <%= if not @step_info.is_first_step do %>
                <button
                  type="button"
                  phx-click="preview_prev_step"
                  class={[
                    get_button_classes(@device, "secondary"),
                    case @device do
                      "mobile" -> "w-full justify-center"
                      _ -> ""
                    end
                  ]}
                >
                  <.icon name="hero-chevron-left" class="h-4 w-4 mr-2" />
                  Previous
                </button>
              <% else %>
                <%= if @device != "mobile" do %>
                  <div></div>
                <% end %>
              <% end %>

              <button
                type="button"
                phx-click="preview_next_step"
                class={[
                  get_button_classes(@device, "primary"),
                  case @device do
                    "mobile" -> "w-full justify-center"
                    _ -> ""
                  end
                ]}
              >
                <%= if @step_info.is_last_step do %>
                  Complete Wizard
                  <.icon name="hero-check" class="h-4 w-4 ml-2" />
                <% else %>
                  Next Step
                  <.icon name="hero-chevron-right" class="h-4 w-4 ml-2" />
                <% end %>
              </button>
            </div>
          </div>
        </div>

        <!-- Step Information -->
        <%= if @device != "mobile" do %>
          <div class="mx-2 mt-4">
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <div class="flex items-center space-x-2 mb-2">
                <.icon name="hero-information-circle" class="h-5 w-5 text-indigo-600" />
                <h4 class="text-sm font-medium text-indigo-900">Step Information</h4>
              </div>
              <div class="space-y-1 text-sm text-indigo-700">
                <div class="flex justify-between">
                  <span>Progress:</span>
                  <span class="font-medium">Step <%= @step_info.current_step %> of <%= @step_info.total_steps %></span>
                </div>
                <div class="flex justify-between">
                  <span>Form:</span>
                  <span class="font-medium"><%= @form.name %></span>
                </div>
                <%= if @is_mobile_form do %>
                  <div class="flex justify-between">
                    <span>Type:</span>
                    <span class="font-medium text-blue-600">Mobile Form</span>
                  </div>
                  <%= if @form.mobile_form_data && @form.mobile_form_data.page && @form.mobile_form_data.page.screen do %>
                    <div class="flex justify-between">
                      <span>Screen:</span>
                      <span class="font-medium"><%= @form.mobile_form_data.page.screen.name %></span>
                    </div>
                    <div class="flex justify-between">
                      <span>Page:</span>
                      <span class="font-medium"><%= @form.mobile_form_data.page.name %></span>
                    </div>
                  <% end %>
                <% else %>
                  <div class="flex justify-between">
                    <span>Method:</span>
                    <span class="font-medium"><%= @form.http_method %></span>
                  </div>
                  <%= if @form.required do %>
                    <div class="flex justify-between">
                      <span>Status:</span>
                      <span class="font-medium text-red-600">Required Form</span>
                    </div>
                  <% end %>
                <% end %>
                <div class="flex justify-between">
                  <span>Fields:</span>
                  <span class="font-medium"><%= length(@form_fields) %> field<%= if length(@form_fields) != 1, do: "s" %></span>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="h-full flex items-center justify-center text-gray-500">
          <div class="text-center">
            <.icon name="hero-exclamation-triangle" class="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p class="text-lg font-medium mb-2">No Form Available</p>
            <p class="text-sm">No form is associated with this step.</p>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end