defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessChainComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink

  @impl true
  def update(%{source_process: source_process} = assigns, socket) do
    # Get all available processes for chaining
    all_processes = ProcessManager.list_processes()

    # Get complete chain information for this process
    {:ok, chain_info} = ProcessManager.get_complete_chain(source_process.id)
    root_process = Map.get(chain_info, :root)
    chain_processes = Map.get(chain_info, :chain, [])
    chain_id = Map.get(chain_info, :chain_id)

    # If no root process exists, auto-initialize this process as root
    {actual_root, updated_chain_processes, updated_chain_id} =
      case root_process do
        nil ->
          # Auto-initialize as root when opening the chain modal
          case ProcessManager.initialize_as_root_process(source_process.id, assigns[:current_user] && assigns[:current_user].id) do
            {:ok, _root_link} ->
              # Refresh the chain info after initialization
              {:ok, updated_chain_info} = ProcessManager.get_complete_chain(source_process.id)
              {
                Map.get(updated_chain_info, :root, source_process),
                Map.get(updated_chain_info, :chain, []),
                Map.get(updated_chain_info, :chain_id)
              }

            {:error, _reason} ->
              # Fallback if initialization fails
              {source_process, [], nil}
          end

        existing_root ->
          {existing_root, chain_processes, chain_id}
      end

    # Filter out the root process and already chained processes from available processes
    available_processes =
      all_processes
      |> Enum.filter(fn p ->
        p.id != actual_root.id &&
        !Enum.any?(updated_chain_processes, fn cp -> cp.id == p.id end)
      end)

    # Get usage statistics
    routes_using_process = get_routes_using_process(source_process.id)
    plugins_using_process = get_plugins_using_process(source_process.id, all_processes)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form, %Phoenix.HTML.Form{})
     |> assign(:available_processes, available_processes)
     |> assign(:chain_processes, updated_chain_processes)
     |> assign(:root_process, actual_root)
     |> assign(:chain_id, updated_chain_id)
     |> assign(:selected_process_id, nil)
     |> assign(:routes_using_process, routes_using_process)
     |> assign(:plugins_using_process, plugins_using_process)}
  end

  @impl true
  def handle_event("initialize-as-root", _params, socket) do
    source_id = socket.assigns.source_process.id

    # Initialize the process as root
    case ProcessManager.initialize_as_root_process(source_id, socket.assigns.current_user.id) do
      {:ok, _root_link} ->
        # Refresh the complete chain
        {:ok, chain_info} = ProcessManager.get_complete_chain(source_id)
        root_process = Map.get(chain_info, :root)
        chain_processes = Map.get(chain_info, :chain, [])
        chain_id = Map.get(chain_info, :chain_id)

        # Refresh usage statistics
        all_processes = ProcessManager.list_processes()
        routes_using_process = get_routes_using_process(socket.assigns.source_process.id)
        plugins_using_process = get_plugins_using_process(socket.assigns.source_process.id, all_processes)

        {:noreply,
         socket
         |> assign(:root_process, root_process)
         |> assign(:chain_processes, chain_processes)
         |> assign(:chain_id, chain_id)
         |> assign(:routes_using_process, routes_using_process)
         |> assign(:plugins_using_process, plugins_using_process)
         |> put_flash(:info, "Process initialized as root successfully")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to initialize as root: #{inspect(reason)}")}
    end
  end

  def handle_event("add-to-chain", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)
    source_id = socket.assigns.root_process.id

    # Get the next position
    position = length(socket.assigns.chain_processes)

    # Link the processes
    case ProcessManager.link_processes(source_id, process_id, position, socket.assigns.current_user.id) do
      {:ok, _link} ->
        # Refresh the complete chain
        {:ok, chain_info} = ProcessManager.get_complete_chain(source_id)
        root_process = Map.get(chain_info, :root)
        chain_processes = Map.get(chain_info, :chain, [])
        chain_id = Map.get(chain_info, :chain_id)

        # Update available processes
        available_processes =
          socket.assigns.available_processes
          |> Enum.filter(fn p -> p.id != process_id end)

        # Refresh usage statistics
        all_processes = ProcessManager.list_processes()
        routes_using_process = get_routes_using_process(socket.assigns.source_process.id)
        plugins_using_process = get_plugins_using_process(socket.assigns.source_process.id, all_processes)

        {:noreply,
         socket
         |> assign(:root_process, root_process)
         |> assign(:chain_processes, chain_processes)
         |> assign(:chain_id, chain_id)
         |> assign(:available_processes, available_processes)
         |> assign(:selected_process_id, nil)
         |> assign(:routes_using_process, routes_using_process)
         |> assign(:plugins_using_process, plugins_using_process)
         |> put_flash(:info, "Process added to chain successfully")}

      {:error, changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to add process to chain: #{inspect(changeset.errors)}")}
    end
  end

  def handle_event("remove-from-chain", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)
    source_id = socket.assigns.root_process.id

    # Unlink the processes
    case ProcessManager.unlink_processes(source_id, process_id) do
      {:ok, _} ->
        # Get the process that was removed
        removed_process = Enum.find(socket.assigns.chain_processes, fn p -> p.id == process_id end)

        # Refresh the complete chain
        {:ok, chain_info} = ProcessManager.get_complete_chain(source_id)
        root_process = Map.get(chain_info, :root)
        chain_processes = Map.get(chain_info, :chain, [])
        chain_id = Map.get(chain_info, :chain_id)

        # Update available processes
        available_processes =
          if removed_process do
            [removed_process | socket.assigns.available_processes]
          else
            socket.assigns.available_processes
          end

        # Refresh usage statistics
        all_processes = ProcessManager.list_processes()
        routes_using_process = get_routes_using_process(socket.assigns.source_process.id)
        plugins_using_process = get_plugins_using_process(socket.assigns.source_process.id, all_processes)

        {:noreply,
         socket
         |> assign(:root_process, root_process)
         |> assign(:chain_processes, chain_processes)
         |> assign(:chain_id, chain_id)
         |> assign(:available_processes, available_processes)
         |> assign(:routes_using_process, routes_using_process)
         |> assign(:plugins_using_process, plugins_using_process)
         |> put_flash(:info, "Process removed from chain successfully")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to remove process from chain: #{inspect(reason)}")}
    end
  end

  def handle_event("move-up", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)

    case ProcessManager.reorder_chain_process(process_id, :up, socket.assigns.current_user.id) do
      {:ok, _} ->
        # Refresh the complete chain
        source_id = socket.assigns.root_process.id
        {:ok, chain_info} = ProcessManager.get_complete_chain(source_id)
        root_process = Map.get(chain_info, :root)
        chain_processes = Map.get(chain_info, :chain, [])
        chain_id = Map.get(chain_info, :chain_id)

        {:noreply,
         socket
         |> assign(:root_process, root_process)
         |> assign(:chain_processes, chain_processes)
         |> assign(:chain_id, chain_id)
         |> put_flash(:info, "Process moved up successfully")}

      {:error, :cannot_move_further} ->
        {:noreply,
         socket
         |> put_flash(:error, "Cannot move process up further")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move process: #{inspect(reason)}")}
    end
  end

  def handle_event("move-down", %{"process-id" => process_id}, socket) do
    process_id = String.to_integer(process_id)

    case ProcessManager.reorder_chain_process(process_id, :down, socket.assigns.current_user.id) do
      {:ok, _} ->
        # Refresh the complete chain
        source_id = socket.assigns.root_process.id
        {:ok, chain_info} = ProcessManager.get_complete_chain(source_id)
        root_process = Map.get(chain_info, :root)
        chain_processes = Map.get(chain_info, :chain, [])
        chain_id = Map.get(chain_info, :chain_id)

        {:noreply,
         socket
         |> assign(:root_process, root_process)
         |> assign(:chain_processes, chain_processes)
         |> assign(:chain_id, chain_id)
         |> put_flash(:info, "Process moved down successfully")}

      {:error, :cannot_move_further} ->
        {:noreply,
         socket
         |> put_flash(:error, "Cannot move process down further")}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to move process: #{inspect(reason)}")}
    end
  end

  def handle_event("select-process", %{"process-id" => process_id}, socket) when process_id != "" do
    process_id = String.to_integer(process_id)
    {:noreply, assign(socket, :selected_process_id, process_id)}
  end

  def handle_event("select-process", %{"process-id" => ""}, socket) do
    {:noreply, assign(socket, :selected_process_id, nil)}
  end

  # Helper functions
  defp get_routes_using_process(process_id) do
    import Ecto.Query
    alias ServiceManager.Routing.DynamicRoute
    alias ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink
    
    query = from r in DynamicRoute,
            join: rpl in RouteProcessLink, on: rpl.route_id == r.id,
            where: rpl.initial_process_id == ^process_id,
            select: r
    
    ServiceManager.Repo.all(query)
  end
  
  defp get_plugins_using_process(process_id, all_processes) do
    import Ecto.Query
    alias ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink
    
    query = from pcl in ProcessChainLink,
            where: pcl.target_process_id == ^process_id and not pcl.is_root,
            select: pcl.source_process_id
    
    source_ids = ServiceManager.Repo.all(query)
    Enum.filter(all_processes, fn p -> p.id in source_ids end)
  end
  
  defp plugin_type_color("system"), do: "bg-red-100 text-red-800"
  defp plugin_type_color("public"), do: "bg-green-100 text-green-800"
  defp plugin_type_color("protected"), do: "bg-yellow-100 text-yellow-800"
  defp plugin_type_color("private"), do: "bg-gray-100 text-gray-800"
  defp plugin_type_color("enterprise"), do: "bg-purple-100 text-purple-800"
  defp plugin_type_color(_), do: "bg-blue-100 text-blue-800"
  
  defp method_color("GET"), do: "bg-green-100 text-green-800"
  defp method_color("POST"), do: "bg-blue-100 text-blue-800"
  defp method_color("PUT"), do: "bg-yellow-100 text-yellow-800"
  defp method_color("PATCH"), do: "bg-orange-100 text-orange-800"
  defp method_color("DELETE"), do: "bg-red-100 text-red-800"
  defp method_color(_), do: "bg-gray-100 text-gray-800"

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- Plugin Header Info -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-3">
              <.icon name="hero-puzzle-piece" class="h-6 w-6 text-indigo-600" />
              <h1 class="text-xl font-bold text-gray-900"><%= @source_process.name %></h1>
              <%= if @source_process.verified do %>
                <.icon name="hero-check-badge" class="h-5 w-5 text-blue-500" />
              <% end %>
              <span class={["inline-flex items-center px-2 py-1 rounded text-sm font-medium", plugin_type_color(@source_process.plugin_type)]}>
                <%= String.capitalize(@source_process.plugin_type) %>
              </span>
            </div>
            
            <p class="text-gray-600 mb-4 max-w-2xl"><%= @source_process.description || "No description provided" %></p>
            
            <div class="grid grid-cols-4 gap-6 text-sm">
              <div>
                <span class="text-gray-500 block">Version</span>
                <span class="text-gray-900 font-mono">v<%= @source_process.version %></span>
              </div>
              <div>
                <span class="text-gray-500 block">Category</span>
                <span class="text-gray-900"><%= @source_process.category %></span>
              </div>
              <div>
                <span class="text-gray-500 block">Group</span>
                <span class="text-gray-900"><%= @source_process.group %></span>
              </div>
              <div>
                <span class="text-gray-500 block">Rating</span>
                <div class="flex items-center space-x-1">
                  <.icon name="hero-star" class="h-4 w-4 text-yellow-400" />
                  <span class="text-gray-900"><%= @source_process.rating %></span>
                </div>
              </div>
            </div>

            <!-- Tags -->
            <%= if @source_process.tags && length(@source_process.tags) > 0 do %>
              <div class="mt-4">
                <div class="flex flex-wrap gap-2">
                  <%= for tag <- @source_process.tags do %>
                    <span class="inline-block px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded">
                      <%= tag %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>

          <%= if @source_process.author do %>
            <div class="text-right">
              <span class="text-sm text-gray-500 block">Author</span>
              <span class="text-sm text-gray-900"><%= @source_process.author %></span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Process Chain Flow - Now horizontal layout -->
        <div class="lg:col-span-2 bg-white border border-gray-200 rounded-lg p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <.icon name="hero-squares-plus" class="h-5 w-5 mr-2 text-purple-600" />
            Process Chain Flow
          </h2>

          <div class="flex items-center space-x-4 overflow-x-auto pb-4">
            <!-- Entry Process -->
            <%= if @root_process do %>
              <div class="flex-shrink-0 bg-gradient-to-br from-green-100 to-green-200 rounded-lg p-4 border-2 border-green-300 min-w-[200px]">
                <div class="flex items-center justify-between mb-2">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-white">
                    ROOT
                  </span>
                </div>
                <h5 class="font-medium text-gray-900 text-sm"><%= @root_process.name %></h5>
                <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                  <%= @root_process.description || "No description" %>
                </p>
              </div>
            <% else %>
              <div class="flex-shrink-0 bg-yellow-50 border-2 border-dashed border-yellow-300 rounded-lg p-4 min-w-[200px]">
                <div class="text-center">
                  <.icon name="hero-exclamation-triangle" class="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                  <p class="text-sm font-medium text-yellow-600 mb-2">No Root Process</p>
                  <button
                    phx-click="initialize-as-root"
                    phx-target={@myself}
                    class="text-xs px-3 py-1 bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200"
                  >
                    Initialize as Root
                  </button>
                </div>
              </div>
            <% end %>

            <!-- Chain Processes -->
            <%= if !Enum.empty?(@chain_processes) do %>
              <%= for {process, index} <- Enum.with_index(@chain_processes) do %>
                <!-- Arrow -->
                <div class="flex-shrink-0 text-gray-400">
                  <.icon name="hero-arrow-right" class="h-6 w-6" />
                </div>

                <!-- Process Card -->
                <div class="flex-shrink-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 min-w-[200px]">
                  <div class="flex items-center justify-between mb-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                      #<%= index + 1 %>
                    </span>
                    <div class="flex items-center space-x-1">
                      <button
                        phx-click="move-up"
                        phx-value-process-id={process.id}
                        phx-target={@myself}
                        class={["p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100", if(index == 0, do: "opacity-50 cursor-not-allowed", else: "")]}
                        disabled={index == 0}
                        title="Move up"
                      >
                        <.icon name="hero-chevron-up" class="h-3 w-3" />
                      </button>
                      <button
                        phx-click="move-down"
                        phx-value-process-id={process.id}
                        phx-target={@myself}
                        class={["p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100", if(index == length(@chain_processes) - 1, do: "opacity-50 cursor-not-allowed", else: "")]}
                        disabled={index == length(@chain_processes) - 1}
                        title="Move down"
                      >
                        <.icon name="hero-chevron-down" class="h-3 w-3" />
                      </button>
                      <button
                        phx-click="remove-from-chain"
                        phx-value-process-id={process.id}
                        phx-target={@myself}
                        class="p-1 rounded text-red-400 hover:text-red-600 hover:bg-red-50"
                        title="Remove from chain"
                      >
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                  <h5 class="font-medium text-gray-900 text-sm"><%= process.name %></h5>
                  <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                    <%= process.description || "No description" %>
                  </p>
                </div>
              <% end %>
            <% end %>

            <!-- Add Process Card -->
            <%= if @root_process && !Enum.empty?(@available_processes) do %>
              <!-- Arrow -->
              <div class="flex-shrink-0 text-gray-400">
                <.icon name="hero-arrow-right" class="h-6 w-6" />
              </div>

              <!-- Add Card -->
              <div class="flex-shrink-0 bg-white rounded-lg p-4 border-2 border-dashed border-indigo-300 min-w-[200px] hover:border-indigo-400 hover:bg-indigo-50 transition-colors duration-200">
                <div class="text-center">
                  <.icon name="hero-plus" class="h-8 w-8 text-indigo-400 mx-auto mb-2" />
                  <p class="text-sm font-medium text-indigo-600 mb-3">Add to Chain</p>
                  
                  <.simple_form
                    for={@form}
                    id="process-select-form"
                    phx-change="select-process"
                    phx-target={@myself}
                    class="mb-2"
                  >
                    <select
                      name="process-id"
                      class="block w-full text-xs rounded border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    >
                      <option value="">Select Plugin</option>
                      <%= for process <- @available_processes do %>
                        <option value={process.id} selected={@selected_process_id == process.id}>
                          <%= process.name %>
                        </option>
                      <% end %>
                    </select>
                  </.simple_form>
                  
                  <%= if @selected_process_id do %>
                    <button
                      phx-click="add-to-chain"
                      phx-value-process-id={@selected_process_id}
                      phx-target={@myself}
                      class="w-full px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700"
                    >
                      Add Plugin
                    </button>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Statistics Sidebar -->
        <div class="space-y-6">
          <!-- API Routes -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-globe-alt" class="h-5 w-5 mr-2 text-blue-600" />
              API Routes
            </h3>
            
            <div class="space-y-3">
              <%= if Enum.empty?(@routes_using_process) do %>
                <p class="text-sm text-gray-500 italic">No routes linked to this plugin</p>
              <% else %>
                <%= for route <- @routes_using_process do %>
                  <div class="bg-gray-50 border border-gray-100 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-1">
                      <div class="flex items-center space-x-2">
                        <span class={["inline-flex items-center px-2 py-1 rounded text-xs font-mono", method_color(route.method)]}>
                          <%= route.method %>
                        </span>
                        <span class="text-sm text-gray-900 font-mono truncate"><%= route.path %></span>
                      </div>
                      <span class={["inline-flex items-center px-2 py-1 rounded text-xs", if(route.enabled, do: "bg-green-100 text-green-800", else: "bg-gray-100 text-gray-600")]}>
                        <%= if route.enabled, do: "ON", else: "OFF" %>
                      </span>
                    </div>
                    <%= if route.name && route.name != "" do %>
                      <p class="text-sm text-gray-600 truncate"><%= route.name %></p>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>

          <!-- Plugin Usage -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-puzzle-piece" class="h-5 w-5 mr-2 text-purple-600" />
              Plugin Usage
            </h3>
            
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <span class="text-sm font-medium text-gray-700">Used By (<%= length(@plugins_using_process) %> plugins)</span>
              </div>
              <%= if Enum.empty?(@plugins_using_process) do %>
                <p class="text-sm text-gray-500 italic">Not used by other plugins</p>
              <% else %>
                <div class="space-y-2">
                  <%= for plugin <- @plugins_using_process do %>
                    <div class="bg-purple-50 border border-purple-100 rounded-lg p-3">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-sm text-gray-900 font-medium truncate"><%= plugin.name %></span>
                        <span class={["inline-flex items-center px-2 py-1 rounded text-xs font-medium", plugin_type_color(plugin.plugin_type)]}>
                          <%= String.capitalize(plugin.plugin_type) %>
                        </span>
                      </div>
                      <%= if plugin.description do %>
                        <p class="text-sm text-gray-600 truncate"><%= plugin.description %></p>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end