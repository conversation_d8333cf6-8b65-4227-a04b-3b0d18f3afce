defmodule ServiceManagerWeb.Backend.DynamicFormsLive.DashboardAsyncManager do
  @moduledoc """
  Handles async queries for the Dynamic Forms Dashboard.
  Extracted from dashboard.ex to improve performance and organization.
  """

  import Ecto.Query
  alias ServiceManager.Routing.DynamicRouteManager
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Repo

  @doc """
  Starts async loading of all dashboard data
  """
  def start_dashboard_async_queries(socket) do
    ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager.start_async_queries([
      {"dashboard_stats", fn -> load_dashboard_stats() end},
      {"plugin_usage_data", fn -> get_plugin_usage_data() end},
      {"route_method_data", fn -> get_route_method_distribution() end},
      {"recent_activity", fn -> get_recent_activity() end}
    ], socket)
  end

  @doc """
  Loads dashboard statistics synchronously (used in async task)
  """
  def load_dashboard_stats do
    # Get counts
    forms_count = length(DynamicFormsManager.list_forms())
    routes_count = length(DynamicRouteManager.list_routes())
    processes_count = length(ProcessManager.list_processes())

    # Get route-process links for usage stats
    route_links = get_route_process_links()
    used_processes = route_links |> Enum.map(& &1.initial_process_id) |> Enum.uniq()
    unused_processes_count = processes_count - length(used_processes)

    %{
      forms_count: forms_count,
      routes_count: routes_count,
      processes_count: processes_count,
      used_processes_count: length(used_processes),
      unused_processes_count: unused_processes_count,
      active_routes_count: count_active_routes(),
      chain_links_count: count_chain_links()
    }
  end

  @doc """
  Gets route-process links with preloaded associations
  """
  def get_route_process_links do
    query = from rpl in ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink,
            preload: [:route, :initial_process]

    Repo.all(query)
  end

  @doc """
  Counts active (enabled) routes
  """
  def count_active_routes do
    DynamicRouteManager.list_routes()
    |> Enum.count(& &1.enabled)
  end

  @doc """
  Counts process chain links (excluding root links)
  """
  def count_chain_links do
    query = from pcl in ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink,
            where: pcl.is_root == false

    Repo.aggregate(query, :count, :id)
  end

  @doc """
  Gets plugin usage data for dashboard charts
  """
  def get_plugin_usage_data do
    route_links = get_route_process_links()

    route_links
    |> Enum.group_by(& &1.initial_process.name)
    |> Enum.map(fn {name, links} -> %{name: name, count: length(links)} end)
    |> Enum.sort_by(& &1.count, :desc)
    |> Enum.take(10)
  end

  @doc """
  Gets HTTP method distribution data for routes
  """
  def get_route_method_distribution do
    DynamicRouteManager.list_routes()
    |> Enum.group_by(& &1.method)
    |> Enum.map(fn {method, routes} -> %{method: method, count: length(routes)} end)
    |> Enum.sort_by(& &1.count, :desc)
  end

  @doc """
  Gets recent activity data for dashboard timeline
  """
  def get_recent_activity do
    # Get recently created items
    recent_forms = DynamicFormsManager.list_forms() |> Enum.take(5)
    recent_routes = DynamicRouteManager.list_routes() |> Enum.take(5)
    recent_processes = ProcessManager.list_processes() |> Enum.take(5)

    activities = []

    activities = activities ++
      Enum.map(recent_forms, fn form ->
        %{
          type: :form,
          title: form.name,
          description: "Form created",
          timestamp: form.inserted_at,
          icon: "document-text",
          color: "blue"
        }
      end)

    activities = activities ++
      Enum.map(recent_routes, fn route ->
        %{
          type: :route,
          title: "#{route.method} #{route.path}",
          description: "Route created",
          timestamp: route.inserted_at,
          icon: "globe-alt",
          color: "green"
        }
      end)

    activities = activities ++
      Enum.map(recent_processes, fn process ->
        %{
          type: :process,
          title: process.name,
          description: "Process created",
          timestamp: process.inserted_at,
          icon: "cog",
          color: "purple"
        }
      end)

    activities
    |> Enum.sort_by(& &1.timestamp, {:desc, NaiveDateTime})
    |> Enum.take(10)
  end
end