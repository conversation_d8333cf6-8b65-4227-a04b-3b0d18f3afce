defmodule ServiceManagerWeb.Backend.DynamicFormsLive.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Forms.DynamicForm
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Routing.DynamicRouteManager

  @impl true
  def update(%{form: form} = assigns, socket) do
    changeset = DynamicForm.changeset(form, %{})

    # Create a map for the new field form
    new_field = %{
      name: "",
      type: "string",
      description: "",
      required: false,
      min_length: nil,
      max_length: nil,
      minimum: nil,
      maximum: nil,
      format: nil
    }


    # Ensure current_user is properly assigned if it exists in assigns
    current_user = Map.get(assigns, :current_user)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form, to_form(changeset))
     |> assign(:http_methods, ["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
     |> assign(:field_types, ["string", "number", "integer", "boolean", "array", "object"])
     |> assign(:form_fields, get_form_fields(form))
     |> assign(:new_field, new_field)
     |> assign(:current_field_values, new_field)  # Add current_field_values assign
     |> assign(:field_form, to_form(%{"field" => new_field}))
     |> assign(:current_user, current_user)}
  end

  defp get_form_fields(form) do
    if form.form && form.form["fields"] do
      form.form["fields"]
    else
      []
    end
  end

  @impl true
  def handle_event("validate", %{"dynamic_form" => form_params}, socket) do
    changeset =
      socket.assigns.form.data
      |> DynamicForm.changeset(form_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :form, to_form(changeset))}
  end


  def handle_event("validate-field", _params, socket) do

    # Get the current field values from the socket
    current_field = socket.assigns.new_field
    current_field_values = socket.assigns.current_field_values || %{}

    # Get the current form params - ensure we're getting all params
    current_form_params = socket.assigns.field_form.params || %{}

    # Update only the changed field in the form params
    updated_form_params = Map.put(current_form_params, "field", current_field)

    {:noreply,
     socket
     |> assign(:new_field, current_field)
     |> assign(:current_field_values, current_field_values)
     |> assign(:field_form, to_form(updated_form_params))}
  end


  @impl true
  def handle_event("add-field", params, socket) do

    field_params = cond do
      # Case 1: Params are in the expected format with a "field" key
      Map.has_key?(params, "field") ->
        case params["field"] do
          encoded_field when is_binary(encoded_field) ->
            Jason.decode!(encoded_field)
          field_params ->
            field_params
        end

      # Case 2: Params are at the top level (direct form values)
      true ->
        # Filter out the _target and _unused keys
        params
        |> Map.drop(["_target" | Enum.filter(Map.keys(params), &String.starts_with?(&1, "_unused_"))])
    end

    # Skip if name is empty
    if field_params["name"] || field_params[:name] do
      # Get the name from either string or atom key
      name = field_params["name"] || field_params[:name]

      # Skip if name is empty string
      if name != "" do
        # Convert string keys to atoms for the new field
      field = %{
        "name" => field_params["name"],
        "type" => field_params["type"] || "string",
        "description" => field_params["description"] || "",
        "required" => field_params["required"] == "true"
      }

      # Add optional parameters if they're not empty
      field = add_optional_param(field, "minLength", field_params["min_length"])
      field = add_optional_param(field, "maxLength", field_params["max_length"])
      field = add_optional_param(field, "minimum", field_params["minimum"])
      field = add_optional_param(field, "maximum", field_params["maximum"])
      field = add_optional_param(field, "format", field_params["format"])

      # Add the new field to the form fields
      updated_fields = socket.assigns.form_fields ++ [field]

      # Get the current form data
      form_data = socket.assigns.form.data

      # Update the form data with the new fields
      updated_form_data = Map.update(form_data, :form, %{"fields" => updated_fields}, fn current_form ->
        if is_map(current_form) do
          Map.put(current_form, "fields", updated_fields)
        else
          %{"fields" => updated_fields}
        end
      end)

      # Create a new changeset with the updated form data
      changeset = DynamicForm.changeset(updated_form_data, %{})

      # Reset the new field form
      new_field = %{
        name: "",
        type: "string",
        description: "",
        required: false,
        min_length: nil,
        max_length: nil,
        minimum: nil,
        maximum: nil,
        format: nil
      }

      {:noreply,
       socket
       |> assign(:form_fields, updated_fields)
       |> assign(:form, to_form(changeset))
       |> assign(:new_field, new_field)
       |> assign(:current_field_values, new_field)  # Reset current_field_values
       |> assign(:field_form, to_form(%{"field" => new_field}))}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("remove-field", %{"index" => index}, socket) do
    index = String.to_integer(index)
    updated_fields = List.delete_at(socket.assigns.form_fields, index)

    # Get the current form data
    form_data = socket.assigns.form.data

    # Update the form data with the updated fields
    updated_form_data = Map.update(form_data, :form, %{"fields" => updated_fields}, fn current_form ->
      if is_map(current_form) do
        Map.put(current_form, "fields", updated_fields)
      else
        %{"fields" => updated_fields}
      end
    end)

    # Create a new changeset with the updated form data
    changeset = DynamicForm.changeset(updated_form_data, %{})

    # Reset the new field form
    new_field = %{
      name: "",
      type: "string",
      description: "",
      required: false,
      min_length: nil,
      max_length: nil,
      minimum: nil,
      maximum: nil,
      format: nil
    }

    {:noreply,
     socket
     |> assign(:form_fields, updated_fields)
     |> assign(:form, to_form(changeset))
     |> assign(:new_field, new_field)
     |> assign(:current_field_values, new_field)  # Reset current_field_values
     |> assign(:field_form, to_form(%{"field" => new_field}))}
  end

  @impl true
  def handle_event("save", params, socket) do
    form_params = params["dynamic_form"] || %{}

    # Check if there's a new field being added at the same time
    field_params = params["field"]

    # Get the current form fields
    form_fields = socket.assigns.form_fields

    # If there's a new field with a name, add it to the form fields
    form_fields = if field_params && field_params["name"] && field_params["name"] != "" do
      field = %{
        "name" => field_params["name"],
        "type" => field_params["type"] || "string",
        "description" => field_params["description"] || "",
        "required" => field_params["required"] == "true"
      }

      # Add optional parameters if they're not empty
      field = add_optional_param(field, "minLength", field_params["min_length"])
      field = add_optional_param(field, "maxLength", field_params["max_length"])
      field = add_optional_param(field, "minimum", field_params["minimum"])
      field = add_optional_param(field, "maximum", field_params["maximum"])
      field = add_optional_param(field, "format", field_params["format"])

      form_fields ++ [field]
    else
      form_fields
    end

    # Get the current form data
    form_data = socket.assigns.form.data

    # Update the form data with the updated fields
    updated_form_data = Map.update(form_data, :form, %{"fields" => form_fields}, fn current_form ->
      if is_map(current_form) do
        Map.put(current_form, "fields", form_fields)
      else
        %{"fields" => form_fields}
      end
    end)

    # Create a new changeset with the updated form data
    changeset = DynamicForm.changeset(updated_form_data, form_params)

    # Reset the new field form
    new_field = %{
      name: "",
      type: "string",
      description: "",
      required: false,
      min_length: nil,
      max_length: nil,
      minimum: nil,
      maximum: nil,
      format: nil
    }

    # Add the form fields to the form params
    form_params = Map.put(form_params, "form", %{
      "fields" => form_fields
    })

    # Update the socket assigns before saving
    socket = socket
      |> assign(:form_fields, form_fields)
      |> assign(:form, to_form(changeset))
      |> assign(:new_field, new_field)
      |> assign(:current_field_values, new_field)  # Reset current_field_values
      |> assign(:field_form, to_form(%{"field" => new_field}))

    save_form(socket, socket.assigns.action, form_params)
  end

  defp add_optional_param(field, _key, value) when is_nil(value) or value == "", do: field
  defp add_optional_param(field, key, value) do
    # Try to convert to integer or float if appropriate
    parsed_value = case key do
      "minLength" -> parse_integer(value)
      "maxLength" -> parse_integer(value)
      "minimum" -> parse_number(value)
      "maximum" -> parse_number(value)
      _ -> value
    end

    Map.put(field, key, parsed_value)
  end

  defp parse_integer(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> value
    end
  end

  defp parse_number(value) do
    case Float.parse(value) do
      {float, ""} -> float
      _ ->
        case Integer.parse(value) do
          {int, ""} -> int
          _ -> value
        end
    end
  end

  defp save_form(socket, :edit, form_params) do
    # Generate validation schema from form definition
    validation_schema = DynamicFormsManager.generate_json_schema(%{
      "form" => form_params["form"]
    })

    # Add validation schema to form params
    form_params = Map.put(form_params, "validation_schema", validation_schema)
    form_params = Map.put(form_params, "form", form_params["form"])

    case DynamicFormsManager.update_form(socket.assigns.form.data.id |> ServiceManager.Forms.DynamicForm.find(), form_params) do
      {:ok, _form} ->
        {:noreply,
         socket
         |> put_flash(:info, "Form updated successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  defp save_form(socket, :new, form_params) do
    # Generate validation schema from form definition
    validation_schema = DynamicFormsManager.generate_json_schema(%{
      "form" => form_params["form"]
    })

    # Add validation schema to form params
    form_params = Map.put(form_params, "validation_schema", validation_schema)
    form_params = Map.put(form_params, "form", form_params["form"])

    case DynamicFormsManager.create_form(form_params) do
      {:ok, _form} ->
        {:noreply,
         socket
         |> put_flash(:info, "Form created successfully")
         |> push_navigate(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-4xl mx-auto bg-white shadow-md rounded-lg overflow-hidden">
      <div class="px-6 py-4 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <h2 class="text-xl font-bold"><%= @title %></h2>
      </div>

      

      <div class="p-6">



        <!-- Main form for basic form data -->
        <.simple_form
          for={@form}
          id="form-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
          class="space-y-6"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="col-span-1 md:col-span-2">
              <.input field={@form[:name]} type="text" label="Form Name" required class="w-full" />
            </div>

            <div class="col-span-1 md:col-span-2">
              <.input field={@form[:description]} type="textarea" label="Description" class="w-full" />
            </div>

            <div>
              <.input
                field={@form[:http_method]}
                type="select"
                label="HTTP Method"
                options={@http_methods}
                required
                class="w-full"
              />
            </div>

            <div class="flex items-center mt-6">
              <.input
                field={@form[:required]}
                type="checkbox"
                label="Required for Route"
                class="mr-2"
              />
              <span class="text-xs text-gray-500 ml-2">
                If checked, this form will be required for any route it's linked to
              </span>
            </div>
          </div>

          <!-- Form Fields Section -->
          <div class="mt-8 border-t pt-6">
            <h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              Form Fields
            </h3>

            <!-- Field list cards -->
            <div class="mb-6 space-y-4">
              <%= if Enum.empty?(@form_fields) do %>
                <div class="bg-gray-50 rounded-lg p-4 text-center text-gray-500 border border-dashed border-gray-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p>No fields added yet. Add fields using the form below.</p>
                </div>
              <% else %>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <%= for {field, index} <- Enum.with_index(@form_fields) do %>
                    <div class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                      <div class="p-4">
                        <div class="flex justify-between items-start">
                          <div class="flex items-center">
                            <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 mr-3">
                              <%= String.first(field["name"] || "") %>
                            </span>
                            <div>
                              <h4 class="text-md font-medium text-gray-900"><%= field["name"] %></h4>
                              <p class="text-sm text-gray-500"><%= field["type"] %></p>
                            </div>
                          </div>
                          <div class="flex space-x-2">
                            <.button
                              type="button"
                              phx-click="remove-field"
                              phx-value-index={index}
                              phx-target={@myself}
                              class="text-red-600 hover:text-red-900 bg-transparent border-0 p-1 rounded-full hover:bg-red-50"
                              title="Remove field"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </.button>
                            <.button
                              type="button"
                              phx-click="remove-field"
                              phx-value-index={index}
                              phx-target={@myself}
                              class="text-white bg-red-600 hover:bg-red-700 rounded-full w-6 h-6 flex items-center justify-center"
                              title="Remove field"
                            >
                              -
                            </.button>
                          </div>
                        </div>

                        <div class="mt-3 text-sm">
                          <%= if field["description"] && field["description"] != "" do %>
                            <p class="text-gray-600 mb-2"><%= field["description"] %></p>
                          <% end %>

                          <div class="flex flex-wrap gap-2 mt-2">
                            <%= if field["required"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Required
                              </span>
                            <% end %>

                            <%= if field["minLength"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Min Length: <%= field["minLength"] %>
                              </span>
                            <% end %>

                            <%= if field["maxLength"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Max Length: <%= field["maxLength"] %>
                              </span>
                            <% end %>

                            <%= if field["minimum"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Min Value: <%= field["minimum"] %>
                              </span>
                            <% end %>

                            <%= if field["maximum"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Max Value: <%= field["maximum"] %>
                              </span>
                            <% end %>

                            <%= if field["format"] do %>
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Format: <%= field["format"] %>
                              </span>
                            <% end %>
                          </div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>


          </div>

          <div class="flex justify-between items-center pt-6 border-t mt-8">
            <.button
              type="button"
              class="px-4 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 flex items-center"
              phx-click={JS.navigate(@patch)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Cancel
            </.button>
            <.button type="submit" phx-disable-with="Saving..." class="px-4 py-2 text-sm text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Save Form
            </.button>
          </div>
        </.simple_form>

         <!-- Add New Field Form -->
            <div class="bg-gray-50 rounded-lg p-5 border border-gray-200">
              <h4 class="text-md font-semibold mb-4 text-gray-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Field
              </h4>

              <.simple_form
                for={@field_form}
                id="add-field-form"
                phx-target={@myself}
                phx-change="validate-field"
                phx-submit="add-field"
              >
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <.input
                      field={@field_form[:name]}
                      type="text"
                      label="Field Name"
                      placeholder="Enter field name"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:type]}
                      type="select"
                      label="Field Type"
                      options={@field_types}
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:required]}
                      type="select"
                      label="Required"
                      options={[{"No", "false"}, {"Yes", "true"}]}
                      class="w-full"
                    />
                  </div>

                  <div class="col-span-1 md:col-span-3">
                    <.input
                      field={@field_form[:description]}
                      type="text"
                      label="Description"
                      placeholder="Field description (optional)"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:min_length]}
                      type="number"
                      label="Min Length"
                      placeholder="Optional"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:max_length]}
                      type="number"
                      label="Max Length"
                      placeholder="Optional"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:format]}
                      type="text"
                      label="Format"
                      placeholder="e.g., email, date-time"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:minimum]}
                      type="number"
                      label="Minimum Value"
                      placeholder="Optional"
                      class="w-full"
                    />
                  </div>

                  <div>
                    <.input
                      field={@field_form[:maximum]}
                      type="number"
                      label="Maximum Value"
                      placeholder="Optional"
                      class="w-full"
                    />
                  </div>

                  <div class="flex items-end">
                    <.button type="submit" class="w-full bg-green-600 hover:bg-green-700 flex items-center justify-center" phx-disable-with="Adding...">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Field
                    </.button>
                  </div>
                </div>
              </.simple_form>
            </div>

      </div>
    </div>
    """
  end

  # Helper function to get chain information for all routes
  defp get_route_chain_info(routes) do
    Enum.map(routes, fn route ->
      # Get the initial process for this route
      case ProcessManager.get_initial_process(route.id) do
        {:ok, initial_process} ->
          # Get the complete chain starting from the initial process
          case ProcessManager.get_complete_chain(initial_process.id) do
            {:ok, chain_info} ->
              %{
                route: route,
                initial_process: initial_process,
                root_process: Map.get(chain_info, :root),
                chain_processes: Map.get(chain_info, :chain, []),
                chain_id: Map.get(chain_info, :chain_id)
              }
            
            {:error, _} ->
              %{
                route: route,
                initial_process: initial_process,
                root_process: nil,
                chain_processes: [],
                chain_id: nil
              }
          end
        
        {:error, _} ->
          %{
            route: route,
            initial_process: nil,
            root_process: nil,
            chain_processes: [],
            chain_id: nil
          }
      end
    end)
  end
end
