defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessEditLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Schemas.Dynamic.Processes.{DynamicProcess, ProcessManager, CodeStepManager}
  alias ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(%{"id" => id}, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "Edit Process")
      |> assign(:process, nil)
      |> assign(:changeset, nil)
      |> assign(:code_content, "")
      |> assign(:validation_errors, [])
      |> assign(:loading, true)
      |> assign(:loading_usage_stats, true)
      |> assign(:routes_using_process, [])
      |> assign(:plugins_using_process, [])
      |> assign(:active_tab, "basic")
      |> assign(:active_right_tab, "editor")
      |> assign(:code_steps, [])
      |> assign(:editing_step, nil)
      |> assign(:step_changeset, nil)
      |> assign(:process_id, String.to_integer(id))
      |> AsyncQueryManager.init_async_tracking()
      |> load_process_async(String.to_integer(id))

    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    IO.puts("📨 Received async result for ref: #{inspect(ref)}")
    IO.inspect(result, label: "📦 Async result")
    updated_socket = AsyncQueryManager.handle_async_result(ref, result, socket, &handle_usage_stats_result/3)
    IO.puts("✅ Async result handled")
    {:noreply, updated_socket}
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    {:noreply, AsyncQueryManager.handle_async_failure(ref, socket)}
  end

  @impl true
  def handle_event("validate", %{"dynamic_process" => process_params}, socket) do
    # Include the current code content in the validation parameters
    process_params_with_code = Map.put(process_params, "code", socket.assigns.code_content)
    
    # Convert tags string to array if needed
    processed_params = process_tags_input(process_params_with_code)
    
    changeset =
      socket.assigns.process
      |> DynamicProcess.changeset(processed_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("code_change", %{"code" => code}, socket) do
    # Only update the code content, don't update changeset until save/validate
    # This prevents unnecessary re-renders and cursor jumping
    {:noreply, assign(socket, :code_content, code)}
  end

  def handle_event("save", %{"dynamic_process" => process_params}, socket) do
    # Include the current code content in the save parameters
    process_params_with_code = Map.put(process_params, "code", socket.assigns.code_content)
    
    # Process tags input
    processed_params = process_tags_input(process_params_with_code)

    # Debug logging
    IO.puts("🔧 Save attempt with params:")
    IO.inspect(processed_params, label: "Processed params")

    case ProcessManager.update_process(socket.assigns.process, processed_params) do
      {:ok, process} ->
        IO.puts("✅ Process saved successfully")
        {:noreply,
         socket
         |> put_flash(:info, "Process updated successfully")
         |> assign(:process, process)
         |> assign(:changeset, DynamicProcess.changeset(process, %{}))}

      {:error, %Ecto.Changeset{} = changeset} ->
        IO.puts("❌ Save failed with changeset errors:")
        IO.inspect(changeset.errors, label: "Changeset errors")
        
        {:noreply, 
         socket
         |> assign(:changeset, changeset)
         |> put_flash(:error, "Failed to save: #{format_changeset_errors(changeset)}")}
    end
  end

  def handle_event("validate_code", _params, socket) do
    code = socket.assigns.code_content
    
    # Basic Elixir syntax validation
    validation_errors = validate_elixir_code(code)
    
    {:noreply, assign(socket, :validation_errors, validation_errors)}
  end

  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end

  def handle_event("toggle_execution_mode", %{"mode" => mode}, socket) do
    # Update the changeset with the new execution mode
    updated_changeset = 
      socket.assigns.changeset
      |> Ecto.Changeset.put_change(:execution_mode, mode)

    {:noreply, assign(socket, :changeset, updated_changeset)}
  end

  def handle_event("switch_right_tab", %{"tab" => tab}, socket) do
    socket = 
      case tab do
        "steps" ->
          load_code_steps(socket)
        _ ->
          socket
      end
    
    {:noreply, assign(socket, :active_right_tab, tab)}
  end

  def handle_event("cancel", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/dynamic-forms/processes")}
  end

  def handle_event("add_step", _params, socket) do
    # For now, create a simple line step as example
    case CodeStepManager.create_step(%{
      process_id: socket.assigns.process_id,
      step_type: "line",
      content: "# New step",
      created_by_id: socket.assigns.current_user.id
    }) do
      {:ok, _step} ->
        {:noreply, 
         socket
         |> put_flash(:info, "Step added successfully")
         |> load_code_steps()}
      {:error, _changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to add step")}
    end
  end

  def handle_event("edit_step", %{"id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case CodeStepManager.get_step(step_id) do
      {:ok, step} ->
        # Convert expected_inputs map to JSON string for form display
        expected_inputs_json = if(step.expected_inputs == %{} or is_nil(step.expected_inputs),
          do: "",
          else: Jason.encode!(step.expected_inputs))
        
        # Create attrs with JSON string for the form
        form_attrs = %{
          step_type: step.step_type,
          content: step.content,
          step_category: step.step_category,
          order_position: step.order_position,
          indentation_level: step.indentation_level,
          expected_inputs: expected_inputs_json,
          input_count: step.input_count,
          is_root: step.is_root
        }
        
        changeset = ServiceManager.Schemas.Dynamic.Processes.CodeStep.changeset(step, form_attrs)
        {:noreply,
         socket
         |> assign(:editing_step, step)
         |> assign(:step_changeset, changeset)
         |> assign(:step_form, to_form(changeset))}
      {:error, :not_found} ->
        {:noreply, put_flash(socket, :error, "Step not found")}
    end
  end

  def handle_event("delete_step", %{"id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    # Find the step in our loaded steps
    case Enum.find(socket.assigns.code_steps, &(&1.id == step_id)) do
      nil ->
        {:noreply, put_flash(socket, :error, "Step not found")}
      step ->
        case CodeStepManager.delete_step(step) do
          {:ok, _} ->
            {:noreply,
             socket
             |> put_flash(:info, "Step deleted successfully")
             |> load_code_steps()}
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to delete step")}
        end
    end
  end

  def handle_event("generate_code_from_steps", _params, socket) do
    {:ok, generated_code} = CodeStepManager.generate_code_from_steps(socket.assigns.process_id)
    {:noreply,
     socket
     |> assign(:code_content, generated_code)
     |> put_flash(:info, "Code generated from steps successfully")}
  end

  def handle_event("save_step", %{"code_step" => step_params}, socket) do
    # Convert expected_inputs JSON string back to map
    processed_params = case Map.get(step_params, "expected_inputs", "") do
      "" -> 
        Map.put(step_params, "expected_inputs", %{})
      json_string ->
        case Jason.decode(json_string) do
          {:ok, parsed_map} -> Map.put(step_params, "expected_inputs", parsed_map)
          {:error, _} -> Map.put(step_params, "expected_inputs", %{})
        end
    end
    
    case CodeStepManager.update_step(socket.assigns.editing_step, processed_params) do
      {:ok, _step} ->
        {:noreply,
         socket
         |> assign(:editing_step, nil)
         |> assign(:step_changeset, nil)
         |> assign(:step_form, nil)
         |> put_flash(:info, "Step updated successfully")
         |> load_code_steps()}
      {:error, changeset} ->
        {:noreply, 
         socket
         |> assign(:step_changeset, changeset)
         |> assign(:step_form, to_form(changeset))}
    end
  end

  def handle_event("validate_step", %{"code_step" => step_params}, socket) do
    # Convert expected_inputs JSON string back to map for validation
    processed_params = case Map.get(step_params, "expected_inputs", "") do
      "" -> 
        Map.put(step_params, "expected_inputs", %{})
      json_string ->
        case Jason.decode(json_string) do
          {:ok, parsed_map} -> Map.put(step_params, "expected_inputs", parsed_map)
          {:error, _} -> Map.put(step_params, "expected_inputs", %{})
        end
    end
    
    changeset =
      socket.assigns.editing_step
      |> ServiceManager.Schemas.Dynamic.Processes.CodeStep.changeset(processed_params)
      |> Map.put(:action, :validate)

    {:noreply, 
     socket
     |> assign(:step_changeset, changeset)
     |> assign(:step_form, to_form(changeset))}
  end

  def handle_event("cancel_edit_step", _params, socket) do
    {:noreply,
     socket
     |> assign(:editing_step, nil)
     |> assign(:step_changeset, nil)
     |> assign(:step_form, nil)}
  end

  def handle_event("sync", _params, socket) do
    case ProcessManager.sync_process(socket.assigns.process.id) do
      {:ok, updated_process} ->
        {:noreply,
         socket
         |> assign(:process, updated_process)
         |> put_flash(:info, "Process synced successfully - in-memory code reloaded")}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Sync failed: #{reason}")}
    end
  end

  # Async loading functions
  defp load_process_async(socket, process_id) do
    IO.puts("🔍 Starting async process load for ID: #{process_id}")
    AsyncQueryManager.start_async_query("load_process", fn ->
      IO.puts("📊 Executing ProcessManager.get_process(#{process_id})")
      result = ProcessManager.get_process(process_id)
      IO.inspect(result, label: "📋 Process query result")
      result
    end, socket)
  end
  
  defp load_usage_stats_async(socket, process_id) do
    IO.puts("📈 Starting usage stats queries for process ID: #{process_id}")
    AsyncQueryManager.load_process_dependencies_async(process_id, socket)
  end

  defp handle_usage_stats_result(socket, query_key, result) do
    IO.puts("🎯 Handling result for query: #{query_key}")
    IO.inspect(result, label: "🔍 Query result details")
    
    case {query_key, result} do
      {"load_process", {:ok, {:ok, process}}} ->
        IO.puts("✅ Successfully loaded process: #{process.name}")
        changeset = DynamicProcess.changeset(process, %{})
        
        updated_socket =
          socket
          |> assign(:page_title, "Edit Process - #{process.name}")
          |> assign(:process, process)
          |> assign(:changeset, changeset)
          |> assign(:code_content, process.code || "")
          |> assign(:loading, false)
          |> load_usage_stats_async(process.id)
        
        IO.puts("🔄 Socket updated with process data, loading set to false")
        updated_socket
      
      {"load_process", {:ok, {:error, :not_found}}} ->
        IO.puts("❌ Process not found!")
        socket
        |> put_flash(:error, "Process not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/processes")
      
      {"routes_using_process", {:ok, routes}} ->
        assign(socket, :routes_using_process, routes)
      
      {"plugins_using_process", {:ok, plugins}} ->
        # Check if all queries are complete
        loading_stats = AsyncQueryManager.has_pending_tasks?(socket)
        
        socket
        |> assign(:plugins_using_process, plugins)
        |> assign(:loading_usage_stats, loading_stats)
      
      {_, {:error, _error}} ->
        # On error, just continue without the data
        loading_stats = AsyncQueryManager.has_pending_tasks?(socket)
        assign(socket, :loading_usage_stats, loading_stats)
    end
  end


  # Helper function to validate Elixir code syntax
  defp validate_elixir_code(""), do: []
  defp validate_elixir_code(code) do
    try do
      Code.string_to_quoted!(code)
      []
    rescue
      SyntaxError -> ["Syntax error in Elixir code"]
      TokenMissingError -> ["Missing token in Elixir code"] 
      _ -> ["Invalid Elixir code syntax"]
    end
  end

  # Helper function to process form inputs (tags and expected_params)
  defp process_tags_input(params) do
    params
    |> process_tags_field()
    |> process_expected_params_field()
  end

  defp process_tags_field(params) do
    case Map.get(params, "tags") do
      nil -> params
      tags when is_binary(tags) ->
        tag_list = 
          tags
          |> String.split(",")
          |> Enum.map(&String.trim/1)
          |> Enum.filter(&(&1 != ""))
        Map.put(params, "tags", tag_list)
      _ -> params
    end
  end

  defp process_expected_params_field(params) do
    case Map.get(params, "expected_params") do
      nil -> params
      json_string when is_binary(json_string) ->
        case Jason.decode(json_string) do
          {:ok, parsed_json} -> Map.put(params, "expected_params", parsed_json)
          {:error, _} -> params # Keep the string, let validation handle the error
        end
      _ -> params
    end
  end

  # Helper function to convert tags array to comma-separated string for display
  defp tags_to_string(tags) when is_list(tags), do: Enum.join(tags, ", ")
  defp tags_to_string(_), do: ""

  defp load_code_steps(socket) do
    {:ok, steps} = CodeStepManager.get_process_steps(socket.assigns.process_id)
    assign(socket, :code_steps, steps)
  end

  defp plugin_type_color("system"), do: "bg-red-100 text-red-800"
  defp plugin_type_color("public"), do: "bg-green-100 text-green-800"
  defp plugin_type_color("protected"), do: "bg-yellow-100 text-yellow-800"
  defp plugin_type_color("private"), do: "bg-gray-100 text-gray-800"
  defp plugin_type_color("enterprise"), do: "bg-purple-100 text-purple-800"
  defp plugin_type_color(_), do: "bg-blue-100 text-blue-800"

  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _opts}} ->
      "#{field}: #{message}"
    end)
    |> Enum.join(", ")
  end

  defp sync_status_color(status) when status in ["synced"], do: "text-green-500"
  defp sync_status_color(status) when status in ["pending"], do: "text-orange-500"  
  defp sync_status_color(status) when status in ["error"], do: "text-red-500"
  defp sync_status_color(_), do: "text-gray-500"

  defp sync_status_icon(status) when status in ["synced"], do: "hero-check-circle"
  defp sync_status_icon(status) when status in ["pending"], do: "hero-clock"
  defp sync_status_icon(status) when status in ["error"], do: "hero-exclamation-triangle"
  defp sync_status_icon(_), do: "hero-question-mark-circle"

  defp sync_status_title(status) when status in ["synced"], do: "Code is synced with database"
  defp sync_status_title(status) when status in ["pending"], do: "Code sync pending"
  defp sync_status_title(status) when status in ["error"], do: "Code sync failed"
  defp sync_status_title(_), do: "Code sync status unknown"

  @impl true
  def render(assigns) do
    ~H"""
    <%= if @loading do %>
      <div class="min-h-screen bg-gray-50 flex items-center justify-center">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p class="text-gray-600">Loading process...</p>
        </div>
      </div>
    <% else %>
      <div class="min-h-screen bg-gray-50">
        <!-- Navigation -->
        <.live_component
          module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
          id="navigation"
          page_title="Edit Process"
          subtitle={@process.name}
          current_page={:processes}
          breadcrumb={[
            %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
            %{title: "Plugins", link: ~p"/mobileBanking/dynamic-forms/processes"},
            %{title: "Edit #{@process.name}"}
          ]}
        />

      <!-- Header -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <.icon name="hero-puzzle-piece" class="h-8 w-8 text-indigo-600" />
              <div>
                <h1 class="text-2xl font-bold text-gray-900"><%= @process.name %></h1>
                <div class="flex items-center space-x-2 mt-1">
                  <span class={["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium", plugin_type_color(@process.plugin_type)]}>
                    <%= String.capitalize(@process.plugin_type) %>
                  </span>
                  <span class="text-sm text-gray-500">Version <%= @process.version %></span>
                  <%= if @process.verified do %>
                    <.icon name="hero-check-badge" class="h-4 w-4 text-blue-500" />
                  <% end %>
                  <.icon 
                    name={sync_status_icon(@process.sync_status || "pending")} 
                    class={["h-4 w-4", sync_status_color(@process.sync_status || "pending")]}
                    title={sync_status_title(@process.sync_status || "pending")}
                  />
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <button
                phx-click="cancel"
                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <.icon name="hero-x-mark" class="h-4 w-4 mr-2" />
                Cancel
              </button>
              
              <button
                phx-click="validate_code"
                class="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md shadow-sm text-blue-700 bg-white hover:bg-blue-50"
              >
                <.icon name="hero-check-circle" class="h-4 w-4 mr-2" />
                Validate Code
              </button>

              <button
                phx-click="sync"
                class="inline-flex items-center px-4 py-2 border border-green-300 text-sm font-medium rounded-md shadow-sm text-green-700 bg-white hover:bg-green-50"
              >
                <.icon name="hero-arrow-path" class="h-4 w-4 mr-2" />
                Sync Code
              </button>
              
              <button
                form="process-form"
                type="submit"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <.icon name="hero-check" class="h-4 w-4 mr-2" />
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content - Split Layout -->
      <div class="flex h-[calc(100vh-12rem)]">
        <!-- Left Side - Form -->
        <div class="w-1/2 bg-white border-r border-gray-200 overflow-y-auto">
          <div class="p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <.icon name="hero-document-text" class="h-5 w-5 mr-2 text-indigo-600" />
              Process Configuration
            </h2>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 mb-6">
              <nav class="-mb-px flex space-x-8">
                <button
                  phx-click="switch_tab"
                  phx-value-tab="basic"
                  class={[
                    "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap",
                    if(@active_tab == "basic", 
                      do: "border-indigo-500 text-indigo-600", 
                      else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
                  ]}
                >
                  <.icon name="hero-information-circle" class="h-4 w-4 mr-2 inline" />
                  Basic Info
                </button>
                
                <button
                  phx-click="switch_tab"
                  phx-value-tab="classification"
                  class={[
                    "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap",
                    if(@active_tab == "classification", 
                      do: "border-indigo-500 text-indigo-600", 
                      else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
                  ]}
                >
                  <.icon name="hero-tag" class="h-4 w-4 mr-2 inline" />
                  Classification
                </button>
                
                <button
                  phx-click="switch_tab"
                  phx-value-tab="metadata"
                  class={[
                    "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap",
                    if(@active_tab == "metadata", 
                      do: "border-indigo-500 text-indigo-600", 
                      else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
                  ]}
                >
                  <.icon name="hero-cog-6-tooth" class="h-4 w-4 mr-2 inline" />
                  Metadata
                </button>
                
                <button
                  phx-click="switch_tab"
                  phx-value-tab="usage"
                  class={[
                    "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap",
                    if(@active_tab == "usage", 
                      do: "border-indigo-500 text-indigo-600", 
                      else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
                  ]}
                >
                  <.icon name="hero-chart-bar" class="h-4 w-4 mr-2 inline" />
                  Usage
                </button>
              </nav>
            </div>

            <.simple_form
              for={@changeset}
              id="process-form"
              phx-change="validate"
              phx-submit="save"
              class="space-y-6"
            >
              <!-- Basic Information Tab -->
              <div :if={@active_tab == "basic"} class="space-y-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <.icon name="hero-information-circle" class="h-5 w-5 mr-2 text-indigo-600" />
                    Basic Information
                  </h3>
                  <div class="grid grid-cols-1 gap-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Process Name *</label>
                      <input
                        type="text"
                        name="dynamic_process[name]"
                        value={Ecto.Changeset.get_field(@changeset, :name)}
                        placeholder="Enter process name"
                        required
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                      <p class="mt-1 text-xs text-gray-500">A unique, descriptive name for your process</p>
                    </div>
                    
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                      <textarea
                        name="dynamic_process[description]"
                        rows="4"
                        placeholder="Describe what this process does, its purpose, and key functionality..."
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      ><%= Ecto.Changeset.get_field(@changeset, :description) %></textarea>
                      <p class="mt-1 text-xs text-gray-500">Help others understand what this process does</p>
                    </div>

                    <!-- Execution Mode Selection -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Execution Mode *</label>
                      <div class="grid grid-cols-2 gap-4">
                        <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 relative">
                          <input
                            type="radio"
                            name="dynamic_process[execution_mode]"
                            value="plugin_code"
                            checked={Ecto.Changeset.get_field(@changeset, :execution_mode) == "plugin_code"}
                            phx-click="toggle_execution_mode"
                            phx-value-mode="plugin_code"
                            class="sr-only"
                          />
                          <div class="flex items-center w-full">
                            <div class="flex-shrink-0">
                              <.icon name="hero-code-bracket" class="h-6 w-6 text-blue-600" />
                            </div>
                            <div class="ml-3 flex-1">
                              <div class="text-sm font-medium text-gray-900">Plugin Code</div>
                              <div class="text-xs text-gray-500">Execute from the code editor</div>
                            </div>
                            <div class="absolute top-2 right-2">
                              <div class={[
                                "h-4 w-4 border-2 rounded-full bg-white",
                                if(Ecto.Changeset.get_field(@changeset, :execution_mode) == "plugin_code",
                                  do: "border-blue-600", else: "border-gray-300")
                              ]}>
                                <div class={[
                                  "h-2 w-2 bg-blue-600 rounded-full m-0.5",
                                  if(Ecto.Changeset.get_field(@changeset, :execution_mode) == "plugin_code",
                                    do: "opacity-100", else: "opacity-0")
                                ]}></div>
                              </div>
                            </div>
                          </div>
                        </label>
                        
                        <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 relative">
                          <input
                            type="radio"
                            name="dynamic_process[execution_mode]"
                            value="steps"
                            checked={Ecto.Changeset.get_field(@changeset, :execution_mode) == "steps"}
                            phx-click="toggle_execution_mode"
                            phx-value-mode="steps"
                            class="sr-only"
                          />
                          <div class="flex items-center w-full">
                            <div class="flex-shrink-0">
                              <.icon name="hero-list-bullet" class="h-6 w-6 text-green-600" />
                            </div>
                            <div class="ml-3 flex-1">
                              <div class="text-sm font-medium text-gray-900">Step-by-Step</div>
                              <div class="text-xs text-gray-500">Execute from code steps</div>
                            </div>
                            <div class="absolute top-2 right-2">
                              <div class={[
                                "h-4 w-4 border-2 rounded-full bg-white",
                                if(Ecto.Changeset.get_field(@changeset, :execution_mode) == "steps",
                                  do: "border-green-600", else: "border-gray-300")
                              ]}>
                                <div class={[
                                  "h-2 w-2 bg-green-600 rounded-full m-0.5",
                                  if(Ecto.Changeset.get_field(@changeset, :execution_mode) == "steps",
                                    do: "opacity-100", else: "opacity-0")
                                ]}></div>
                              </div>
                            </div>
                          </div>
                        </label>
                      </div>
                      <p class="mt-2 text-xs text-gray-500">Choose whether to execute from the raw code or from the structured steps</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Version *</label>
                        <input
                          type="text"
                          name="dynamic_process[version]"
                          value={Ecto.Changeset.get_field(@changeset, :version)}
                          placeholder="1.0.0"
                          required
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Semantic version (e.g., 1.0.0)</p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Author</label>
                        <input
                          type="text"
                          name="dynamic_process[author]"
                          value={Ecto.Changeset.get_field(@changeset, :author)}
                          placeholder="Author name"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Process creator or maintainer</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">License</label>
                        <input
                          type="text"
                          name="dynamic_process[license]"
                          value={Ecto.Changeset.get_field(@changeset, :license)}
                          placeholder="MIT"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Software license type</p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                        <input
                          type="number"
                          name="dynamic_process[rating]"
                          value={Ecto.Changeset.get_field(@changeset, :rating)}
                          placeholder="0.0"
                          step="0.1"
                          min="0"
                          max="5"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Quality rating (0.0 - 5.0)</p>
                      </div>
                    </div>

                    <div class="space-y-3">
                      <label class="text-sm font-medium text-gray-700">Options</label>
                      <div class="space-y-2">
                        <label class="flex items-center">
                          <input
                            type="checkbox"
                            name="dynamic_process[verified]"
                            checked={Ecto.Changeset.get_field(@changeset, :verified)}
                            class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          />
                          <span class="ml-3 text-sm text-gray-700">Verified Process</span>
                          <span class="ml-2 text-xs text-gray-500">- Mark as verified and trusted</span>
                        </label>
                        
                        <label class="flex items-center">
                          <input
                            type="checkbox"
                            name="dynamic_process[featured]"
                            checked={Ecto.Changeset.get_field(@changeset, :featured)}
                            class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          />
                          <span class="ml-3 text-sm text-gray-700">Featured Process</span>
                          <span class="ml-2 text-xs text-gray-500">- Highlight in library listings</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Classification Tab -->
              <div :if={@active_tab == "classification"} class="space-y-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <.icon name="hero-tag" class="h-5 w-5 mr-2 text-indigo-600" />
                    Classification & Organization
                  </h3>
                  <div class="grid grid-cols-1 gap-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Plugin Type *</label>
                      <select
                        name="dynamic_process[plugin_type]"
                        required
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      >
                        <%= for {label, value, description} <- [
                          {"Public", "public", "Available to all users"},
                          {"Private", "private", "Restricted access"},
                          {"Protected", "protected", "Limited visibility"},
                          {"System", "system", "Core system processes"},
                          {"Enterprise", "enterprise", "Enterprise-only features"}
                        ] do %>
                          <option value={value} selected={Ecto.Changeset.get_field(@changeset, :plugin_type) == value}>
                            <%= label %> - <%= description %>
                          </option>
                        <% end %>
                      </select>
                      <p class="mt-1 text-xs text-gray-500">Determines access level and visibility</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <input
                          type="text"
                          name="dynamic_process[category]"
                          value={Ecto.Changeset.get_field(@changeset, :category)}
                          placeholder="e.g., authentication, payment, data-processing"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Functional category for grouping</p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Group</label>
                        <input
                          type="text"
                          name="dynamic_process[group]"
                          value={Ecto.Changeset.get_field(@changeset, :group)}
                          placeholder="e.g., core, third-party, custom"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Organizational grouping</p>
                      </div>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
                      <input
                        type="text"
                        name="dynamic_process[tags]"
                        value={tags_to_string(Ecto.Changeset.get_field(@changeset, :tags))}
                        placeholder="authentication, security, api, webhook, transformer"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                      <p class="mt-1 text-xs text-gray-500">Keywords for discovery and filtering (separate with commas)</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Metadata Tab -->
              <div :if={@active_tab == "metadata"} class="space-y-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <.icon name="hero-cog-6-tooth" class="h-5 w-5 mr-2 text-indigo-600" />
                    Metadata & Configuration
                  </h3>
                  <div class="grid grid-cols-1 gap-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Expected Parameters (JSON Schema)</label>
                      <textarea
                        name="dynamic_process[expected_params]"
                        rows="8"
                        placeholder='{
  "user_id": {
    "type": "string", 
    "required": true,
    "description": "User identifier"
  },
  "amount": {
    "type": "number",
    "minimum": 0,
    "description": "Transaction amount"
  }
}'
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                      ><%= Jason.encode!(Ecto.Changeset.get_field(@changeset, :expected_params) || %{}, pretty: true) %></textarea>
                      <p class="mt-1 text-xs text-gray-500">Define expected parameters as JSON schema for validation and documentation</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Repository URL</label>
                        <input
                          type="url"
                          name="dynamic_process[repository_url]"
                          value={Ecto.Changeset.get_field(@changeset, :repository_url)}
                          placeholder="https://github.com/user/repo"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Source code repository</p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Documentation URL</label>
                        <input
                          type="url"
                          name="dynamic_process[documentation_url]"
                          value={Ecto.Changeset.get_field(@changeset, :documentation_url)}
                          placeholder="https://docs.example.com"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p class="mt-1 text-xs text-gray-500">Documentation or help URL</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Usage Tab -->
              <div :if={@active_tab == "usage"} class="space-y-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <.icon name="hero-chart-bar" class="h-5 w-5 mr-2 text-indigo-600" />
                    Usage Statistics & Dependencies
                  </h3>
                  
                  <%= if @loading_usage_stats do %>
                    <div class="animate-pulse space-y-4">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="h-20 bg-gray-200 rounded"></div>
                        <div class="h-20 bg-gray-200 rounded"></div>
                      </div>
                      <div class="h-32 bg-gray-200 rounded"></div>
                    </div>
                  <% else %>
                    <div class="grid grid-cols-1 gap-6">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                          <div class="flex items-center justify-between">
                            <div>
                              <p class="text-sm font-medium text-gray-500">Used in Routes</p>
                              <p class="text-2xl font-bold text-blue-600"><%= length(@routes_using_process) %></p>
                            </div>
                            <.icon name="hero-globe-alt" class="h-8 w-8 text-blue-400" />
                          </div>
                        </div>
                        
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                          <div class="flex items-center justify-between">
                            <div>
                              <p class="text-sm font-medium text-gray-500">Used by Plugins</p>
                              <p class="text-2xl font-bold text-purple-600"><%= length(@plugins_using_process) %></p>
                            </div>
                            <.icon name="hero-puzzle-piece" class="h-8 w-8 text-purple-400" />
                          </div>
                        </div>
                      </div>
                      
                      <%= if !Enum.empty?(@routes_using_process) do %>
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                          <h4 class="text-sm font-medium text-gray-900 mb-3">Routes using this plugin:</h4>
                          <div class="space-y-2">
                            <%= for route <- @routes_using_process do %>
                              <div class="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                <div class="flex items-center space-x-3">
                                  <span class={[
                                    "inline-flex items-center px-2 py-1 rounded text-xs font-mono font-medium",
                                    case route.method do
                                      "GET" -> "bg-green-100 text-green-800"
                                      "POST" -> "bg-blue-100 text-blue-800"
                                      "PUT" -> "bg-yellow-100 text-yellow-800"
                                      "DELETE" -> "bg-red-100 text-red-800"
                                      _ -> "bg-gray-100 text-gray-800"
                                    end
                                  ]}>
                                    <%= route.method %>
                                  </span>
                                  <span class="text-sm text-gray-900 font-mono"><%= route.path %></span>
                                </div>
                                <span class="text-xs text-gray-500"><%= route.name %></span>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% end %>

                      <%= if !Enum.empty?(@plugins_using_process) do %>
                        <div class="bg-white rounded-lg p-4 border border-gray-200">
                          <h4 class="text-sm font-medium text-gray-900 mb-3">Plugins depending on this one:</h4>
                          <div class="space-y-2">
                            <%= for plugin <- @plugins_using_process do %>
                              <div class="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                <div class="flex items-center space-x-3">
                                  <.icon name="hero-puzzle-piece" class="h-4 w-4 text-purple-500" />
                                  <span class="text-sm text-gray-900 font-medium"><%= plugin.name %></span>
                                </div>
                                <span class="text-xs text-gray-500"><%= plugin.plugin_type %></span>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% end %>

                      <%= if Enum.empty?(@routes_using_process) && Enum.empty?(@plugins_using_process) do %>
                        <div class="text-center py-8">
                          <.icon name="hero-chart-bar-square" class="h-12 w-12 text-gray-400 mx-auto mb-3" />
                          <p class="text-gray-500 text-sm">This process is not currently being used by any routes or plugins.</p>
                          <p class="text-gray-400 text-xs mt-1">Usage statistics will appear here once the process is integrated.</p>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </.simple_form>
          </div>
        </div>

        <!-- Right Side - Code Editor -->
        <div class="w-1/2 bg-gray-900 overflow-hidden flex flex-col">
          <div class="bg-gray-800 px-4 py-3 border-b border-gray-700">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-sm font-medium text-white flex items-center">
                <.icon name="hero-code-bracket" class="h-4 w-4 mr-2" />
                Code Editor
              </h2>
              <div class="flex items-center space-x-2">
                <%= if @validation_errors != [] do %>
                  <span class="text-xs text-red-400 flex items-center">
                    <.icon name="hero-exclamation-triangle" class="h-3 w-3 mr-1" />
                    Syntax Error
                  </span>
                <% else %>
                  <span class="text-xs text-green-400 flex items-center">
                    <.icon name="hero-check-circle" class="h-3 w-3 mr-1" />
                    Valid
                  </span>
                <% end %>
                <span class="text-xs text-gray-400">
                  <%= String.length(@code_content) %> chars
                </span>
              </div>
            </div>
            
            <!-- Right Panel Tabs -->
            <div class="border-b border-gray-700">
              <nav class="-mb-px flex space-x-8">
                <button
                  phx-click="switch_right_tab"
                  phx-value-tab="editor"
                  class={[
                    "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm",
                    if(@active_right_tab == "editor", 
                      do: "border-indigo-400 text-indigo-400", 
                      else: "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600")
                  ]}
                >
                  <.icon name="hero-code-bracket" class="h-4 w-4 mr-1" />
                  Raw Code
                </button>
                
                <button
                  phx-click="switch_right_tab"
                  phx-value-tab="steps"
                  class={[
                    "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm",
                    if(@active_right_tab == "steps", 
                      do: "border-indigo-400 text-indigo-400", 
                      else: "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600")
                  ]}
                >
                  <.icon name="hero-list-bullet" class="h-4 w-4 mr-1" />
                  Step Editor
                </button>
              </nav>
            </div>
          </div>
          
          <div class="flex-1 relative">
            <!-- Raw Code Editor Tab -->
            <div :if={@active_right_tab == "editor"} class="h-full">
              <!-- Hidden input for form submission -->
              <input type="hidden" id="code-input" name="code" value={@code_content} />
              
              <!-- Ace Editor Container -->
              <div
                id="ace-editor"
                phx-hook="CodeEditor"
                data-target="code-input"
                data-mode="elixir"
                class="w-full h-full"
                phx-update="ignore"
              ></div>

              <!-- Validation Errors -->
              <%= if @validation_errors != [] do %>
                <div class="absolute bottom-0 left-0 right-0 bg-red-900 border-t border-red-700 p-2 z-10">
                  <%= for error <- @validation_errors do %>
                    <p class="text-xs text-red-400"><%= error %></p>
                  <% end %>
                </div>
              <% end %>
            </div>

            <!-- Step Editor Tab -->
            <div :if={@active_right_tab == "steps"} class="h-full overflow-y-auto bg-gray-800">
              <div class="p-4">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-sm font-medium text-white">Code Steps</h3>
                  <button
                    phx-click="add_step"
                    class="inline-flex items-center px-3 py-1 border border-indigo-600 text-xs font-medium rounded text-indigo-400 bg-gray-900 hover:bg-gray-700"
                  >
                    <.icon name="hero-plus" class="h-3 w-3 mr-1" />
                    Add Step
                  </button>
                </div>

                <%= if Enum.empty?(@code_steps) do %>
                  <div class="text-center py-8">
                    <.icon name="hero-list-bullet" class="h-12 w-12 text-gray-600 mx-auto mb-3" />
                    <p class="text-sm text-gray-400 mb-4">No code steps defined yet</p>
                    <button
                      phx-click="add_step"
                      class="inline-flex items-center px-4 py-2 border border-indigo-600 text-sm font-medium rounded text-indigo-400 bg-gray-900 hover:bg-gray-700"
                    >
                      <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                      Create First Step
                    </button>
                  </div>
                <% else %>
                  <div class="space-y-2">
                    <%= for {step, index} <- Enum.with_index(@code_steps) do %>
                      <div class="bg-gray-900 border border-gray-700 rounded-lg p-3">
                        <div class="flex items-start justify-between">
                          <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-2">
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <%= step.step_type %>
                              </span>
                              <span class="text-xs text-gray-400">#<%= index + 1 %></span>
                              <%= if step.indentation_level > 0 do %>
                                <span class="text-xs text-gray-400">indent: <%= step.indentation_level %></span>
                              <% end %>
                            </div>
                            <code class="text-sm text-green-400 font-mono block bg-gray-800 p-2 rounded">
                              <%= String.duplicate(" ", step.indentation_level) %><%= step.content %>
                            </code>
                          </div>
                          <div class="flex items-center space-x-1 ml-2">
                            <button
                              phx-click="edit_step"
                              phx-value-id={step.id}
                              class="inline-flex items-center p-1 border border-gray-600 text-xs rounded text-gray-400 bg-gray-800 hover:bg-gray-700"
                              title="Edit Step"
                            >
                              <.icon name="hero-pencil" class="h-3 w-3" />
                            </button>
                            <button
                              phx-click="delete_step"
                              phx-value-id={step.id}
                              class="inline-flex items-center p-1 border border-red-600 text-xs rounded text-red-400 bg-gray-800 hover:bg-gray-700"
                              title="Delete Step"
                              data-confirm="Are you sure you want to delete this step?"
                            >
                              <.icon name="hero-trash" class="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                  
                  <div class="mt-6 pt-4 border-t border-gray-700">
                    <button
                      phx-click="generate_code_from_steps"
                      class="w-full inline-flex justify-center items-center px-4 py-2 border border-green-600 text-sm font-medium rounded text-green-400 bg-gray-900 hover:bg-gray-700"
                    >
                      <.icon name="hero-arrow-path" class="h-4 w-4 mr-2" />
                      Generate Raw Code from Steps
                    </button>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step Editing Modal -->
      <%= if @editing_step do %>
        <.modal id="step-edit-modal" show on_cancel={JS.exec("phx-click", to: "#cancel-edit-step")}>
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-lg font-semibold text-gray-900">Edit Code Step</h2>
              <button
                id="cancel-edit-step"
                phx-click="cancel_edit_step"
                class="text-gray-400 hover:text-gray-600"
              >
                <.icon name="hero-x-mark" class="h-5 w-5" />
              </button>
            </div>

            <.simple_form
              for={@step_changeset}
              id="step-form"
              phx-change="validate_step"
              phx-submit="save_step"
            >
              <div class="grid grid-cols-1 gap-4">
                <!-- Step Type -->
                <.input
                  field={@step_form[:step_type]}
                  type="select"
                  label="Step Type"
                  options={[
                    {"Line of Code", "line"},
                    {"Function Opening", "function_open"},
                    {"Function Closing", "function_close"},
                    {"Block Opening", "block_open"},
                    {"Block Closing", "block_close"},
                    {"Comment", "comment"},
                    {"Import Statement", "import"},
                    {"Custom", "custom"}
                  ]}
                  required
                />

                <!-- Content -->
                <.input
                  field={@step_form[:content]}
                  type="textarea"
                  label="Code Content"
                  placeholder="Enter the code content for this step..."
                  rows={4}
                  required
                />

                <!-- Step Category -->
                <.input
                  field={@step_form[:step_category]}
                  type="text"
                  label="Category (Optional)"
                  placeholder="e.g., validation, processing, response"
                />

                <div class="grid grid-cols-2 gap-4">
                  <!-- Order Position -->
                  <.input
                    field={@step_form[:order_position]}
                    type="number"
                    label="Order Position"
                    min="0"
                    required
                  />

                  <!-- Indentation Level -->
                  <.input
                    field={@step_form[:indentation_level]}
                    type="number"
                    label="Indentation Level (spaces: 0, 2, 4, 6, etc.)"
                    min="0"
                    required
                  />
                </div>

                <!-- Expected Inputs (JSON) -->
                <.input
                  field={@step_form[:expected_inputs]}
                  type="textarea"
                  label="Expected Inputs (Optional JSON Format)"
                  placeholder='{"param1": "string", "param2": "integer"}'
                  rows={3}
                />

                <div class="grid grid-cols-2 gap-4">
                  <!-- Input Count -->
                  <.input
                    field={@step_form[:input_count]}
                    type="number"
                    label="Input Count"
                    min="0"
                  />

                  <!-- Root Step Checkbox -->
                  <div class="flex items-center pt-6">
                    <.input
                      field={@step_form[:is_root]}
                      type="checkbox"
                      label="Root Step (top-level step)"
                    />
                  </div>
                </div>
              </div>

              <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  phx-click="cancel_edit_step"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700"
                >
                  <.icon name="hero-check" class="h-4 w-4 mr-2" />
                  Save Step
                </button>
              </div>
            </.simple_form>
          </div>
        </.modal>
      <% end %>
      </div>
    <% end %>
    """
  end
end