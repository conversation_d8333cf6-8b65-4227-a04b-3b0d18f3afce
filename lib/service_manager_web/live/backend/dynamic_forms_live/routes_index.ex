defmodule ServiceManagerWeb.Backend.DynamicFormsLive.RoutesIndex do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Routing.{DynamicRoute, DynamicRouteManager}
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/routes")
      |> assign(:loading_route_data, true)
      |> assign(:view_mode, "list")
      |> assign(:tree_view, true)
      |> assign(:expanded_categories, MapSet.new())
      |> assign(:expanded_groups, MapSet.new())
      |> AsyncQueryManager.init_async_tracking()
      |> load_routes()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        socket
        |> assign(:page_title, "Edit Route")
        |> assign(:route, route)
    end
  end

  defp apply_action(socket, :link_forms, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        # Get all forms for linking
        forms = ServiceManager.Forms.DynamicFormsManager.list_forms()
        routes = DynamicRouteManager.list_routes()
        
        socket
        |> assign(:page_title, "Link Forms or Wizards to Route")
        |> assign(:route, route)
        |> assign(:route_form, %{route_id: route.id, form_id: nil})
        |> assign(:forms, forms)
        |> assign(:all_routes, routes)
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Route")
    |> assign(:route, %DynamicRoute{})
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        socket
        |> put_flash(:error, "Route not found")
        |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/routes")
      
      route ->
        {:noreply, push_navigate(socket, to: ~p"/mobileBanking/dynamic-forms/routes/#{route.id}/details")}
    end
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "API Routes")
    |> assign(:route, nil)
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    AsyncQueryManager.handle_async_result(ref, result, socket, &handle_route_data_result/4)
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    {:noreply, AsyncQueryManager.handle_async_failure(ref, socket)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Route not found")
         |> load_routes()}
      
      route ->
        {:ok, _} = DynamicRouteManager.delete_route(route)
        {:noreply, 
         socket
         |> put_flash(:info, "Route deleted successfully")
         |> load_routes()}
    end
  end

  @impl true
  def handle_event("toggle-enabled", %{"id" => id}, socket) do
    case DynamicRouteManager.get_route(id) do
      nil ->
        {:noreply, 
         socket
         |> put_flash(:error, "Route not found")
         |> load_routes()}
      
      route ->
        {:ok, _} = DynamicRouteManager.update_route(route, %{enabled: !route.enabled})
        {:noreply, 
         socket
         |> put_flash(:info, "Route #{route.enabled && "disabled" || "enabled"} successfully")
         |> load_routes()}
    end
  end

  @impl true
  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  @impl true
  def handle_event("toggle_tree_view", _params, socket) do
    {:noreply, assign(socket, :tree_view, !socket.assigns.tree_view)}
  end

  @impl true
  def handle_event("toggle_category", %{"category" => category}, socket) do
    expanded_categories = socket.assigns.expanded_categories
    
    updated_expanded = if MapSet.member?(expanded_categories, category) do
      MapSet.delete(expanded_categories, category)
    else
      MapSet.put(expanded_categories, category)
    end
    
    {:noreply, assign(socket, :expanded_categories, updated_expanded)}
  end

  @impl true
  def handle_event("toggle_group", %{"group" => group, "category" => category}, socket) do
    group_key = "#{category}:#{group}"
    expanded_groups = socket.assigns.expanded_groups
    
    updated_expanded = if MapSet.member?(expanded_groups, group_key) do
      MapSet.delete(expanded_groups, group_key)
    else
      MapSet.put(expanded_groups, group_key)
    end
    
    {:noreply, assign(socket, :expanded_groups, updated_expanded)}
  end

  defp load_routes(socket) do
    routes = DynamicRouteManager.list_routes()
    
    # Load routes with placeholder data initially
    routes_with_placeholder = Enum.map(routes, fn route ->
      Map.merge(route, %{
        initial_process: nil,
        linked_forms_count: 0,
        linked_wizards_count: 0,
        linked_wizard: nil,
        loading: true
      })
    end)
    
    socket
    |> assign(:routes, routes_with_placeholder)
    |> load_route_data_async()
  end

  defp load_route_data_async(socket) do
    AsyncQueryManager.start_async_item_queries(
      socket.assigns.routes,
      "route_data",
      &calculate_route_data/1,
      socket
    )
  end

  defp handle_route_data_result(socket, _query_key, route_id, result) do
    case result do
      {:ok, route_data} ->
        # Update the specific route with the loaded data
        updated_routes = Enum.map(socket.assigns.routes, fn route ->
          if route.id == route_id do
            route
            |> Map.merge(route_data)
            |> Map.put(:loading, false)
          else
            route
          end
        end)

        # Check if all route data tasks are complete
        loading_route_data = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "route_data")

        {:noreply,
         socket
         |> assign(:routes, updated_routes)
         |> assign(:loading_route_data, loading_route_data)}

      {:error, _error} ->
        # On error, just mark as not loading with empty data
        updated_routes = Enum.map(socket.assigns.routes, fn route ->
          if route.id == route_id do
            route
            |> Map.put(:initial_process, nil)
            |> Map.put(:linked_forms_count, 0)
            |> Map.put(:linked_wizards_count, 0)
            |> Map.put(:linked_wizard, nil)
            |> Map.put(:loading, false)
          else
            route
          end
        end)

        loading_route_data = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "route_data")

        {:noreply,
         socket
         |> assign(:routes, updated_routes)
         |> assign(:loading_route_data, loading_route_data)}
    end
  end

  defp calculate_route_data(route) do
    try do
      # Get linked process (with individual error handling)
      initial_process = try do
        case ProcessManager.get_initial_process(route.id) do
          {:ok, process} -> process
          {:error, _} -> nil
        end
      rescue
        error -> 
          IO.inspect(error, label: "ProcessManager error for route #{route.id}")
          nil
      end
      
      # Get linked forms count (with individual error handling)
      linked_forms_count = try do
        count_linked_forms(route.id)
      rescue
        error ->
          IO.inspect(error, label: "Forms count error for route #{route.id}")
          0
      end
      
      # Get linked wizards count (with individual error handling)
      linked_wizards_count = try do
        count_linked_wizards(route.id)
      rescue
        error ->
          IO.inspect(error, label: "Wizards count error for route #{route.id}")
          0
      end
      
      # Get wizard info if linked (with individual error handling)
      linked_wizard = try do
        DynamicFormsManager.get_wizard_for_route(route.id)
      rescue
        error ->
          IO.inspect(error, label: "Wizard info error for route #{route.id}")
          nil
      end
      
      # Debug output
      IO.inspect(%{
        route_id: route.id,
        route_name: route.name,
        initial_process: !!initial_process,
        linked_forms_count: linked_forms_count,
        linked_wizards_count: linked_wizards_count,
        linked_wizard: !!linked_wizard
      }, label: "Route data calculated")
      
      %{
        initial_process: initial_process,
        linked_forms_count: linked_forms_count,
        linked_wizards_count: linked_wizards_count,
        linked_wizard: linked_wizard
      }
    rescue
      error ->
        IO.inspect(error, label: "Overall route data calculation error for route #{route.id}")
        %{
          initial_process: nil,
          linked_forms_count: 0,
          linked_wizards_count: 0,
          linked_wizard: nil
        }
    end
  end

  defp count_linked_forms(route_id) do
    import Ecto.Query
    
    query = from drf in "dynamic_route_forms",
            where: drf.route_id == ^route_id
    
    ServiceManager.Repo.aggregate(query, :count, :route_id)
  end

  defp count_linked_wizards(route_id) do
    import Ecto.Query
    
    query = from drw in "dynamic_route_wizards",
            where: drw.route_id == ^route_id
    
    ServiceManager.Repo.aggregate(query, :count, :route_id)
  end

  defp render_tree_structure(routes, expanded_categories, expanded_groups) do
    # Create a hierarchical tree structure: Category > Group > Method > Routes
    tree_data = routes
    |> Enum.group_by(fn route -> route.category || "Uncategorized" end)
    |> Enum.map(fn {category, category_routes} ->
      # Group routes within each category by group_name
      groups = category_routes
      |> Enum.group_by(fn route -> route.group_name || "Default" end)
      |> Enum.map(fn {group_name, group_routes} ->
        # Group routes within each group by method
        methods = group_routes
        |> Enum.group_by(fn route -> route.method || "Unknown" end)
        |> Enum.map(fn {method, method_routes} ->
          %{
            name: method,
            type: :method,
            routes: method_routes,
            count: length(method_routes)
          }
        end)
        |> Enum.sort_by(& &1.name)
        
        %{
          name: group_name,
          type: :group,
          methods: methods,
          routes: group_routes,
          count: length(group_routes)
        }
      end)
      |> Enum.sort_by(& &1.name)
      
      %{
        name: category,
        type: :category,
        groups: groups,
        routes: category_routes,
        count: length(category_routes)
      }
    end)
    |> Enum.sort_by(fn %{name: name} -> 
      if name == "Uncategorized", do: "zzz", else: name
    end)
    
    assigns = %{
      tree_data: tree_data,
      expanded_categories: expanded_categories,
      expanded_groups: expanded_groups
    }
    
    ~H"""
    <div class="space-y-2">
      <%= for category <- @tree_data do %>
        <div class="border border-gray-200 rounded-lg bg-white shadow-sm">
          <!-- Category Header (Always visible) -->
          <div 
            class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            phx-click="toggle_category"
            phx-value-category={category.name}
          >
            <div class="flex items-center space-x-3">
              <!-- Expand/Collapse Icon -->
              <%= if MapSet.member?(@expanded_categories, category.name) do %>
                <.icon name="hero-chevron-down" class="h-4 w-4 text-gray-500 transition-transform" />
              <% else %>
                <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-500 transition-transform" />
              <% end %>
              
              <!-- Category Icon and Name -->
              <%= if category.name == "Uncategorized" do %>
                <.icon name="hero-folder-open" class="h-5 w-5 text-gray-400" />
                <h3 class="text-base font-medium text-gray-600"><%= category.name %></h3>
              <% else %>
                <.icon name="hero-folder" class="h-5 w-5 text-indigo-500" />
                <h3 class="text-base font-medium text-gray-900"><%= category.name %></h3>
              <% end %>
            </div>
            
            <!-- Category Stats -->
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span><%= length(category.groups) %> groups</span>
              <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-700">
                <%= category.count %> route<%= if category.count != 1, do: "s" %>
              </span>
            </div>
          </div>
          
          <!-- Category Content (Expandable) -->
          <%= if MapSet.member?(@expanded_categories, category.name) do %>
            <div class="border-t border-gray-200 bg-gray-50">
              <%= for group <- category.groups do %>
                <div class="ml-6 border-l border-gray-200">
                  <!-- Group Header -->
                  <div 
                    class="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
                    phx-click="toggle_group"
                    phx-value-group={group.name}
                    phx-value-category={category.name}
                  >
                    <!-- Connection line -->
                    <div class="absolute left-0 top-1/2 w-4 h-px bg-gray-200"></div>
                    
                    <div class="flex items-center space-x-3 ml-4">
                      <!-- Expand/Collapse Icon for Group -->
                      <%= if MapSet.member?(@expanded_groups, "#{category.name}:#{group.name}") do %>
                        <.icon name="hero-chevron-down" class="h-3 w-3 text-gray-400" />
                      <% else %>
                        <.icon name="hero-chevron-right" class="h-3 w-3 text-gray-400" />
                      <% end %>
                      
                      <!-- Group Icon and Name -->
                      <.icon name="hero-rectangle-group" class="h-4 w-4 text-purple-500" />
                      <span class="text-sm font-medium text-gray-700"><%= group.name %></span>
                    </div>
                    
                    <!-- Group Stats -->
                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                      <span><%= length(group.methods) %> methods</span>
                      <span class="bg-gray-200 px-2 py-1 rounded">
                        <%= group.count %> route<%= if group.count != 1, do: "s" %>
                      </span>
                    </div>
                  </div>
                  
                  <!-- Group Content (Routes by Method) -->
                  <%= if MapSet.member?(@expanded_groups, "#{category.name}:#{group.name}") do %>
                    <div class="ml-8 pb-4">
                      <%= for method <- group.methods do %>
                        <div class="mb-4 border-l border-gray-200 pl-4">
                          <!-- Method Header -->
                          <div class="flex items-center justify-between mb-3 relative">
                            <!-- Connection line -->
                            <div class="absolute left-0 top-1/2 w-4 h-px bg-gray-200"></div>
                            
                            <div class="flex items-center space-x-2 ml-4">
                              <span class={[
                                "inline-flex items-center px-2 py-1 rounded text-xs font-medium",
                                case method.name do
                                  "GET" -> "bg-green-100 text-green-800"
                                  "POST" -> "bg-blue-100 text-blue-800"
                                  "PUT" -> "bg-yellow-100 text-yellow-800"
                                  "PATCH" -> "bg-orange-100 text-orange-800"
                                  "DELETE" -> "bg-red-100 text-red-800"
                                  _ -> "bg-gray-100 text-gray-800"
                                end
                              ]}>
                                <%= method.name %>
                              </span>
                              <span class="text-xs text-gray-500">
                                <%= method.count %> route<%= if method.count != 1, do: "s" %>
                              </span>
                            </div>
                          </div>
                          
                          <!-- Routes List -->
                          <div class="space-y-2 ml-4">
                            <%= for route <- method.routes do %>
                              <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md hover:shadow-sm transition-shadow">
                                <div class="flex items-center space-x-3">
                                  <!-- Route Status -->
                                  <%= if route.enabled do %>
                                    <.icon name="hero-check-circle" class="h-4 w-4 text-green-500" />
                                  <% else %>
                                    <.icon name="hero-x-circle" class="h-4 w-4 text-red-500" />
                                  <% end %>
                                  
                                  <!-- Route Info -->
                                  <div>
                                    <div class="flex items-center space-x-2">
                                      <span class="text-sm font-medium text-gray-900"><%= route.name %></span>
                                      <%= if route.priority && route.priority > 0 do %>
                                        <span class={[
                                          "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                                          case route.priority do
                                            p when p >= 8 -> "bg-red-100 text-red-800"
                                            p when p >= 5 -> "bg-yellow-100 text-yellow-800" 
                                            _ -> "bg-green-100 text-green-800"
                                          end
                                        ]}>
                                          <.icon name="hero-flag" class="h-3 w-3 mr-1" />
                                          <%= route.priority %>
                                        </span>
                                      <% end %>
                                    </div>
                                    <code class="text-xs text-gray-500 font-mono"><%= route.path %></code>
                                    <%= if route.description do %>
                                      <p class="text-xs text-gray-500 mt-1"><%= route.description %></p>
                                    <% end %>
                                  </div>
                                </div>
                                
                                <!-- Route Actions -->
                                <div class="flex items-center space-x-2">
                                  <!-- Route Stats -->
                                  <div class="flex items-center space-x-2 text-xs text-gray-500 mr-2">
                                    <%= if route.initial_process do %>
                                      <.icon name="hero-cog" class="h-3 w-3" />
                                    <% end %>
                                    <%= if route.linked_forms_count > 0 do %>
                                      <span class="flex items-center">
                                        <.icon name="hero-document-text" class="h-3 w-3 mr-1" />
                                        <%= route.linked_forms_count %>
                                      </span>
                                    <% end %>
                                    <%= if Map.get(route, :linked_wizards_count, 0) > 0 do %>
                                      <span class="flex items-center">
                                        <.icon name="hero-squares-plus" class="h-3 w-3 mr-1" />
                                        <%= Map.get(route, :linked_wizards_count, 0) %>W
                                      </span>
                                    <% end %>
                                  </div>
                                  
                                  <!-- Action Buttons -->
                                  <.link
                                    patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                                    class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs rounded text-gray-700 bg-white hover:bg-gray-50"
                                    title="Edit Route"
                                  >
                                    <.icon name="hero-pencil" class="h-3 w-3" />
                                  </.link>
                                  
                                  <button
                                    phx-click="toggle-enabled"
                                    phx-value-id={route.id}
                                    class={[
                                      "inline-flex items-center px-2 py-1 border text-xs rounded",
                                      if route.enabled do
                                        "border-red-300 text-red-700 bg-white hover:bg-red-50"
                                      else
                                        "border-green-300 text-green-700 bg-white hover:bg-green-50"
                                      end
                                    ]}
                                    title={route.enabled && "Disable" || "Enable"}
                                  >
                                    <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-3 w-3" />
                                  </button>
                                </div>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  defp render_category_overview(routes) do
    # Group routes by category, with "Uncategorized" for nil categories
    categorized_routes = routes
    |> Enum.group_by(fn route ->
      route.category || "Uncategorized"
    end)
    |> Enum.sort_by(fn {category, _routes} -> 
      # Sort so "Uncategorized" comes last
      if category == "Uncategorized", do: "zzz", else: category
    end)
    
    assigns = %{categorized_routes: categorized_routes}
    
    ~H"""
    <div class="space-y-8">
      <%= for {category, category_routes} <- @categorized_routes do %>
        <div>
          <!-- Category Header -->
          <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200">
            <div class="flex items-center space-x-2">
              <%= if category == "Uncategorized" do %>
                <.icon name="hero-folder-open" class="h-5 w-5 text-gray-400" />
                <h3 class="text-lg font-medium text-gray-600"><%= category %></h3>
              <% else %>
                <.icon name="hero-folder" class="h-5 w-5 text-indigo-500" />
                <h3 class="text-lg font-medium text-gray-900"><%= category %></h3>
              <% end %>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-700">
              <%= length(category_routes) %> route<%= if length(category_routes) != 1, do: "s" %>
            </span>
          </div>
          
          <!-- Routes Grid -->
          <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
            <%= for route <- category_routes do %>
              <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <!-- Same card structure as the existing grid view -->
                <div class="p-3 border-b border-gray-100">
                  <div class="flex items-start justify-between mb-1">
                    <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= route.name %></h3>
                    <div class="flex space-x-1 flex-shrink-0">
                      <%= if !route.enabled do %>
                        <.icon name="hero-x-circle" class="h-3 w-3 text-red-500" />
                      <% else %>
                        <.icon name="hero-check-circle" class="h-3 w-3 text-green-500" />
                      <% end %>
                    </div>
                  </div>

                  <div class="flex items-center space-x-1 mb-2">
                    <span class={[
                      "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                      case route.method do
                        "GET" -> "bg-green-100 text-green-800"
                        "POST" -> "bg-blue-100 text-blue-800"
                        "PUT" -> "bg-yellow-100 text-yellow-800"
                        "PATCH" -> "bg-orange-100 text-orange-800"
                        "DELETE" -> "bg-red-100 text-red-800"
                        _ -> "bg-gray-100 text-gray-800"
                      end
                    ]}>
                      <%= route.method %>
                    </span>
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                      Route
                    </span>
                  </div>

                  <code class="text-xs text-gray-600 bg-gray-50 px-1.5 py-0.5 rounded block mb-2 truncate">
                    <%= route.path %>
                  </code>

                  <!-- Route Stats -->
                  <div class="flex items-center justify-between text-xs">
                    <div class="flex items-center space-x-2">
                      <%= if route.initial_process do %>
                        <span class="flex items-center text-gray-500">
                          <.icon name="hero-cog" class="h-3 w-3 mr-1" />
                          Process
                        </span>
                      <% end %>
                      <%= if route.linked_forms_count > 0 do %>
                        <span class="flex items-center text-gray-500">
                          <.icon name="hero-document-text" class="h-3 w-3 mr-1" />
                          <%= route.linked_forms_count %> forms
                        </span>
                      <% end %>
                      <%= if Map.get(route, :linked_wizards_count, 0) > 0 do %>
                        <span class="flex items-center text-gray-500">
                          <.icon name="hero-squares-plus" class="h-3 w-3 mr-1" />
                          <%= Map.get(route, :linked_wizards_count, 0) %> wizards
                        </span>
                      <% end %>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class={[
                        "text-xs font-medium",
                        if(route.enabled, do: "text-green-600", else: "text-red-600")
                      ]}>
                        <%= if route.enabled, do: "Active", else: "Inactive" %>
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Actions -->
                <div class="p-2">
                  <div class="flex space-x-1">
                    <.link
                      patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                      class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                      Edit
                    </.link>
                    <button
                      phx-click="toggle-enabled"
                      phx-value-id={route.id}
                      class={[
                        "inline-flex items-center px-2 py-1 border text-xs font-medium rounded",
                        if route.enabled do
                          "border-red-300 text-red-700 bg-white hover:bg-red-50"
                        else
                          "border-green-300 text-green-700 bg-white hover:bg-green-50"
                        end
                      ]}
                      title={route.enabled && "Disable" || "Enable"}
                    >
                      <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-3 w-3" />
                    </button>
                    <button
                      phx-click="delete"
                      phx-value-id={route.id}
                      data-confirm="Are you sure you want to delete this route?"
                      class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                    >
                      <.icon name="hero-trash" class="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="API Routes"
        subtitle="Manage API endpoints and routing"
        current_page={:routes}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Routes"}
        ]}
      />


      <div class="px-6 py-6">
        <%= if Enum.empty?(@routes) do %>
          <!-- Empty State -->
          <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <.icon name="hero-globe-alt" class="h-12 w-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No routes yet</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first API route</p>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/routes/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Create Route
            </.link>
          </div>
        <% else %>
          <!-- Header with View Controls -->
          <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-6">
              <div class="flex items-center justify-between mb-6">
                <div>
                  <h2 class="text-xl font-semibold text-gray-900">All Routes</h2>
                  <p class="text-sm text-gray-600">Manage your API endpoints and routing</p>
                </div>
                <.link
                  patch={~p"/mobileBanking/dynamic-forms/routes/new"}
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Create Route
                </.link>
              </div>

              <!-- Controls -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-600"><%= length(@routes) %> routes</span>
                  
                  <!-- Tree View Toggle -->
                  <%= if @view_mode == "grid" do %>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-500">Layout:</span>
                      <button
                        phx-click="toggle_tree_view"
                        class={[
                          "inline-flex items-center px-2 py-1 rounded text-xs font-medium transition-colors border",
                          if @tree_view do
                            "border-indigo-300 bg-indigo-50 text-indigo-700"
                          else
                            "border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                          end
                        ]}
                        title="Toggle Tree View"
                      >
                        <.icon name="hero-bars-3" class="h-3 w-3 mr-1" />
                        <%= if @tree_view, do: "Tree", else: "Cards" %>
                      </button>
                    </div>
                  <% end %>
                </div>

                <!-- View Toggle -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    phx-click="toggle_view"
                    phx-value-view="grid"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-squares-2x2" class="h-4 w-4" />
                  </button>
                  <button
                    phx-click="toggle_view"
                    phx-value-view="list"
                    class={[
                      "px-3 py-1 rounded text-sm font-medium transition-colors",
                      if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                    ]}
                  >
                    <.icon name="hero-list-bullet" class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-6">
            <!-- Grid View -->
            <div :if={@view_mode == "grid"}>
              <%= if @tree_view do %>
                <%= render_tree_structure(@routes, @expanded_categories, @expanded_groups) %>
              <% else %>
                <%= render_category_overview(@routes) %>
              <% end %>
            </div>

            <!-- Original Grid View (kept for backup) -->
            <div style="display: none;" class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
              <%= for route <- @routes do %>
                <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  <!-- Header -->
                  <div class="p-3 border-b border-gray-100">
                    <div class="flex items-start justify-between mb-1">
                      <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= route.name %></h3>
                      <div class="flex space-x-1 flex-shrink-0">
                        <%= if !route.enabled do %>
                          <.icon name="hero-x-circle" class="h-3 w-3 text-red-500" />
                        <% else %>
                          <.icon name="hero-check-circle" class="h-3 w-3 text-green-500" />
                        <% end %>
                      </div>
                    </div>

                    <div class="flex items-center space-x-1 mb-2">
                      <span class={[
                        "inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",
                        case route.method do
                          "GET" -> "bg-green-100 text-green-800"
                          "POST" -> "bg-blue-100 text-blue-800"
                          "PUT" -> "bg-yellow-100 text-yellow-800"
                          "PATCH" -> "bg-orange-100 text-orange-800"
                          "DELETE" -> "bg-red-100 text-red-800"
                          _ -> "bg-gray-100 text-gray-800"
                        end
                      ]}>
                        <%= route.method %>
                      </span>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                        Route
                      </span>
                    </div>

                    <code class="text-xs text-gray-600 bg-gray-50 px-1.5 py-0.5 rounded block mb-2 truncate">
                      <%= route.path %>
                    </code>

                    <!-- Route Stats -->
                    <div class="flex items-center justify-between text-xs">
                      <div class="flex items-center space-x-2">
                        <%= if route.initial_process do %>
                          <span class="flex items-center text-gray-500">
                            <.icon name="hero-cog" class="h-3 w-3 mr-1" />
                            Process
                          </span>
                        <% end %>
                        <%= if route.linked_forms_count > 0 do %>
                          <span class="flex items-center text-gray-500">
                            <.icon name="hero-document-text" class="h-3 w-3 mr-1" />
                            <%= route.linked_forms_count %> forms
                          </span>
                        <% end %>
                        <%= if Map.get(route, :linked_wizards_count, 0) > 0 do %>
                          <span class="flex items-center text-gray-500">
                            <.icon name="hero-squares-plus" class="h-3 w-3 mr-1" />
                            <%= Map.get(route, :linked_wizards_count, 0) %> wizards
                          </span>
                        <% end %>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span class={[
                          "text-xs font-medium",
                          if(route.enabled, do: "text-green-600", else: "text-red-600")
                        ]}>
                          <%= if route.enabled, do: "Active", else: "Inactive" %>
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="p-2">
                    <div class="flex space-x-1">
                      <.link
                        patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                        class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                        Edit
                      </.link>
                      <button
                        phx-click="toggle-enabled"
                        phx-value-id={route.id}
                        class={[
                          "inline-flex items-center px-2 py-1 border text-xs font-medium rounded",
                          if route.enabled do
                            "border-red-300 text-red-700 bg-white hover:bg-red-50"
                          else
                            "border-green-300 text-green-700 bg-white hover:bg-green-50"
                          end
                        ]}
                        title={route.enabled && "Disable" || "Enable"}
                      >
                        <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-3 w-3" />
                      </button>
                      <button
                        phx-click="delete"
                        phx-value-id={route.id}
                        data-confirm="Are you sure you want to delete this route?"
                        class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                      >
                        <.icon name="hero-trash" class="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <!-- List View - Compact Table -->
            <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Route</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Method</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Path</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Process</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Forms</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Wizards</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for route <- @routes do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 whitespace-nowrap">
                          <.link 
                            navigate={~p"/mobileBanking/dynamic-forms/routes/#{route.id}/details"}
                            class="text-sm font-medium text-indigo-600 hover:text-indigo-900 truncate max-w-32 block"
                            title={route.name}
                          >
                            <%= route.name %>
                          </.link>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class={[
                            "inline-flex items-center px-2 py-1 rounded text-sm font-medium",
                            case route.method do
                              "GET" -> "bg-green-100 text-green-800"
                              "POST" -> "bg-blue-100 text-blue-800"
                              "PUT" -> "bg-yellow-100 text-yellow-800"
                              "PATCH" -> "bg-orange-100 text-orange-800"
                              "DELETE" -> "bg-red-100 text-red-800"
                              _ -> "bg-gray-100 text-gray-800"
                            end
                          ]}>
                            <%= route.method %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <code class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded max-w-40 block truncate" title={route.path}>
                            <%= route.path %>
                          </code>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <%= if Map.get(route, :initial_process) do %>
                            <div class="flex items-center">
                              <.icon name="hero-cog" class="h-4 w-4 mr-1 text-gray-400" />
                              <span class="text-sm text-gray-900 truncate max-w-24 block" title={Map.get(route.initial_process, :name)}>
                                <%= Map.get(route.initial_process, :name) %>
                              </span>
                            </div>
                          <% else %>
                            <span class="text-sm text-gray-400">No process</span>
                          <% end %>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <%= if Map.get(route, :linked_forms_count, 0) > 0 do %>
                            <div class="flex items-center">
                              <.icon name="hero-document-text" class="h-4 w-4 mr-1 text-gray-400" />
                              <span class="text-sm text-gray-900"><%= Map.get(route, :linked_forms_count, 0) %></span>
                            </div>
                          <% else %>
                            <span class="text-sm text-gray-400">0</span>
                          <% end %>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <%= if Map.get(route, :linked_wizards_count, 0) > 0 do %>
                            <div class="flex items-center">
                              <.icon name="hero-squares-plus" class="h-4 w-4 mr-1 text-purple-400" />
                              <span class="text-sm text-gray-900" title={Map.get(route, :linked_wizard) && Map.get(route.linked_wizard, :name)}>
                                <%= Map.get(route, :linked_wizards_count, 0) %>
                              </span>
                            </div>
                          <% else %>
                            <span class="text-sm text-gray-400">0</span>
                          <% end %>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center">
                            <div class={[
                              "w-2 h-2 rounded-full mr-2",
                              if(route.enabled, do: "bg-green-400", else: "bg-red-400")
                            ]}></div>
                            <span class={[
                              "text-sm font-medium",
                              if(route.enabled, do: "text-green-800", else: "text-red-800")
                            ]}>
                              <%= if route.enabled, do: "Active", else: "Inactive" %>
                            </span>
                          </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <span class="text-sm text-gray-500">
                            <%= Calendar.strftime(route.inserted_at, "%b %d, %Y") %>
                          </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                          <div class="flex items-center space-x-2">
                            <!-- Test Route Button -->
                            <%= if route.enabled && route.initial_process do %>
                              <a
                                href={"/dynamic#{route.path}"}
                                target="_blank"
                                class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                                title="Test Route"
                              >
                                <.icon name="hero-play" class="h-4 w-4" />
                              </a>
                            <% end %>

                            <!-- Toggle Enable/Disable -->
                            <button
                              phx-click="toggle-enabled"
                              phx-value-id={route.id}
                              class={[
                                "inline-flex items-center px-2 py-1 border text-sm rounded",
                                if route.enabled do
                                  "border-red-300 text-red-700 bg-white hover:bg-red-50"
                                else
                                  "border-green-300 text-green-700 bg-white hover:bg-green-50"
                                end
                              ]}
                              title={route.enabled && "Disable" || "Enable"}
                            >
                              <.icon name={route.enabled && "hero-x-mark" || "hero-check"} class="h-4 w-4" />
                            </button>

                            <!-- Link Forms/Wizards Button -->
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/link-forms"}
                              class="inline-flex items-center px-2 py-1 border border-blue-300 text-sm rounded text-blue-700 bg-white hover:bg-blue-50"
                              title="Link Forms or Wizards"
                            >
                              <.icon name="hero-link" class="h-4 w-4" />
                            </.link>

                            <!-- Edit Button -->
                            <.link
                              patch={~p"/mobileBanking/dynamic-forms/routes/#{route}/edit"}
                              class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                              title="Edit"
                            >
                              <.icon name="hero-pencil" class="h-4 w-4" />
                            </.link>

                            <!-- Delete Button -->
                            <button
                              phx-click="delete"
                              phx-value-id={route.id}
                              data-confirm="Are you sure you want to delete this route?"
                              class="inline-flex items-center px-2 py-1 border border-red-300 text-sm rounded text-red-700 bg-white hover:bg-red-50"
                              title="Delete"
                            >
                              <.icon name="hero-trash" class="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <.modal :if={@live_action in [:new, :edit]} id="route-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/routes")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.RouteFormComponent}
        id={@route.id || :new}
        title={@page_title}
        action={@live_action}
        route={@route}
        patch={~p"/mobileBanking/dynamic-forms/routes"}
      />
    </.modal>

    <.modal :if={@live_action == :link_forms} id="link-forms-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/routes")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.LinkFormComponent}
        id="link-forms"
        title={@page_title}
        action={@live_action}
        route_form={@route_form}
        routes={@all_routes}
        forms={@forms}
        patch={~p"/mobileBanking/dynamic-forms/routes"}
      />
    </.modal>
    """
  end
end