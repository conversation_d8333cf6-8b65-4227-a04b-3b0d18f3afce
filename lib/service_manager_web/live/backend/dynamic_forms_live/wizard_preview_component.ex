defmodule ServiceManagerWeb.Backend.DynamicFormsLive.WizardPreviewComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Forms.DynamicFormsManager

  @impl true
  def update(assigns, socket) do
    wizard = assigns.wizard
    current_step_number = assigns.current_step_number
    
    # Get the current step and form
    current_step = get_current_step(wizard, current_step_number)
    {current_form, is_mobile_form} = get_form_with_type(current_step)
    
    progress_info = get_progress_info(wizard, current_step_number)
    
    # Get ordered steps for reordering UI
    ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(wizard.id)
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:current_step, current_step)
     |> assign(:current_form, current_form)
     |> assign(:is_mobile_form, is_mobile_form)
     |> assign(:progress_info, progress_info)
     |> assign(:ordered_steps, ordered_steps)
     |> assign(:show_reorder_modal, false)
     |> assign(:device_options, [
       %{value: "mobile", label: "Mobile", width: "375px", height: "667px", icon: "hero-device-phone-mobile"},
       %{value: "tablet", label: "Tablet", width: "768px", height: "1024px", icon: "hero-device-tablet"},
       %{value: "desktop", label: "Desktop", width: "1200px", height: "800px", icon: "hero-computer-desktop"}
     ])}
  end

  defp get_current_step(wizard, step_number) do
    wizard.steps
    |> Enum.find(&(&1.step_number == step_number))
  end

  defp get_form_with_type(current_step) do
    cond do
      # Mobile form step
      current_step && ServiceManager.Forms.FormWizardStep.mobile_form_step?(current_step) ->
        case load_mobile_form_with_fields(current_step.mobile_form_id) do
          {:ok, mobile_form_with_fields} -> {mobile_form_with_fields, true}
          _ -> {nil, true}
        end
      
      # Dynamic form step (legacy)
      current_step && current_step.form ->
        {current_step.form, false}
      
      # No form
      true ->
        {nil, false}
    end
  end

  defp load_mobile_form_with_fields(mobile_form_id) do
    alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service
    alias ServiceManager.Repo
    alias ServiceManagerWeb.Api.FormV2Schema
    
    with {:ok, form} <- MobileFormsV2Service.get_form(%{"form_id" => mobile_form_id}),
         {:ok, fields} <- MobileFormsV2Service.get_form_fields(mobile_form_id) do
      
      # Preload the page and screen associations manually
      form_with_preloads = Repo.preload(form, page: :screen)
      
      # Convert mobile form to a structure similar to dynamic forms for rendering
      mobile_form_structure = %{
        id: form_with_preloads.id,
        name: form_with_preloads.name,
        description: nil, # Mobile forms don't have descriptions
        http_method: "POST", # Default for mobile forms
        form: %{
          "fields" => Enum.map(fields, &convert_mobile_field_to_render_format/1)
        },
        # Add mobile form specific data
        mobile_form_data: %{
          submit_to: form_with_preloads.submit_to,
          page: form_with_preloads.page,
          original_fields: fields
        }
      }
      
      {:ok, mobile_form_structure}
    end
  end

  defp convert_mobile_field_to_render_format(field) do
    %{
      "name" => field.field_name,
      "type" => map_mobile_field_type_to_render(field.field_type),
      "required" => field.is_required,
      "description" => field.label,
      "placeholder" => nil,
      # Mobile field specific data
      "mobile_field_data" => %{
        "original_type" => field.field_type,
        "order" => field.field_order,
        "options" => field.options,
        "label" => field.label
      }
    }
  end

  defp map_mobile_field_type_to_render(mobile_type) do
    case mobile_type do
      "string" -> "string"
      "number" -> "number" 
      "integer" -> "integer"
      "boolean" -> "boolean"
      "date" -> "string"
      "datetime" -> "string"
      "email" -> "string"
      "password" -> "string"
      "phone" -> "string"
      "select" -> "string" # Will handle options in render
      "multiselect" -> "array"
      "textarea" -> "string" # Will set format to textarea
      _ -> "string"
    end
  end

  defp get_progress_info(wizard, current_step) do
    total_steps = length(wizard.steps || [])
    progress_percentage = if total_steps > 0, do: round((current_step / total_steps) * 100), else: 0
    
    %{
      current_step: current_step,
      total_steps: total_steps,
      progress_percentage: progress_percentage,
      is_first_step: current_step == 1,
      is_last_step: current_step == total_steps
    }
  end

  defp get_device_config(device_options, current_device) do
    Enum.find(device_options, &(&1.value == current_device)) || 
      %{value: "mobile", label: "Mobile", width: "375px", height: "667px", icon: "hero-device-phone-mobile"}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="relative">
      <div class="h-screen bg-white flex flex-col">
      <!-- Header Controls -->
      <div class="bg-white border-b border-gray-200 shadow-sm px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button
              phx-click="close_preview"
              phx-target={@myself}
              class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600 hover:text-white transition-colors"
            >
              <.icon name="hero-arrow-left" class="h-4 w-4 mr-2" />
              Back to Wizards
            </button>
            
            <div class="text-gray-900">
              <h1 class="text-lg font-semibold"><%= @wizard.name %></h1>
              <p class="text-sm text-gray-600"><%= @wizard.description || "No description" %></p>
            </div>

            <!-- Reorder Steps Button -->
            <button
              phx-click="show_reorder_modal"
              phx-target={@myself}
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <.icon name="hero-bars-3" class="h-4 w-4 mr-2" />
              Reorder Steps
            </button>
          </div>

          <!-- Device Selector -->
          <div class="flex items-center space-x-2">
            <%= for device <- @device_options do %>
              <button
                phx-click="change_preview_device"
                phx-value-device={device.value}
                phx-target={@myself}
                class={[
                  "inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors border",
                  if(@preview_device == device.value, 
                    do: "bg-indigo-600 text-white border-indigo-600", 
                    else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  )
                ]}
                title={device.label}
              >
                <.icon name={device.icon} class="h-4 w-4 mr-2" />
                <%= device.label %>
              </button>
            <% end %>
          </div>
        </div>

        <!-- Progress Bar -->
        <div class="mt-4">
          <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Step <%= @progress_info.current_step %> of <%= @progress_info.total_steps %></span>
            <span><%= @progress_info.progress_percentage %>% Complete</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-indigo-600 h-2 rounded-full transition-all duration-300 ease-in-out" 
              style={"width: #{@progress_info.progress_percentage}%"}
            ></div>
          </div>
        </div>
      </div>

      <!-- Preview Area -->
      <div class="flex-1 bg-gray-100 flex items-center justify-center p-8">
        <div class="relative">
          <!-- Device Frame -->
          <.live_component
            module={ServiceManagerWeb.Backend.DynamicFormsLive.DeviceFrameComponent}
            id="device_frame"
            device={@preview_device}
            width={get_device_config(@device_options, @preview_device).width}
            height={get_device_config(@device_options, @preview_device).height}
          >
            <!-- Form Content -->
            <%= if @current_form do %>
              <.live_component
                module={ServiceManagerWeb.Backend.DynamicFormsLive.FormRendererComponent}
                id="form_renderer"
                form={@current_form}
                device={@preview_device}
                form_data={@wizard_form_data}
                step_info={@progress_info}
                wizard={@wizard}
                current_step={@current_step}
                is_mobile_form={@is_mobile_form}
              />
            <% else %>
              <div class="h-full flex items-center justify-center text-gray-500">
                <div class="text-center">
                  <.icon name="hero-exclamation-triangle" class="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p class="text-lg font-medium mb-2">No Form Found</p>
                  <p class="text-sm">This step doesn't have a form associated with it.</p>
                </div>
              </div>
            <% end %>
          </.live_component>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="bg-white border-t border-gray-200 shadow-sm px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- Current Step Info -->
            <%= if @current_step do %>
              <div class="text-gray-900">
                <div class="text-sm font-medium">Current Step</div>
                <div class="text-xs text-gray-600">
                  <%= @current_form && @current_form.name || "Unknown Form" %>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Step Navigation -->
          <div class="flex items-center space-x-3">
            <button
              phx-click="preview_prev_step"
              phx-target={@myself}
              disabled={@progress_info.is_first_step}
              class={[
                "inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md transition-colors",
                if(@progress_info.is_first_step, 
                  do: "border-gray-300 text-gray-400 bg-gray-100 cursor-not-allowed", 
                  else: "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                )
              ]}
            >
              <.icon name="hero-chevron-left" class="h-4 w-4 mr-2" />
              Previous
            </button>

            <div class="text-gray-600 text-sm">
              Step <%= @progress_info.current_step %> / <%= @progress_info.total_steps %>
            </div>

            <button
              phx-click="preview_next_step"
              phx-target={@myself}
              disabled={@progress_info.is_last_step}
              class={[
                "inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md transition-colors",
                if(@progress_info.is_last_step, 
                  do: "border-gray-300 text-gray-400 bg-gray-100 cursor-not-allowed", 
                  else: "border-indigo-500 text-white bg-indigo-600 hover:bg-indigo-700"
                )
              ]}
            >
              <%= if @progress_info.is_last_step do %>
                Complete
                <.icon name="hero-check" class="h-4 w-4 ml-2" />
              <% else %>
                Next
                <.icon name="hero-chevron-right" class="h-4 w-4 ml-2" />
              <% end %>
            </button>
          </div>
        </div>
      </div>
      </div>

      <!-- Reorder Modal -->
    <%= if @show_reorder_modal do %>
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="hide_reorder_modal" phx-target={@myself}>
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" phx-click-away="hide_reorder_modal" phx-target={@myself}>
          <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Reorder Wizard Steps</h3>
              <button
                phx-click="hide_reorder_modal"
                phx-target={@myself}
                class="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <.icon name="hero-x-mark" class="h-5 w-5" />
              </button>
            </div>

            <!-- Steps List -->
            <div class="space-y-2 mb-4">
              <%= for {step, index} <- Enum.with_index(@ordered_steps) do %>
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-gray-50">
                  <div class="flex items-center space-x-3">
                    <span class="text-sm font-medium text-gray-600">Step <%= step.step_number %></span>
                    <div class="text-sm text-gray-900">
                      <div class="font-medium"><%= step.form_info.name %></div>
                      <div class="text-xs text-gray-500"><%= step.form_info.type %> • <%= step.form_info.fields_count %> fields</div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-1">
                    <button
                      phx-click="move_step_up"
                      phx-value-step-id={step.id}
                      phx-target={@myself}
                      disabled={index == 0}
                      class={[
                        "p-1 rounded text-xs transition-colors",
                        if(index == 0,
                          do: "text-gray-300 cursor-not-allowed",
                          else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                        )
                      ]}
                    >
                      <.icon name="hero-chevron-up" class="h-4 w-4" />
                    </button>
                    
                    <button
                      phx-click="move_step_down"
                      phx-value-step-id={step.id}
                      phx-target={@myself}
                      disabled={index == length(@ordered_steps) - 1}
                      class={[
                        "p-1 rounded text-xs transition-colors",
                        if(index == length(@ordered_steps) - 1,
                          do: "text-gray-300 cursor-not-allowed",
                          else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                        )
                      ]}
                    >
                      <.icon name="hero-chevron-down" class="h-4 w-4" />
                    </button>
                  </div>
                </div>
              <% end %>
            </div>

            <!-- Modal Actions -->
            <div class="flex items-center justify-end space-x-3">
              <button
                phx-click="hide_reorder_modal"
                phx-target={@myself}
                class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    </div>
    """
  end

  @impl true
  def handle_event("change_preview_device", %{"device" => device}, socket) do
    send(self(), {:change_preview_device, device})
    {:noreply, assign(socket, :preview_device, device)}
  end

  @impl true
  def handle_event("close_preview", _params, socket) do
    send(self(), :close_preview)
    {:noreply, socket}
  end

  @impl true
  def handle_event("preview_prev_step", _params, socket) do
    send(self(), :preview_prev_step)
    {:noreply, socket}
  end

  @impl true
  def handle_event("preview_next_step", _params, socket) do
    send(self(), :preview_next_step)
    {:noreply, socket}
  end

  @impl true
  def handle_event("show_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, true)}
  end

  @impl true
  def handle_event("hide_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, false)}
  end

  @impl true
  def handle_event("move_step_up", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.move_wizard_step_up(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)
        
        {:noreply, assign(socket, :ordered_steps, ordered_steps)}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end

  @impl true
  def handle_event("move_step_down", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.move_wizard_step_down(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)
        
        {:noreply, assign(socket, :ordered_steps, ordered_steps)}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end
end