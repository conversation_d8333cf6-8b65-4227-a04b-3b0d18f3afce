defmodule ServiceManagerWeb.Backend.DynamicFormsLive.Dashboard do
  use ServiceManagerWeb, :live_view
  alias ServiceManagerWeb.Backend.DynamicFormsLive.{DashboardAsyncManager, AsyncQueryManager}

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :dashboard)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms")
      |> AsyncQueryManager.init_async_tracking()
      |> assign(:stats, %{
        forms_count: 0,
        routes_count: 0,
        processes_count: 0,
        used_processes_count: 0,
        unused_processes_count: 0,
        active_routes_count: 0,
        chain_links_count: 0,
        loading: true
      })
      |> assign(:plugin_usage_data, [])
      |> assign(:route_method_data, [])
      |> assign(:recent_activity, [])
      |> assign(:loading_stats, true)
      |> assign(:loading_plugins, true)
      |> assign(:loading_methods, true)
      |> assign(:loading_activity, true)

    socket = if connected?(socket) do
      :timer.send_interval(30000, :refresh_dashboard)
      DashboardAsyncManager.start_dashboard_async_queries(socket)
    else
      socket
    end

    {:ok, socket}
  end

  @impl true
  def handle_info(:refresh_dashboard, socket) do
    socket = DashboardAsyncManager.start_dashboard_async_queries(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    socket = AsyncQueryManager.handle_async_result(ref, result, socket, &handle_dashboard_result/3)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    socket = AsyncQueryManager.handle_async_failure(ref, socket)
    {:noreply, socket}
  end

  defp handle_dashboard_result(socket, query_key, result) do
    case {query_key, result} do
      {"dashboard_stats", {:ok, stats}} ->
        updated_stats = Map.put(stats, :loading, false)
        socket 
        |> assign(:stats, updated_stats)
        |> assign(:loading_stats, false)
        
      {"plugin_usage_data", {:ok, data}} ->
        socket 
        |> assign(:plugin_usage_data, data)
        |> assign(:loading_plugins, false)
        
      {"route_method_data", {:ok, data}} ->
        socket 
        |> assign(:route_method_data, data)
        |> assign(:loading_methods, false)
        
      {"recent_activity", {:ok, data}} ->
        socket 
        |> assign(:recent_activity, data)
        |> assign(:loading_activity, false)
        
      {_query_key, {:error, _error}} ->
        # On error, still set loading to false for that section
        case query_key do
          "dashboard_stats" -> assign(socket, :loading_stats, false)
          "plugin_usage_data" -> assign(socket, :loading_plugins, false)
          "route_method_data" -> assign(socket, :loading_methods, false)
          "recent_activity" -> assign(socket, :loading_activity, false)
          _ -> socket
        end
        
      _ ->
        socket
    end
  end


  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Dynamic Forms Dashboard"
        subtitle="Overview and statistics"
        current_page={:dashboard}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Dashboard"}
        ]}
      />


      <div class="px-6 py-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Forms -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-document-text" class="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Forms</p>
                <%= if @loading_stats do %>
                  <div class="animate-pulse">
                    <div class="h-8 bg-gray-200 rounded w-16"></div>
                  </div>
                <% else %>
                  <p class="text-2xl font-bold text-gray-900"><%= @stats.forms_count %></p>
                <% end %>
              </div>
            </div>
          </div>

          <!-- API Routes -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-globe-alt" class="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">API Routes</p>
                <%= if @loading_stats do %>
                  <div class="animate-pulse space-y-1">
                    <div class="h-8 bg-gray-200 rounded w-16"></div>
                    <div class="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                <% else %>
                  <p class="text-2xl font-bold text-gray-900"><%= @stats.routes_count %></p>
                  <p class="text-xs text-green-600"><%= @stats.active_routes_count %> active</p>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Total Processes -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-cog" class="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Processes</p>
                <%= if @loading_stats do %>
                  <div class="animate-pulse space-y-1">
                    <div class="h-8 bg-gray-200 rounded w-16"></div>
                    <div class="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                <% else %>
                  <p class="text-2xl font-bold text-gray-900"><%= @stats.processes_count %></p>
                  <p class="text-xs text-purple-600"><%= @stats.used_processes_count %> in use</p>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Chain Links -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-link" class="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Chain Links</p>
                <%= if @loading_stats do %>
                  <div class="animate-pulse">
                    <div class="h-8 bg-gray-200 rounded w-16"></div>
                  </div>
                <% else %>
                  <p class="text-2xl font-bold text-gray-900"><%= @stats.chain_links_count %></p>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Plugin Usage Chart -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Most Used Plugins</h3>
            <%= if @loading_plugins do %>
              <div class="animate-pulse space-y-4">
                <%= for _ <- 1..5 do %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-3 h-3 bg-gray-200 rounded-full"></div>
                      <div class="h-4 bg-gray-200 rounded w-24"></div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="w-24 bg-gray-200 rounded-full h-2"></div>
                      <div class="h-4 bg-gray-200 rounded w-8"></div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
            <div class="space-y-4">
              <%= for plugin <- @plugin_usage_data do %>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-indigo-500 rounded-full"></div>
                    <span class="text-sm font-medium text-gray-900"><%= plugin.name %></span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-indigo-500 h-2 rounded-full"
                        style={"width: #{min(plugin.count * 10, 100)}%"}
                      ></div>
                    </div>
                    <span class="text-sm text-gray-600 w-8 text-right"><%= plugin.count %></span>
                  </div>
                </div>
              <% end %>
              <%= if Enum.empty?(@plugin_usage_data) do %>
                <div class="text-center py-8 text-gray-500">
                  <.icon name="hero-chart-bar" class="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>No plugin usage data yet</p>
                  <p class="text-sm">Create routes and link them to processes to see usage statistics</p>
                </div>
              <% end %>
            </div>
            <% end %>
          </div>

          <!-- HTTP Methods Distribution -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">HTTP Methods Distribution</h3>
            <%= if @loading_methods do %>
              <div class="animate-pulse space-y-4">
                <%= for _ <- 1..4 do %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="h-6 bg-gray-200 rounded-full w-12"></div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="w-24 bg-gray-200 rounded-full h-2"></div>
                      <div class="h-4 bg-gray-200 rounded w-8"></div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
            <div class="space-y-4">
              <%= for method_data <- @route_method_data do %>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <span class={[
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                      case method_data.method do
                        "GET" -> "bg-green-100 text-green-800"
                        "POST" -> "bg-blue-100 text-blue-800"
                        "PUT" -> "bg-yellow-100 text-yellow-800"
                        "PATCH" -> "bg-orange-100 text-orange-800"
                        "DELETE" -> "bg-red-100 text-red-800"
                        _ -> "bg-gray-100 text-gray-800"
                      end
                    ]}>
                      <%= method_data.method %>
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-blue-500 h-2 rounded-full"
                        style={"width: #{min(method_data.count * 20, 100)}%"}
                      ></div>
                    </div>
                    <span class="text-sm text-gray-600 w-8 text-right"><%= method_data.count %></span>
                  </div>
                </div>
              <% end %>
              <%= if Enum.empty?(@route_method_data) do %>
                <div class="text-center py-8 text-gray-500">
                  <.icon name="hero-globe-alt" class="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>No routes created yet</p>
                  <p class="text-sm">Create your first API route to see distribution</p>
                </div>
              <% end %>
            </div>
            <% end %>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="mt-8">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
            <%= if @loading_activity do %>
              <div class="animate-pulse space-y-6">
                <%= for _ <- 1..5 do %>
                  <div class="flex space-x-3">
                    <div class="h-8 w-8 bg-gray-200 rounded-full"></div>
                    <div class="flex-1 space-y-2">
                      <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div class="h-3 bg-gray-200 rounded w-1/4"></div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
            <div class="flow-root">
              <%= if Enum.empty?(@recent_activity) do %>
                <div class="text-center py-8 text-gray-500">
                  <.icon name="hero-clock" class="h-12 w-12 mx-auto mb-3 text-gray-400" />
                  <p>No recent activity</p>
                  <p class="text-sm">Activity will appear here as you create forms, routes, and processes</p>
                </div>
              <% else %>
                <ul role="list" class="-mb-8">
                  <%= for {activity, index} <- Enum.with_index(@recent_activity) do %>
                    <li>
                      <div class="relative pb-8">
                        <%= if index < length(@recent_activity) - 1 do %>
                          <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        <% end %>
                        <div class="relative flex space-x-3">
                          <div>
                            <span class={[
                              "h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white",
                              case activity.color do
                                "blue" -> "bg-blue-500"
                                "green" -> "bg-green-500"
                                "purple" -> "bg-purple-500"
                                _ -> "bg-gray-500"
                              end
                            ]}>
                              <.icon name={"hero-#{activity.icon}"} class="h-4 w-4 text-white" />
                            </span>
                          </div>
                          <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                            <div>
                              <p class="text-sm text-gray-900">
                                <span class="font-medium"><%= activity.title %></span>
                                <span class="text-gray-500"> • <%= activity.description %></span>
                              </p>
                            </div>
                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                              <time datetime={activity.timestamp}>
                                <%= Calendar.strftime(activity.timestamp, "%b %d, %Y") %>
                              </time>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              <% end %>
            </div>
            <% end %>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <.link
              navigate={~p"/mobileBanking/dynamic-forms/forms"}
              class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-document-text" class="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h4 class="text-lg font-medium text-gray-900">Manage Forms</h4>
                  <p class="text-sm text-gray-600">Create and edit API forms</p>
                </div>
              </div>
            </.link>

            <.link
              navigate={~p"/mobileBanking/dynamic-forms/processes"}
              class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-cog" class="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h4 class="text-lg font-medium text-gray-900">Manage Processes</h4>
                  <p class="text-sm text-gray-600">Create plugins and chains</p>
                </div>
              </div>
            </.link>

            <.link
              navigate={~p"/mobileBanking/dynamic-forms/routes"}
              class="block p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <.icon name="hero-globe-alt" class="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h4 class="text-lg font-medium text-gray-900">Manage Routes</h4>
                  <p class="text-sm text-gray-600">Configure API endpoints</p>
                </div>
              </div>
            </.link>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
