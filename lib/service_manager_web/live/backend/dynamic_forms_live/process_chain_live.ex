defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessChainLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(%{"id" => process_id}, _session, socket) do
    case ProcessManager.get_process(process_id) do
      {:error, :not_found} ->
        {:ok,
         socket
         |> put_flash(:error, "Process not found")
         |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/processes")}

      {:ok, process} ->
        socket =
          socket
          |> assign(:live_action, :chain)
          |> assign(:url, ~p"/mobileBanking/dynamic-forms/processes/#{process_id}/chain")
          |> assign(:source_process, process)
          |> assign(:page_title, "Configure Process Chain - #{process.name}")

        {:ok, socket}
    end
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Process Chain Configuration"
        subtitle={@source_process.name}
        current_page={:processes}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Plugin Library", link: ~p"/mobileBanking/dynamic-forms/processes"},
          %{title: @source_process.name}
        ]}
      />


      <div class="px-6 py-6">
        <!-- Chain Configuration Component -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <.live_component
            module={ServiceManagerWeb.Backend.DynamicFormsLive.ProcessChainComponent}
            id="process-chain"
            title="Configure Process Chain"
            source_process={@source_process}
            current_user={@current_user}
            patch={~p"/mobileBanking/dynamic-forms/processes"}
          />
        </div>
      </div>
    </div>
    """
  end
end