defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessLinkComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessManager
  alias ServiceManager.Routing.DynamicRouteManager

  @impl true
  def update(assigns, socket) do
    routes = DynamicRouteManager.list_routes()
    processes = ProcessManager.list_processes()
    
    route_id = Map.get(assigns, :route_id)
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:routes, routes)
     |> assign(:processes, processes)
     |> assign(:selected_route_id, route_id)
     |> assign(:selected_process_id, nil)}
  end

  @impl true
  def handle_event("select-route", %{"route-id" => route_id}, socket) do
    route_id = 
      case route_id do
        "" -> nil
        id -> String.to_integer(id)
      end
      
    {:noreply, assign(socket, :selected_route_id, route_id)}
  end

  def handle_event("select-process", %{"process-id" => process_id}, socket) do
    process_id = 
      case process_id do
        "" -> nil
        id -> String.to_integer(id)
      end
      
    {:noreply, assign(socket, :selected_process_id, process_id)}
  end

  def handle_event("link", _params, socket) do
    route_id = socket.assigns.selected_route_id
    process_id = socket.assigns.selected_process_id
    
    if route_id && process_id do
      case ProcessManager.link_process_to_route(route_id, process_id, socket.assigns.current_user.id) do
        {:ok, _link} ->
          {:noreply,
           socket
           |> put_flash(:info, "Process linked to route successfully")
           |> push_navigate(to: socket.assigns.patch)}
           
        {:error, changeset} ->
          {:noreply,
           socket
           |> put_flash(:error, "Failed to link process to route: #{inspect(changeset.errors)}")}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "Please select both a route and a process")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>
          Link a process to a route
        </:subtitle>
      </.header>

      <div class="mt-6 space-y-6">
        <div>
          <label for="route-select" class="block text-sm font-medium text-gray-700 mb-2">
            Select a Route
          </label>
          <select
            id="route-select"
            phx-change="select-route"
            phx-target={@myself}
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">-- Select a route --</option>
            <%= for route <- @routes do %>
              <option value={route.id} selected={@selected_route_id == route.id}>
                <%= route.name %> (<%= route.method %> <%= route.path %>)
              </option>
            <% end %>
          </select>
        </div>

        <div>
          <label for="process-select" class="block text-sm font-medium text-gray-700 mb-2">
            Select a Process
          </label>
          <select
            id="process-select"
            phx-change="select-process"
            phx-target={@myself}
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">-- Select a process --</option>
            <%= for process <- @processes do %>
              <option value={process.id} selected={@selected_process_id == process.id}>
                <%= process.name %>
              </option>
            <% end %>
          </select>
        </div>

        <div class="flex justify-end space-x-3">
          <.link
            navigate={@patch}
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </.link>
          
          <button
            phx-click="link"
            phx-target={@myself}
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={!@selected_route_id || !@selected_process_id}
          >
            Link Process to Route
          </button>
        </div>
      </div>
    </div>
    """
  end
end
