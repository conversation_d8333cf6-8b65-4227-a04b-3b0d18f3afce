defmodule ServiceManagerWeb.Backend.DynamicFormsLive.WizardStepsManagerComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Forms.DynamicFormsManager

  @impl true
  def update(assigns, socket) do
    wizard = assigns.wizard
    
    # Get ordered steps with detailed information
    ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(wizard.id)
    
    # Get available mobile forms for adding to wizard
    available_mobile_forms = case DynamicFormsManager.list_mobile_forms_for_wizard() do
      {:ok, forms} -> 
        # Filter out forms already in this wizard
        current_form_ids = Enum.map(ordered_steps, & &1.mobile_form_id)
        Enum.reject(forms, &(&1.id in current_form_ids))
      {:error, _} -> 
        []
    end
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:ordered_steps, ordered_steps)
     |> assign(:available_mobile_forms, available_mobile_forms)
     |> assign(:filtered_available_forms, available_mobile_forms)
     |> assign(:show_reorder_modal, false)
     |> assign(:show_add_form_modal, false)
     |> assign(:form_search_query, "")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-900">Wizard Steps</h4>
        <div class="flex items-center space-x-2">
          <button
            phx-click="show_add_form_modal"
            phx-target={@myself}
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
          >
            <.icon name="hero-plus" class="h-4 w-4 mr-2" />
            Add Form
          </button>
          <button
            phx-click="show_reorder_modal"
            phx-target={@myself}
            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <.icon name="hero-bars-3" class="h-4 w-4 mr-2" />
            Reorder Steps
          </button>
        </div>
      </div>

      <!-- Steps List -->
      <div class="space-y-3">
        <%= for {step, index} <- Enum.with_index(@ordered_steps) do %>
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-gray-50">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-indigo-600"><%= step.step_number %></span>
              </div>
              <div class="text-sm text-gray-900">
                <div class="font-medium"><%= step.form_info.name %></div>
                <div class="text-xs text-gray-500"><%= step.form_info.type %> • <%= step.form_info.fields_count %> fields</div>
              </div>
            </div>
            
            <div class="flex items-center space-x-1">
              <button
                phx-click="move_step_up"
                phx-value-step-id={step.id}
                phx-target={@myself}
                disabled={index == 0}
                class={[
                  "p-1 rounded text-xs transition-colors",
                  if(index == 0,
                    do: "text-gray-300 cursor-not-allowed",
                    else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                  )
                ]}
                title="Move step up"
              >
                <.icon name="hero-chevron-up" class="h-4 w-4" />
              </button>
              
              <button
                phx-click="move_step_down"
                phx-value-step-id={step.id}
                phx-target={@myself}
                disabled={index == length(@ordered_steps) - 1}
                class={[
                  "p-1 rounded text-xs transition-colors",
                  if(index == length(@ordered_steps) - 1,
                    do: "text-gray-300 cursor-not-allowed",
                    else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                  )
                ]}
                title="Move step down"
              >
                <.icon name="hero-chevron-down" class="h-4 w-4" />
              </button>
              
              <button
                phx-click="remove_step"
                phx-value-step-id={step.id}
                phx-target={@myself}
                data-confirm="Are you sure you want to remove this form from the wizard?"
                class="p-1 rounded text-xs transition-colors text-red-600 hover:text-red-900 hover:bg-red-100"
                title="Remove step"
              >
                <.icon name="hero-trash" class="h-4 w-4" />
              </button>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Reorder Modal -->
      <%= if @show_reorder_modal do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="hide_reorder_modal" phx-target={@myself}>
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" phx-click-away="hide_reorder_modal" phx-target={@myself}>
            <div class="mt-3">
              <!-- Modal Header -->
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reorder Wizard Steps</h3>
                <button
                  phx-click="hide_reorder_modal"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <!-- Steps List -->
              <div class="space-y-2 mb-4">
                <%= for {step, index} <- Enum.with_index(@ordered_steps) do %>
                  <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-gray-50">
                    <div class="flex items-center space-x-3">
                      <span class="text-sm font-medium text-gray-600">Step <%= step.step_number %></span>
                      <div class="text-sm text-gray-900">
                        <div class="font-medium"><%= step.form_info.name %></div>
                        <div class="text-xs text-gray-500"><%= step.form_info.type %> • <%= step.form_info.fields_count %> fields</div>
                      </div>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                      <button
                        phx-click="move_step_up"
                        phx-value-step-id={step.id}
                        phx-target={@myself}
                        disabled={index == 0}
                        class={[
                          "p-1 rounded text-xs transition-colors",
                          if(index == 0,
                            do: "text-gray-300 cursor-not-allowed",
                            else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                          )
                        ]}
                      >
                        <.icon name="hero-chevron-up" class="h-4 w-4" />
                      </button>
                      
                      <button
                        phx-click="move_step_down"
                        phx-value-step-id={step.id}
                        phx-target={@myself}
                        disabled={index == length(@ordered_steps) - 1}
                        class={[
                          "p-1 rounded text-xs transition-colors",
                          if(index == length(@ordered_steps) - 1,
                            do: "text-gray-300 cursor-not-allowed",
                            else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                          )
                        ]}
                      >
                        <.icon name="hero-chevron-down" class="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                <% end %>
              </div>

              <!-- Modal Actions -->
              <div class="flex items-center justify-end space-x-3">
                <button
                  phx-click="hide_reorder_modal"
                  phx-target={@myself}
                  class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Add Form Modal -->
      <%= if @show_add_form_modal do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="hide_add_form_modal" phx-target={@myself}>
          <div class="relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white" phx-click-away="hide_add_form_modal" phx-target={@myself}>
            <div class="mt-3">
              <!-- Modal Header -->
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add Form to Wizard</h3>
                <button
                  phx-click="hide_add_form_modal"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <!-- Search -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Forms</label>
                <input
                  type="text"
                  placeholder="Search by form name..."
                  value={@form_search_query}
                  phx-keyup="search_forms"
                  phx-target={@myself}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <!-- Available Forms -->
              <%= if Enum.empty?(@filtered_available_forms) do %>
                <div class="text-center py-12">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <%= if @form_search_query != "" do %>
                      <.icon name="hero-magnifying-glass" class="h-8 w-8 text-gray-400" />
                    <% else %>
                      <.icon name="hero-document-text" class="h-8 w-8 text-gray-400" />
                    <% end %>
                  </div>
                  <h4 class="text-md font-medium text-gray-900 mb-2">
                    <%= if @form_search_query != "" do %>
                      No forms match your search
                    <% else %>
                      No available forms
                    <% end %>
                  </h4>
                  <p class="text-gray-600 mb-4">
                    <%= if @form_search_query != "" do %>
                      Try adjusting your search terms
                    <% else %>
                      All mobile forms are already added to this wizard
                    <% end %>
                  </p>
                </div>
              <% else %>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  <%= for form <- @filtered_available_forms do %>
                    <div class="relative rounded-lg border-2 border-gray-200 p-4 cursor-pointer transition-all duration-200 hover:border-indigo-300 hover:bg-indigo-50">
                      <button
                        phx-click="add_form_to_wizard"
                        phx-value-form_id={form.id}
                        phx-target={@myself}
                        class="w-full text-left"
                      >
                        <div class="flex items-start space-x-3">
                          <div class="flex-shrink-0">
                            <.icon name="hero-device-phone-mobile" class="h-6 w-6 text-blue-500" />
                          </div>
                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              <%= form.name %>
                            </p>
                            <p class="text-xs text-gray-500 mt-1">
                              <%= if form.page do %>
                                <%= form.page.screen.name %> → <%= form.page.name %>
                              <% else %>
                                No screen/page assigned
                              <% end %>
                            </p>
                            <%= if form.submit_to do %>
                              <div class="mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                  <.icon name="hero-check" class="h-3 w-3 mr-1" />
                                  Has Validation
                                </span>
                              </div>
                            <% else %>
                              <div class="mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  <.icon name="hero-exclamation-triangle" class="h-3 w-3 mr-1" />
                                  No Validation
                                </span>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      </button>
                    </div>
                  <% end %>
                </div>
              <% end %>

              <!-- Modal Actions -->
              <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  phx-click="hide_add_form_modal"
                  phx-target={@myself}
                  class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def handle_event("show_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, true)}
  end

  @impl true
  def handle_event("hide_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, false)}
  end

  @impl true
  def handle_event("show_add_form_modal", _params, socket) do
    {:noreply, assign(socket, :show_add_form_modal, true)}
  end

  @impl true
  def handle_event("hide_add_form_modal", _params, socket) do
    {:noreply, assign(socket, :show_add_form_modal, false)}
  end

  @impl true
  def handle_event("search_forms", %{"value" => query}, socket) do
    filtered_forms = if query == "" do
      socket.assigns.available_mobile_forms
    else
      socket.assigns.available_mobile_forms
      |> Enum.filter(&String.contains?(String.downcase(&1.name), String.downcase(query)))
    end

    socket =
      socket
      |> assign(:form_search_query, query)
      |> assign(:filtered_available_forms, filtered_forms)

    {:noreply, socket}
  end

  @impl true
  def handle_event("add_form_to_wizard", %{"form_id" => form_id}, socket) do
    wizard_id = socket.assigns.wizard.id
    
    # Calculate the next step number
    next_step_number = length(socket.assigns.ordered_steps) + 1
    
    case DynamicFormsManager.add_mobile_wizard_step(wizard_id, form_id, next_step_number) do
      {:ok, _step} ->
        # Refresh data
        updated_socket = refresh_component_data(socket)
        send(self(), :refresh_wizard_data)
        
        {:noreply, 
         updated_socket
         |> assign(:show_add_form_modal, false)
         |> put_flash(:info, "Form added to wizard successfully")}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to add form: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("remove_step", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.remove_wizard_step_and_reorder(step_id) do
      {:ok, :ok} ->
        # Refresh data
        updated_socket = refresh_component_data(socket)
        send(self(), :refresh_wizard_data)
        
        {:noreply, 
         updated_socket
         |> put_flash(:info, "Form removed from wizard successfully")}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to remove form: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("move_step_up", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.move_wizard_step_up(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)
        
        {:noreply, assign(socket, :ordered_steps, ordered_steps)}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end

  @impl true
  def handle_event("move_step_down", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.move_wizard_step_down(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)
        
        {:noreply, assign(socket, :ordered_steps, ordered_steps)}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end

  # Helper function to refresh component data
  defp refresh_component_data(socket) do
    wizard_id = socket.assigns.wizard.id
    ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(wizard_id)
    
    # Get available mobile forms for adding to wizard
    available_mobile_forms = case DynamicFormsManager.list_mobile_forms_for_wizard() do
      {:ok, forms} -> 
        # Filter out forms already in this wizard
        current_form_ids = Enum.map(ordered_steps, & &1.mobile_form_id)
        Enum.reject(forms, &(&1.id in current_form_ids))
      {:error, _} -> 
        []
    end
    
    # Apply current search filter
    filtered_forms = if socket.assigns.form_search_query == "" do
      available_mobile_forms
    else
      available_mobile_forms
      |> Enum.filter(&String.contains?(String.downcase(&1.name), String.downcase(socket.assigns.form_search_query)))
    end

    socket
    |> assign(:ordered_steps, ordered_steps)
    |> assign(:available_mobile_forms, available_mobile_forms)
    |> assign(:filtered_available_forms, filtered_forms)
  end
end