defmodule ServiceManagerWeb.Backend.DynamicFormsLive.DeviceFrameComponent do
  use ServiceManagerWeb, :live_component

  @impl true
  def update(assigns, socket) do
    device_config = get_device_config(assigns.device)
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:device_config, device_config)}
  end

  defp get_device_config(device) do
    case device do
      "mobile" -> %{
        width: "375px",
        height: "667px",
        border_radius: "24px",
        border_width: "8px",
        border_color: "border-gray-800",
        screen_border_radius: "16px",
        has_notch: true,
        has_home_indicator: true
      }
      "tablet" -> %{
        width: "768px", 
        height: "1024px",
        border_radius: "32px",
        border_width: "12px",
        border_color: "border-gray-700",
        screen_border_radius: "20px",
        has_notch: false,
        has_home_indicator: false
      }
      "desktop" -> %{
        width: "1200px",
        height: "800px",
        border_radius: "8px",
        border_width: "24px",
        border_color: "border-gray-800",
        screen_border_radius: "4px",
        has_notch: false,
        has_home_indicator: false,
        has_stand: true
      }
      _ -> get_device_config("mobile")
    end
  end

  defp get_frame_style(device_config) do
    "width: #{device_config.width}; height: #{device_config.height}; border-radius: #{device_config.border_radius};"
  end

  defp get_screen_style(device_config) do
    "border-radius: #{device_config.screen_border_radius};"
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="relative">
      <%= case @device do %>
        <% "mobile" -> %>
          <!-- Mobile Frame -->
          <div 
            class={"relative bg-gray-900 #{@device_config.border_color} shadow-2xl"}
            style={get_frame_style(@device_config)}
          >
            <!-- Mobile Border -->
            <div class={"absolute inset-2 bg-black rounded-[16px] overflow-hidden"}>
              <!-- Notch -->
              <%= if @device_config.has_notch do %>
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-900 rounded-b-xl z-20"></div>
              <% end %>
              
              <!-- Screen Content -->
              <div class="h-full w-full bg-white overflow-hidden flex flex-col" style={get_screen_style(@device_config)}>
                <!-- Mobile Status Bar -->
                <div class="bg-white px-4 py-1 flex items-center justify-between text-xs font-medium border-b border-gray-100">
                  <div class="flex items-center space-x-1">
                    <div class="flex space-x-1">
                      <div class="w-1 h-1 bg-gray-900 rounded-full"></div>
                      <div class="w-1 h-1 bg-gray-900 rounded-full"></div>
                      <div class="w-1 h-1 bg-gray-400 rounded-full"></div>
                    </div>
                    <span class="text-gray-900 ml-2">Verizon</span>
                  </div>
                  <div class="text-gray-900">9:41 AM</div>
                  <div class="flex items-center space-x-1">
                    <.icon name="hero-wifi" class="h-3 w-3 text-gray-900" />
                    <.icon name="hero-battery-100" class="h-3 w-3 text-gray-900" />
                  </div>
                </div>
                
                <!-- Mobile App Header -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-b border-gray-200 shadow-sm">
                  <div class="flex items-center space-x-3">
                    <button class="p-1">
                      <.icon name="hero-chevron-left" class="h-5 w-5 text-gray-700" />
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">Form Wizard</h1>
                  </div>
                  <button class="p-1">
                    <.icon name="hero-ellipsis-horizontal" class="h-5 w-5 text-gray-700" />
                  </button>
                </div>
                
                <!-- Mobile Content Area -->
                <div class="flex-1 overflow-hidden">
                  <%= render_slot(@inner_block) %>
                </div>
                
                <!-- Mobile Bottom Navigation -->
                <div class="bg-white border-t border-gray-200 px-4 py-2 flex items-center justify-around">
                  <button class="flex flex-col items-center space-y-1 py-1">
                    <.icon name="hero-home" class="h-5 w-5 text-indigo-600" />
                    <span class="text-xs text-indigo-600 font-medium">Home</span>
                  </button>
                  <button class="flex flex-col items-center space-y-1 py-1">
                    <.icon name="hero-document-text" class="h-5 w-5 text-gray-400" />
                    <span class="text-xs text-gray-400">Forms</span>
                  </button>
                  <button class="flex flex-col items-center space-y-1 py-1">
                    <.icon name="hero-chart-bar" class="h-5 w-5 text-gray-400" />
                    <span class="text-xs text-gray-400">Reports</span>
                  </button>
                  <button class="flex flex-col items-center space-y-1 py-1">
                    <.icon name="hero-user" class="h-5 w-5 text-gray-400" />
                    <span class="text-xs text-gray-400">Profile</span>
                  </button>
                </div>
              </div>
              
              <!-- Home Indicator -->
              <%= if @device_config.has_home_indicator do %>
                <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gray-600 rounded-full z-20"></div>
              <% end %>
            </div>
          </div>

        <% "tablet" -> %>
          <!-- Tablet Frame -->
          <div 
            class={"relative bg-gray-800 #{@device_config.border_color} shadow-2xl"}
            style={get_frame_style(@device_config)}
          >
            <!-- Tablet Border -->
            <div class={"absolute inset-3 bg-black rounded-[20px] overflow-hidden"}>
              <!-- Screen Content -->
              <div class="h-full w-full bg-white overflow-hidden flex flex-col" style={get_screen_style(@device_config)}>
                <!-- Tablet Header -->
                <div class="bg-white px-6 py-4 flex items-center justify-between border-b border-gray-200 shadow-sm">
                  <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                      <.icon name="hero-document-text" class="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h1 class="text-xl font-bold text-gray-900">Service Manager</h1>
                      <p class="text-sm text-gray-600">Form Wizard Platform</p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <button class="p-2 hover:bg-gray-100 rounded-lg">
                      <.icon name="hero-bell" class="h-5 w-5 text-gray-700" />
                    </button>
                    <button class="p-2 hover:bg-gray-100 rounded-lg">
                      <.icon name="hero-cog-6-tooth" class="h-5 w-5 text-gray-700" />
                    </button>
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <.icon name="hero-user" class="h-5 w-5 text-gray-600" />
                    </div>
                  </div>
                </div>
                
                <!-- Tablet Navigation Tabs -->
                <div class="bg-white px-6 border-b border-gray-200">
                  <nav class="flex space-x-6">
                    <button class="py-3 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm">
                      Form Wizard
                    </button>
                    <button class="py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                      Templates
                    </button>
                    <button class="py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                      Analytics
                    </button>
                    <button class="py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                      Settings
                    </button>
                  </nav>
                </div>
                
                <!-- Tablet Content Area -->
                <div class="flex-1 overflow-hidden">
                  <%= render_slot(@inner_block) %>
                </div>
              </div>
            </div>
            
            <!-- Home Button -->
            <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gray-700 rounded-full border-4 border-gray-600"></div>
          </div>

        <% "desktop" -> %>
          <!-- Desktop Frame -->
          <div class="relative">
            <!-- Monitor -->
            <div 
              class={"relative bg-gray-900 #{@device_config.border_color} shadow-2xl"}
              style={get_frame_style(@device_config)}
            >
              <!-- Monitor Border -->
              <div class={"absolute inset-6 bg-black rounded-[4px] overflow-hidden"}>
                <!-- Screen Content -->
                <div class="h-full w-full bg-white overflow-hidden flex flex-col" style={get_screen_style(@device_config)}>
                  <!-- Desktop Header -->
                  <div class="bg-white px-8 py-4 flex items-center justify-between border-b border-gray-200 shadow-sm">
                    <div class="flex items-center space-x-6">
                      <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                          <.icon name="hero-document-text" class="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h1 class="text-xl font-bold text-gray-900">Service Manager</h1>
                          <p class="text-sm text-gray-600">Dynamic Forms & Wizard Platform</p>
                        </div>
                      </div>
                      
                      <!-- Desktop Navigation -->
                      <nav class="hidden lg:flex items-center space-x-6 ml-8">
                        <a href="#" class="text-indigo-600 font-medium text-sm hover:text-indigo-700">
                          Dashboard
                        </a>
                        <a href="#" class="text-gray-700 font-medium text-sm hover:text-gray-900">
                          Forms
                        </a>
                        <a href="#" class="text-gray-700 font-medium text-sm hover:text-gray-900">
                          Wizards
                        </a>
                        <a href="#" class="text-gray-700 font-medium text-sm hover:text-gray-900">
                          Analytics
                        </a>
                        <a href="#" class="text-gray-700 font-medium text-sm hover:text-gray-900">
                          Templates
                        </a>
                      </nav>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                      <!-- Desktop Search -->
                      <div class="relative">
                        <input
                          type="text"
                          placeholder="Search forms..."
                          class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        />
                        <.icon name="hero-magnifying-glass" class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                      </div>
                      
                      <!-- Desktop User Menu -->
                      <div class="flex items-center space-x-3">
                        <button class="p-2 hover:bg-gray-100 rounded-lg relative">
                          <.icon name="hero-bell" class="h-5 w-5 text-gray-700" />
                          <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
                        </button>
                        <div class="flex items-center space-x-2 pl-2">
                          <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <.icon name="hero-user" class="h-5 w-5 text-gray-600" />
                          </div>
                          <div class="hidden lg:block text-right">
                            <p class="text-sm font-medium text-gray-900">John Doe</p>
                            <p class="text-xs text-gray-600">Administrator</p>
                          </div>
                          <.icon name="hero-chevron-down" class="h-4 w-4 text-gray-500 ml-1" />
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Desktop Breadcrumb -->
                  <div class="bg-gray-50 px-8 py-3 border-b border-gray-200">
                    <nav class="flex items-center space-x-2 text-sm">
                      <a href="#" class="text-gray-500 hover:text-gray-700">Dashboard</a>
                      <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-400" />
                      <a href="#" class="text-gray-500 hover:text-gray-700">Forms</a>
                      <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-400" />
                      <span class="text-gray-900 font-medium">Wizard Preview</span>
                    </nav>
                  </div>
                  
                  <!-- Desktop Content Area -->
                  <div class="flex-1 overflow-hidden">
                    <%= render_slot(@inner_block) %>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Monitor Stand -->
            <%= if @device_config.has_stand do %>
              <div class="relative mx-auto">
                <!-- Stand Neck -->
                <div class="w-2 h-16 bg-gray-700 mx-auto"></div>
                <!-- Stand Base -->
                <div class="w-48 h-8 bg-gray-700 rounded-lg mx-auto"></div>
              </div>
            <% end %>
          </div>

        <% _ -> %>
          <!-- Default Mobile Frame -->
          <div 
            class="relative bg-gray-900 border-8 border-gray-800 shadow-2xl"
            style="width: 375px; height: 667px; border-radius: 24px;"
          >
            <div class="absolute inset-2 bg-black rounded-[16px] overflow-hidden">
              <div class="h-full w-full bg-white overflow-hidden rounded-[16px] flex flex-col">
                <!-- Default Mobile UI -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-b border-gray-200">
                  <button>
                    <.icon name="hero-chevron-left" class="h-5 w-5 text-gray-700" />
                  </button>
                  <h1 class="text-lg font-semibold text-gray-900">Form Wizard</h1>
                  <button>
                    <.icon name="hero-ellipsis-horizontal" class="h-5 w-5 text-gray-700" />
                  </button>
                </div>
                
                <div class="flex-1 overflow-hidden">
                  <%= render_slot(@inner_block) %>
                </div>
              </div>
            </div>
          </div>
      <% end %>
      
      <!-- Device Label -->
      <div class="absolute -top-8 left-0 right-0 text-center">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-800 text-white">
          <.icon name={
            case @device do
              "mobile" -> "hero-device-phone-mobile"
              "tablet" -> "hero-device-tablet"
              "desktop" -> "hero-computer-desktop"
              _ -> "hero-device-phone-mobile"
            end
          } class="h-3 w-3 mr-1" />
          <%= String.capitalize(@device) %> Preview
        </span>
      </div>
      
      <!-- Resolution Info -->
      <div class="absolute -bottom-8 left-0 right-0 text-center">
        <span class="text-xs text-gray-500">
          <%= @device_config.width %> × <%= @device_config.height %>
        </span>
      </div>
    </div>
    """
  end
end