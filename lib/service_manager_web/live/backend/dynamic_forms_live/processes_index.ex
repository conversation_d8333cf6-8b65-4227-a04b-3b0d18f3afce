defmodule ServiceManagerWeb.Backend.DynamicFormsLive.ProcessesIndex do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Schemas.Dynamic.Processes.{DynamicProcess, ProcessManager}
  alias ServiceManagerWeb.Backend.DynamicFormsLive.AsyncQueryManager

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/dynamic-forms/processes")
      |> assign(:search_query, "")
      |> assign(:selected_category, "all")
      |> assign(:selected_group, "all")
      |> assign(:selected_type, "all")
      |> assign(:sort_by, "usage")
      |> assign(:view_mode, "grid")
      |> assign(:tree_view, true)
      |> assign(:expanded_categories, MapSet.new())
      |> assign(:expanded_groups, MapSet.new())
      |> assign(:expanded_types, MapSet.new())
      |> assign(:search_timer, nil)
      |> assign(:loading_stats, true)
      |> AsyncQueryManager.init_async_tracking()
      |> load_processes()
      |> load_filters()
      |> load_stats_async()

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    # Redirect to full-page edit view
    socket
    |> push_navigate(to: ~p"/mobileBanking/dynamic-forms/processes/#{id}/edit")
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Process")
    |> assign(:process, %DynamicProcess{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Plugin Library")
    |> assign(:process, nil)
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case ProcessManager.get_process(id) do
      {:error, :not_found} ->
        {:noreply,
         socket
         |> put_flash(:error, "Process not found")
         |> load_processes()
         |> load_stats_async()}

      {:ok, process} ->
        {:ok, _} = ProcessManager.delete_process(process)
        {:noreply,
         socket
         |> put_flash(:info, "Process deleted successfully")
         |> load_processes()
         |> load_stats_async()}
    end
  end

  @impl true
  def handle_event("chain", %{"id" => id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/mobileBanking/dynamic-forms/processes/#{id}/chain")}
  end

  @impl true
  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    # Cancel any existing search timer
    if socket.assigns[:search_timer] do
      Process.cancel_timer(socket.assigns.search_timer)
    end

    # Set a debounced timer for search to prevent rapid-fire searches
    timer = Process.send_after(self(), {:perform_search, query}, 300)

    {:noreply, assign(socket, :search_timer, timer)}
  end

  @impl true
  def handle_event("filter", %{"filter" => filters}, socket) do
    {:noreply,
     socket
     |> assign(:selected_category, Map.get(filters, "category", "all"))
     |> assign(:selected_group, Map.get(filters, "group", "all"))
     |> assign(:selected_type, Map.get(filters, "type", "all"))
     |> load_processes()
     |> load_stats_async()}
  end

  @impl true
  def handle_event("sort", %{"sort_by" => sort_by}, socket) do
    {:noreply,
     socket
     |> assign(:sort_by, sort_by)
     |> load_processes()
     |> load_stats_async()}
  end

  @impl true
  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  @impl true
  def handle_event("toggle_tree_view", _params, socket) do
    {:noreply, assign(socket, :tree_view, !socket.assigns.tree_view)}
  end

  @impl true
  def handle_event("toggle_category", %{"category" => category}, socket) do
    expanded_categories = socket.assigns.expanded_categories
    
    updated_expanded = if MapSet.member?(expanded_categories, category) do
      MapSet.delete(expanded_categories, category)
    else
      MapSet.put(expanded_categories, category)
    end
    
    {:noreply, assign(socket, :expanded_categories, updated_expanded)}
  end

  @impl true
  def handle_event("toggle_group", %{"group" => group, "category" => category}, socket) do
    group_key = "#{category}:#{group}"
    expanded_groups = socket.assigns.expanded_groups
    
    updated_expanded = if MapSet.member?(expanded_groups, group_key) do
      MapSet.delete(expanded_groups, group_key)
    else
      MapSet.put(expanded_groups, group_key)
    end
    
    {:noreply, assign(socket, :expanded_groups, updated_expanded)}
  end

  @impl true
  def handle_event("toggle_type", %{"type" => type, "group" => group, "category" => category}, socket) do
    type_key = "#{category}:#{group}:#{type}"
    expanded_types = socket.assigns.expanded_types
    
    updated_expanded = if MapSet.member?(expanded_types, type_key) do
      MapSet.delete(expanded_types, type_key)
    else
      MapSet.put(expanded_types, type_key)
    end
    
    {:noreply, assign(socket, :expanded_types, updated_expanded)}
  end

  @impl true
  def handle_info({:perform_search, query}, socket) do
    {:noreply,
     socket
     |> assign(:search_query, query)
     |> assign(:search_timer, nil)
     |> load_processes()
     |> load_stats_async()}
  end

  @impl true
  def handle_info({ref, result}, socket) when is_reference(ref) do
    AsyncQueryManager.handle_async_result(ref, result, socket, &handle_stats_result/4)
  end

  @impl true
  def handle_info({:DOWN, ref, :process, _pid, _reason}, socket) do
    {:noreply, AsyncQueryManager.handle_async_failure(ref, socket)}
  end

  @impl true
  def handle_event("feature", %{"id" => id}, socket) do
    case ProcessManager.get_process(id) do
      {:ok, process} ->
        {:ok, _} = ProcessManager.update_process(process, %{featured: !process.featured})
        {:noreply,
         socket
         |> put_flash(:info, "Process updated successfully")
         |> load_processes()
         |> load_stats_async()}

      _ ->
        {:noreply, put_flash(socket, :error, "Process not found")}
    end
  end

  @impl true
  def handle_event("sync", %{"id" => id}, socket) do
    process_id = String.to_integer(id)
    
    case ProcessManager.sync_process(process_id) do
      {:ok, _updated_process} ->
        {:noreply,
         socket
         |> put_flash(:info, "Process synced successfully - in-memory code reloaded")
         |> load_processes()
         |> load_stats_async()}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Sync failed: #{reason}")}
    end
  end

  defp load_processes(socket) do
    processes = ProcessManager.list_processes()


    # Load processes with placeholder stats initially - real stats loaded async
    processes_with_stats = Enum.map(processes, fn process ->
      Map.put(process, :stats, %{
        used_in_routes: 0,
        used_in_chains: 0,
        total_uses: 0,
        chain_length: 0,
        is_root: false,
        loading: true
      })
    end)

    # Apply filters
    filtered_processes =
      processes_with_stats
      |> filter_by_search(socket.assigns.search_query)
      |> filter_by_category(socket.assigns.selected_category)
      |> filter_by_group(socket.assigns.selected_group)
      |> filter_by_type(socket.assigns.selected_type)
      |> sort_processes(socket.assigns.sort_by)

    assign(socket, :processes, filtered_processes)
  end

  defp load_stats_async(socket) do
    AsyncQueryManager.start_async_item_queries(
      socket.assigns.processes,
      "process_stats",
      &calculate_process_stats/1,
      socket
    )
  end

  defp handle_stats_result(socket, query_key, process_id, result) do
    case result do
      {:ok, stats} ->
        # Update the specific process with the loaded stats
        updated_processes = Enum.map(socket.assigns.processes, fn process ->
          if process.id == process_id do
            Map.put(process, :stats, Map.put(stats, :loading, false))
          else
            process
          end
        end)

        # Check if all stats tasks are complete
        loading_stats = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "process_stats")

        {:noreply,
         socket
         |> assign(:processes, updated_processes)
         |> assign(:loading_stats, loading_stats)}

      {:error, _error} ->
        # On error, just mark as not loading with empty stats
        updated_processes = Enum.map(socket.assigns.processes, fn process ->
          if process.id == process_id do
            Map.put(process, :stats, %{
              used_in_routes: 0,
              used_in_chains: 0,
              total_uses: 0,
              chain_length: 0,
              is_root: false,
              loading: false
            })
          else
            process
          end
        end)

        loading_stats = AsyncQueryManager.has_pending_tasks_with_prefix?(socket, "process_stats")

        {:noreply,
         socket
         |> assign(:processes, updated_processes)
         |> assign(:loading_stats, loading_stats)}
    end
  end

  defp calculate_process_stats(process) do
    try do
      # Check if process is used as initial process in any route
      used_in_routes = count_route_usage_real(process.id)

      # Check if process is part of any chain
      chain_info_result = ProcessManager.get_complete_chain(process.id)
      {chain_length, is_root} = case chain_info_result do
        {:ok, chain_info} ->
          {length(Map.get(chain_info, :chain, [])), Map.get(chain_info, :root) != nil}
        {:error, _} ->
          {0, false}
      end

      # Count how many times this process is used in other chains
      used_in_chains = count_chain_usage_real(process.id)

      # Count total usage (routes + chains)
      total_uses = used_in_routes + used_in_chains

      %{
        used_in_routes: used_in_routes,
        used_in_chains: used_in_chains,
        total_uses: total_uses,
        chain_length: chain_length,
        is_root: is_root
      }
    rescue
      _ ->
        %{
          used_in_routes: 0,
          used_in_chains: 0,
          total_uses: 0,
          chain_length: 0,
          is_root: false
        }
    end
  end

  defp load_filters(socket) do
    processes = ProcessManager.list_processes()

    categories = processes
                |> Enum.map(& &1.category)
                |> Enum.uniq()
                |> Enum.reject(&is_nil/1)
                |> Enum.sort()

    groups = processes
            |> Enum.map(& &1.group)
            |> Enum.uniq()
            |> Enum.reject(&is_nil/1)
            |> Enum.sort()

    types = processes
           |> Enum.map(& &1.plugin_type)
           |> Enum.uniq()
           |> Enum.reject(&is_nil/1)
           |> Enum.sort()

    socket
    |> assign(:categories, categories)
    |> assign(:groups, groups)
    |> assign(:plugin_types, types)
  end

  defp filter_by_search(processes, ""), do: processes
  defp filter_by_search(processes, query) do
    query = String.downcase(query)
    Enum.filter(processes, fn process ->
      String.contains?(String.downcase(process.name || ""), query) ||
      String.contains?(String.downcase(process.description || ""), query) ||
      Enum.any?(process.tags || [], &String.contains?(String.downcase(&1), query))
    end)
  end

  defp filter_by_category(processes, "all"), do: processes
  defp filter_by_category(processes, category) do
    Enum.filter(processes, &(&1.category == category))
  end

  defp filter_by_group(processes, "all"), do: processes
  defp filter_by_group(processes, group) do
    Enum.filter(processes, &(&1.group == group))
  end

  defp filter_by_type(processes, "all"), do: processes
  defp filter_by_type(processes, type) do
    Enum.filter(processes, &(&1.plugin_type == type))
  end

  defp sort_processes(processes, "usage") do
    Enum.sort_by(processes, &(&1.stats.total_uses), :desc)
  end
  defp sort_processes(processes, "rating") do
    Enum.sort_by(processes, & &1.rating, :desc)
  end
  defp sort_processes(processes, "name") do
    Enum.sort_by(processes, & &1.name)
  end
  defp sort_processes(processes, "created") do
    Enum.sort_by(processes, & &1.inserted_at, {:desc, DateTime})
  end
  defp sort_processes(processes, _), do: processes

  defp count_route_usage(process_id) do
    # Return 0 for immediate UI response - real data loaded async
    0
  end

  defp count_chain_usage(process_id) do
    # Return 0 for immediate UI response - real data loaded async
    0
  end

  defp count_route_usage_real(process_id) do
    import Ecto.Query

    query = from rpl in ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink,
            where: rpl.initial_process_id == ^process_id

    ServiceManager.Repo.aggregate(query, :count, :id)
  end

  defp count_chain_usage_real(process_id) do
    import Ecto.Query

    query = from pcl in ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink,
            where: (pcl.source_process_id == ^process_id or pcl.target_process_id == ^process_id) and not pcl.is_root

    ServiceManager.Repo.aggregate(query, :count, :id)
  end

  defp plugin_type_color("system"), do: "bg-red-100 text-red-800"
  defp plugin_type_color("public"), do: "bg-green-100 text-green-800"
  defp plugin_type_color("protected"), do: "bg-yellow-100 text-yellow-800"
  defp plugin_type_color("private"), do: "bg-gray-100 text-gray-800"
  defp plugin_type_color("enterprise"), do: "bg-purple-100 text-purple-800"
  defp plugin_type_color(_), do: "bg-blue-100 text-blue-800"

  defp sync_status_color(status) when status in ["synced"], do: "text-green-500"
  defp sync_status_color(status) when status in ["pending"], do: "text-orange-500"
  defp sync_status_color(status) when status in ["error"], do: "text-red-500"
  defp sync_status_color(_), do: "text-gray-500"

  defp sync_status_icon(status) when status in ["synced"], do: "hero-check-circle"
  defp sync_status_icon(status) when status in ["pending"], do: "hero-clock"
  defp sync_status_icon(status) when status in ["error"], do: "hero-exclamation-triangle"
  defp sync_status_icon(_), do: "hero-question-mark-circle"

  defp sync_status_title(status) when status in ["synced"], do: "Code is synced with database"
  defp sync_status_title(status) when status in ["pending"], do: "Code sync pending"
  defp sync_status_title(status) when status in ["error"], do: "Code sync failed"
  defp sync_status_title(_), do: "Code sync status unknown"

  defp render_tree_structure(processes, expanded_categories, expanded_groups, expanded_types) do
    # Create hierarchical tree structure: Category > Group > Plugin Type > Plugins
    tree_data = processes
    |> Enum.group_by(fn process -> process.category || "Uncategorized" end)
    |> Enum.map(fn {category, category_processes} ->
      # Group processes within each category by group
      groups = category_processes
      |> Enum.group_by(fn process -> process.group || "Default" end)
      |> Enum.map(fn {group_name, group_processes} ->
        # Group processes within each group by plugin_type
        types = group_processes
        |> Enum.group_by(fn process -> process.plugin_type || "public" end)
        |> Enum.map(fn {plugin_type, type_processes} ->
          %{
            name: plugin_type,
            type: :plugin_type,
            processes: type_processes,
            count: length(type_processes)
          }
        end)
        |> Enum.sort_by(fn type_data ->
          # Sort types by priority: system > enterprise > private > protected > public
          case type_data.name do
            "system" -> 1
            "enterprise" -> 2
            "private" -> 3
            "protected" -> 4
            "public" -> 5
            _ -> 6
          end
        end)
        
        %{
          name: group_name,
          type: :group,
          types: types,
          processes: group_processes,
          count: length(group_processes)
        }
      end)
      |> Enum.sort_by(fn group_data ->
        # Sort groups by usage (total processes count) descending, then alphabetical
        {-group_data.count, group_data.name}
      end)
      
      %{
        name: category,
        type: :category,
        groups: groups,
        processes: category_processes,
        count: length(category_processes)
      }
    end)
    |> Enum.sort_by(fn %{name: name} -> 
      if name == "Uncategorized", do: "zzz", else: name
    end)
    
    assigns = %{
      tree_data: tree_data,
      expanded_categories: expanded_categories,
      expanded_groups: expanded_groups,
      expanded_types: expanded_types
    }
    
    ~H"""
    <div class="space-y-2">
      <%= for category <- @tree_data do %>
        <div class="border border-gray-200 rounded-lg bg-white shadow-sm">
          <!-- Category Header (Always visible) -->
          <div 
            class="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            phx-click="toggle_category"
            phx-value-category={category.name}
          >
            <div class="flex items-center space-x-3">
              <!-- Expand/Collapse Icon -->
              <%= if MapSet.member?(@expanded_categories, category.name) do %>
                <.icon name="hero-chevron-down" class="h-4 w-4 text-gray-500 transition-transform" />
              <% else %>
                <.icon name="hero-chevron-right" class="h-4 w-4 text-gray-500 transition-transform" />
              <% end %>
              
              <!-- Category Icon and Name -->
              <%= if category.name == "Uncategorized" do %>
                <.icon name="hero-folder-open" class="h-5 w-5 text-gray-400" />
                <h3 class="text-base font-medium text-gray-600"><%= category.name %></h3>
              <% else %>
                <.icon name="hero-folder" class="h-5 w-5 text-indigo-500" />
                <h3 class="text-base font-medium text-gray-900"><%= category.name %></h3>
              <% end %>
            </div>
            
            <!-- Category Stats -->
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span><%= length(category.groups) %> groups</span>
              <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-700">
                <%= category.count %> plugin<%= if category.count != 1, do: "s" %>
              </span>
            </div>
          </div>
          
          <!-- Category Content (Expandable) -->
          <%= if MapSet.member?(@expanded_categories, category.name) do %>
            <div class="border-t border-gray-200 bg-gray-50">
              <%= for group <- category.groups do %>
                <div class="ml-6 border-l border-gray-200">
                  <!-- Group Header -->
                  <div 
                    class="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-100 transition-colors relative"
                    phx-click="toggle_group"
                    phx-value-group={group.name}
                    phx-value-category={category.name}
                  >
                    <!-- Connection line -->
                    <div class="absolute left-0 top-1/2 w-4 h-px bg-gray-200"></div>
                    
                    <div class="flex items-center space-x-3 ml-4">
                      <!-- Expand/Collapse Icon for Group -->
                      <%= if MapSet.member?(@expanded_groups, "#{category.name}:#{group.name}") do %>
                        <.icon name="hero-chevron-down" class="h-3 w-3 text-gray-400" />
                      <% else %>
                        <.icon name="hero-chevron-right" class="h-3 w-3 text-gray-400" />
                      <% end %>
                      
                      <!-- Group Icon and Name -->
                      <.icon name="hero-rectangle-group" class="h-4 w-4 text-purple-500" />
                      <span class="text-sm font-medium text-gray-700"><%= group.name %></span>
                    </div>
                    
                    <!-- Group Stats -->
                    <div class="flex items-center space-x-3 text-xs text-gray-500">
                      <span><%= length(group.types) %> types</span>
                      <span class="bg-gray-200 px-2 py-1 rounded">
                        <%= group.count %> plugin<%= if group.count != 1, do: "s" %>
                      </span>
                    </div>
                  </div>
                  
                  <!-- Group Content (Plugin Types) -->
                  <%= if MapSet.member?(@expanded_groups, "#{category.name}:#{group.name}") do %>
                    <div class="ml-8 pb-4">
                      <%= for type <- group.types do %>
                        <div class="mb-4 border-l border-gray-200 pl-4">
                          <!-- Type Header -->
                          <div 
                            class="flex items-center justify-between mb-3 relative cursor-pointer hover:bg-gray-50 p-2 rounded"
                            phx-click="toggle_type"
                            phx-value-type={type.name}
                            phx-value-group={group.name}
                            phx-value-category={category.name}
                          >
                            <!-- Connection line -->
                            <div class="absolute left-0 top-1/2 w-4 h-px bg-gray-200"></div>
                            
                            <div class="flex items-center space-x-2 ml-4">
                              <!-- Expand/Collapse Icon for Type -->
                              <%= if MapSet.member?(@expanded_types, "#{category.name}:#{group.name}:#{type.name}") do %>
                                <.icon name="hero-chevron-down" class="h-3 w-3 text-gray-400" />
                              <% else %>
                                <.icon name="hero-chevron-right" class="h-3 w-3 text-gray-400" />
                              <% end %>
                              
                              <span class={[
                                "inline-flex items-center px-2 py-1 rounded text-xs font-medium",
                                plugin_type_color(type.name)
                              ]}>
                                <%= String.capitalize(type.name) %>
                              </span>
                              <span class="text-xs text-gray-500">
                                <%= type.count %> plugin<%= if type.count != 1, do: "s" %>
                              </span>
                            </div>
                          </div>
                          
                          <!-- Plugins Grid -->
                          <%= if MapSet.member?(@expanded_types, "#{category.name}:#{group.name}:#{type.name}") do %>
                            <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ml-4">
                              <%= for process <- type.processes do %>
                                <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                                  <!-- Header -->
                                  <div class="p-3 border-b border-gray-100">
                                    <div class="flex items-start justify-between mb-1">
                                      <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= process.name %></h3>
                                      <div class="flex space-x-1 flex-shrink-0">
                                        <%= if process.featured do %>
                                          <.icon name="hero-star" class="h-3 w-3 text-yellow-400" />
                                        <% end %>
                                        <%= if process.verified do %>
                                          <.icon name="hero-check-badge" class="h-3 w-3 text-blue-500" />
                                        <% end %>
                                        <.icon 
                                          name={sync_status_icon(process.sync_status || "pending")} 
                                          class={["h-3 w-3", sync_status_color(process.sync_status || "pending")]}
                                          title={sync_status_title(process.sync_status || "pending")}
                                        />
                                      </div>
                                    </div>

                                    <p class="text-xs text-gray-600 line-clamp-2 mb-2">
                                      <%= process.description || "No description provided" %>
                                    </p>

                                    <!-- Usage Stats -->
                                    <div class="flex items-center justify-between text-xs">
                                      <div class="flex items-center space-x-2">
                                        <span class="flex items-center text-gray-500">
                                          <.icon name="hero-link" class="h-3 w-3 mr-1" />
                                          <%= format_usage_stats(process.stats) %>
                                        </span>
                                      </div>
                                      <div class="flex items-center space-x-2">
                                        <span class="flex items-center text-gray-500">
                                          <.icon name="hero-star" class="h-3 w-3 mr-1" />
                                          <%= process.rating %>
                                        </span>
                                        <span class="text-gray-400">v<%= process.version %></span>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Tags -->
                                  <%= if process.tags && length(process.tags) > 0 do %>
                                    <div class="p-2 border-b border-gray-100">
                                      <div class="flex flex-wrap gap-1">
                                        <%= for tag <- Enum.take(process.tags, 2) do %>
                                          <span class="inline-block px-1.5 py-0.5 text-xs bg-blue-50 text-blue-700 rounded">
                                            <%= tag %>
                                          </span>
                                        <% end %>
                                        <%= if length(process.tags) > 2 do %>
                                          <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-50 text-gray-500 rounded">
                                            +<%= length(process.tags) - 2 %>
                                          </span>
                                        <% end %>
                                      </div>
                                    </div>
                                  <% end %>

                                  <!-- Actions -->
                                  <div class="p-2">
                                    <div class="flex space-x-1">
                                      <.link
                                        navigate={~p"/mobileBanking/dynamic-forms/processes/#{process}/edit"}
                                        class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                      >
                                        <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                                        Edit
                                      </.link>
                                      <button
                                        phx-click="chain"
                                        phx-value-id={process.id}
                                        class="inline-flex items-center px-2 py-1 border border-indigo-300 text-xs font-medium rounded text-indigo-700 bg-white hover:bg-indigo-50"
                                        title="Configure Chain"
                                      >
                                        <.icon name="hero-link" class="h-3 w-3" />
                                      </button>
                                      <button
                                        phx-click="sync"
                                        phx-value-id={process.id}
                                        class="inline-flex items-center px-2 py-1 border border-green-300 text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50"
                                        title="Sync - Reload code from database"
                                      >
                                        <.icon name="hero-arrow-path" class="h-3 w-3" />
                                      </button>
                                      <button
                                        phx-click="delete"
                                        phx-value-id={process.id}
                                        data-confirm="Are you sure you want to delete this plugin?"
                                        class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                                      >
                                        <.icon name="hero-trash" class="h-3 w-3" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              <% end %>
                            </div>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    """
  end

  defp format_usage_stats(stats) do
    if Map.get(stats, :loading, false) do
      "Loading..."
    else
      parts = []

      parts = if stats.used_in_routes > 0 do
        ["#{stats.used_in_routes} routes" | parts]
      else
        parts
      end

      parts = if stats.used_in_chains > 0 do
        ["#{stats.used_in_chains} chains" | parts]
      else
        parts
      end

      parts = if stats.is_root do
        ["root" | parts]
      else
        parts
      end

      case parts do
        [] -> "0 uses"
        _ -> Enum.join(parts, ", ")
      end
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.NavigationComponent}
        id="navigation"
        page_title="Plugin Library"
        subtitle="Discover and manage code plugins"
        current_page={:processes}
        breadcrumb={[
          %{title: "Dynamic Forms", link: ~p"/mobileBanking/dynamic-forms"},
          %{title: "Plugin Library"}
        ]}
      />

      <!-- Search and Filters -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-6">
          <!-- Search and Filters -->
          <div class="flex flex-col lg:flex-row gap-4 mb-6">
            <!-- Search Bar -->
            <div class="flex-1">
              <.form for={%{}} as={:search} phx-change="search" class="relative">
                <input
                  type="text"
                  name="search[query]"
                  value={@search_query}
                  placeholder="Search plugins..."
                  class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                <.icon name="hero-magnifying-glass" class="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
              </.form>
            </div>

            <!-- Filters -->
            <div class="flex gap-3">
              <.form for={%{}} as={:filter} phx-change="filter" class="flex gap-3">
                <select name="filter[category]" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="all">All Categories</option>
                  <%= for category <- @categories do %>
                    <option value={category} selected={@selected_category == category}><%= category %></option>
                  <% end %>
                </select>

                <select name="filter[group]" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="all">All Groups</option>
                  <%= for group <- @groups do %>
                    <option value={group} selected={@selected_group == group}><%= group %></option>
                  <% end %>
                </select>

                <select name="filter[type]" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="all">All Types</option>
                  <%= for type <- @plugin_types do %>
                    <option value={type} selected={@selected_type == type}><%= String.capitalize(type) %></option>
                  <% end %>
                </select>
              </.form>
            </div>
          </div>

          <!-- Sort and View Controls -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600"><%= length(@processes) %> plugins</span>
              
              <!-- Tree View Toggle -->
              <%= if @view_mode == "grid" do %>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">Layout:</span>
                  <button
                    phx-click="toggle_tree_view"
                    class={[
                      "inline-flex items-center px-2 py-1 rounded text-xs font-medium transition-colors border",
                      if @tree_view do
                        "border-indigo-300 bg-indigo-50 text-indigo-700"
                      else
                        "border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                      end
                    ]}
                    title="Toggle Tree View"
                  >
                    <.icon name="hero-bars-3" class="h-3 w-3 mr-1" />
                    <%= if @tree_view, do: "Tree", else: "Cards" %>
                  </button>
                </div>
              <% end %>

              <!-- Sort Controls -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">Sort:</span>
                <.form for={%{}} as={:sort} phx-change="sort" class="inline">
                  <select name="sort_by" class="border border-gray-300 rounded text-sm px-2 py-1">
                    <option value="usage" selected={@sort_by == "usage"}>Most Used</option>
                    <option value="rating" selected={@sort_by == "rating"}>Highest Rated</option>
                    <option value="name" selected={@sort_by == "name"}>Name A-Z</option>
                    <option value="created" selected={@sort_by == "created"}>Recently Created</option>
                  </select>
                </.form>
              </div>
            </div>

            <!-- View Toggle and Create Button -->
            <div class="flex items-center space-x-3">
              <!-- Create Button -->
              <.link
                patch={~p"/mobileBanking/dynamic-forms/processes/new"}
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                Create Plugin
              </.link>

              <!-- View Toggle -->
              <div class="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  phx-click="toggle_view"
                  phx-value-view="grid"
                  class={[
                    "px-3 py-1 rounded text-sm font-medium transition-colors",
                    if(@view_mode == "grid", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                  ]}
                >
                  <.icon name="hero-squares-2x2" class="h-4 w-4" />
                </button>
                <button
                  phx-click="toggle_view"
                  phx-value-view="list"
                  class={[
                    "px-3 py-1 rounded text-sm font-medium transition-colors",
                    if(@view_mode == "list", do: "bg-white shadow text-gray-900", else: "text-gray-600 hover:text-gray-900")
                  ]}
                >
                  <.icon name="hero-list-bullet" class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="px-6 py-6">
        <%= if Enum.empty?(@processes) do %>
          <!-- Empty State -->
          <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <.icon name="hero-puzzle-piece" class="h-12 w-12 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No plugins found</h3>
            <p class="text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
            <.link
              patch={~p"/mobileBanking/dynamic-forms/processes/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Create Your First Plugin
            </.link>
          </div>
        <% else %>
          <!-- Grid View -->
          <%= if @view_mode == "grid" do %>
            <%= if @tree_view do %>
              <%= render_tree_structure(@processes, @expanded_categories, @expanded_groups, @expanded_types) %>
            <% else %>
              <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
                <%= for process <- @processes do %>
              <div class="bg-white rounded-md border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <!-- Header -->
                <div class="p-3 border-b border-gray-100">
                  <div class="flex items-start justify-between mb-1">
                    <h3 class="text-sm font-medium text-gray-900 truncate pr-1"><%= process.name %></h3>
                    <div class="flex space-x-1 flex-shrink-0">
                      <%= if process.featured do %>
                        <.icon name="hero-star" class="h-3 w-3 text-yellow-400" />
                      <% end %>
                      <%= if process.verified do %>
                        <.icon name="hero-check-badge" class="h-3 w-3 text-blue-500" />
                      <% end %>
                      <.icon 
                        name={sync_status_icon(process.sync_status || "pending")} 
                        class={["h-3 w-3", sync_status_color(process.sync_status || "pending")]}
                        title={sync_status_title(process.sync_status || "pending")}
                      />
                    </div>
                  </div>

                  <div class="flex items-center space-x-1 mb-2">
                    <span class={["inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium", plugin_type_color(process.plugin_type)]}>
                      <%= String.capitalize(process.plugin_type) %>
                    </span>
                    <%= if process.category do %>
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                        <%= process.category %>
                      </span>
                    <% end %>
                  </div>

                  <p class="text-xs text-gray-600 line-clamp-2 mb-2">
                    <%= process.description || "No description provided" %>
                  </p>

                  <!-- Usage Stats -->
                  <div class="flex items-center justify-between text-xs">
                    <div class="flex items-center space-x-2">
                      <span class="flex items-center text-gray-500">
                        <.icon name="hero-link" class="h-3 w-3 mr-1" />
                        <%= format_usage_stats(process.stats) %>
                      </span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="flex items-center text-gray-500">
                        <.icon name="hero-star" class="h-3 w-3 mr-1" />
                        <%= process.rating %>
                      </span>
                      <span class="text-gray-400">v<%= process.version %></span>
                    </div>
                  </div>
                </div>

                <!-- Tags -->
                <%= if process.tags && length(process.tags) > 0 do %>
                  <div class="p-2 border-b border-gray-100">
                    <div class="flex flex-wrap gap-1">
                      <%= for tag <- Enum.take(process.tags, 2) do %>
                        <span class="inline-block px-1.5 py-0.5 text-xs bg-blue-50 text-blue-700 rounded">
                          <%= tag %>
                        </span>
                      <% end %>
                      <%= if length(process.tags) > 2 do %>
                        <span class="inline-block px-1.5 py-0.5 text-xs bg-gray-50 text-gray-500 rounded">
                          +<%= length(process.tags) - 2 %>
                        </span>
                      <% end %>
                    </div>
                  </div>
                <% end %>

                <!-- Actions -->
                <div class="p-2">
                  <div class="flex space-x-1">
                    <.link
                      navigate={~p"/mobileBanking/dynamic-forms/processes/#{process}/edit"}
                      class="flex-1 inline-flex justify-center items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <.icon name="hero-pencil" class="h-3 w-3 mr-1" />
                      Edit
                    </.link>
                    <button
                      phx-click="chain"
                      phx-value-id={process.id}
                      class="inline-flex items-center px-2 py-1 border border-indigo-300 text-xs font-medium rounded text-indigo-700 bg-white hover:bg-indigo-50"
                      title="Configure Chain"
                    >
                      <.icon name="hero-link" class="h-3 w-3" />
                    </button>
                    <button
                      phx-click="sync"
                      phx-value-id={process.id}
                      class="inline-flex items-center px-2 py-1 border border-green-300 text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50"
                      title="Sync - Reload code from database"
                    >
                      <.icon name="hero-arrow-path" class="h-3 w-3" />
                    </button>
                    <button
                      phx-click="delete"
                      phx-value-id={process.id}
                      data-confirm="Are you sure you want to delete this plugin?"
                      class="inline-flex items-center px-2 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
                    >
                      <.icon name="hero-trash" class="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
                <% end %>
              </div>
            <% end %>
          <% end %>

          <!-- List View - Compact Table -->
          <div :if={@view_mode == "list"} class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Plugin</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Usage</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Version</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Created</th>
                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <%= for process <- @processes do %>
                    <tr class="hover:bg-gray-50">
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 mr-3">
                            <div class="flex space-x-1">
                              <%= if process.featured do %>
                                <.icon name="hero-star" class="h-4 w-4 text-yellow-400" />
                              <% end %>
                              <%= if process.verified do %>
                                <.icon name="hero-check-badge" class="h-4 w-4 text-blue-500" />
                              <% end %>
                              <.icon 
                                name={sync_status_icon(process.sync_status || "pending")} 
                                class={["h-4 w-4", sync_status_color(process.sync_status || "pending")]}
                                title={sync_status_title(process.sync_status || "pending")}
                              />
                            </div>
                          </div>
                          <div class="min-w-0 flex-1">
                            <span class="text-sm font-medium text-gray-900 truncate max-w-32 block" title={process.name}>
                              <%= process.name %>
                            </span>
                            <p class="text-xs text-gray-500 truncate max-w-32" title={process.description}>
                              <%= process.description || "No description" %>
                            </p>
                          </div>
                        </div>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class={["inline-flex items-center px-2 py-1 rounded text-sm font-medium", plugin_type_color(process.plugin_type)]}>
                          <%= String.capitalize(process.plugin_type) %>
                        </span>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <%= if process.category do %>
                          <span class="text-sm text-gray-900"><%= process.category %></span>
                        <% else %>
                          <span class="text-sm text-gray-400">None</span>
                        <% end %>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="flex items-center">
                          <.icon name="hero-link" class="h-4 w-4 mr-1 text-gray-400" />
                          <span class="text-sm text-gray-900">
                            <%= format_usage_stats(process.stats) %>
                          </span>
                        </div>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="flex items-center">
                          <.icon name="hero-star" class="h-4 w-4 mr-1 text-gray-400" />
                          <span class="text-sm text-gray-900"><%= process.rating %></span>
                        </div>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class="text-sm text-gray-900">v<%= process.version %></span>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class="text-sm text-gray-500">
                          <%= Calendar.strftime(process.inserted_at, "%b %d, %Y") %>
                        </span>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="flex items-center space-x-2">
                          <.link
                            navigate={~p"/mobileBanking/dynamic-forms/processes/#{process}/edit"}
                            class="inline-flex items-center px-2 py-1 border border-gray-300 text-sm rounded text-gray-700 bg-white hover:bg-gray-50"
                            title="Edit"
                          >
                            <.icon name="hero-pencil" class="h-4 w-4" />
                          </.link>
                          <button
                            phx-click="chain"
                            phx-value-id={process.id}
                            class="inline-flex items-center px-2 py-1 border border-indigo-300 text-sm rounded text-indigo-700 bg-white hover:bg-indigo-50"
                            title="Configure Chain"
                          >
                            <.icon name="hero-link" class="h-4 w-4" />
                          </button>
                          <button
                            phx-click="sync"
                            phx-value-id={process.id}
                            class="inline-flex items-center px-2 py-1 border border-green-300 text-sm rounded text-green-700 bg-white hover:bg-green-50"
                            title="Sync - Reload code from database"
                          >
                            <.icon name="hero-arrow-path" class="h-4 w-4" />
                          </button>
                          <button
                            phx-click="delete"
                            phx-value-id={process.id}
                            data-confirm="Are you sure you want to delete this plugin?"
                            class="inline-flex items-center px-2 py-1 border border-red-300 text-sm rounded text-red-700 bg-white hover:bg-red-50"
                            title="Delete"
                          >
                            <.icon name="hero-trash" class="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <.modal :if={@live_action in [:new]} id="process-modal" show on_cancel={JS.patch(~p"/mobileBanking/dynamic-forms/processes")}>
      <.live_component
        module={ServiceManagerWeb.Backend.DynamicFormsLive.ProcessFormComponent}
        id={@process.id || :new}
        title={@page_title}
        action={@live_action}
        process={@process}
        current_user={@current_user}
        patch={~p"/mobileBanking/dynamic-forms/processes"}
      />
    </.modal>
    """
  end
end
