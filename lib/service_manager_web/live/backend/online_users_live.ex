defmodule ServiceManagerWeb.Backend.OnlineUsersLive do
  use ServiceManagerWeb, :live_view
  alias ServiceManagerWeb.Presence
  alias ServiceManager.Accounts

  @presence_topic "users:presence"
  # 2 seconds in prod, 5 in dev
  @refresh_interval if Mix.env() == :prod, do: 20000, else: 5000
  # 1 second
  @heartbeat_interval 1000

  @impl true
  def mount(_params, session, socket) do
    # Configure presence timeout in seconds
    # default 5 minutes
    presence_timeout = Application.get_env(:service_manager, :presence_timeout, 300)

    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/online-users")
      |> assign(:presence_timeout, presence_timeout)

    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, @presence_topic)

      if socket.assigns[:current_user] do
        # Set up both refresh and heartbeat intervals
        :timer.send_interval(@refresh_interval, :refresh)
        # :timer.send_interval(@heartbeat_interval, :heartbeat)

        # Initial presence tracking
        track_user_presence(socket, socket.assigns.current_user)
      end
    end

    online_users =
      Presence.list(@presence_topic)
      |> Enum.map(fn {"user:" <> user_id, %{metas: [meta | _]}} ->
        # Convert string ID back to integer
        {id, _} = Integer.parse(user_id)
        Map.put(meta, :user_id, id)
      end)
      |> fetch_users()

    socket =
      socket
      |> assign(:online_users, online_users)
      |> assign(:filter_params, %{
        "amount_from" => "",
        "amount_to" => "",
        "end_date" => "",
        "from_date" => "",
        "operator" => "",
        "page" => 1,
        "page_size" => 10,
        "query_field" => "",
        "query_search" => "",
        "reference" => "",
        "search" => "",
        "sort_field" => "last_seen_at",
        "sort_order" => "desc",
        "status" => "",
        "type" => ""
      })
      |> assign(:pagination, %{
        page_number: 1,
        page_size: 10,
        total_entries: length(online_users),
        total_pages: ceil(length(online_users) / 10)
      })
      |> assign(:selected_column, "last_seen_at")

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Online Users")
    |> assign(:data, nil)
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Online Users")
    |> assign(:data, %{})
  end

  defp apply_action(socket, :export, _params) do
    socket
    |> assign(:page_title, "Export Online Users")
    |> assign(:data, %{})
  end

  @impl true
  def handle_info(:heartbeat, socket) do
    if socket.assigns[:current_user] do
      track_user_presence(socket, socket.assigns.current_user)
    end

    {:noreply, socket}
  end

  @impl true
  def handle_info(:refresh, socket) do
    # Update presence on refresh
    track_user_presence(socket, socket.assigns.current_user)

    online_users =
      Presence.list(@presence_topic)
      |> Enum.map(fn {"user:" <> user_id, %{metas: [meta | _]}} ->
        {id, _} = Integer.parse(user_id)
        Map.put(meta, :user_id, id)
      end)
      |> fetch_users()

    socket =
      socket
      |> assign(:online_users, online_users)
      |> assign(:pagination, %{
        page_number: socket.assigns.pagination.page_number,
        page_size: socket.assigns.pagination.page_size,
        total_entries: length(online_users),
        total_pages: ceil(length(online_users) / socket.assigns.pagination.page_size)
      })

    {:noreply, socket}
  end

  @impl true
  def handle_info(%Phoenix.Socket.Broadcast{event: "presence_diff"}, socket) do
    # Update presence on presence_diff
    track_user_presence(socket, socket.assigns.current_user)

    online_users =
      Presence.list(@presence_topic)
      |> Enum.map(fn {"user:" <> user_id, %{metas: [meta | _]}} ->
        {id, _} = Integer.parse(user_id)
        Map.put(meta, :user_id, id)
      end)
      |> fetch_users()

    socket =
      socket
      |> assign(:online_users, online_users)
      |> assign(:pagination, %{
        page_number: socket.assigns.pagination.page_number,
        page_size: socket.assigns.pagination.page_size,
        total_entries: length(online_users),
        total_pages: ceil(length(online_users) / socket.assigns.pagination.page_size)
      })

    {:noreply, socket}
  end

  defp fetch_users(presence_metas) do
    user_ids = Enum.map(presence_metas, & &1.user_id)
    users = Accounts.list_users_by_ids(user_ids)
    wallet_users = ServiceManager.WalletAccounts.list_wallet_users_by_ids(user_ids)

    Enum.map(presence_metas, fn meta ->
      user =
        Enum.find(users, &(&1.id == meta.user_id)) ||
          Enum.find(wallet_users, &(&1.id == meta.user_id))

      # Keep all metadata for third-party users
      case meta[:user_type] do
        "third_party" ->
          meta
          |> Map.put(:user, user)
          |> Map.merge(%{
            api_key: meta[:api_key],
            description: meta[:description]
          })

        _ ->
          Map.put(meta, :user, user)
      end
    end)
  end

  @impl true
  def render(assigns) do
    ~H"""
    <.header>
      Listing Online Users
    </.header>

    <.table
      id="online_users"
      rows={@online_users}
      streams={%{data: @online_users}}
      filter_params={@filter_params}
      pagination={@pagination}
      selected_column={@selected_column}
      filter_url={~p"/mobileBanking/online-users/filter"}
      export_url={~p"/mobileBanking/online-users/export"}
      show_filter={true}
      show_export={true}
    >
      <:col :let={user_meta} label="Status">
        <%= if DateTime.diff(DateTime.utc_now(), user_meta.last_seen_at, :second) < @presence_timeout do %>
          <span class="inline-flex items-center gap-x-1 rounded-md px-1 py-0.5 text-xs font-medium text-green-700">
            <svg class="h-4 w-4 fill-green-500" viewBox="0 0 6 6" aria-hidden="true">
              <circle cx="3" cy="3" r="3" />
            </svg>
            Online
          </span>
        <% else %>
          <span class="inline-flex items-center gap-x-1 rounded-full px-1 py-0.5 text-xs font-medium text-gray-800">
            <svg class="h-4 w-4 fill-gray-500" viewBox="0 0 6 6" aria-hidden="true">
              <circle cx="3" cy="3" r="3" />
            </svg>
            Offline
          </span>
        <% end %>
      </:col>

      <:col :let={user_meta} label="Type">
        <%= case {user_meta.user, user_meta[:user_type]} do %>
          <% {%ServiceManager.Accounts.User{}, _} -> %>
            <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
              Mobile Banking
            </span>
          <% {%ServiceManager.WalletAccounts.WalletUser{}, _} -> %>
            <span class="inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10">
              Wallet
            </span>
          <% {_, "third_party"} -> %>
            <span class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-700/10">
              Third Party
            </span>
          <% _ -> %>
            <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-700/10">
              Unknown
            </span>
        <% end %>
      </:col>

      <:col :let={user_meta} label="Name/Description">
        <div class="text-[12px] font-medium">
          <%= case {user_meta.user, user_meta[:user_type]} do %>
            <% {%ServiceManager.Accounts.User{} = user, _} -> %>
              <%= user.first_name %> <%= user.last_name %>
            <% {%ServiceManager.WalletAccounts.WalletUser{} = user, _} -> %>
              <%= user.first_name %> <%= user.last_name %>
            <% {_, "third_party"} -> %>
              <%= user_meta[:description] %>
            <% _ -> %>
              <span class="text-gray-500">Unknown User</span>
          <% end %>
        </div>
      </:col>

      <:col :let={user_meta} label="Contact/API Key">
        <div class="text-[12px] whitespace-nowrap">
          <%= case {user_meta.user, user_meta[:user_type]} do %>
            <% {%ServiceManager.Accounts.User{} = user, _} -> %>
              <%= user.email %>
            <% {%ServiceManager.WalletAccounts.WalletUser{} = user, _} -> %>
              <%= user.mobile_number %>
            <% {_, "third_party"} -> %>
              <span class="font-mono"><%= user_meta[:api_key] %></span>
            <% _ -> %>
              <span class="text-gray-500">-</span>
          <% end %>
        </div>
      </:col>

      <:col :let={user_meta} label="Device ID">
        <div class="text-[12px] font-mono"><%= user_meta.device_id %></div>
      </:col>

      <:col :let={user_meta} label="Last Seen">
        <div class="text-[12px] whitespace-nowrap">
          <%= Calendar.strftime(user_meta.last_seen_at, "%Y-%m-%d %H:%M:%S") %>
        </div>
      </:col>
    </.table>

    <.modal
      :if={@live_action in [:filter, :export]}
      id="data-modal"
      show
      on_cancel={JS.patch(~p"/mobileBanking/online-users")}
    >
      <.live_component
        module={ServiceManagerWeb.Backend.OnlineUsersLive.FilterComponent}
        id={:filters}
        title={@page_title}
        action={@live_action}
        data={@data}
        filter_params={@filter_params}
        url={@url}
        patch={~p"/mobileBanking/online-users"}
      />
    </.modal>
    """
  end

  @impl true
  def handle_event(event, params, socket) do
    # Update presence on any user interaction
    track_user_presence(socket, socket.assigns.current_user)

    case event do
      "filter" ->
        filtered_users = apply_filters(socket.assigns.online_users, params, socket)

        socket =
          socket
          |> assign(:online_users, filtered_users)
          |> assign(:filter_params, params)
          |> assign(:pagination, %{
            page_number: 1,
            page_size: socket.assigns.pagination.page_size,
            total_entries: length(filtered_users),
            total_pages: ceil(length(filtered_users) / socket.assigns.pagination.page_size)
          })

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def terminate(_reason, socket) do
    if socket.assigns[:current_user] do
      Presence.untrack(
        self(),
        @presence_topic,
        "user:#{socket.assigns.current_user.id}"
      )
    end

    :ok
  end

  # Track user presence with metadata and error handling
  defp track_user_presence(socket, user) when not is_nil(user) do
    try do
      Presence.track(
        self(),
        @presence_topic,
        "user:#{user.id}",
        %{
          user_id: user.id,
          device_id: socket.assigns[:device_id] || "web",
          last_seen_at: DateTime.utc_now()
        }
      )
    rescue
      _ ->
        # If tracking fails, try to untrack first then track again
        Presence.untrack(self(), @presence_topic, "user:#{user.id}")

        Presence.track(
          self(),
          @presence_topic,
          "user:#{user.id}",
          %{
            user_id: user.id,
            device_id: socket.assigns[:device_id] || "web",
            last_seen_at: DateTime.utc_now()
          }
        )
    end
  end

  defp track_user_presence(_socket, _user), do: :ok

  defp apply_filters(users, params, socket) do
    users
    |> filter_by_type(params["type"])
    |> filter_by_status(params["status"], socket)
    |> filter_by_search(params["search"])
  end

  defp filter_by_type(users, nil), do: users
  defp filter_by_type(users, ""), do: users

  defp filter_by_type(users, "mobile_banking") do
    Enum.filter(users, fn user_meta ->
      match?(%ServiceManager.Accounts.User{}, user_meta.user)
    end)
  end

  defp filter_by_type(users, "wallet") do
    Enum.filter(users, fn user_meta ->
      match?(%ServiceManager.WalletAccounts.WalletUser{}, user_meta.user)
    end)
  end

  defp filter_by_type(users, "third_party") do
    Enum.filter(users, fn user_meta ->
      user_meta[:user_type] == "third_party"
    end)
  end

  defp filter_by_status(users, nil, _socket), do: users
  defp filter_by_status(users, "", _socket), do: users

  defp filter_by_status(users, "online", socket) do
    Enum.filter(users, fn user_meta ->
      DateTime.diff(DateTime.utc_now(), user_meta.last_seen_at, :second) <
        socket.assigns.presence_timeout
    end)
  end

  defp filter_by_status(users, "offline", socket) do
    Enum.filter(users, fn user_meta ->
      DateTime.diff(DateTime.utc_now(), user_meta.last_seen_at, :second) >=
        socket.assigns.presence_timeout
    end)
  end

  defp filter_by_search(users, nil), do: users
  defp filter_by_search(users, ""), do: users

  defp filter_by_search(users, search) do
    search = String.downcase(search)

    Enum.filter(users, fn user_meta ->
      case {user_meta.user, user_meta[:user_type]} do
        {%ServiceManager.Accounts.User{} = user, _} ->
          String.contains?(String.downcase(user.first_name), search) ||
            String.contains?(String.downcase(user.last_name), search) ||
            String.contains?(String.downcase(user.email), search)

        {%ServiceManager.WalletAccounts.WalletUser{} = user, _} ->
          String.contains?(String.downcase(user.first_name), search) ||
            String.contains?(String.downcase(user.last_name), search) ||
            String.contains?(String.downcase(user.mobile_number), search)

        {_, "third_party"} ->
          String.contains?(String.downcase(user_meta[:description] || ""), search) ||
            String.contains?(String.downcase(user_meta[:api_key] || ""), search)

        _ ->
          false
      end
    end)
  end

  defp format_last_seen(last_seen_at, assigns) do
    diff = DateTime.diff(DateTime.utc_now(), last_seen_at, :second)

    cond do
      diff < assigns.presence_timeout ->
        "Online"

      true ->
        "Last seen: #{Calendar.strftime(last_seen_at, "%Y-%m-%d %H:%M:%S")}"
    end
  end
end
