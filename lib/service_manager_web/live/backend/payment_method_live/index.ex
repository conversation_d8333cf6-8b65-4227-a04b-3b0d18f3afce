defmodule ServiceManagerWeb.Backend.PaymentMethodLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.PaymentMethodsContext
  alias ServiceManager.Schemas.PaymentMethod
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/paymentMethods"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    # Retrieve data based on params and generate pagination details
    data = PaymentMethodsContext.retrieve(params)
    pagination = generate_pagination_details(data)

    # Remove existing streams from socket assigns
    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:payment_methods, data)}
  end

  def handle_params(params, _url, socket) do
    # Retrieve all data and generate pagination details
    data = PaymentMethodsContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:payment_methods, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Payment method")
    |> assign(:payment_method, PaymentMethodsContext.get_payment_method!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Payment method")
    |> assign(:payment_method, %PaymentMethod{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Payment methods")
    |> assign(:payment_method, nil)
  end

  @impl true
  def handle_info(
        {ServiceManagerWeb.Backend.PaymentMethodLive.FormComponent, {:saved, payment_method}},
        socket
      ) do
    {:noreply, stream_insert(socket, :payment_methods, payment_method)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    payment_method = PaymentMethodsContext.get_payment_method!(id)
    {:ok, _} = PaymentMethodsContext.delete_payment_method(payment_method)

    {:noreply, stream_delete(socket, :payment_methods, payment_method)}
  end

  def handle_event("activate", %{"id" => id}, socket) do
    data = PaymentMethodsContext.get_payment_method!(id)

    PaymentMethodsContext.update_data(data, %{"status" => "active"}, socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Currency activated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Currency activation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("deactivate", %{"id" => id}, socket) do
    data = PaymentMethodsContext.get_payment_method!(id)

    PaymentMethodsContext.update_data(
      data,
      %{"status" => "inactive"},
      socket.assigns.current_user
    )
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Currency inactivated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Currency inactivation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
