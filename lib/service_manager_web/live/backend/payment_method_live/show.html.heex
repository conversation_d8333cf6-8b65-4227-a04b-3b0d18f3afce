<.header>
  Payment method <%= @payment_method.id %>
  <:subtitle>This is a payment_method record from your database.</:subtitle>
  <:actions>
    <.link
      patch={~p"/mobileBanking/paymentMethods/#{@payment_method}/show/edit"}
      phx-click={JS.push_focus()}
    >
      <.button>Edit payment_method</.button>
    </.link>
  </:actions>
</.header>

<.list>
  <:item title="Name"><%= @payment_method.name %></:item>
  <:item title="Type"><%= @payment_method.type %></:item>
  <:item title="Description"><%= @payment_method.description %></:item>
  <:item title="Status"><%= @payment_method.status %></:item>
  <:item title="Fees"><%= @payment_method.fees_id %></:item>
  <:item title="Created by"><%= @payment_method.created_by %></:item>
  <:item title="Updated by"><%= @payment_method.updated_by %></:item>
</.list>

<.back navigate={~p"/mobileBanking/paymentMethods"}>Back to payment_methods</.back>

<.modal
  :if={@live_action == :edit}
  id="payment_method-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/paymentMethods/#{@payment_method}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.PaymentMethodLive.FormComponent}
    id={@payment_method.id}
    title={@page_title}
    action={@live_action}
    payment_method={@payment_method}
    patch={~p"/mobileBanking/paymentMethods/#{@payment_method}"}
  />
</.modal>
