<.header>
  Payment Methods Management
  <:subtitle>Configure and manage supported payment methods, their types and status.</:subtitle>

  <:actions>
    <%= if can_create?(@current_user, :payment_methods) do %>
      <.link patch={~p"/mobileBanking/paymentMethods/new"}>
        <.button>New Payment method</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="payment_methods"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.payment_methods}
  row_click={
    fn {_id, payment_method} ->
      JS.navigate(~p"/mobileBanking/paymentMethods/#{payment_method}")
    end
  }
>
  <:col :let={{_id, payment_method}} filter_item="name" label="Name">
    <%= payment_method.name %>
  </:col>
  <:col :let={{_id, payment_method}} filter_item="type" label="Type">
    <%= payment_method.type %>
  </:col>
  <:col :let={{_id, dataset}} filter_item="status" label="Status">
    <.status_pill status={dataset.status} text={dataset.status} />
  </:col>

  <:action :let={{id, dataset}}>
    <%= if has_any_permission?(@current_user, [:update, :delete, :activate], :payment_methods) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_activate?(@current_user, :payment_methods) do %>
          <%= if dataset.status == "active" do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("deactivate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Deactivate
            </.link>
          <% else %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("activate", value: %{id: dataset.id})}
              data-confirm="Are you sure?"
            >
              Activate
            </.link>
          <% end %>
        <% end %>

        <%= if can_update?(@current_user, :payment_methods) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/paymentMethods/#{dataset}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :payment_methods) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: dataset.id}) |> hide("##{id}")}
            data-confirm="Are you sure?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="payment_method-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/paymentMethods")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.PaymentMethodLive.FormComponent}
    id={@payment_method.id || :new}
    title={@page_title}
    action={@live_action}
    payment_method={@payment_method}
    current_user={@current_user}
    patch={~p"/mobileBanking/paymentMethods"}
  />
</.modal>
