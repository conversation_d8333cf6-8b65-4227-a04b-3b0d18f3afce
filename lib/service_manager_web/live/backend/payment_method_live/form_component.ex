defmodule ServiceManagerWeb.Backend.PaymentMethodLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.PaymentMethodsContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage payment_method records in your database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="payment_method-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.input field={@form[:name]} type="text" label="Name" />
          <.input field={@form[:type]} type="text" label="Type" />
          <.input field={@form[:fees_id]} type="number" label="Fees" />
          <.input field={@form[:description]} type="textarea" label="Description" />
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Save Payment method</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{payment_method: payment_method} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(PaymentMethodsContext.change_payment_method(payment_method))
     end)}
  end

  @impl true
  def handle_event("validate", %{"payment_method" => payment_method_params}, socket) do
    changeset =
      PaymentMethodsContext.change_payment_method(
        socket.assigns.payment_method,
        payment_method_params
      )

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"payment_method" => payment_method_params}, socket) do
    save_payment_method(socket, socket.assigns.action, payment_method_params)
  end

  defp save_payment_method(socket, :edit, payment_method_params) do
    case PaymentMethodsContext.update_payment_method(
           socket.assigns.payment_method,
           payment_method_params
         ) do
      {:ok, payment_method} ->
        notify_parent({:saved, payment_method})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Payment Method updated successfully",
              details: inspect(payment_method)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Payment method updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Payment Method updated Failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_payment_method(socket, :new, payment_method_params) do
    case PaymentMethodsContext.create_payment_method(payment_method_params) do
      {:ok, payment_method} ->
        notify_parent({:saved, payment_method})

        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Payment Method created successfully",
              details: inspect(payment_method)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Payment method created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Payment Method create failed",
              details: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end
