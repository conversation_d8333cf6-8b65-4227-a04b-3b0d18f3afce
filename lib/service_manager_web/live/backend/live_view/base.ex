defmodule ServiceManagerWeb.Backend.LiveView.Base do
  @moduledoc """
  Base module for LiveView implementations providing common functionality
  for handling actions, pagination, sorting, and filtering.
  """

  defmacro __using__(opts) do
    quote do
      use Phoenix.LiveView
      import ServiceManagerWeb.CoreComponents

      @context Keyword.fetch!(unquote(opts), :context)
      @schema Keyword.fetch!(unquote(opts), :schema)
      @url Keyword.fetch!(unquote(opts), :url)

      @impl true
      def mount(_params, _session, socket) do
        socket = assign(socket, :current_path, @url)
        {:ok, socket}
      end

      @impl true
      def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
        data = @context.retrieve(params)
        pagination = generate_pagination_details(data)

        assigns = socket.assigns |> Map.delete(:streams)

        socket =
          socket
          |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

        {:noreply,
         apply_action(socket, socket.assigns.live_action, params)
         |> assign(:selected_column, sort_field)
         |> assign(:filter_params, params)
         |> assign(pagination: pagination)
         |> stream(:data, data)}
      end

      def handle_params(params, _url, socket) do
        data = @context.retrieve()
        pagination = generate_pagination_details(data)

        {:noreply,
         apply_action(socket, socket.assigns.live_action, params)
         |> stream(:data, data)
         |> assign(pagination: pagination)}
      end

      @impl true
      def handle_event("search", %{"isearch" => search}, socket) do
        {params, endpoint} =
          socket.assigns.filter_params
          |> search_encode_url(search)

        {:noreply,
         socket
         |> assign(:filter_params, params)
         |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
      end

      def handle_event("page_size", %{"page_size" => page_size}, socket) do
        {params, endpoint} =
          socket.assigns.filter_params
          |> page_size_encode_url(page_size)

        {:noreply,
         socket
         |> assign(:filter_params, params)
         |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
      end

      # Default action handlers that can be overridden
      defp apply_action(socket, :index, _params) do
        socket
        |> assign(:page_title, "Listing #{@schema.__schema__(:source)}")
        |> assign(:data, nil)
      end

      defp apply_action(socket, :new, _params) do
        socket
        |> assign(:page_title, "New #{@schema.__schema__(:source)}")
        |> assign(:data, struct(@schema))
      end

      defp apply_action(socket, :edit, %{"id" => id}) do
        socket
        |> assign(:page_title, "Edit #{@schema.__schema__(:source)}")
        |> assign(:data, @context.get_data!(id))
      end

      # Allow overriding any function
      defoverridable mount: 3,
                     handle_params: 3,
                     apply_action: 3
    end
  end
end
