<div class="space-y-6">
  <.header>
    Mobile Forms V2
    <:subtitle>Manage screens, pages and forms for the mobile app</:subtitle>
  </.header>

  <!-- Tabs -->
  <div class="border-b border-gray-200">
    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
      <%= for {tab, label} <- [{"screens", "Screens"}, {"pages", "Pages"}, {"forms", "Forms"}, {"hierarchy", "Hierarchy"}] do %>
        <button phx-click="select_tab" phx-value-tab={tab}
          class={[
            "py-2 px-1 border-b-2 font-medium text-sm",
            if(@active_tab == tab, do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
          ]}>
          <%= label %>
        </button>
      <% end %>
    </nav>
  </div>

  <!-- Tab bodies -->
  <%= case @active_tab do %>
    <% "screens" -> %>
      <Components.screens_tab
        screens={@v2_screens}
        selected_screen_id={@v2_selected_screen_id}
      />
    <% "pages" -> %>
      <Components.pages_tab
        screens={@v2_screens}
        pages={@v2_pages}
        selected_screen_id={@v2_selected_screen_id}
        selected_page_id={@v2_selected_page_id}
        all_pages={@v2_all_pages}
      />
    <% "forms" -> %>
      <Components.forms_tab
        pages={@v2_pages}
        forms={@v2_forms}
        selected_page_id={@v2_selected_page_id}
        selected_form_id={@v2_selected_form_id}
        submit_to={@v2_form_submit_to}
        selected_screen_id={@v2_selected_screen_id}
        fields={@v2_fields}
        screens={@v2_screens}
        all_pages={@v2_all_pages}
        all_forms={@v2_all_forms}
        editing_field_id={@v2_editing_field_id}
        dynamic_routes={@dynamic_routes}
      />
    <% _ -> %>
      <Components.hierarchy_tab
        screens={@v2_screens}
        pages={@v2_pages}
        forms={@v2_forms}
        selected_screen_id={@v2_selected_screen_id}
        selected_page_id={@v2_selected_page_id}
        selected_form_id={@v2_selected_form_id}
      />
  <% end %>
</div>
