defmodule ServiceManagerWeb.Backend.MobileFormsLive.DataLoaders do
  @moduledoc """
  Data loading functions for the Mobile Forms LiveView.
  Centralizes all data fetching logic to reduce duplication.
  """

  # Import Phoenix functions for helper modules
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]
  alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
  alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service, as: V2
  alias ServiceManagerWeb.Api.Services.Local.UssdService

  @doc """
  Load all V2 data (screens, pages, forms, fields)
  """
  def load_v2_data(socket) do
    case V2.list_screens(%{}) do
      {:ok, screens} ->
        socket
        |> assign(:v2_screens, screens)
        |> load_all_v2_pages()
        |> load_all_v2_forms()
        |> load_v2_pages_if_selected()
        |> load_v2_forms_if_selected()
        |> load_v2_fields_if_selected()
      {:error, _} ->
        put_flash(socket, :error, "Failed to load screens")
    end
  end

  @doc """
  Load legacy form data with filtering and sorting
  """
  def load_legacy_data(socket) do
    case MobileAppFormsService.list_fields() do
      {:ok, fields} ->
        # Group fields by form name with statistics
        grouped_forms = fields
        |> Enum.group_by(& &1.form)
        |> Enum.map(fn {form_name, form_fields} ->
          field_types = form_fields |> Enum.map(& &1.field_type) |> Enum.uniq()
          has_buttons = "button" in field_types

          %{
            name: form_name,
            fields: form_fields,
            field_count: length(form_fields),
            screen_count: form_fields |> Enum.map(& &1.screen) |> Enum.reject(&is_nil/1) |> Enum.uniq() |> length(),
            page_count: form_fields |> Enum.map(& &1.page) |> Enum.reject(&is_nil/1) |> Enum.uniq() |> length(),
            field_types: field_types,
            has_buttons: has_buttons,
            version: form_fields |> List.first() |> Map.get(:version, "1.0"),
            updated_at: form_fields |> Enum.map(& &1.updated_at) |> Enum.max()
          }
        end)
        |> filter_forms(socket.assigns.search_query, socket.assigns.selected_form_filter, socket.assigns.selected_type)
        |> sort_forms(socket.assigns.sort_by)

        socket
        |> assign(:fields, fields)
        |> assign(:grouped_forms, grouped_forms)
        |> assign(:unique_forms, get_unique_forms(fields))
        |> assign(:unique_screens, get_unique_screens(fields, socket.assigns[:selected_form]))
        |> assign(:unique_pages, get_unique_pages(fields, socket.assigns[:selected_form], socket.assigns[:selected_screen]))
        |> assign(:field_types, get_unique_field_types(fields))
      {:error, _} ->
        socket
        |> assign(:fields, [])
        |> assign(:grouped_forms, [])
        |> assign(:unique_forms, [])
        |> assign(:unique_screens, [])
        |> assign(:unique_pages, [])
        |> assign(:field_types, [])
        |> put_flash(:error, "Failed to load form data")
    end
  end

  @doc """
  Load filtered data based on current selections
  """
  def load_filtered_data(socket) do
    form = socket.assigns.selected_form
    screen = socket.assigns.selected_screen
    page = socket.assigns.selected_page

    case MobileAppFormsService.list_fields() do
      {:ok, fields} ->
        filtered_fields = 
          fields
          |> filter_by_hierarchy(form, screen, page)
          |> filter_by_search(socket.assigns.search_query)
          |> sort_fields(socket.assigns.sort_by)

        socket |> assign(:fields, filtered_fields)
      {:error, _} ->
        socket
        |> assign(:fields, [])
        |> put_flash(:error, "Failed to load filtered data")
    end
  end

  @doc """
  Load USSD data
  """
  def load_ussd_data(socket) do
    case UssdService.list_menus() do
      {:ok, menus} ->
        socket |> assign(:ussd_menus, menus)
      {:error, _reason} ->
        socket |> put_flash(:error, "Failed to load USSD data")
    end
  end

  ## Private Helper Functions

  defp load_v2_pages_if_selected(socket) do
    case socket.assigns[:v2_selected_screen_id] do
      nil -> socket
      screen_id ->
        case V2.list_pages(%{screen_id: screen_id}) do
          {:ok, pages} -> assign(socket, :v2_pages, pages)
          {:error, _} -> socket
        end
    end
  end

  defp load_v2_forms_if_selected(socket) do
    case socket.assigns[:v2_selected_page_id] do
      nil -> socket
      page_id ->
        case V2.list_forms(%{page_id: page_id}) do
          {:ok, forms} -> assign(socket, :v2_forms, forms)
          {:error, _} -> socket
        end
    end
  end

  defp load_v2_fields_if_selected(socket) do
    case socket.assigns[:v2_selected_form_id] do
      nil -> socket
      form_id ->
        case V2.get_form_fields(form_id) do
          {:ok, fields} -> assign(socket, :v2_fields, fields)
          {:error, _} -> socket
        end
    end
  end

  defp filter_by_search(fields, ""), do: fields
  defp filter_by_search(fields, query) do
    query_lower = String.downcase(query)
    Enum.filter(fields, fn field ->
      String.contains?(String.downcase(field.field_name || ""), query_lower) ||
      String.contains?(String.downcase(field.label || ""), query_lower)
    end)
  end

  defp filter_by_form(fields, "all"), do: fields
  defp filter_by_form(fields, form_name) do
    Enum.filter(fields, fn field -> field.form_name == form_name end)
  end



  defp filter_by_hierarchy(fields, form, screen, page) do
    fields
    |> filter_by_form_if_present(form)
    |> filter_by_screen_if_present(screen)
    |> filter_by_page_if_present(page)
  end

  defp filter_by_form_if_present(fields, nil), do: fields
  defp filter_by_form_if_present(fields, form), do: filter_by_form(fields, form)

  defp filter_by_screen_if_present(fields, nil), do: fields
  defp filter_by_screen_if_present(fields, screen) do
    Enum.filter(fields, fn field -> field.screen_name == screen end)
  end

  defp filter_by_page_if_present(fields, nil), do: fields
  defp filter_by_page_if_present(fields, page) do
    Enum.filter(fields, fn field -> field.page_name == page end)
  end

  defp sort_fields(fields, "name") do
    Enum.sort_by(fields, & &1.field_name)
  end

  defp sort_fields(fields, "form") do
    Enum.sort_by(fields, & &1.form_name)
  end

  defp sort_fields(fields, "type") do
    Enum.sort_by(fields, & &1.field_type)
  end

  defp sort_fields(fields, _), do: fields



  # Additional helper functions for legacy data processing
  defp filter_forms(grouped_forms, query, form_filter, type_filter) do
    grouped_forms
    |> filter_forms_by_search(query)
    |> filter_forms_by_name(form_filter)
    |> filter_forms_by_type(type_filter)
  end

  defp filter_forms_by_search(forms, ""), do: forms
  defp filter_forms_by_search(forms, query) do
    query_lower = String.downcase(query)
    Enum.filter(forms, fn form ->
      String.contains?(String.downcase(form.name || ""), query_lower)
    end)
  end

  defp filter_forms_by_name(forms, "all"), do: forms
  defp filter_forms_by_name(forms, name) do
    Enum.filter(forms, fn form -> form.name == name end)
  end

  defp filter_forms_by_type(forms, "all"), do: forms
  defp filter_forms_by_type(forms, field_type) do
    Enum.filter(forms, fn form -> field_type in form.field_types end)
  end

  defp sort_forms(forms, "name"), do: Enum.sort_by(forms, & &1.name)
  defp sort_forms(forms, "fields"), do: Enum.sort_by(forms, & &1.field_count, :desc)
  defp sort_forms(forms, "updated"), do: Enum.sort_by(forms, & &1.updated_at, {:desc, DateTime})
  defp sort_forms(forms, _), do: forms

  defp get_unique_forms(fields) do
    fields |> Enum.map(& &1.form) |> Enum.uniq() |> Enum.reject(&is_nil/1) |> Enum.sort()
  end

  defp get_unique_screens(fields, selected_form) do
    fields
    |> filter_by_form_if_present(selected_form)
    |> Enum.map(& &1.screen)
    |> Enum.uniq()
    |> Enum.reject(&is_nil/1)
    |> Enum.sort()
  end

  defp get_unique_pages(fields, selected_form, selected_screen) do
    fields
    |> filter_by_form_if_present(selected_form)
    |> filter_by_screen_if_present(selected_screen)
    |> Enum.map(& &1.page)
    |> Enum.uniq()
    |> Enum.reject(&is_nil/1)
    |> Enum.sort()
  end

  defp get_unique_field_types(fields) do
    fields |> Enum.map(& &1.field_type) |> Enum.uniq() |> Enum.reject(&is_nil/1) |> Enum.sort()
  end

  # Load ALL pages (not just selected screen's pages)
  defp load_all_v2_pages(socket) do
    # Get all pages across all screens
    all_pages = socket.assigns.v2_screens
    |> Enum.flat_map(fn screen ->
      case V2.list_pages(%{screen_id: screen.id}) do
        {:ok, pages} -> pages
        {:error, _} -> []
      end
    end)

    assign(socket, :v2_all_pages, all_pages)
  end

  # Load ALL forms (not just selected page's forms)
  defp load_all_v2_forms(socket) do
    # Get all forms across all pages
    all_forms = case socket.assigns[:v2_all_pages] do
      nil -> []
      pages ->
        pages
        |> Enum.flat_map(fn page ->
          case V2.list_forms(%{page_id: page.id}) do
            {:ok, forms} -> forms
            {:error, _} -> []
          end
        end)
    end

    assign(socket, :v2_all_forms, all_forms)
  end
end
