defmodule ServiceManagerWeb.Backend.MobileFormsLive.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
  alias ServiceManagerWeb.Api.MobileAppFormsSchema
  alias ServiceManager.Routing.DynamicRouteManager

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Configure mobile form field properties</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="field-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div class="space-y-4">
            <.input field={@form[:form]} type="text" label="Form" placeholder="e.g., transfer, beneficiary" required />
            <.input field={@form[:screen]} type="text" label="Screen (Optional)" placeholder="e.g., main, confirmation" />
            <.input field={@form[:page]} type="text" label="Page (Optional)" placeholder="e.g., step1, step2" />
            <.input field={@form[:version]} type="text" label="Version" placeholder="1.0" value="1.0" required />
          </div>

          <!-- Right Column -->
          <div class="space-y-4">
            <.input field={@form[:field_name]} type="text" label="Field Name" placeholder="e.g., amount, account_number" required />
            <.input 
              field={@form[:field_type]} 
              type="select" 
              label="Field Type" 
              options={field_type_options()}
              required 
            />
            <.input field={@form[:label]} type="text" label="Display Label" placeholder="e.g., Amount, Account Number" required />
            <div class="grid grid-cols-2 gap-4">
              <.input field={@form[:field_order]} type="number" label="Display Order" value="0" min="0" step="1" />
              <div class="space-y-2">
                <.input field={@form[:is_required]} type="checkbox" label="Required Field" />
                <.input field={@form[:active]} type="checkbox" label="Active" checked />
              </div>
            </div>
          </div>
        </div>

        <!-- Dynamic Submit To Section -->
        <div class="mt-6">
          <div id="submit-to-section" phx-update="ignore">
            <%= case Ecto.Changeset.get_field(@form.source, :field_type) do %>
              <% "button" -> %>
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 class="text-sm font-medium text-green-900 mb-2">Button Action Configuration</h4>
                  
                  <!-- Route Selection Dropdown -->
                  <.input 
                    field={@form[:route_selection]} 
                    type="select" 
                    label="Select Route (Optional)" 
                    options={route_options(@routes)}
                    phx-target={@myself}
                    phx-change="route_selected"
                    prompt="Choose a route or enter manually"
                  />
                  
                  <.input field={@form[:submit_to]} type="text" label="Action Target" placeholder="e.g., /api/transfers, https://api.example.com/submit, transfer-service" />
                  <p class="text-sm text-green-700 mt-1">
                    Define where this button should submit data or what action it should trigger. You can select a route above to auto-fill the path.
                  </p>
                </div>
              <% _ -> %>
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 class="text-sm font-medium text-blue-900 mb-2">Form Submission Configuration</h4>
                  
                  <!-- Route Selection Dropdown -->
                  <.input 
                    field={@form[:route_selection]} 
                    type="select" 
                    label="Select Route (Optional)" 
                    options={route_options(@routes)}
                    phx-target={@myself}
                    phx-change="route_selected"
                    prompt="Choose a route or enter manually"
                  />
                  
                  <.input field={@form[:submit_to]} type="text" label="Submit To (Optional)" placeholder="e.g., /api/transfers, https://api.example.com/submit, transfer-service" />
                  <p class="text-sm text-blue-700 mt-1">
                    Define where form data should be submitted. You can select a route above to auto-fill the path. Leave empty to use form-level default.
                  </p>
                </div>
            <% end %>
          </div>
        </div>

        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 class="text-sm font-medium text-blue-900 mb-2">Field Hierarchy</h4>
          <p class="text-sm text-blue-700 mb-2">
            Fields are organized hierarchically: <strong>Form → Screen → Page → Field</strong>
          </p>
          <ul class="text-xs text-blue-600 space-y-1">
            <li>• <strong>Form</strong> is required (e.g., "transfer", "beneficiary")</li>
            <li>• <strong>Screen</strong> is optional (e.g., "main", "confirmation")</li>
            <li>• <strong>Page</strong> is optional but requires a screen (e.g., "step1", "step2")</li>
            <li>• <strong>Field Order</strong> determines the display sequence within the same level</li>
          </ul>
        </div>

        <:actions>
          <.button phx-disable-with="Saving..." type="submit">Save Field</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{field: field} = assigns, socket) do
    changeset = MobileAppFormsSchema.changeset(field, %{})
    routes = DynamicRouteManager.list_routes()

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:routes, routes)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"mobile_app_forms_schema" => field_params}, socket) do
    changeset =
      socket.assigns.field
      |> MobileAppFormsSchema.changeset(field_params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"mobile_app_forms_schema" => field_params}, socket) do
    save_field(socket, socket.assigns.action, field_params)
  end

  def handle_event("route_selected", %{"mobile_app_forms_schema" => %{"route_selection" => route_id}}, socket) when route_id != "" do
    case DynamicRouteManager.get_route(route_id) do
      nil -> {:noreply, socket}
      route ->
        changeset = 
          socket.assigns.field
          |> MobileAppFormsSchema.changeset(%{submit_to: "/dynamic#{route.path}"})
          |> Map.put(:action, :validate)
        
        {:noreply, assign_form(socket, changeset)}
    end
  end

  def handle_event("route_selected", _params, socket) do
    {:noreply, socket}
  end

  defp save_field(socket, :edit, field_params) do
    case MobileAppFormsService.update_field(socket.assigns.field.field_id, field_params) do
      {:ok, field} ->
        notify_parent({:saved, field})

        {:noreply,
         socket
         |> put_flash(:info, "Field updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, message} ->
        {:noreply, 
         socket
         |> put_flash(:error, "Failed to update field: #{message}")
         |> assign_form(MobileAppFormsSchema.changeset(socket.assigns.field, field_params))}
    end
  end

  defp save_field(socket, :new, field_params) do
    case MobileAppFormsService.create_field(field_params) do
      {:ok, field} ->
        notify_parent({:saved, field})

        {:noreply,
         socket
         |> put_flash(:info, "Field created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, message} ->
        {:noreply, 
         socket
         |> put_flash(:error, "Failed to create field: #{message}")
         |> assign_form(MobileAppFormsSchema.changeset(%MobileAppFormsSchema{}, field_params))}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})

  defp field_type_options do
    [
      {"String", "string"},
      {"Number", "number"}, 
      {"Integer", "integer"},
      {"Boolean", "boolean"},
      {"Date", "date"},
      {"DateTime", "datetime"},
      {"Email", "email"},
      {"Password", "password"},
      {"Phone", "phone"},
      {"Select", "select"},
      {"Multi-Select", "multiselect"},
      {"Text Area", "textarea"},
      {"Button", "button"}
    ]
  end

  defp route_options(routes) do
    routes
    |> Enum.filter(&(&1.enabled))
    |> Enum.map(fn route ->
      display_name = "#{route.method} #{route.name} (#{route.path})"
      {display_name, route.id}
    end)
    |> Enum.sort_by(&elem(&1, 0))
  end
end