defmodule ServiceManagerWeb.Backend.MobileFormsLive.Index do
  use ServiceManagerWeb, :live_view
  import Logger
  alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
  alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service, as: V2
  alias ServiceManagerWeb.Api.Services.Local.UssdService
  alias ServiceManagerWeb.Api.UssdMenuSchema
  alias ServiceManagerWeb.Api.UssdOptionSchema
  alias ServiceManagerWeb.Api.MobileAppFormsSchema
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components
  alias ServiceManagerWeb.Backend.MobileFormsLive.Helpers
  alias ServiceManagerWeb.Backend.MobileFormsLive.UssdHandlers
  alias ServiceManagerWeb.Backend.MobileFormsLive.DataLoaders
  alias ServiceManager.Routing.DynamicRouteManager
  import ServiceManagerWeb.Utilities.PermissionHelpers

  on_mount {ServiceManagerWeb.UserAuth, :ensure_authenticated}

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:live_action, :index)
      |> assign(:url, ~p"/mobileBanking/mobile-forms")
      |> assign(:search_query, "")
      |> assign(:selected_form_filter, "all")
      |> assign(:selected_type, "all")
      |> assign(:sort_by, "name")
      |> assign(:view_mode, "grid")
      |> assign(:active_tab, "screens")
      |> assign(:selected_form, nil)
      |> assign(:selected_screen, nil)
      |> assign(:selected_page, nil)
      |> assign(:create_form_form, to_form(%{}, as: :v2_form))
      |> assign(:create_screen_form, to_form(%{}, as: :v2_screen))
      |> assign(:create_page_form, to_form(%{}, as: :v2_page))
      |> assign(:ussd_menus, [])
      |> assign(:current_ussd_menu, nil)
      |> assign(:ussd_menu_stack, [])
      |> assign(:ussd_code, nil)
      |> assign(:editing_ussd_menu, nil)
      |> assign(:editing_ussd_option, nil)
      |> assign(:ussd_menu_form, nil)
      |> assign(:ussd_option_form, nil)
      |> assign(:form_session, nil)
      |> assign(:current_form_field, nil)
      |> assign(:form_input_mode, false)
      |> assign(:letter_input_mode, false)
      |> assign(:current_letter_input, "")
      |> assign(:last_key_press, nil)
      |> assign(:key_press_count, 0)
      |> assign(:v2_screens, [])
      |> assign(:v2_pages, [])
      |> assign(:v2_forms, [])
      |> assign(:v2_fields, [])
      |> assign(:v2_all_pages, [])
      |> assign(:v2_all_forms, [])
      |> assign(:v2_selected_screen_id, nil)
      |> assign(:v2_selected_page_id, nil)
      |> assign(:v2_selected_form_id, nil)
      |> assign(:v2_form_submit_to, nil)
      |> assign(:v2_editing_field_id, nil)
      |> assign(:dynamic_routes, DynamicRouteManager.list_routes())

    socket = DataLoaders.load_v2_data(socket)

    if connected?(socket) do
      :timer.send_interval(30_000, :refresh)
    end

    {:ok, load_data(socket) |> DataLoaders.load_ussd_data()}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    case MobileAppFormsService.get_field(id) do
      {:ok, field} ->
        socket
        |> assign(:page_title, "Edit Mobile Form Field")
        |> assign(:field, field)
      {:error, _} ->
        socket
        |> put_flash(:error, "Field not found")
        |> push_navigate(to: ~p"/mobileBanking/mobile-forms")
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Mobile Form Field")
    |> assign(:field, %MobileAppFormsSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Mobile Forms Management")
    |> assign(:field, nil)
  end

  @impl true
  def handle_info(:refresh, socket) do
    {:noreply, load_data(socket) |> DataLoaders.load_ussd_data()}
  end

  def handle_info({ServiceManagerWeb.Backend.MobileFormsLive.FormComponent, {:saved, _field}}, socket) do
    {:noreply,
     socket
     |> put_flash(:info, "Field saved successfully")
     |> push_navigate(to: ~p"/mobileBanking/mobile-forms")
     |> load_data()}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    case MobileAppFormsService.delete_field(id) do
      {:ok, _message} ->
        {:noreply,
         socket
         |> put_flash(:info, "Field deleted successfully")
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to delete field: #{message}")}
    end
  end







  def handle_event("select_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :active_tab, tab)}
  end


  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    {:noreply,
     socket
     |> assign(:search_query, query)
     |> load_data()}
  end

  def handle_event("filter", %{"filter" => filters}, socket) do
    {:noreply,
     socket
     |> assign(:selected_form_filter, Map.get(filters, "form", "all"))
     |> assign(:selected_type, Map.get(filters, "type", "all"))
     |> load_data()}
  end

  def handle_event("sort", %{"sort_by" => sort_by}, socket) do
    {:noreply,
     socket
     |> assign(:sort_by, sort_by)
     |> load_data()}
  end

  def handle_event("toggle_view", %{"view" => view}, socket) do
    {:noreply, assign(socket, :view_mode, view)}
  end

  def handle_event("filter_by_form", %{"form" => form}, socket) do
    form = if form == "", do: nil, else: form

    socket =
      socket
      |> assign(:selected_form, form)
      |> assign(:selected_screen, nil)
      |> assign(:selected_page, nil)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("filter_by_screen", %{"screen" => screen}, socket) do
    screen = if screen == "", do: nil, else: screen

    socket =
      socket
      |> assign(:selected_screen, screen)
      |> assign(:selected_page, nil)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("filter_by_page", %{"page" => page}, socket) do
    page = if page == "", do: nil, else: page

    socket =
      socket
      |> assign(:selected_page, page)
      |> load_filtered_data()

    {:noreply, socket}
  end

  def handle_event("create_form", %{"create_form" => %{"form_name" => form_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_form(form_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Form '#{form_name}' created successfully")
         |> assign(:create_form_form, to_form(%{}, as: :create_form))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create form: #{message}")}
    end
  end

  def handle_event("create_screen", %{"create_screen" => %{"form_name" => form_name, "screen_name" => screen_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_screen(form_name, screen_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Screen '#{screen_name}' created successfully")
         |> assign(:create_screen_form, to_form(%{}, as: :create_screen))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create screen: #{message}")}
    end
  end

  def handle_event("create_page", %{"create_page" => %{"form_name" => form_name, "screen_name" => screen_name, "page_name" => page_name, "version" => version}}, socket) do
    case MobileAppFormsService.create_page(form_name, screen_name, page_name, version) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Page '#{page_name}' created successfully")
         |> assign(:create_page_form, to_form(%{}, as: :create_page))
         |> load_data()}
      {:error, message} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to create page: #{message}")}
    end
  end

  def handle_event("select_hierarchy", params, socket) do
    form = Map.get(params, "form")
    screen = Map.get(params, "screen")
    page = Map.get(params, "page")

    socket =
      socket
      |> assign(:selected_form, form)
      |> assign(:selected_screen, screen)
      |> assign(:selected_page, page)
      |> load_filtered_data()

    {:noreply, socket}
  end

  # USSD Event Handlers
  def handle_event("add_ussd_menu", _params, socket) do
    Helpers.handle_ussd_operation(
      socket,
      fn -> UssdService.create_menu(%{
        title: "New Menu",
        version: "1.0",
        menu_order: length(socket.assigns.ussd_menus)
      }) end,
      "Menu created successfully",
      "Failed to create menu",
      fn s -> DataLoaders.load_ussd_data(s) end
    )
  end

  def handle_event("edit_ussd_menu", %{"index" => index}, socket) do
    index = String.to_integer(index)
    menu = Enum.at(socket.assigns.ussd_menus, index)

    form_data = %{
      "title" => menu.title || "",
      "menu_id" => menu.menu_id,
      "menu_index" => index
    }

    socket =
      socket
      |> assign(:editing_ussd_menu, {index, menu})
      |> assign(:ussd_menu_form, to_form(form_data, as: :ussd_menu))

    {:noreply, socket}
  end

  def handle_event("delete_ussd_menu", %{"index" => index}, socket) do
    index = String.to_integer(index)
    menu = Enum.at(socket.assigns.ussd_menus, index)

    case UssdService.delete_menu(menu.menu_id) do
      {:ok, _message} ->
        socket =
          socket
          |> DataLoaders.load_ussd_data()
          |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
          |> Helpers.generate_ussd_code()
          |> put_flash(:info, "Menu deleted successfully")

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to delete menu: #{message}")}
    end
  end

  def handle_event("add_ussd_option", %{"menu-index" => menu_index}, socket) do
    menu_index = String.to_integer(menu_index)
    menu = Enum.at(socket.assigns.ussd_menus, menu_index)

    case UssdService.create_option(%{
      menu_id: menu.menu_id,
      text: "New Option",
      action: "none",
      option_order: length(menu.options || [])
    }) do
      {:ok, _option} ->
        socket =
          socket
          |> DataLoaders.load_ussd_data()
          |> Helpers.generate_ussd_code()
          |> put_flash(:info, "Option added successfully")

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to add option: #{message}")}
    end
  end

  def handle_event("edit_ussd_option", %{"menu-index" => menu_index, "option-index" => option_index}, socket) do
    menu_index = String.to_integer(menu_index)
    option_index = String.to_integer(option_index)

    menu = Enum.at(socket.assigns.ussd_menus, menu_index)
    option = Enum.at(menu.options || [], option_index)

    form_data = %{
      "text" => option.text || "",
      "action" => option.action || "none",
      "target_menu_id" => option.target_menu_id || "",
      "form_name" => option.form_name || "",
      "option_id" => option.option_id,
      "menu_index" => menu_index,
      "option_index" => option_index
    }

    socket =
      socket
      |> assign(:editing_ussd_option, {menu_index, option_index, option})
      |> assign(:ussd_option_form, to_form(form_data, as: :ussd_option))

    {:noreply, socket}
  end

  def handle_event("delete_ussd_option", %{"menu-index" => menu_index, "option-index" => option_index}, socket) do
    menu_index = String.to_integer(menu_index)
    option_index = String.to_integer(option_index)

    menu = Enum.at(socket.assigns.ussd_menus, menu_index)
    option = Enum.at(menu.options || [], option_index)

    case UssdService.delete_option(option.option_id) do
      {:ok, _message} ->
        socket =
          socket
          |> DataLoaders.load_ussd_data()
          |> Helpers.generate_ussd_code()
          |> put_flash(:info, "Option deleted successfully")

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to delete option: #{message}")}
    end
  end

  def handle_event("generate_ussd_code", _params, socket) do
    socket = Helpers.generate_ussd_code(socket)
    {:noreply, socket}
  end

  # USSD Simulator Events
  def handle_event("simulate_ussd_option", %{"option" => option_index}, socket) do
    option_index = String.to_integer(option_index)
    UssdHandlers.simulate_ussd_option(socket, option_index)
  end



  def handle_event("ussd_go_back", _params, socket) do
    UssdHandlers.ussd_go_back(socket)
  end

  def handle_event("ussd_cancel", _params, socket) do
    UssdHandlers.ussd_cancel(socket)
  end

  def handle_event("reset_ussd_simulation", _params, socket) do
    {:noreply, Helpers.reset_ussd_simulation(socket)}
  end

  def handle_event("test_ussd_flow", _params, socket) do
    {:noreply, Helpers.reset_ussd_simulation(socket)}
  end

  def handle_event("ussd_key_press", %{"key" => key}, socket) do
    UssdHandlers.handle_ussd_key_press(socket, key)
  end

  defp handle_form_key_press("#", socket) do
    # Go back to previous field in form
    if socket.assigns.form_session do
      case UssdService.go_back_form_field(socket.assigns.form_session) do
        {:ok, updated_session} ->
          # Get current field directly instead of next field
          current_field = Enum.at(updated_session.field_definitions, updated_session.current_field_index)

          if current_field do
            socket =
              socket
              |> assign(:form_session, updated_session)
              |> assign(:current_form_field, current_field)
              |> assign(:current_letter_input, "")
              |> assign(:last_key_press, nil)
              |> assign(:key_press_count, 0)
              |> put_flash(:info, "Going back to previous field...")

            {:noreply, socket}
          else
            {:noreply, put_flash(socket, :error, "Cannot navigate back further")}
          end

        {:error, reason} ->
          {:noreply, put_flash(socket, :error, reason)}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_form_key_press("*", socket) do
    # Toggle letter input mode
    socket = assign(socket, :letter_input_mode, !socket.assigns.letter_input_mode)
    {:noreply, socket}
  end

  defp handle_form_key_press("0", socket) do
    if socket.assigns.letter_input_mode do
      handle_letter_input("0", socket)
    else
      # Add "0" to current input
      updated_input = socket.assigns.current_letter_input <> "0"
      socket = assign(socket, :current_letter_input, updated_input)
      {:noreply, socket}
    end
  end

  defp handle_form_key_press(key, socket) when key in ["2", "3", "4", "5", "6", "7", "8", "9"] do
    if socket.assigns.letter_input_mode do
      handle_letter_input(key, socket)
    else
      # Add number to current input
      updated_input = socket.assigns.current_letter_input <> key
      socket = assign(socket, :current_letter_input, updated_input)
      {:noreply, socket}
    end
  end

  defp handle_form_key_press("1", socket) do
    # "1" doesn't have letters, just add to input
    updated_input = socket.assigns.current_letter_input <> "1"
    socket = assign(socket, :current_letter_input, updated_input)
    {:noreply, socket}
  end

  defp handle_form_key_press(_, socket) do
    {:noreply, socket}
  end

  # Letter mapping for multi-tap input
  defp get_letters_for_key(key) do
    case key do
      "2" -> ["a", "b", "c"]
      "3" -> ["d", "e", "f"]
      "4" -> ["g", "h", "i"]
      "5" -> ["j", "k", "l"]
      "6" -> ["m", "n", "o"]
      "7" -> ["p", "q", "r", "s"]
      "8" -> ["t", "u", "v"]
      "9" -> ["w", "x", "y", "z"]
      "0" -> [" "]
      _ -> []
    end
  end

  defp handle_letter_input(key, socket) do
    current_time = System.monotonic_time(:millisecond)
    letters = get_letters_for_key(key)

    if length(letters) > 0 do
      if socket.assigns.last_key_press == key do
        # Same key pressed again - cycle through letters
        count = rem(socket.assigns.key_press_count, length(letters))
        letter = Enum.at(letters, count)

        # Replace the last character if it was from the same key
        current_input = socket.assigns.current_letter_input
        updated_input =
          if String.length(current_input) > 0 && socket.assigns.key_press_count > 0 do
            String.slice(current_input, 0..-2) <> letter
          else
            current_input <> letter
          end

        socket =
          socket
          |> assign(:current_letter_input, updated_input)
          |> assign(:key_press_count, socket.assigns.key_press_count + 1)
          |> assign(:last_key_press, key)

        {:noreply, socket}
      else
        # New key pressed - add first letter
        letter = Enum.at(letters, 0)
        updated_input = socket.assigns.current_letter_input <> letter

        socket =
          socket
          |> assign(:current_letter_input, updated_input)
          |> assign(:key_press_count, 1)
          |> assign(:last_key_press, key)

        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  def handle_event("ussd_input", _params, socket) do
    # Handle text input - placeholder for now
    {:noreply, socket}
  end

  # Form Input Event Handlers
  def handle_event("form_input_submit", params, socket) do
    # Handle both "input" and "value" parameters for different form submission methods
    input = params["input"] || params["value"] || ""

    # Use keypad input if available, otherwise use form input
    actual_input = if String.length(socket.assigns.current_letter_input) > 0 do
      socket.assigns.current_letter_input
    else
      input
    end

    if socket.assigns.form_input_mode && socket.assigns.form_session do
      case UssdService.process_form_input(socket.assigns.form_session, actual_input) do
        {:ok, updated_session} ->
          case UssdService.get_next_form_field(updated_session) do
            {:ok, next_field, _has_more} ->
              # Move to next field
              socket =
                socket
                |> assign(:form_session, updated_session)
                |> assign(:current_form_field, next_field)
                |> assign(:current_letter_input, "")
                |> assign(:last_key_press, nil)
                |> assign(:key_press_count, 0)
                |> put_flash(:info, "Input saved. Next field...")

              {:noreply, socket}

            {:completed, collected_data} ->
              # Form completed
              socket =
                socket
                |> assign(:form_session, nil)
                |> assign(:current_form_field, nil)
                |> assign(:form_input_mode, false)
                |> assign(:current_letter_input, "")
                |> assign(:last_key_press, nil)
                |> assign(:key_press_count, 0)
                |> put_flash(:success, "Form completed! Data collected: #{inspect(collected_data)}")

              {:noreply, socket}

            {:error, reason} ->
              {:noreply, put_flash(socket, :error, "Form error: #{reason}")}
          end

        {:error, reason} ->
          {:noreply, put_flash(socket, :error, "Invalid input: #{reason}")}
      end
    else
      {:noreply, socket}
    end
  end

  # Fallback for form_input_submit with no parameters or different structure
  def handle_event("form_input_submit", params, socket) when not socket.assigns.form_input_mode do
    {:noreply, socket}
  end

  def handle_event("cancel_form_input", _params, socket) do
    socket =
      socket
      |> Helpers.reset_form_input_state()
      |> put_flash(:info, "Form input cancelled")

    {:noreply, socket}
  end

  def handle_event("clear_input", _params, socket) do
    socket =
      socket
      |> assign(:current_letter_input, "")
      |> assign(:last_key_press, nil)
      |> assign(:key_press_count, 0)

    {:noreply, socket}
  end

  # USSD Edit Form Handlers
  def handle_event("save_ussd_menu", %{"ussd_menu" => menu_params}, socket) do
    menu_id = menu_params["menu_id"]
    title = menu_params["title"]

    case UssdService.update_menu(menu_id, %{title: title}) do
      {:ok, _menu} ->
        socket =
          socket
          |> DataLoaders.load_ussd_data()
          |> assign(:editing_ussd_menu, nil)
          |> assign(:ussd_menu_form, nil)
          |> Helpers.generate_ussd_code()
          |> put_flash(:info, "Menu updated successfully")

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to update menu: #{message}")}
    end
  end

  def handle_event("save_ussd_option", %{"ussd_option" => option_params}, socket) do
    option_id = option_params["option_id"]
    text = option_params["text"]
    action = option_params["action"]
    target_menu_id = if option_params["target_menu_id"] != "", do: option_params["target_menu_id"], else: nil
    form_name = if option_params["form_name"] != "", do: option_params["form_name"], else: nil

    update_attrs = %{
      text: text,
      action: action,
      target_menu_id: target_menu_id,
      form_name: form_name
    }

    case UssdService.update_option(option_id, update_attrs) do
      {:ok, _option} ->
        socket =
          socket
          |> DataLoaders.load_ussd_data()
          |> assign(:editing_ussd_option, nil)
          |> assign(:ussd_option_form, nil)
          |> Helpers.generate_ussd_code()
          |> put_flash(:info, "Option updated successfully")

        {:noreply, socket}

      {:error, message} ->
        {:noreply, put_flash(socket, :error, "Failed to update option: #{message}")}
    end
  end

  def handle_event("cancel_ussd_edit", _params, socket) do
    socket =
      socket
      |> assign(:editing_ussd_menu, nil)
      |> assign(:editing_ussd_option, nil)
      |> assign(:ussd_menu_form, nil)
      |> assign(:ussd_option_form, nil)

    {:noreply, socket}
  end

  defp load_data(socket) do
    DataLoaders.load_legacy_data(socket)
  end



  # V2 UI handlers
  def handle_event("v2_select_screen", %{"id" => screen_id}, socket) do
    {:noreply, Helpers.select_v2_screen(socket, screen_id)}
  end

  def handle_event("v2_select_page", %{"id" => page_id}, socket) do
    {:noreply, Helpers.select_v2_page(socket, page_id)}
  end

  def handle_event("v2_select_form", %{"id" => form_id}, socket) do
    {:noreply, Helpers.select_v2_form(socket, form_id)}
  end

  def handle_event("v2_update_form_submit_to", %{"submit_to" => submit_to}, socket) do
    case socket.assigns.v2_selected_form_id do
      nil -> {:noreply, socket}
      form_id ->
        case V2.update_form(form_id, %{submit_to: submit_to}) do
          {:ok, form} ->
            forms = Enum.map(socket.assigns.v2_forms, fn f -> if f.id == form.id, do: form, else: f end)
            {:noreply, socket |> assign(:v2_forms, forms) |> assign(:v2_form_submit_to, form.submit_to)}
          {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to update submit URL")}
        end
    end
  end

  # Create / Rename / Delete / Reorder - Screens
  def handle_event("v2_create_screen", %{"v2_screen" => %{"name" => name}}, socket) do
    # Add some debugging
    require Logger
    Logger.info("Creating screen with name: #{name}")

    # Validate name is not empty
    name = String.trim(name)
    if name == "" do
      {:noreply, put_flash(socket, :error, "Screen name cannot be empty")}
    else
      attrs = %{name: name, version: "1.0", order: length(socket.assigns.v2_screens)}
      Logger.info("Screen attributes: #{inspect(attrs)}")

      Helpers.handle_v2_create(
        socket,
        fn -> V2.create_screen(attrs) end,
        fn -> V2.list_screens(%{}) end,
        :v2_screens,
        Helpers.create_success_message("screen", name),
        Helpers.create_error_message("screen")
      )
    end
  end

  def handle_event("v2_rename_screen_row", %{"id" => id, "name" => name}, socket) do
    Helpers.handle_v2_rename(
      socket,
      fn -> V2.update_screen(id, %{name: name}) end,
      fn s -> Helpers.load_v2_screens(s) end,
      Helpers.rename_error_message("screen")
    )
  end

  def handle_event("v2_delete_screen", %{"id" => id}, socket) do
    case V2.delete_screen(id) do
      {:ok, _} ->
        {:ok, screens} = V2.list_screens(%{})
        {:noreply,
          socket
          |> assign(:v2_screens, screens)
          |> assign(:v2_pages, [])
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_screen_id, nil)
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)}
      {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to delete screen")}
    end
  end

  # Inline row rename for screens in Screens tab
  def handle_event("v2_rename_screen_row", %{"v2_screen_row" => %{"id" => id, "name" => name}}, socket) do
    Helpers.handle_v2_rename(
      socket,
      fn -> V2.update_screen(id, %{name: name}) end,
      fn s -> Helpers.load_v2_screens(s) end,
      Helpers.rename_error_message("screen")
    )
  end

  def handle_event("v2_move_screen_up", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_screens, &V2.update_screen/2, id, :up)}
  def handle_event("v2_move_screen_down", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_screens, &V2.update_screen/2, id, :down)}

  # Pages
  # Inline row rename for pages
  def handle_event("v2_rename_page_row", %{"v2_page_row" => %{"id" => id, "name" => name}}, socket) do
    case V2.update_page(id, %{name: name}) do
      {:ok, _} ->
        {:ok, pages} = V2.list_pages(%{screen_id: socket.assigns.v2_selected_screen_id})
        {:noreply, assign(socket, :v2_pages, pages)}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to rename page")}
    end
  end

  # Inline row rename for forms
  def handle_event("v2_rename_form_row", %{"v2_form_row" => %{"id" => id, "name" => name}}, socket) do
    case V2.update_form(id, %{name: name}) do
      {:ok, _} ->
        {:ok, forms} = V2.list_forms(%{page_id: socket.assigns.v2_selected_page_id})
        {:noreply, assign(socket, :v2_forms, forms)}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to rename form")}
    end
  end

  def handle_event("v2_create_page", %{"v2_page" => %{"name" => name, "screen_id" => screen_id}}, socket) do
    # Add some debugging
    require Logger
    Logger.info("Creating page with name: #{name}, screen_id: #{screen_id}")

    # Validate inputs
    name = String.trim(name)
    if name == "" do
      {:noreply, put_flash(socket, :error, "Page name cannot be empty")}
    else
      case screen_id do
        "" ->
          {:noreply, put_flash(socket, :error, "Please select a screen for this page")}
        _ ->
          # screen_id is a UUID string, don't convert to integer
          attrs = %{name: name, screen_id: screen_id, order: length(socket.assigns.v2_pages)}
          Logger.info("Page attributes: #{inspect(attrs)}")

          case V2.create_page(attrs) do
            {:ok, page} ->
              Logger.info("Page created successfully: #{inspect(page)}")
              {:ok, pages} = V2.list_pages(%{screen_id: screen_id})
              # Also update the all_pages list
              updated_all_pages = [page | socket.assigns.v2_all_pages || []]
              {:noreply,
                socket
                |> assign(:v2_pages, pages)
                |> assign(:v2_all_pages, updated_all_pages)
                |> put_flash(:info, "Page '#{name}' created successfully")
              }
            {:error, changeset} ->
              Logger.error("Failed to create page: #{inspect(changeset)}")
              error_msg = if changeset.errors != [] do
                changeset.errors
                |> Enum.map(fn {field, {msg, _}} -> "#{field}: #{msg}" end)
                |> Enum.join(", ")
              else
                "Failed to create page"
              end
              {:noreply, put_flash(socket, :error, error_msg)}
          end
      end
    end
  end

  def handle_event("v2_rename_page", %{"v2_page" => %{"name" => name}}, socket) do
    case socket.assigns.v2_selected_page_id do
      nil -> {:noreply, socket}
      id ->
        case V2.update_page(id, %{name: name}) do
          {:ok, _p} ->
            {:ok, pages} = V2.list_pages(%{screen_id: socket.assigns.v2_selected_screen_id})
            {:noreply, assign(socket, :v2_pages, pages)}
          {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to rename page")}
        end
    end
  end

  def handle_event("v2_delete_page", %{"id" => id}, socket) do
    case V2.delete_page(id) do
      {:ok, _} ->
        {:ok, pages} = V2.list_pages(%{screen_id: socket.assigns.v2_selected_screen_id})
        {:noreply,
          socket
          |> assign(:v2_pages, pages)
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)}
      {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to delete page")}
    end
  end

  def handle_event("v2_move_page_up", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_pages, &V2.update_page/2, id, :up)}
  def handle_event("v2_move_page_down", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_pages, &V2.update_page/2, id, :down)}

  # Forms
  def handle_event("v2_create_form", params, socket) do
    # Add comprehensive debugging
    require Logger
    Logger.info("v2_create_form event received with params: #{inspect(params)}")

    case params do
      %{"v2_form" => form_params = %{"name" => name, "page_id" => page_id}} ->
        Logger.info("Creating form with name: #{name}, page_id: #{page_id}")

        # Validate inputs
        name = String.trim(name)
        if name == "" do
          {:noreply, put_flash(socket, :error, "Form name cannot be empty")}
        else
          case page_id do
            "" ->
              {:noreply, put_flash(socket, :error, "Please select a page for this form")}
            _ ->
              # page_id is a UUID string, don't convert to integer
              # Extract checkbox values
              active = Map.get(form_params, "active") in ["true", "on", true]
              app_view = Map.get(form_params, "app_view") in ["true", "on", true]
              
              attrs = %{
                name: name, 
                page_id: page_id, 
                order: length(socket.assigns.v2_forms),
                active: active,
                app_view: app_view
              }
              Logger.info("Form attributes: #{inspect(attrs)}")

              case V2.create_form(attrs) do
                {:ok, form} ->
                  Logger.info("Form created successfully: #{inspect(form)}")
                  {:ok, forms} = V2.list_forms(%{page_id: page_id})
                  # Also update the all_forms list
                  updated_all_forms = [form | socket.assigns.v2_all_forms || []]
                  {:noreply,
                    socket
                    |> assign(:v2_forms, forms)
                    |> assign(:v2_all_forms, updated_all_forms)
                    |> put_flash(:info, "Form '#{name}' created successfully")
                  }
                {:error, changeset} ->
                  Logger.error("Failed to create form: #{inspect(changeset)}")
                  error_msg = if changeset.errors != [] do
                    changeset.errors
                    |> Enum.map(fn {field, {msg, _}} -> "#{field}: #{msg}" end)
                    |> Enum.join(", ")
                  else
                    "Failed to create form"
                  end
                  {:noreply, put_flash(socket, :error, error_msg)}
              end
          end
        end
      _ ->
        Logger.error("Invalid form parameters: #{inspect(params)}")
        {:noreply, put_flash(socket, :error, "Invalid form data")}
    end
  end

  def handle_event("v2_rename_form", %{"v2_form" => %{"name" => name}}, socket) do
    case socket.assigns.v2_selected_form_id do
      nil -> {:noreply, socket}
      id ->
        case V2.update_form(id, %{name: name}) do
          {:ok, _f} ->
            {:ok, forms} = V2.list_forms(%{page_id: socket.assigns.v2_selected_page_id})
            {:noreply, assign(socket, :v2_forms, forms)}
          {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to rename form")}
        end
    end
  end

  def handle_event("v2_delete_form", %{"id" => id}, socket) do
    case V2.delete_form(id) do
      {:ok, _} ->
        {:ok, forms} = V2.list_forms(%{page_id: socket.assigns.v2_selected_page_id})
        {:noreply,
          socket
          |> assign(:v2_forms, forms)
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)}
      {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to delete form")}
    end
  end

  def handle_event("v2_move_form_up", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_forms, &V2.update_form/2, id, :up)}
  def handle_event("v2_move_form_down", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_forms, &V2.update_form/2, id, :down)}

  def handle_event("v2_toggle_form_active", params, socket) do
    {id, active_bool} = case params do
      %{"id" => id, "active" => active} -> 
        {id, active in ["true", true]}
      %{"id" => id, "value" => _} -> 
        # If value is present, find the form and toggle its current state
        form = Enum.find(socket.assigns.v2_forms, &(&1.id == id))
        current_active = if form, do: form.active, else: false
        {id, !current_active}
      _ ->
        {nil, false}
    end
    
    if id do
      status_text = if active_bool, do: "enabled", else: "disabled"
      
      case V2.update_form(id, %{active: active_bool}) do
        {:ok, _form} ->
          socket = DataLoaders.load_v2_data(socket)
          {:noreply, put_flash(socket, :info, "Form #{status_text} successfully")}
        {:error, _} -> 
          {:noreply, put_flash(socket, :error, "Failed to update form status")}
      end
    else
      {:noreply, put_flash(socket, :error, "Invalid form ID")}
    end
  end

  def handle_event("v2_toggle_form_app_view", params, socket) do
    {id, app_view_bool} = case params do
      %{"id" => id, "app-view" => app_view} -> 
        {id, app_view in ["true", true]}
      %{"id" => id, "value" => _} -> 
        # If value is present, find the form and toggle its current state
        form = Enum.find(socket.assigns.v2_forms, &(&1.id == id))
        current_app_view = if form, do: Map.get(form, :app_view, true), else: true
        {id, !current_app_view}
      _ ->
        {nil, false}
    end
    
    if id do
      view_text = if app_view_bool, do: "shown in app view", else: "hidden from app view"
      
      case V2.update_form(id, %{app_view: app_view_bool}) do
        {:ok, _form} ->
          socket = DataLoaders.load_v2_data(socket)
          {:noreply, put_flash(socket, :info, "Form #{view_text} successfully")}
        {:error, _} -> 
          {:noreply, put_flash(socket, :error, "Failed to update form app view")}
      end
    else
      {:noreply, put_flash(socket, :error, "Invalid form ID")}
    end
  end

  def handle_event("v2_update_form_settings", params, socket) do
    case params do
      %{"form-id" => form_id, "active" => active, "app_view" => app_view} ->
        active_bool = active in ["true", "on", true]
        app_view_bool = app_view in ["true", "on", true]
        
        attrs = %{active: active_bool, app_view: app_view_bool}
        
        case V2.update_form(form_id, attrs) do
          {:ok, _form} ->
            socket = DataLoaders.load_v2_data(socket)
            {:noreply, put_flash(socket, :info, "Form settings updated successfully")}
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to update form settings")}
        end
      
      %{"form-id" => form_id, "active" => active} ->
        active_bool = active in ["true", "on", true]
        
        case V2.update_form(form_id, %{active: active_bool}) do
          {:ok, _form} ->
            socket = DataLoaders.load_v2_data(socket)
            status_text = if active_bool, do: "enabled", else: "disabled"
            {:noreply, put_flash(socket, :info, "Form #{status_text} successfully")}
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to update form status")}
        end
      
      %{"form-id" => form_id, "app_view" => app_view} ->
        app_view_bool = app_view in ["true", "on", true]
        
        case V2.update_form(form_id, %{app_view: app_view_bool}) do
          {:ok, _form} ->
            socket = DataLoaders.load_v2_data(socket)
            view_text = if app_view_bool, do: "shown in app view", else: "hidden from app view"
            {:noreply, put_flash(socket, :info, "Form #{view_text} successfully")}
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to update form app view")}
        end
      
      _ ->
        {:noreply, socket}
    end
  end

  # Fields
  def handle_event("v2_create_field", params, socket) do
    require Logger
    Logger.info("v2_create_field event received with params: #{inspect(params)}")

    case params do
      %{"v2_field" => field_params} ->
        case socket.assigns.v2_selected_form_id do
          nil ->
            Logger.warning("No form selected for field creation")
            {:noreply, put_flash(socket, :error, "Select a form first")}
          form_id ->
            Logger.info("Creating field for form_id: #{form_id}")
            attrs = %{
              form_id: form_id,
              field_name: field_params["field_name"],
              label: field_params["label"],
              field_type: field_params["field_type"],
              is_required: field_params["is_required"] in ["true", "on", true],
              field_order: String.to_integer(field_params["field_order"] || "0")
            }
            Logger.info("Field attributes: #{inspect(attrs)}")

            case V2.create_field(attrs) do
              {:ok, field} ->
                Logger.info("Field created successfully: #{inspect(field)}")
                {:ok, fields} = V2.get_form_fields(form_id)
                {:noreply,
                  socket
                  |> assign(:v2_fields, fields)
                  |> put_flash(:info, "Field '#{attrs.label}' added successfully")
                }
              {:error, changeset} ->
                Logger.error("Failed to create field: #{inspect(changeset)}")
                error_msg = if changeset.errors != [] do
                  changeset.errors
                  |> Enum.map(fn {field, {msg, _}} -> "#{field}: #{msg}" end)
                  |> Enum.join(", ")
                else
                  "Failed to create field"
                end
                {:noreply, put_flash(socket, :error, error_msg)}
            end
        end
      _ ->
        Logger.error("Invalid field parameters: #{inspect(params)}")
        {:noreply, put_flash(socket, :error, "Invalid field data")}
    end
  end

  def handle_event("v2_delete_field", %{"id" => id}, socket) do
    case V2.delete_field(id) do
      {:ok, _} ->
        {:ok, fields} = V2.get_form_fields(socket.assigns.v2_selected_form_id)
        {:noreply, assign(socket, :v2_fields, fields)}
      {:error, _} -> {:noreply, put_flash(socket, :error, "Failed to delete field")}
    end
  end

  def handle_event("v2_move_field_up", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_fields, &V2.update_field/2, id, :up)}
  def handle_event("v2_move_field_down", %{"id" => id}, socket), do: {:noreply, Helpers.move_order(socket, :v2_fields, &V2.update_field/2, id, :down)}



  defp convert_menu_to_legacy_format(menu) do
    options = Enum.map(menu.options || [], &convert_option_to_legacy_format/1)

    %{
      menu_id: menu.menu_id,
      title: menu.title,
      options: options,
      menu_order: menu.menu_order,
      version: menu.version,
      active: menu.active
    }
  end

  defp convert_option_to_legacy_format(option) do
    %{
      option_id: option.option_id,
      text: option.text,
      action: option.action,
      target_menu_id: option.target_menu_id,
      form_id: option.form_id,
      form_name: option.form_name,
      option_order: option.option_order,
      active: option.active
    }
  end

  defp load_filtered_data(socket) do
    DataLoaders.load_filtered_data(socket)
  end

  # Drag and Drop Reordering Events
  def handle_event("v2_reorder_screens", %{"item_id" => item_id, "new_position" => new_position}, socket) do
    case V2.reorder_screens(item_id, new_position) do
      {:ok, _} ->
        {:ok, screens} = V2.list_screens(%{})
        {:noreply, assign(socket, :v2_screens, screens)}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to reorder screens")}
    end
  end



  def handle_event("v2_reorder_pages", %{"item_id" => item_id, "new_position" => new_position}, socket) do
    case V2.reorder_pages(item_id, new_position, socket.assigns.v2_selected_screen_id) do
      {:ok, _} ->
        {:ok, pages} = V2.list_pages(%{screen_id: socket.assigns.v2_selected_screen_id})
        {:noreply, assign(socket, :v2_pages, pages)}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to reorder pages")}
    end
  end

  def handle_event("v2_reorder_forms", %{"item_id" => item_id, "new_position" => new_position}, socket) do
    case V2.reorder_forms(item_id, new_position, socket.assigns.v2_selected_page_id) do
      {:ok, _} ->
        {:ok, forms} = V2.list_forms(%{page_id: socket.assigns.v2_selected_page_id})
        {:noreply, assign(socket, :v2_forms, forms)}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to reorder forms")}
    end
  end
  # Mobile Preview Navigation Events
  def handle_event("v2_mobile_preview_select_page", %{"id" => page_id}, socket) do
    {:noreply, assign(socket, :v2_selected_page_id, page_id)}
  end

  def handle_event("v2_mobile_preview_select_form", %{"id" => form_id}, socket) do
    {:noreply, Helpers.select_v2_form(socket, form_id)}
  end

  def handle_event("v2_mobile_preview_back_to_pages", _params, socket) do
    {:noreply, assign(socket, :v2_selected_page_id, nil)}
  end

  def handle_event("v2_mobile_preview_back_to_forms", _params, socket) do
    {:noreply,
      socket
      |> assign(:v2_selected_form_id, nil)
      |> assign(:v2_fields, [])
      |> assign(:v2_form_submit_to, nil)
    }
  end

  # Screen filtering in Forms tab
  def handle_event("v2_filter_by_screen", %{"value" => screen_id}, socket) do
    case screen_id do
      "" ->
        # Show all screens - clear selection but keep all_forms visible
        {:noreply,
          socket
          |> assign(:v2_selected_screen_id, nil)
          |> assign(:v2_pages, [])
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)
        }
      _ ->
        # Filter by selected screen and clear downstream selections
        case V2.list_pages(%{screen_id: screen_id}) do
          {:ok, pages} ->
            {:noreply,
              socket
              |> assign(:v2_selected_screen_id, screen_id)
              |> assign(:v2_pages, pages)
              |> assign(:v2_forms, [])
              |> assign(:v2_fields, [])
              |> assign(:v2_selected_page_id, nil)
              |> assign(:v2_selected_form_id, nil)
              |> assign(:v2_form_submit_to, nil)
            }
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to load pages for selected screen")}
        end
    end
  end

  # Page filtering in Forms tab (from form creation dropdown)
  def handle_event("v2_filter_by_page", %{"v2_form" => %{"page_id" => page_id}}, socket) do
    case page_id do
      "" ->
        # Clear page selection - go back to screen level filtering
        {:noreply,
          socket
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)
        }
      _ ->
        # Select page and clear downstream selections
        {:noreply,
          socket
          |> assign(:v2_selected_page_id, page_id)
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)
        }
    end
  end

  # Screen filtering in Pages tab
  def handle_event("v2_filter_by_screen_pages", %{"value" => screen_id}, socket) do
    case screen_id do
      "" ->
        # Show all screens - clear selection
        {:noreply,
          socket
          |> assign(:v2_selected_screen_id, nil)
          |> assign(:v2_pages, [])
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)
        }
      _ ->
        # Filter by selected screen
        case V2.list_pages(%{screen_id: screen_id}) do
          {:ok, pages} ->
            {:noreply,
              socket
              |> assign(:v2_selected_screen_id, screen_id)
              |> assign(:v2_pages, pages)
              |> assign(:v2_forms, [])
              |> assign(:v2_fields, [])
              |> assign(:v2_selected_page_id, nil)
              |> assign(:v2_selected_form_id, nil)
              |> assign(:v2_form_submit_to, nil)
            }
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to load pages for selected screen")}
        end
    end
  end

  # Screen filtering in Pages tab (from page creation dropdown)
  def handle_event("v2_filter_by_screen_pages", %{"v2_page" => %{"screen_id" => screen_id}}, socket) do
    case screen_id do
      "" ->
        # Show all screens - clear selection
        {:noreply,
          socket
          |> assign(:v2_selected_screen_id, nil)
          |> assign(:v2_pages, [])
          |> assign(:v2_forms, [])
          |> assign(:v2_fields, [])
          |> assign(:v2_selected_page_id, nil)
          |> assign(:v2_selected_form_id, nil)
          |> assign(:v2_form_submit_to, nil)
        }
      _ ->
        # Filter by selected screen
        case V2.list_pages(%{screen_id: screen_id}) do
          {:ok, pages} ->
            {:noreply,
              socket
              |> assign(:v2_selected_screen_id, screen_id)
              |> assign(:v2_pages, pages)
              |> assign(:v2_forms, [])
              |> assign(:v2_fields, [])
              |> assign(:v2_selected_page_id, nil)
              |> assign(:v2_selected_form_id, nil)
              |> assign(:v2_form_submit_to, nil)
            }
          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to load pages for selected screen")}
        end
    end
  end

  # Field editing - enter edit mode
  def handle_event("v2_edit_field", %{"id" => field_id}, socket) do
    {:noreply, assign(socket, :v2_editing_field_id, field_id)}
  end

  # Cancel field editing
  def handle_event("v2_cancel_edit_field", _params, socket) do
    {:noreply, assign(socket, :v2_editing_field_id, nil)}
  end

  # Update field
  def handle_event("v2_update_field", %{"id" => field_id, "field" => field_params}, socket) do
    case V2.update_field(field_id, field_params) do
      {:ok, _updated_field} ->
        # Reload fields for the current form
        case socket.assigns.v2_selected_form_id do
          nil ->
            {:noreply,
              socket
              |> assign(:v2_editing_field_id, nil)
              |> put_flash(:info, "Field updated successfully")
            }
          form_id ->
            {:ok, fields} = V2.get_form_fields(form_id)
            {:noreply,
              socket
              |> assign(:v2_fields, fields)
              |> assign(:v2_editing_field_id, nil)
              |> put_flash(:info, "Field updated successfully")
            }
        end
      {:error, _changeset} ->
        {:noreply, put_flash(socket, :error, "Failed to update field")}
    end
  end

  # Handle route selection for form submit_to
  def handle_event("v2_form_route_selected", %{"route_selection" => route_id}, socket) when route_id != "" do
    Logger.info("[v2_form_route_selected] Route selected: #{route_id}")
    Logger.debug("[v2_form_route_selected] Current form_id: #{inspect(socket.assigns.v2_selected_form_id)}")

    case socket.assigns.v2_selected_form_id do
      nil ->
        Logger.warning("[v2_form_route_selected] No form selected, ignoring route selection")
        {:noreply, socket}
      form_id ->
        Logger.info("[v2_form_route_selected] Updating form #{form_id} with route #{route_id}")
        case DynamicRouteManager.get_route(route_id) do
          nil ->
            Logger.error("[v2_form_route_selected] Route not found: #{route_id}")
            {:noreply, socket}
          route ->
            submit_to = "/dynamic#{route.path}"
            Logger.info("[v2_form_route_selected] Setting submit_to: #{submit_to} for form #{form_id}")

            case V2.update_form(form_id, %{submit_to: submit_to, route_id: route_id}) do
              {:ok, form} ->
                Logger.info("[v2_form_route_selected] Successfully updated form #{form_id} with route #{route_id}")
                # Update the forms list and form submit_to
                forms = Enum.map(socket.assigns.v2_forms, fn f ->
                  if f.id == form.id, do: form, else: f
                end)
                {:noreply,
                  socket
                  |> assign(:v2_forms, forms)
                  |> assign(:v2_form_submit_to, form.submit_to)
                  |> put_flash(:info, "Form submit URL updated with route path")
                }
              {:error, changeset} ->
                Logger.error("[v2_form_route_selected] Failed to update form: #{inspect(changeset)}")
                {:noreply, put_flash(socket, :error, "Failed to update form submit URL")}
            end
        end
    end
  end

  def handle_event("v2_form_route_selected", %{"route_selection" => ""}, socket) do
    Logger.info("[v2_form_route_selected] Clearing route selection")
    Logger.debug("[v2_form_route_selected] Current form_id: #{inspect(socket.assigns.v2_selected_form_id)}")

    # Clear route selection
    case socket.assigns.v2_selected_form_id do
      nil ->
        Logger.warning("[v2_form_route_selected] No form selected, cannot clear route")
        {:noreply, socket}
      form_id ->
        Logger.info("[v2_form_route_selected] Clearing route for form #{form_id}")
        case V2.update_form(form_id, %{submit_to: nil, route_id: nil}) do
          {:ok, form} ->
            Logger.info("[v2_form_route_selected] Successfully cleared route for form #{form_id}")
            # Update the forms list and form submit_to
            forms = Enum.map(socket.assigns.v2_forms, fn f ->
              if f.id == form.id, do: form, else: f
            end)
            {:noreply,
              socket
              |> assign(:v2_forms, forms)
              |> assign(:v2_form_submit_to, form.submit_to)
              |> put_flash(:info, "Form submit URL cleared")
            }
          {:error, changeset} ->
            Logger.error("[v2_form_route_selected] Failed to clear form route: #{inspect(changeset)}")
            {:noreply, put_flash(socket, :error, "Failed to clear form submit URL")}
        end
    end
  end

  def handle_event("v2_form_route_selected", params, socket) do
    Logger.warning("[v2_form_route_selected] Unhandled params: #{inspect(params)}")
    {:noreply, socket}
  end

  # Field reordering
  def handle_event("v2_reorder_fields", %{"item_id" => item_id, "new_position" => new_position}, socket) do
    case socket.assigns.v2_selected_form_id do
      nil ->
        {:noreply, put_flash(socket, :error, "No form selected")}
      form_id ->
        # Get current fields to find the target field at new_position
        current_fields = socket.assigns.v2_fields || []
        sorted_fields = Enum.sort_by(current_fields, & &1.field_order)

        # Find the target field at the new position
        target_field = Enum.at(sorted_fields, new_position)

        case target_field do
          nil ->
            {:noreply, put_flash(socket, :error, "Invalid target position")}
          target ->
            case V2.reorder_fields(form_id, item_id, target.id) do
              {:ok, _} ->
                # Reload fields to get updated order
                {:ok, fields} = V2.get_form_fields(form_id)
                {:noreply,
                  socket
                  |> assign(:v2_fields, fields)
                  |> put_flash(:info, "Fields reordered successfully")
                }
              {:error, _} ->
                {:noreply, put_flash(socket, :error, "Failed to reorder fields")}
            end
        end
    end
  end
end
