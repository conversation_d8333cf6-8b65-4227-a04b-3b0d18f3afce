defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.ManagementRows do
  use Phoenix.Component

  ## Management Row Components

  # Basic management row with arrow buttons
  attr :item, :map, required: true
  attr :selected_id, :any
  attr :select_event, :string, required: true
  attr :move_up_event, :string, required: true
  attr :move_down_event, :string, required: true
  attr :delete_event, :string, required: true
  attr :rename_event, :string, required: true
  attr :rename_form_name, :string, required: true
  def management_row(assigns) do
    ~H"""
    <div class={["p-2 flex items-center justify-between", if(@selected_id == @item.id, do: "bg-indigo-50", else: "")]}> 
      <button class="text-left flex-1" phx-click={@select_event} phx-value-id={@item.id}><%= @item.name %></button>
      <div class="space-x-1">
        <button title="Up" phx-click={@move_up_event} phx-value-id={@item.id}>↑</button>
        <button title="Down" phx-click={@move_down_event} phx-value-id={@item.id}>↓</button>
        <button title="Delete" phx-click={@delete_event} phx-value-id={@item.id} data-confirm="Delete this item?">✕</button>
      </div>
    </div>
    """
  end

  # Draggable management row component
  attr :item, :map, required: true
  attr :selected_id, :any
  attr :select_event, :string, required: true
  attr :delete_event, :string, required: true
  attr :rename_event, :string, required: true
  attr :rename_form_name, :string, required: true
  attr :reorder_event, :string, required: true
  attr :item_type, :string, required: true
  def draggable_management_row(assigns) do
    ~H"""
    <div
      id={"draggable-#{@item_type}-#{@item.id}"}
      class={[
        "p-2 border border-gray-200 rounded-lg mb-2 bg-white transition-all duration-200 cursor-move",
        if(@selected_id == @item.id, do: "ring-2 ring-indigo-500 bg-indigo-50", else: "hover:shadow-md")
      ]}
      draggable="true"
      data-item-id={@item.id}
      data-item-type={@item_type}
      data-reorder-event={@reorder_event}
      phx-hook="DragDrop"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2 flex-1">
          <!-- Drag Handle -->
          <svg class="w-4 h-4 text-gray-400 cursor-move" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
          </svg>
          
          <button 
            class="text-left flex-1 font-medium text-sm" 
            phx-click={@select_event} 
            phx-value-id={@item.id}
          >
            <%= @item.name %>
          </button>
        </div>
        
        <div class="flex items-center space-x-1">
          <button 
            title="Delete" 
            phx-click={@delete_event} 
            phx-value-id={@item.id} 
            data-confirm="Delete this item?" 
            class="text-red-500 hover:text-red-700 p-1"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
    """
  end
end
