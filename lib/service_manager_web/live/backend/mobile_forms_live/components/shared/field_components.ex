defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.FieldComponents do
  use Phoenix.Component

  ## Field Components

  # Draggable field row component for fields
  attr :field, :map, required: true
  attr :editing_field_id, :any
  def draggable_field_row(assigns) do
    ~H"""
    <div
      id={"draggable-field-#{@field.id}"}
      class="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow cursor-move"
      draggable="true"
      data-item-id={@field.id}
      data-item-type="field"
      data-reorder-event="v2_reorder_fields"
      phx-hook="DragDrop"
    >
      <!-- Field Header -->
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center space-x-2">
          <!-- Drag Handle -->
          <svg class="w-4 h-4 text-gray-400 cursor-move" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
          </svg>
          <span class="text-xs font-medium text-gray-600">#<%= @field.field_order %></span>
          <span class="font-medium text-sm"><%= @field.label %></span>
          <%= if @field.is_required do %>
            <span class="text-red-500 text-xs">*</span>
          <% end %>
        </div>
        <div class="flex items-center space-x-1">
          <button 
            phx-click="v2_edit_field" 
            phx-value-id={@field.id} 
            class="text-blue-500 hover:text-blue-700 p-1"
            title="Edit field"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
          </button>
          <button 
            phx-click="v2_delete_field" 
            phx-value-id={@field.id} 
            data-confirm="Delete this field?" 
            class="text-red-500 hover:text-red-700 p-1"
            title="Delete field"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Field Details -->
      <div class="flex items-center space-x-3 text-xs text-gray-600">
        <span class="flex items-center">
          <span class={["w-2 h-2 rounded-full mr-1", field_type_color(@field.field_type)]}></span>
          <%= field_type_display(@field.field_type) %>
        </span>
        <span class="text-gray-400">•</span>
        <span><%= @field.field_name %></span>
        <%= if !@field.active do %>
          <span class="text-gray-400">•</span>
          <span class="text-orange-600">Inactive</span>
        <% end %>
      </div>
      
      <!-- Additional Field Info -->
      <%= if @field.field_type in ["select", "multiselect"] && @field.options && @field.options != "" do %>
        <div class="mt-2 pt-2 border-t border-gray-100 space-y-1">
          <div class="text-xs text-gray-500">
            <span class="font-medium">Options:</span> <%= String.replace(@field.options, "\n", ", ") %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  ## Helper functions for field display
  defp field_type_color(field_type) do
    case field_type do
      type when type in ["string", "textarea"] -> "bg-blue-500"
      type when type in ["number", "integer"] -> "bg-green-500"
      type when type in ["email", "password", "phone"] -> "bg-purple-500"
      type when type in ["date", "datetime"] -> "bg-orange-500"
      type when type in ["select", "multiselect"] -> "bg-yellow-500"
      "boolean" -> "bg-indigo-500"
      _ -> "bg-gray-500"
    end
  end

  defp field_type_display(field_type) do
    case field_type do
      "string" -> "Text Input"
      "textarea" -> "Text Area"
      "email" -> "Email"
      "password" -> "Password"
      "phone" -> "Phone"
      "number" -> "Number"
      "integer" -> "Integer"
      "date" -> "Date"
      "datetime" -> "Date & Time"
      "select" -> "Dropdown"
      "multiselect" -> "Multi-Select"
      "boolean" -> "Checkbox"
      _ -> String.capitalize(field_type)
    end
  end
end
