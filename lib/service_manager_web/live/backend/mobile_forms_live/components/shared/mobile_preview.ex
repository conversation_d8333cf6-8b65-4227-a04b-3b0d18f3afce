defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview do
  use Phoenix.Component

  ## Mobile Preview Component

  attr :level, :string, required: true
  attr :items, :list, default: []
  attr :active_id, :any, default: nil
  attr :all_screens, :list, default: []
  attr :selected_screen_id, :any, default: nil
  attr :all_pages, :list, default: []
  attr :selected_page_id, :any, default: nil
  attr :all_fields, :list, default: []
  attr :tab_context, :string, default: nil
  def mobile_preview(assigns) do
    ~H"""
    <!-- Flutter-like Mobile Preview Styles -->
    <style>
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .flutter-scroll {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
      }
    </style>

    <div class="bg-gray-100 rounded-lg p-4">
      <div class="text-xs text-gray-600 mb-4 flex items-center justify-center">
        <svg class="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
        </svg>
        <span class="font-medium">Mobile Preview</span>
        <span class="mx-2 text-gray-400">•</span>
        <span class="text-indigo-600 font-medium"><%= String.capitalize(@level) %></span>
      </div>

      <!-- Realistic Mobile Phone Frame -->
      <div class="relative mx-auto" style="width: 320px;">
        <!-- Phone Shadow -->
        <div class="absolute inset-0 bg-black/20 rounded-[2.5rem] blur-xl transform translate-y-2"></div>

        <!-- Phone Body -->
        <div class="relative bg-gradient-to-b from-gray-800 to-black rounded-[2.5rem] p-2" style="width: 320px; height: 640px;">
          <!-- Screen -->
          <div class="bg-black rounded-[2rem] h-full overflow-hidden relative">
            <!-- Screen Content -->
            <div class="bg-white h-full flex flex-col relative">
              <!-- Dynamic Island / Notch -->
              <div class="absolute top-2 left-1/2 transform -translate-x-1/2 bg-black rounded-full w-24 h-6 z-10"></div>

              <!-- Status Bar -->
              <div class="flex justify-between items-center px-6 pt-8 pb-2 text-sm font-medium bg-white relative z-0">
                <div class="flex items-center space-x-1">
                  <span class="text-black">9:41</span>
                </div>
                <div class="flex items-center space-x-1">
                  <svg class="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                  </svg>
                  <svg class="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  <div class="flex items-center">
                    <div class="w-6 h-3 border border-black rounded-sm relative">
                      <div class="absolute inset-0.5 bg-green-500 rounded-sm"></div>
                      <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-black rounded-r-sm"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- App Header with Gradient -->
              <div class="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white px-6 py-4 shadow-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <h3 class="font-bold text-lg">FDH Mobile</h3>
                    <p class="text-blue-100 text-xs opacity-90">Banking Made Simple</p>
                  </div>
                  <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                </div>
              </div>
          
          <!-- Scrollable Content Area (Flutter-like) -->
          <div class="flex-1 overflow-y-auto scrollbar-hide" style="scroll-behavior: smooth;">
            <%= case @level do %>
              <% "screens" -> %>
                <.screens_view items={@items} active_id={@active_id} />
              <% "pages" -> %>
                <.pages_view
                  items={@items}
                  active_id={@active_id}
                  all_screens={@all_screens}
                  selected_screen_id={@selected_screen_id}
                  level={@level}
                  tab_context={@tab_context || @level}
                />
              <% "forms" -> %>
                <.forms_view
                  items={@items}
                  active_id={@active_id}
                  all_fields={@all_fields}
                  all_screens={@all_screens}
                  all_pages={@all_pages}
                  selected_screen_id={@selected_screen_id}
                  selected_page_id={@selected_page_id}
                  level={@level}
                />
              <% "fields" -> %>
                <.fields_view items={@all_fields} />
              <% _ -> %>
                <div class="p-4 text-center text-gray-400 text-xs">
                  Unknown view level: <%= @level %>
                </div>
            <% end %>
          </div>
          
          <!-- Modern Bottom Navigation -->
          <%= if @level in ["screens", "pages"] && (@all_screens || []) != [] do %>
            <div class="bg-white border-t border-gray-200 px-4 py-2 safe-area-bottom">
              <div class="flex justify-around items-center">
                <%= for screen <- Enum.take(@all_screens || @items, 4) do %>
                  <button
                    class={[
                      "flex flex-col items-center p-3 rounded-xl transition-all duration-200 min-w-0 flex-1 mx-1",
                      if(@selected_screen_id == screen.id,
                        do: "bg-blue-50 text-blue-600 transform scale-105",
                        else: "text-gray-500 hover:text-gray-700 hover:bg-gray-50")
                    ]}
                    phx-click="v2_select_screen"
                    phx-value-id={screen.id}
                  >
                    <div class={[
                      "w-7 h-7 rounded-full mb-1 flex items-center justify-center transition-all duration-200",
                      if(@selected_screen_id == screen.id,
                        do: "bg-blue-600 shadow-lg shadow-blue-600/30",
                        else: "bg-gray-300")
                    ]}>
                      <%= case String.downcase(screen.name) do %>
                        <% name when name in ["pay bills", "bills", "payments"] -> %>
                          <svg class={["w-4 h-4", if(@selected_screen_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                          </svg>
                        <% name when name in ["transfers", "transfer", "send"] -> %>
                          <svg class={["w-4 h-4", if(@selected_screen_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                          </svg>
                        <% name when name in ["entertainment", "airtime", "data"] -> %>
                          <svg class={["w-4 h-4", if(@selected_screen_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3"></path>
                          </svg>
                        <% name when name in ["loans", "loan", "credit"] -> %>
                          <svg class={["w-4 h-4", if(@selected_screen_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                          </svg>
                        <% _ -> %>
                          <svg class={["w-4 h-4", if(@selected_screen_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                          </svg>
                      <% end %>
                    </div>
                    <span class={[
                      "text-xs font-medium truncate max-w-full transition-all duration-200",
                      if(@selected_screen_id == screen.id, do: "text-blue-600", else: "text-gray-500")
                    ]}>
                      <%= String.slice(screen.name, 0, 8) %>
                    </span>
                    <%= if @selected_screen_id == screen.id do %>
                      <div class="w-1 h-1 bg-blue-600 rounded-full mt-1"></div>
                    <% end %>
                  </button>
                <% end %>
              </div>
            </div>
          <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Screens view component
  attr :items, :list, default: []
  attr :active_id, :any
  defp screens_view(assigns) do
    ~H"""
    <!-- Flutter-like Scrollable ListView -->
    <div class="h-full overflow-y-auto scrollbar-hide bg-gray-50" style="scroll-behavior: smooth;">
      <div class="px-4 py-6 space-y-4">
        <!-- Flutter-style AppBar Content -->
        <div class="mb-2">
          <h1 class="text-2xl font-bold text-gray-900 mb-1">Welcome back!</h1>
          <p class="text-gray-600 text-base">What would you like to do today?</p>
        </div>

        <!-- Flutter Card: Balance Card -->
        <div class="bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-3xl p-6 mb-4 text-white shadow-2xl shadow-blue-500/25 transform hover:scale-[1.02] transition-all duration-300">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <p class="text-blue-100 text-sm mb-2 opacity-90">Available Balance</p>
              <p class="text-3xl font-bold mb-1">MWK 125,450.00</p>
              <p class="text-blue-100 text-sm opacity-75">Account: ****1234</p>
            </div>
            <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Flutter Card: Quick Actions -->
        <div class="bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 mb-4">
          <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
          <div class="grid grid-cols-4 gap-4">
            <button class="flex flex-col items-center p-3 rounded-2xl bg-blue-50 hover:bg-blue-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-blue-600 rounded-2xl flex items-center justify-center mb-2">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-gray-700">Transfer</span>
            </button>
            <button class="flex flex-col items-center p-3 rounded-2xl bg-green-50 hover:bg-green-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-green-600 rounded-2xl flex items-center justify-center mb-2">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-gray-700">Pay Bills</span>
            </button>
            <button class="flex flex-col items-center p-3 rounded-2xl bg-purple-50 hover:bg-purple-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-purple-600 rounded-2xl flex items-center justify-center mb-2">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-gray-700">Loans</span>
            </button>
            <button class="flex flex-col items-center p-3 rounded-2xl bg-orange-50 hover:bg-orange-100 transition-colors duration-200">
              <div class="w-12 h-12 bg-orange-600 rounded-2xl flex items-center justify-center mb-2">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <span class="text-xs font-medium text-gray-700">Reports</span>
            </button>
          </div>
        </div>

        <!-- Flutter ListView: Services -->
        <%= if @items == [] do %>
          <!-- Flutter Card: Empty State -->
          <div class="bg-white rounded-3xl p-8 shadow-lg shadow-gray-200/50 text-center">
            <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">No Services Available</h3>
            <p class="text-gray-500 text-base">Services will appear here once they're configured</p>
          </div>
        <% else %>
          <!-- Flutter Card: Services Header -->
          <div class="bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 mb-4">
            <h3 class="text-xl font-bold text-gray-900 mb-4">All Services</h3>
            <p class="text-gray-600 text-sm">Choose a service to get started</p>
          </div>

          <!-- Flutter ListView: Service Cards -->
          <div class="space-y-3">
            <%= for screen <- @items do %>
              <button
                class={[
                  "w-full bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 border-2 transition-all duration-300 text-left hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] flex items-center space-x-4",
                  if(@active_id == screen.id,
                    do: "border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-blue-500/20 transform scale-[1.02]",
                    else: "border-transparent hover:border-gray-200")
                ]}
                phx-click="v2_select_screen"
                phx-value-id={screen.id}
              >
                <!-- Flutter-style Leading Icon -->
                <div class={[
                  "w-16 h-16 rounded-3xl flex items-center justify-center flex-shrink-0 transition-all duration-300",
                  if(@active_id == screen.id, do: "bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg shadow-blue-500/30", else: "bg-gray-100")
                ]}>
                  <%= case String.downcase(screen.name) do %>
                    <% name when name in ["pay bills", "bills", "payments"] -> %>
                      <svg class={["w-8 h-8", if(@active_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    <% name when name in ["transfers", "transfer", "send"] -> %>
                      <svg class={["w-8 h-8", if(@active_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                      </svg>
                    <% name when name in ["entertainment", "airtime", "data"] -> %>
                      <svg class={["w-8 h-8", if(@active_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                      </svg>
                    <% name when name in ["loans", "loan", "credit"] -> %>
                      <svg class={["w-8 h-8", if(@active_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                      </svg>
                    <% _ -> %>
                      <svg class={["w-8 h-8", if(@active_id == screen.id, do: "text-white", else: "text-gray-600")]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                      </svg>
                  <% end %>
                </div>

                <!-- Flutter-style Content -->
                <div class="flex-1 min-w-0">
                  <h3 class={[
                    "text-xl font-bold mb-2 transition-colors duration-300",
                    if(@active_id == screen.id, do: "text-blue-900", else: "text-gray-900")
                  ]}>
                    <%= screen.name %>
                  </h3>
                  <p class={[
                    "text-base transition-colors duration-300",
                    if(@active_id == screen.id, do: "text-blue-600", else: "text-gray-500")
                  ]}>
                    Tap to explore services
                  </p>
                </div>

                <!-- Flutter-style Trailing Icon -->
                <div class={[
                  "flex-shrink-0 transition-all duration-300",
                  if(@active_id == screen.id, do: "text-blue-600 transform translate-x-1", else: "text-gray-400")
                ]}>
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </button>
            <% end %>
          </div>

          <!-- Flutter-style Bottom Padding for Scroll -->
          <div class="h-6"></div>
        <% end %>
      </div>
    </div>
    """
  end

  # Pages view component
  attr :items, :list, default: []
  attr :active_id, :any
  attr :all_screens, :list, default: []
  attr :selected_screen_id, :any
  attr :level, :string, default: "pages"
  attr :tab_context, :string, default: "pages"
  defp pages_view(assigns) do
    ~H"""
    <!-- Flutter-like Scrollable Pages ListView -->
    <div class="h-full overflow-y-auto scrollbar-hide bg-gray-50" style="scroll-behavior: smooth;">
      <div class="px-4 py-6 space-y-4">
        <%= if false do %>
          <!-- Back button removed - use bottom navigation to switch between screens -->
        <!-- Pages Header with Back Button (only when on pages tab with screen selected) -->
          <div class="flex items-center mb-3">
            <button
              class="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors mr-3"
              phx-click="v2_select_screen"
              phx-value-id=""
              title="Back to screens"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            <span class="text-xs">Back</span>
          </button>
          <div class="flex-1">
            <h4 class="font-medium text-sm">
              Pages
              <%= case Enum.find(@all_screens, &(&1.id == @selected_screen_id)) do %>
                <% %{name: screen_name} -> %>
                  <span class="text-xs text-gray-500 font-normal">in <%= screen_name %></span>
                <% _ -> %>
              <% end %>
            </h4>
          </div>
        </div>
      <% else %>
        <!-- Flutter-style Header -->
        <div class="mb-2">
          <h1 class="text-2xl font-bold text-gray-900 mb-1">Pages</h1>
          <p class="text-gray-600 text-base">Select a page to continue</p>
        </div>
      <% end %>

      <!-- Flutter ListView: Pages -->
      <%= if @items == [] do %>
        <!-- Flutter Card: Empty State -->
        <div class="bg-white rounded-3xl p-8 shadow-lg shadow-gray-200/50 text-center">
          <%= if @selected_screen_id do %>
            <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">No Pages Available</h3>
            <p class="text-gray-500 text-base">This service doesn't have any pages configured yet</p>
          <% else %>
            <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">Select a Service</h3>
            <p class="text-gray-500 text-base">Choose a service from the bottom navigation to view its pages</p>
          <% end %>
        </div>
      <% else %>
        <!-- Flutter ListView: Page Cards -->
        <div class="space-y-3">
          <%= for page <- @items do %>
            <button
              class={[
                "w-full bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 border-2 transition-all duration-300 text-left hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] flex items-center space-x-4",
                if(@active_id == page.id,
                  do: "border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-blue-500/20 transform scale-[1.02]",
                  else: "border-transparent hover:border-gray-200")
              ]}
              phx-click="v2_select_page"
              phx-value-id={page.id}
            >
              <!-- Flutter-style Leading Icon -->
              <div class={[
                "w-16 h-16 rounded-3xl flex-shrink-0 flex items-center justify-center transition-all duration-300",
                if(@active_id == page.id, do: "bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg shadow-blue-500/30", else: "bg-gray-100")
              ]}>
                <svg class={[
                  "w-8 h-8",
                  if(@active_id == page.id, do: "text-white", else: "text-gray-600")
                ]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>

              <!-- Flutter-style Content -->
              <div class="flex-1 min-w-0">
                <h3 class={[
                  "text-xl font-bold mb-2 transition-colors duration-300",
                  if(@active_id == page.id, do: "text-blue-900", else: "text-gray-900")
                ]}>
                  <%= page.name %>
                </h3>
                <p class={[
                  "text-base transition-colors duration-300",
                  if(@active_id == page.id, do: "text-blue-600", else: "text-gray-500")
                ]}>
                  Tap to view forms
                </p>
              </div>

              <!-- Flutter-style Trailing Icon -->
              <div class={[
                "flex-shrink-0 transition-all duration-300",
                if(@active_id == page.id, do: "text-blue-600 transform translate-x-1", else: "text-gray-400")
              ]}>
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </button>
          <% end %>
        </div>

        <!-- Flutter-style Bottom Padding for Scroll -->
        <div class="h-6"></div>
      <% end %>
      </div>
    </div>
    """
  end

  # Forms view component
  attr :items, :list, default: []
  attr :active_id, :any
  attr :all_fields, :list, default: []
  attr :all_screens, :list, default: []
  attr :all_pages, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :level, :string, default: "forms"
  defp forms_view(assigns) do
    ~H"""
    <%= if @active_id do %>
      <!-- Individual Form View (Mobile-like) -->
      <% selected_form = Enum.find(@items, &(&1.id == @active_id)) %>
      <%= if selected_form do %>
        <div class="flex flex-col h-full">
          <!-- Modern Mobile Header with Back Button -->
          <div class="bg-white border-b border-gray-100 px-6 py-4">
            <div class="flex items-center">
              <button
                class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors mr-4"
                phx-click="v2_select_form"
                phx-value-id=""
                title="Back to forms list"
              >
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <div class="flex-1">
                <h1 class="text-xl font-bold text-gray-900"><%= selected_form.name %></h1>
                <p class="text-sm text-gray-500 mt-1">
                  <%= case Enum.find(@all_pages, &(&1.id == @selected_page_id)) do %>
                    <% %{name: page_name} -> %>
                      <%= case Enum.find(@all_screens, &(&1.id == @selected_screen_id)) do %>
                        <% %{name: screen_name} -> %>
                          <%= screen_name %> › <%= page_name %>
                        <% _ -> %>
                          <%= page_name %>
                      <% end %>
                    <% _ -> %>
                      Complete the form below
                  <% end %>
                </p>
              </div>
            </div>
          </div>

          <!-- Modern Form Content -->
          <div class="flex-1 overflow-y-auto bg-gray-50">
            <% form_fields = Enum.filter(@all_fields, &(&1.form_id == selected_form.id)) |> Enum.sort_by(& &1.field_order) %>
            <%= if form_fields != [] do %>
              <div class="px-6 py-6 space-y-6">
                <%= for field <- form_fields do %>
                  <div class="space-y-2">
                    <label class="block text-sm font-semibold text-gray-900">
                      <%= field.label %>
                      <%= if field.is_required do %>
                        <span class="text-red-500 ml-1">*</span>
                      <% end %>
                    </label>
                    <.field_input field={field} />
                  </div>
                <% end %>
              </div>

              <!-- Modern Submit Button (Fixed at bottom) -->
              <div class="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4 mt-8">
                <button class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-2xl text-base font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl active:scale-[0.98] flex items-center justify-center space-x-2">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                  </svg>
                  <span>Submit <%= selected_form.name %></span>
                </button>
              </div>
            <% else %>
              <div class="text-center py-16 px-6">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Form is Empty</h3>
                <p class="text-gray-500 text-base">Add fields to this form to see them here and create a complete user experience.</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    <% else %>
      <!-- Flutter-like Scrollable Forms ListView -->
      <div class="h-full overflow-y-auto scrollbar-hide bg-gray-50 flutter-scroll">
        <div class="px-4 py-6 space-y-4">
          <!-- Navigation Header with Back Button (only when at forms level, not when showing pages) -->
          <%= if @selected_page_id && @level == "forms" do %>
            <div class="flex items-center mb-2">
              <button
                class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors mr-4"
                phx-click="v2_select_page"
                phx-value-id=""
                title="Back to pages"
              >
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900 mb-1">Forms</h1>
                <p class="text-gray-600 text-base">
                  <%= case {Enum.find(@all_screens, &(&1.id == @selected_screen_id)), Enum.find(@all_pages, &(&1.id == @selected_page_id))} do %>
                    <% {%{name: screen_name}, %{name: page_name}} -> %>
                      <%= screen_name %> › <%= page_name %>
                    <% {%{name: screen_name}, _} -> %>
                      <%= screen_name %>
                    <% {_, %{name: page_name}} -> %>
                      <%= page_name %>
                    <% _ -> %>
                      Select forms to continue
                  <% end %>
                </p>
              </div>
            </div>
          <% else %>
            <!-- Flutter-style Header (when no page selected) -->
            <div class="mb-2">
              <h1 class="text-2xl font-bold text-gray-900 mb-1">Forms</h1>
              <p class="text-gray-600 text-base">
                <%= case Enum.find(@all_screens, &(&1.id == @selected_screen_id)) do %>
                  <% %{name: screen_name} -> %>
                    <%= screen_name %>
                  <% _ -> %>
                    Select forms to continue
                <% end %>
              </p>
            </div>
          <% end %>

          <!-- Flutter ListView: Forms -->
          <%= if @items == [] do %>
            <!-- Flutter Card: Empty State -->
            <div class="bg-white rounded-3xl p-8 shadow-lg shadow-gray-200/50 text-center">
              <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2">No Forms Available</h3>
              <p class="text-gray-500 text-base">This page doesn't have any forms configured yet</p>
            </div>
          <% else %>
            <!-- Flutter ListView: Form Cards -->
            <div class="space-y-3">
            <%= for form <- @items do %>
              <button
                class="w-full bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 border-2 border-transparent transition-all duration-300 text-left hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] flex items-center space-x-4 hover:border-gray-200"
                phx-click="v2_select_form"
                phx-value-id={form.id}
              >
                <!-- Flutter-style Leading Icon -->
                <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex-shrink-0 flex items-center justify-center shadow-lg shadow-indigo-500/30">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>

                <!-- Flutter-style Content -->
                <div class="flex-1 min-w-0">
                  <h3 class="text-xl font-bold text-gray-900 mb-2">
                    <%= form.name %>
                  </h3>
                  <div class="flex items-center text-base text-gray-500">
                    <% field_count = Enum.count(Enum.filter(@all_fields, &(&1.form_id == form.id))) %>
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <%= field_count %> field<%= if field_count != 1, do: "s" %>
                    <span class="mx-2">•</span>
                    <span class="text-indigo-600 font-medium">Tap to open</span>
                  </div>
                </div>

                <!-- Flutter-style Trailing Icon -->
                <div class="flex-shrink-0 text-gray-400 hover:text-indigo-600 transition-colors duration-300">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </button>
            <% end %>
            </div>

            <!-- Flutter-style Bottom Padding for Scroll -->
            <div class="h-6"></div>
          <% end %>
        </div>
      </div>
    <% end %>
    """
  end

  # Fields view component
  attr :items, :list, default: []
  defp fields_view(assigns) do
    ~H"""
    <div class="p-4">
      <h4 class="font-medium text-sm mb-3">Form Fields</h4>
      <%= if @items == [] do %>
        <div class="text-center text-gray-400 text-xs py-8">
          No fields added yet
        </div>
      <% else %>
        <div class="space-y-3">
          <%= for field <- Enum.sort_by(@items, & &1.field_order) do %>
            <div class="space-y-1">
              <label class="block text-xs font-medium text-gray-700">
                <%= field.label %>
                <%= if field.is_required do %>
                  <span class="text-red-500">*</span>
                <% end %>
              </label>
              <.field_input field={field} />
            </div>
          <% end %>
          <button class="w-full mt-4 bg-indigo-600 text-white py-2 px-4 rounded text-xs font-medium">
            Submit
          </button>
        </div>
      <% end %>
    </div>
    """
  end

  # Field input component
  attr :field, :map, required: true
  defp field_input(assigns) do
    ~H"""
    <%= case @field.field_type do %>
      <% "string" -> %>
        <input type="text" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder={"Enter " <> String.downcase(@field.label)} />
      <% "email" -> %>
        <input type="email" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder="Enter email address" />
      <% "password" -> %>
        <div class="relative">
          <input type="password" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200 pr-12" placeholder="Enter password" />
          <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </button>
        </div>
      <% "phone" -> %>
        <input type="tel" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder="Enter phone number" />
      <% "number" -> %>
        <input type="number" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder={"Enter " <> String.downcase(@field.label)} />
      <% "integer" -> %>
        <input type="number" step="1" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder={"Enter " <> String.downcase(@field.label)} />
      <% "date" -> %>
        <input type="date" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" />
      <% "datetime" -> %>
        <input type="datetime-local" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" />
      <% "textarea" -> %>
        <textarea class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200 resize-none" rows="4" placeholder={"Enter " <> String.downcase(@field.label)}></textarea>
      <% "select" -> %>
        <div class="relative">
          <select class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200 appearance-none cursor-pointer">
            <option value="">Select <%= String.downcase(@field.label) %></option>
            <%= if @field.options do %>
              <%= for option <- String.split(@field.options, "\n") do %>
                <option value={String.trim(option)}><%= String.trim(option) %></option>
              <% end %>
            <% else %>
              <option value="option1">Option 1</option>
              <option value="option2">Option 2</option>
              <option value="option3">Option 3</option>
            <% end %>
          </select>
          <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      <% "multiselect" -> %>
        <div class="space-y-3">
          <%= if @field.options do %>
            <%= for option <- String.split(@field.options, "\n") do %>
              <label class="flex items-center p-4 bg-white border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
                <input type="checkbox" class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
                <span class="ml-3 text-base text-gray-900"><%= String.trim(option) %></span>
              </label>
            <% end %>
          <% else %>
            <label class="flex items-center p-4 bg-white border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
              <input type="checkbox" class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
              <span class="ml-3 text-base text-gray-900">Option 1</span>
            </label>
            <label class="flex items-center p-4 bg-white border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
              <input type="checkbox" class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
              <span class="ml-3 text-base text-gray-900">Option 2</span>
            </label>
          <% end %>
        </div>
      <% "boolean" -> %>
        <label class="flex items-center p-4 bg-white border border-gray-200 rounded-2xl cursor-pointer hover:bg-gray-50 transition-colors duration-200">
          <input type="checkbox" class="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
          <span class="ml-3 text-base text-gray-900"><%= @field.label %></span>
        </label>
      <% _ -> %>
        <input type="text" class="w-full px-4 py-4 bg-white border border-gray-200 rounded-2xl text-base placeholder-gray-400 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200" placeholder={"Enter " <> String.downcase(@field.label)} />
    <% end %>
    """
  end
end
