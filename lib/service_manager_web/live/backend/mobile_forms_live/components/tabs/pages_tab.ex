defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.PagesTab do
  use Phoenix.Component
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.ManagementRows
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview

  ## Pages Tab Component

  attr :screens, :list, default: []
  attr :pages, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :all_pages, :list, default: []
  def pages_tab(assigns) do
    ~H"""
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <div class="text-xs text-gray-600 mb-2 flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Pages Management
        </div>
        
        <form phx-submit="v2_create_page" phx-change="v2_filter_by_screen_pages" class="mb-3 space-y-2">
          <div class="flex gap-2 items-center">
            <input type="text" name="v2_page[name]" placeholder="Page name" required class="flex-1 border rounded px-2 py-1 text-xs" />
            <select name="v2_page[screen_id]" required class="border rounded px-2 py-1 text-xs min-w-0 flex-1 bg-white">
              <option value="">📱 Select Screen (filters pages below)</option>
              <%= for screen <- @screens do %>
                <option value={screen.id} selected={@selected_screen_id == screen.id}>
                  🏠 <%= screen.name %>
                </option>
              <% end %>
            </select>
            <button type="submit" class="text-xs px-2 py-1 bg-indigo-600 text-white rounded whitespace-nowrap" disabled={@screens == []}>
              Create Page
            </button>
          </div>
          <%= if @screens == [] do %>
            <div class="text-xs text-amber-600 bg-amber-50 border border-amber-200 rounded px-2 py-1">
              Create a screen first before adding pages
            </div>
          <% end %>
        </form>
        
        <!-- Pages List Section -->
        <div class="mt-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Pages List
            <%= if @selected_screen_id do %>
              <%= case Enum.find(@screens || [], &(&1.id == @selected_screen_id)) do %>
                <% %{name: screen_name} -> %>
                  <span class="text-xs text-gray-500 font-normal ml-1">(in <%= screen_name %>)</span>
                <% _ -> %>
                  
              <% end %>
            <% end %>
          </h4>
          
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <%= cond do %>
              <% @selected_screen_id && (@all_pages || []) != [] -> %>
                <!-- Show pages for selected screen -->
                <% screen_pages = Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id)) %>
                <%= if screen_pages != [] do %>
                  <%= for page <- screen_pages do %>
                    <%= ManagementRows.draggable_management_row(%{
                      item: page,
                      selected_id: @selected_page_id,
                      select_event: "v2_select_page",
                      delete_event: "v2_delete_page",
                      rename_event: "v2_rename_page_row",
                      rename_form_name: "v2_page_row",
                      reorder_event: "v2_reorder_pages",
                      item_type: "page"
                    }) %>
                  <% end %>
                <% else %>
                  <div class="p-4 text-center text-gray-400 text-xs">
                    No pages found for this screen. Create a page above to get started.
                  </div>
                <% end %>
                
              <% !@selected_screen_id && (@all_pages || []) != [] -> %>
                <!-- Show all pages with screen context -->
                <%= for page <- @all_pages || [] do %>
                  <div class={[
                    "flex items-center justify-between p-2 border rounded text-xs cursor-pointer transition-colors",
                    if(@selected_page_id == page.id, do: "bg-blue-50 border-blue-200", else: "bg-gray-50 border-gray-200 hover:bg-gray-100")
                  ]} phx-click="v2_select_page" phx-value-id={page.id}>
                    <div class="flex-1">
                      <div class="font-medium">📄 <%= page.name %></div>
                      <%= case Enum.find(@screens || [], &(&1.id == page.screen_id)) do %>
                        <% %{name: screen_name} -> %>
                          <div class="text-gray-500">in 🏠 <%= screen_name %></div>
                        <% _ -> %>
                          <div class="text-gray-400">Unknown screen</div>
                      <% end %>
                    </div>
                    <div class="flex items-center space-x-1">
                      <button phx-click="v2_delete_page" phx-value-id={page.id} data-confirm="Delete this page?" class="text-red-500 hover:text-red-700 p-1">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                <% end %>
                
              <% true -> %>
                <!-- Empty state -->
                <div class="p-4 text-center text-gray-400 text-xs">
                  <%= if @selected_screen_id do %>
                    No pages found for this screen. Create a page above to get started.
                  <% else %>
                    <%= if (@all_pages || []) == [] do %>
                      No pages created yet. Create your first page above to get started.
                    <% else %>
                      Select a screen to filter pages, or choose "All Screens" to see everything.
                    <% end %>
                  <% end %>
                </div>
            <% end %>
          </div>
        </div>
      </div>
      <div>
        <%= MobilePreview.mobile_preview(%{
          level: (if @selected_screen_id, do: "pages", else: "screens"),
          items: (
            cond do
              @selected_screen_id ->
                Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id))
              true ->
                @screens || []
            end
          ),
          active_id: @selected_page_id,
          all_screens: @screens || [],
          selected_screen_id: @selected_screen_id,
          all_pages: @all_pages || @pages || [],
          selected_page_id: @selected_page_id,
          tab_context: "pages"
        }) %>
      </div>
    </div>
    """
  end
end
