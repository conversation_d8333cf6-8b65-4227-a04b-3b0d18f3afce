defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.HierarchyTab do
  use Phoenix.Component
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview

  ## Hierarchy Tab Component

  attr :screens, :list, default: []
  attr :all_pages, :list, default: []
  attr :all_forms, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  def hierarchy_tab(assigns) do
    ~H"""
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <div class="text-xs text-gray-600 mb-2 flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
          </svg>
          App Hierarchy
        </div>
        
        <div class="bg-white border rounded-lg p-4 max-h-96 overflow-y-auto">
          <%= if @screens == [] do %>
            <div class="text-center text-gray-400 text-sm py-8">
              <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
              <div class="font-medium">No app structure yet</div>
              <div class="text-gray-300 mt-1">Create screens, pages, and forms to see the hierarchy</div>
            </div>
          <% else %>
            <div class="space-y-3">
              <%= for screen <- @screens do %>
                <.hierarchy_screen 
                  screen={screen} 
                  all_pages={@all_pages} 
                  all_forms={@all_forms}
                  selected_screen_id={@selected_screen_id}
                  selected_page_id={@selected_page_id}
                  selected_form_id={@selected_form_id}
                />
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
      <div>
        <%= MobilePreview.mobile_preview(%{
          level: "screens",
          items: @screens,
          active_id: @selected_screen_id,
          all_screens: @screens || [],
          selected_screen_id: @selected_screen_id,
          all_pages: @all_pages || [],
          selected_page_id: @selected_page_id
        }) %>
      </div>
    </div>
    """
  end

  # Hierarchy screen component
  attr :screen, :map, required: true
  attr :all_pages, :list, default: []
  attr :all_forms, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  defp hierarchy_screen(assigns) do
    ~H"""
    <div class={[
      "border rounded-lg p-3 transition-colors",
      if(@selected_screen_id == @screen.id, do: "bg-indigo-50 border-indigo-200", else: "bg-gray-50 border-gray-200")
    ]}>
      <!-- Screen Header -->
      <div class="flex items-center space-x-2 mb-2">
        <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="font-medium text-sm cursor-pointer" phx-click="v2_select_screen" phx-value-id={@screen.id}>
          🏠 <%= @screen.name %>
        </span>
        <% screen_pages = Enum.filter(@all_pages, &(&1.screen_id == @screen.id)) %>
        <span class="text-xs text-gray-500">(<%= length(screen_pages) %> pages)</span>
      </div>

      <!-- Pages -->
      <% screen_pages = Enum.filter(@all_pages, &(&1.screen_id == @screen.id)) %>
      <%= if screen_pages != [] do %>
        <div class="ml-6 space-y-2">
          <%= for page <- screen_pages do %>
            <.hierarchy_page
              page={page}
              all_forms={@all_forms}
              selected_page_id={@selected_page_id}
              selected_form_id={@selected_form_id}
            />
          <% end %>
        </div>
      <% else %>
        <div class="ml-6 text-xs text-gray-400 italic">No pages created</div>
      <% end %>
    </div>
    """
  end

  # Hierarchy page component
  attr :page, :map, required: true
  attr :all_forms, :list, default: []
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  defp hierarchy_page(assigns) do
    ~H"""
    <div class={[
      "border rounded p-2 transition-colors",
      if(@selected_page_id == @page.id, do: "bg-blue-50 border-blue-200", else: "bg-white border-gray-200")
    ]}>
      <!-- Page Header -->
      <div class="flex items-center space-x-2 mb-1">
        <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="font-medium text-xs cursor-pointer" phx-click="v2_select_page" phx-value-id={@page.id}>
          📄 <%= @page.name %>
        </span>
        <% page_forms = Enum.filter(@all_forms, &(&1.page_id == @page.id)) %>
        <span class="text-xs text-gray-500">(<%= length(page_forms) %> forms)</span>
      </div>

      <!-- Forms -->
      <% page_forms = Enum.filter(@all_forms, &(&1.page_id == @page.id)) %>
      <%= if page_forms != [] do %>
        <div class="ml-4 space-y-1">
          <%= for form <- page_forms do %>
            <div class={[
              "flex items-center space-x-2 p-1 rounded text-xs cursor-pointer transition-colors",
              if(@selected_form_id == form.id, do: "bg-green-50 text-green-700", else: "text-gray-600 hover:bg-gray-50")
            ]} phx-click="v2_select_form" phx-value-id={form.id}>
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              <span>📝 <%= form.name %></span>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="ml-4 text-xs text-gray-400 italic">No forms created</div>
      <% end %>
    </div>
    """
  end
end
