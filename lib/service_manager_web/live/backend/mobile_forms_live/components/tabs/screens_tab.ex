defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.ScreensTab do
  use Phoenix.Component
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.ManagementRows
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview

  ## Screens Tab Component

  attr :screens, :list, default: []
  attr :selected_screen_id, :any
  def screens_tab(assigns) do
    ~H"""
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <div class="text-xs text-gray-600 mb-2 flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Screens Management
        </div>
        <form phx-submit="v2_create_screen" class="mb-3 flex gap-2">
          <input type="text" name="v2_screen[name]" placeholder="Screen name" required class="flex-1 border rounded px-2 py-1 text-xs" />
          <button type="submit" class="text-xs px-2 py-1 bg-indigo-600 text-white rounded">Create Screen</button>
        </form>
        <div class="space-y-2">
          <%= for s <- @screens do %>
            <%= ManagementRows.draggable_management_row(%{
              item: s,
              selected_id: @selected_screen_id,
              select_event: "v2_select_screen",
              delete_event: "v2_delete_screen",
              rename_event: "v2_rename_screen_row",
              rename_form_name: "v2_screen_row",
              reorder_event: "v2_reorder_screens",
              item_type: "screen"
            }) %>
          <% end %>
          <%= if @screens == [] do %>
            <div class="p-3 text-xs text-gray-400">No screens created yet</div>
          <% end %>
        </div>
      </div>
      <div>
        <%= MobilePreview.mobile_preview(%{
          level: "screens",
          items: @screens,
          active_id: @selected_screen_id,
          all_screens: @screens,
          selected_screen_id: @selected_screen_id
        }) %>
      </div>
    </div>
    """
  end
end
