defmodule ServiceManagerWeb.Backend.MobileFormsLive.UssdHandlers do
  @moduledoc """
  USSD-specific event handlers and logic for the Mobile Forms LiveView.
  Extracted to reduce complexity in the main LiveView module.
  """

  # Import Phoenix functions for helper modules
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]
  alias ServiceManagerWeb.Api.Services.Local.UssdService
  alias ServiceManagerWeb.Backend.MobileFormsLive.Helpers

  @doc """
  Handle USSD option simulation
  """
  def simulate_ussd_option(socket, option_index) do
    current_menu = socket.assigns.current_ussd_menu

    if current_menu && length(current_menu.options || []) > option_index do
      option = Enum.at(current_menu.options, option_index)
      handle_option_action(socket, option)
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handle USSD navigation back
  """
  def ussd_go_back(socket) do
    stack = socket.assigns.ussd_menu_stack

    if length(stack) > 0 do
      [previous_menu | remaining_stack] = stack
      socket =
        socket
        |> assign(:current_ussd_menu, previous_menu)
        |> assign(:ussd_menu_stack, remaining_stack)
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handle USSD cancel operation
  """
  def ussd_cancel(socket) do
    {:noreply, Helpers.reset_ussd_simulation(socket)}
  end

  @doc """
  Handle keypad input for USSD navigation
  """
  def handle_ussd_key_press(socket, key) do
    if socket.assigns.form_input_mode do
      handle_form_key_press(key, socket)
    else
      case key do
        "0" -> ussd_go_back(socket)
        "#" -> ussd_cancel(socket)
        num when num in ["1", "2", "3", "4", "5", "6", "7", "8", "9"] ->
          option_index = String.to_integer(num) - 1
          simulate_ussd_option(socket, option_index)
        _ -> {:noreply, socket}
      end
    end
  end

  ## Private Helper Functions

  defp handle_option_action(socket, option) do
    case option.action do
      "navigate" ->
        if option.target_menu_id do
          case UssdService.get_menu(option.target_menu_id) do
            {:ok, target_menu} ->
              stack = [socket.assigns.current_ussd_menu | socket.assigns.ussd_menu_stack]
              socket =
                socket
                |> assign(:current_ussd_menu, target_menu)
                |> assign(:ussd_menu_stack, stack)
              {:noreply, socket}
            {:error, _} ->
              {:noreply, put_flash(socket, :error, "Menu not found")}
          end
        else
          {:noreply, socket}
        end

      "form" ->
        if option.form_name do
          case ServiceManagerWeb.Api.Services.Local.MobileAppFormsService.start_form_session(option.form_name) do
            {:ok, form_session} ->
              case ServiceManagerWeb.Api.Services.Local.MobileAppFormsService.get_next_field(form_session.session_id) do
                {:ok, field} ->
                  socket =
                    socket
                    |> assign(:form_session, form_session)
                    |> assign(:current_form_field, field)
                    |> assign(:form_input_mode, true)
                    |> assign(:current_letter_input, "")
                    |> assign(:letter_input_mode, false)
                  {:noreply, socket}

                {:error, reason} ->
                  {:noreply, put_flash(socket, :error, "Form error: #{reason}")}
              end

            {:error, reason} ->
              {:noreply, put_flash(socket, :error, "Failed to start form: #{reason}")}
          end
        else
          {:noreply, put_flash(socket, :error, "No form specified for this option")}
        end

      _ ->
        {:noreply, socket}
    end
  end

  defp handle_form_key_press(key, socket) do
    cond do
      key == "*" ->
        # Toggle letter input mode
        socket =
          socket
          |> assign(:letter_input_mode, !socket.assigns.letter_input_mode)
          |> assign(:current_letter_input, "")
          |> assign(:last_key_press, nil)
          |> assign(:key_press_count, 0)
        {:noreply, socket}

      key == "#" ->
        # Submit current input
        handle_form_input_submit(socket)

      key == "0" ->
        # Cancel form input
        socket =
          socket
          |> Helpers.reset_form_input_state()
          |> put_flash(:info, "Form input cancelled")
        {:noreply, socket}

      socket.assigns.letter_input_mode ->
        handle_letter_input(socket, key)

      true ->
        # Direct number input
        current_input = socket.assigns.current_letter_input <> key
        socket = assign(socket, :current_letter_input, current_input)
        {:noreply, socket}
    end
  end

  defp handle_letter_input(socket, key) do
    # T9-style letter input logic would go here
    # For now, just append the key
    current_input = socket.assigns.current_letter_input <> key
    socket = assign(socket, :current_letter_input, current_input)
    {:noreply, socket}
  end

  defp handle_form_input_submit(socket) do
    input = socket.assigns.current_letter_input
    field = socket.assigns.current_form_field
    session_id = socket.assigns.form_session.session_id

    case ServiceManagerWeb.Api.Services.Local.MobileAppFormsService.submit_field_value(session_id, field.field_name, input) do
      {:ok, _result} ->
        case ServiceManagerWeb.Api.Services.Local.MobileAppFormsService.get_next_field(session_id) do
          {:ok, next_field} ->
            socket =
              socket
              |> assign(:current_form_field, next_field)
              |> assign(:current_letter_input, "")
              |> assign(:letter_input_mode, false)
            {:noreply, socket}

          {:error, "form_complete"} ->
            socket =
              socket
              |> Helpers.reset_form_input_state()
              |> put_flash(:info, "Form completed successfully")
            {:noreply, socket}

          {:error, reason} ->
            {:noreply, put_flash(socket, :error, "Form error: #{reason}")}
        end

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to submit field: #{reason}")}
    end
  end
end
