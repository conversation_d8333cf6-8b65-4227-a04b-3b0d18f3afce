defmodule ServiceManagerWeb.Backend.MobileFormsLive.Helpers do
  @moduledoc """
  Reusable helper functions for the Mobile Forms LiveView to reduce code duplication
  and improve maintainability.
  """

  # Helper functions that work with LiveView sockets
  alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service, as: V2

  # Import Phoenix functions for helper modules
  import Phoenix.Component, only: [assign: 3]
  import Phoenix.LiveView, only: [put_flash: 3]

  ## CRUD Operation Helpers

  @doc """
  Generic CRUD operation handler that follows the pattern:
  1. Call service function
  2. Handle success/error
  3. Update socket with flash message and data reload
  """
  def handle_crud_operation(socket, service_fn, success_message, error_message, reload_fn \\ nil) do
    case service_fn.() do
      {:ok, _result} ->
        socket = if reload_fn, do: reload_fn.(socket), else: socket
        {:noreply, socket |> Phoenix.LiveView.put_flash(:info, success_message)}
      {:error, reason} ->
        error_msg = if is_binary(reason), do: reason, else: error_message
        {:noreply, Phoenix.LiveView.put_flash(socket, :error, error_msg)}
    end
  end

  @doc """
  Handle V2 create operations with consistent pattern
  """
  def handle_v2_create(socket, service_fn, list_fn, assign_key, success_msg, error_msg) do
    case service_fn.() do
      {:ok, _item} ->
        {:ok, items} = list_fn.()
        {:noreply,
          socket
          |> assign(assign_key, items)
          |> put_flash(:info, success_msg)
          |> Phoenix.LiveView.push_event("clear-form", %{})}
      {:error, changeset} ->
        error_details = if is_map(changeset) and Map.has_key?(changeset, :errors) do
          changeset.errors
          |> Enum.map(fn {field, {msg, _}} -> "#{field}: #{msg}" end)
          |> Enum.join(", ")
        else
          inspect(changeset)
        end

        # Check for specific constraint errors
        detailed_error = cond do
          String.contains?(error_details, "mobile_pages_unique_screen_name") ->
            "A page with this name already exists in the selected screen"
          String.contains?(error_details, "mobile_forms_v2_unique_page_name") ->
            "A form with this name already exists in the selected page"
          String.contains?(error_details, "mobile_screens_unique_name_version") ->
            "A screen with this name already exists"
          String.contains?(error_details, "foreign_key") ->
            "Invalid parent selection - please refresh and try again"
          error_details != "" ->
            "#{error_msg} - #{error_details}"
          true ->
            error_msg
        end

        {:noreply, put_flash(socket, :error, detailed_error)}
    end
  end

  @doc """
  Handle V2 delete operations with cascading cleanup
  """
  def handle_v2_delete(socket, delete_fn, reload_fn, cleanup_assigns \\ []) do
    case delete_fn.() do
      {:ok, _} ->
        socket = reload_fn.(socket)
        socket = Enum.reduce(cleanup_assigns, socket, fn {key, value}, acc ->
          assign(acc, key, value)
        end)
        {:noreply, socket}
      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Delete operation failed")}
    end
  end

  @doc """
  Handle V2 rename operations
  """
  def handle_v2_rename(socket, update_fn, reload_fn, error_msg) do
    case update_fn.() do
      {:ok, _item} ->
        {:noreply, reload_fn.(socket)}
      {:error, _} ->
        {:noreply, Phoenix.LiveView.put_flash(socket, :error, error_msg)}
    end
  end

  ## Data Loading Helpers

  @doc """
  Load V2 screens with error handling
  """
  def load_v2_screens(socket) do
    case V2.list_screens(%{}) do
      {:ok, screens} -> assign(socket, :v2_screens, screens)
      {:error, _} -> put_flash(socket, :error, "Failed to load screens")
    end
  end

  @doc """
  Load V2 pages for a screen with error handling
  """
  def load_v2_pages(socket, screen_id) do
    case V2.list_pages(%{screen_id: screen_id}) do
      {:ok, pages} -> assign(socket, :v2_pages, pages)
      {:error, _} -> put_flash(socket, :error, "Failed to load pages")
    end
  end

  @doc """
  Load V2 forms for a page with error handling
  """
  def load_v2_forms(socket, page_id) do
    case V2.list_forms(%{page_id: page_id}) do
      {:ok, forms} -> assign(socket, :v2_forms, forms)
      {:error, _} -> put_flash(socket, :error, "Failed to load forms")
    end
  end

  ## Selection Helpers

  @doc """
  Handle V2 screen selection with cascading data load
  """
  def select_v2_screen(socket, screen_id) do
    # Handle empty string as nil (for back button)
    screen_id = if screen_id == "", do: nil, else: screen_id
    with {:ok, pages} <- V2.list_pages(%{screen_id: screen_id}) do
      socket
      |> assign(:v2_selected_screen_id, screen_id)
      |> assign(:v2_pages, pages)
      |> clear_downstream_selections()
    else
      {:error, _} -> put_flash(socket, :error, "Failed to load pages")
    end
  end

  @doc """
  Handle V2 page selection with cascading data load
  """
  def select_v2_page(socket, page_id) do
    # Handle empty string as nil (for back button)
    page_id = if page_id == "", do: nil, else: page_id
    with {:ok, forms} <- V2.list_forms(%{page_id: page_id}) do
      socket
      |> assign(:v2_selected_page_id, page_id)
      |> assign(:v2_forms, forms)
      |> clear_form_selections()
    else
      {:error, _} -> put_flash(socket, :error, "Failed to load forms")
    end
  end

  @doc """
  Handle V2 form selection with field loading
  """
  def select_v2_form(socket, form_id) do
    # Handle empty string as nil (for back button)
    form_id = if form_id == "", do: nil, else: form_id
    with {:ok, fields} <- V2.get_form_fields(form_id) do
      # Find the form in either v2_forms or v2_all_forms
      form = Enum.find(socket.assigns.v2_forms, &(&1.id == form_id)) ||
             Enum.find(socket.assigns[:v2_all_forms] || [], &(&1.id == form_id))
      
      socket
      |> assign(:v2_selected_form_id, form_id)
      |> assign(:v2_fields, fields)
      |> assign(:v2_form_submit_to, form && form.submit_to)
      |> assign(:v2_form_route_id, form && form.route_id)  # Add route_id for dropdown
    else
      {:error, _} -> put_flash(socket, :error, "Failed to load form fields")
    end
  end

  ## Assignment Helpers

  @doc """
  Clear downstream selections when a parent is changed
  """
  def clear_downstream_selections(socket) do
    socket
    |> assign(:v2_forms, [])
    |> assign(:v2_fields, [])
    |> assign(:v2_selected_page_id, nil)
    |> assign(:v2_selected_form_id, nil)
    |> assign(:v2_form_submit_to, nil)
  end

  @doc """
  Clear form-level selections
  """
  def clear_form_selections(socket) do
    socket
    |> assign(:v2_fields, [])
    |> assign(:v2_selected_form_id, nil)
    |> assign(:v2_form_submit_to, nil)
  end

  ## Reorder Helpers

  @doc """
  Generic reorder function for moving items up/down
  """
  def move_order(socket, items_key, update_fn, item_id, direction) do
    items = socket.assigns[items_key]
    item_index = Enum.find_index(items, &(&1.id == item_id))
    
    if item_index do
      new_index = case direction do
        :up -> max(0, item_index - 1)
        :down -> min(length(items) - 1, item_index + 1)
      end
      
      if new_index != item_index do
        # Update the order in the database
        case update_fn.(item_id, %{order: new_index}) do
          {:ok, _} ->
            # Reload the items to get the updated order
            reload_items_for_key(socket, items_key)
          {:error, _} ->
            Phoenix.LiveView.put_flash(socket, :error, "Failed to reorder item")
        end
      else
        socket
      end
    else
      socket
    end
  end

  defp reload_items_for_key(socket, :v2_screens), do: load_v2_screens(socket)
  defp reload_items_for_key(socket, :v2_pages), do: load_v2_pages(socket, socket.assigns.v2_selected_screen_id)
  defp reload_items_for_key(socket, :v2_forms), do: load_v2_forms(socket, socket.assigns.v2_selected_page_id)
  defp reload_items_for_key(socket, _), do: socket

  ## Form Validation Helpers

  @doc """
  Validate required parent selection for create operations
  """
  def validate_parent_selection(socket, parent_key, error_message) do
    case socket.assigns[parent_key] do
      nil -> {:error, error_message}
      parent_id -> {:ok, parent_id}
    end
  end

  ## Flash Message Helpers

  @doc """
  Standard success message for create operations
  """
  def create_success_message(item_type, name), do: "#{String.capitalize(item_type)} '#{name}' created successfully"

  @doc """
  Standard error message for create operations
  """
  def create_error_message(item_type), do: "Failed to create #{item_type}"

  @doc """
  Standard error message for delete operations
  """
  def delete_error_message(item_type), do: "Failed to delete #{item_type}"

  @doc """
  Standard error message for rename operations
  """
  def rename_error_message(item_type), do: "Failed to rename #{item_type}"

  ## USSD Helpers

  @doc """
  Handle USSD CRUD operations with consistent pattern
  """
  def handle_ussd_operation(socket, service_fn, success_message, error_message, reload_fn \\ nil) do
    case service_fn.() do
      {:ok, _result} ->
        socket = if reload_fn, do: reload_fn.(socket), else: socket
        socket = generate_ussd_code(socket)
        {:noreply, socket |> Phoenix.LiveView.put_flash(:info, success_message)}
      {:error, reason} ->
        error_msg = if is_binary(reason), do: reason, else: error_message
        {:noreply, Phoenix.LiveView.put_flash(socket, :error, error_msg)}
    end
  end

  @doc """
  Load USSD data with error handling
  """
  def load_ussd_data(socket) do
    alias ServiceManagerWeb.Api.Services.Local.UssdService
    case UssdService.list_menus() do
      {:ok, menus} ->
        socket |> assign(:ussd_menus, menus)
      {:error, _reason} ->
        socket |> put_flash(:error, "Failed to load USSD data")
    end
  end

  @doc """
  Generate USSD code and update socket
  """
  def generate_ussd_code(socket) do
    menus = socket.assigns.ussd_menus

    if length(menus) > 0 do
      code = generate_menu_code(menus, 0, "")
      assign(socket, :ussd_code, code)
    else
      assign(socket, :ussd_code, nil)
    end
  end

  @doc """
  Generate mobile input type from field type
  """
  def mobile_input_type(field_type) do
    case field_type do
      "email" -> "email"
      "password" -> "password"
      "phone" -> "tel"
      "number" -> "number"
      "integer" -> "number"
      "date" -> "date"
      "datetime" -> "datetime-local"
      _ -> "text"
    end
  end

  ## Private USSD Code Generation

  defp generate_menu_code(menus, level, parent_code) do
    case Enum.at(menus, level) do
      nil ->
        ""
      menu ->
        code = """
        #{if level == 0, do: "// Main USSD Menu", else: "// Menu Level #{level + 1}"}
        #{parent_code}
        CON #{menu.title || "Menu Level #{level + 1}"}
        #{generate_options_code(menu.options || [])}
        """

        # Generate code for sub-menus if they exist
        if level + 1 < length(menus) do
          code <> "\n" <> generate_menu_code(menus, level + 1, "#{parent_code}*1")
        else
          code
        end
    end
  end

  defp generate_options_code(options) do
    options
    |> Enum.with_index()
    |> Enum.map(fn {option, index} ->
      "#{index + 1}. #{option.text}"
    end)
    |> Enum.join("\n")
    |> case do
      "" -> ""
      opts -> opts <> "\n0. Back\n#. Cancel"
    end
  end

  @doc """
  Get pages for a specific screen and form combination
  """
  def get_pages_for_screen(fields, form, screen) do
    pages = fields
    |> Enum.filter(fn field ->
      field.form == form and field.screen == screen and not is_nil(field.page)
    end)
    |> Enum.map(& &1.page)
    |> Enum.uniq()
    |> Enum.sort()

    # If there are no pages, return a single "Main" page for display
    if length(pages) == 0, do: [nil], else: pages
  end

  @doc """
  Reset USSD simulation state
  """
  def reset_ussd_simulation(socket) do
    socket
    |> assign(:current_ussd_menu, List.first(socket.assigns.ussd_menus))
    |> assign(:ussd_menu_stack, [])
  end

  @doc """
  Reset form input state
  """
  def reset_form_input_state(socket) do
    socket
    |> assign(:form_session, nil)
    |> assign(:current_form_field, nil)
    |> assign(:form_input_mode, false)
    |> assign(:current_letter_input, "")
    |> assign(:letter_input_mode, false)
    |> assign(:last_key_press, nil)
    |> assign(:key_press_count, 0)
  end

end
