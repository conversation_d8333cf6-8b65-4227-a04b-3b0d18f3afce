defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components do
  use ServiceManagerWeb, :html

  # Alias all component modules
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.ScreensTab
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.PagesTab
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.FormsTab
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.HierarchyTab
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.ManagementRows
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.FieldComponents
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview

  ## Tab Component Delegates

  # Screens tab component
  attr :screens, :list, default: []
  attr :selected_screen_id, :any
  def screens_tab(assigns) do
    ScreensTab.screens_tab(assigns)
  end

  # Pages tab component
  attr :screens, :list, default: []
  attr :pages, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :all_pages, :list, default: []
  def pages_tab(assigns) do
    PagesTab.pages_tab(assigns)
  end

  # Forms tab component
  attr :pages, :list, default: []
  attr :forms, :list, default: []
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  attr :submit_to, :string
  attr :selected_screen_id, :any
  attr :fields, :list, default: []
  attr :screens, :list, default: []
  attr :all_pages, :list, default: []
  attr :all_forms, :list, default: []
  attr :editing_field_id, :any, default: nil
  attr :dynamic_routes, :list, default: []
  attr :form_route_id, :string, default: nil
  def forms_tab(assigns) do
    FormsTab.forms_tab(assigns)
  end

  # Hierarchy tab component
  attr :screens, :list, default: []
  attr :all_pages, :list, default: []
  attr :all_forms, :list, default: []
  attr :selected_screen_id, :any
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  def hierarchy_tab(assigns) do
    HierarchyTab.hierarchy_tab(assigns)
  end

  ## Shared Component Delegates

  # Management row component
  attr :item, :map, required: true
  attr :selected_id, :any
  attr :select_event, :string, required: true
  attr :move_up_event, :string, required: true
  attr :move_down_event, :string, required: true
  attr :delete_event, :string, required: true
  attr :rename_event, :string, required: true
  attr :rename_form_name, :string, required: true
  def management_row(assigns) do
    ManagementRows.management_row(assigns)
  end

  # Draggable management row component
  attr :item, :map, required: true
  attr :selected_id, :any
  attr :select_event, :string, required: true
  attr :delete_event, :string, required: true
  attr :rename_event, :string, required: true
  attr :rename_form_name, :string, required: true
  attr :reorder_event, :string, required: true
  attr :item_type, :string, required: true
  def draggable_management_row(assigns) do
    ManagementRows.draggable_management_row(assigns)
  end

  # Draggable field row component
  attr :field, :map, required: true
  attr :editing_field_id, :any
  def draggable_field_row(assigns) do
    FieldComponents.draggable_field_row(assigns)
  end

  # Mobile preview component
  attr :level, :string, required: true
  attr :items, :list, default: []
  attr :active_id, :any, default: nil
  attr :all_screens, :list, default: []
  attr :selected_screen_id, :any, default: nil
  attr :all_pages, :list, default: []
  attr :selected_page_id, :any, default: nil
  attr :all_fields, :list, default: []
  def mobile_preview(assigns) do
    MobilePreview.mobile_preview(assigns)
  end
end