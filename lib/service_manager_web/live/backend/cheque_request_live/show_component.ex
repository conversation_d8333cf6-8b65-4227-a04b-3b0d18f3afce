defmodule ServiceManagerWeb.Backend.ChequeRequestLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Cheque Request Details</h2>
          <p class="text-sm text-gray-500">Request ID: <%= @cheque_request.id %></p>
        </div>
        <div class="text-right">
          <div class="inline-flex items-center px-4 py-2 rounded-md bg-gray-100">
            <div class={"w-2 h-2 rounded-full mr-2 #{status_color_indicator(@cheque_request.status)}"} />
            <span class="text-sm font-medium text-gray-700 capitalize">
              <%= @cheque_request.status %>
            </span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Request Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Request Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Action Type</label>
              <p class="mt-1 capitalize"><%= @cheque_request.action %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Cheque Number</label>
              <p class="mt-1"><%= @cheque_request.cheque_number %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Account Number</label>
              <p class="mt-1"><%= @cheque_request.account_number %></p>
            </div>
          </div>
        </div>
        <!-- Processing Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Processing Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Status</label>
              <p class="mt-1 capitalize"><%= @cheque_request.status %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Processed By</label>
              <p class="mt-1"><%= @cheque_request.processed_by || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Processed At</label>
              <p class="mt-1">
                <%= if @cheque_request.processed_at do %>
                  <%= @cheque_request.processed_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                <% else %>
                  --
                <% end %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Remarks</label>
              <p class="mt-1"><%= @cheque_request.remarks || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Timestamps -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @cheque_request.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @cheque_request.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp status_color_indicator(status) do
    case status do
      "approved" -> "bg-green-500"
      "pending" -> "bg-yellow-500"
      "rejected" -> "bg-red-500"
      _ -> "bg-gray-500"
    end
  end
end
