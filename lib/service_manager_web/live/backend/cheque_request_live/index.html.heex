<.header>
  Cheque Requests
  <:actions>
    <%= if can_create?(@current_user, :cheque_requests) do %>
      <.link patch={~p"/mobileBanking/cheque-requests/new"}>
        <.button>New Request</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-4">
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            class="h-6 w-6 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
            <dd class="flex items-baseline">
              <div class="text-2xl font-semibold text-gray-900"><%= @stats.total %></div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            class="h-6 w-6 text-yellow-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
            <dd class="flex items-baseline">
              <div class="text-2xl font-semibold text-yellow-600"><%= @stats.pending %></div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            class="h-6 w-6 text-green-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Approved</dt>
            <dd class="flex items-baseline">
              <div class="text-2xl font-semibold text-green-600"><%= @stats.approved %></div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg
            class="h-6 w-6 text-red-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Rejected</dt>
            <dd class="flex items-baseline">
              <div class="text-2xl font-semibold text-red-600"><%= @stats.rejected %></div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<br />
<hr />
<br />

<.table
  id="cheque_requests"
  rows={@cheque_requests}
  row_click={fn request -> JS.patch(~p"/mobileBanking/cheque-requests/#{request}") end}
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
>
  <:col
    :let={request}
    label="Action"
    filter_item="action"
    filter_type="select"
    filter_options={["stop", "cancel"]}
  >
    <span class="capitalize"><%= request.action %></span>
  </:col>

  <:col :let={request} label="Cheque No." filter_item="cheque_number" filter_type="text">
    <%= request.cheque_number %>
  </:col>

  <:col :let={request} label="Account" filter_item="account_number" filter_type="text">
    <%= request.account_number %>
  </:col>

  <:col
    :let={request}
    label="Status"
    filter_item="status"
    filter_type="select"
    filter_options={["all", "pending", "approved", "rejected"]}
  >
    <.status_pill status={request.status == "approved"} text={request.status} />
  </:col>

  <:col :let={request} label="Processed By" filter_item="processed_by" filter_type="text">
    <%= request.processed_by || "-" %>
  </:col>

  <:col :let={request} label="Date" filter_item="inserted_at" filter_type="date">
    <%= Calendar.strftime(request.inserted_at, "%Y-%m-%d %H:%M") %>
  </:col>

  <:action :let={request}>
    <%= if has_any_permission?(@current_user, [:view, :approve], :cheque_requests) do %>
      <.dropdown id={"dropdown-#{request.id}"} label="Options">
        <%= if can_view?(@current_user, :cheque_requests) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/cheque-requests/#{request}"}
          >
            View Details
          </.link>
        <% end %>

        <%= if can_approve?(@current_user, :cheque_requests) && request.status == "pending" do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("approve", value: %{id: request.id})}
            data-confirm="Are you sure you want to approve this request?"
          >
            Approve Request
          </.link>

          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("show_reject_modal", value: %{id: request.id})}
          >
            Reject Request
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal :if={@show_reject_modal} id="reject-modal" show on_cancel={JS.push("hide_reject_modal")}>
  <.header>
    Reject Cheque Request
  </.header>

  <.simple_form for={@reject_form} id="reject-form" phx-submit="reject">
    <.input type="hidden" field={@reject_form[:id]} value={@reject_request_id} />

    <.input field={@reject_form[:remarks]} type="textarea" label="Rejection Reason" required />

    <:actions>
      <.button phx-disable-with="Rejecting...">Reject Request</.button>
      <.link
        phx-click={JS.push("hide_reject_modal")}
        class="text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
      >
        Cancel
      </.link>
    </:actions>
  </.simple_form>
</.modal>

<.modal
  :if={@live_action in [:new]}
  id="cheque-request-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cheque-requests")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.ChequeRequestLive.FormComponent}
    id={@cheque_request.id || :new}
    title={@page_title}
    action={@live_action}
    current_user={@current_user}
    cheque_request={@cheque_request}
    patch={~p"/mobileBanking/cheque-requests"}
  />
</.modal>

<.modal
  :if={@live_action == :show}
  id="cheque-request-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/cheque-requests")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.ChequeRequestLive.ShowComponent}
    id={@cheque_request.id}
    title={@page_title}
    cheque_request={@cheque_request}
    patch={~p"/mobileBanking/cheque-requests"}
  />
</.modal>
