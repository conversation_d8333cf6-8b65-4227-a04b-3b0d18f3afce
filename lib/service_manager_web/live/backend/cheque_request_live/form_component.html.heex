<div>
  <.header>
    <%= @title %>
  </.header>

  <.simple_form
    for={@form}
    id="cheque-request-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
    class="mt-8"
  >
    <div class="space-y-6 bg-white">
      <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <%= if @action == :new do %>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <.input
              field={@form[:action]}
              type="select"
              label="Action"
              prompt="Select action type"
              options={["cancel", "stop"]}
              required
            />

            <.input field={@form[:cheque_number]} type="text" label="Cheque Number" required />

            <.input
              field={@form[:account_number]}
              type="text"
              label="Account Number"
              value={@current_user.account_number}
              readonly={true}
            />
          </div>
        <% else %>
          <div class="sm:col-span-6">
            <div class="flex items-center space-x-4 mb-4">
              <div>
                <span class="text-sm font-medium text-gray-500">Action:</span>
                <span class="ml-2 text-sm text-gray-900 capitalize">
                  <%= @cheque_request.action %>
                </span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-500">Cheque Number:</span>
                <span class="ml-2 text-sm text-gray-900">
                  <%= @cheque_request.cheque_number %>
                </span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-500">Account:</span>
                <span class="ml-2 text-sm text-gray-900">
                  <%= @cheque_request.account_number %>
                </span>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              options={["pending", "approved", "rejected"]}
              required
            />

            <.input
              field={@form[:processed_by]}
              type="text"
              label="Processed By"
              value={@current_user.username}
              readonly
            />

            <div class="sm:col-span-2">
              <.input
                field={@form[:remarks]}
                type="textarea"
                label="Remarks"
                placeholder="Add processing remarks..."
                required={@action == :edit}
              />
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <:actions>
      <.button phx-disable-with="Saving...">
        <%= if @action == :new, do: "Submit Request", else: "Update Request" %>
      </.button>
      <.link
        patch={@patch}
        class="text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
      >
        Cancel
      </.link>
    </:actions>
  </.simple_form>
</div>
