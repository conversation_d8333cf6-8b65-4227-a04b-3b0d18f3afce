defmodule ServiceManagerWeb.Backend.ChequeRequestLive.Index do
  use ServiceManagerWeb, :live_view
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeRequest
  alias ServiceManager.Repo
  import Ecto.Query
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, session, socket) do
    if connected?(socket), do: Phoenix.PubSub.subscribe(ServiceManager.PubSub, "cheque_requests")

    {:ok,
     socket
     |> assign(:page_title, "Cheque Requests")
     |> assign(:cheque_request, %ChequeRequest{})
     |> assign(:filter_params, %{"status" => "all"})
     |> assign(:pagination, %{
       page_number: 1,
       page_size: 20,
       total_pages: 1,
       total_entries: 0,
       distance: 5
     })
     |> assign(:selected_column, "inserted_at")
     |> assign(:show_reject_modal, false)
     |> assign(:reject_request_id, nil)
     |> assign(:reject_form, to_form(%{"remarks" => ""}))
     |> assign_stats()
     |> assign_cheque_requests()}
  end

  defp assign_stats(socket) do
    stats = %{
      total: Repo.aggregate(ChequeRequest, :count),
      pending: Repo.aggregate(from(r in ChequeRequest, where: r.status == "pending"), :count),
      approved: Repo.aggregate(from(r in ChequeRequest, where: r.status == "approved"), :count),
      rejected: Repo.aggregate(from(r in ChequeRequest, where: r.status == "rejected"), :count)
    }

    assign(socket, :stats, stats)
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  @impl true
  def handle_event("approve", %{"id" => id}, socket) do
    cheque_request = get_cheque_request!(id)

    attrs = %{
      status: "approved",
      processed_at: DateTime.utc_now()
    }

    case update_cheque_request(cheque_request, attrs) do
      {:ok, _updated_request} ->
        {:noreply,
         socket
         |> put_flash(:info, "Request approved successfully")
         |> assign_cheque_requests()}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Error approving request")}
    end
  end

  @impl true
  def handle_event("show_reject_modal", %{"id" => id}, socket) do
    {:noreply,
     socket
     |> assign(:show_reject_modal, true)
     |> assign(:reject_request_id, id)
     |> assign(:reject_form, to_form(%{"remarks" => ""}))}
  end

  @impl true
  def handle_event("hide_reject_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_reject_modal, false)
     |> assign(:reject_request_id, nil)}
  end

  @impl true
  def handle_event("reject", %{"remarks" => remarks}, socket) do
    cheque_request = get_cheque_request!(socket.assigns.reject_request_id)

    attrs = %{
      status: "rejected",
      processed_at: DateTime.utc_now(),
      remarks: remarks
    }

    case update_cheque_request(cheque_request, attrs) do
      {:ok, _updated_request} ->
        {:noreply,
         socket
         |> assign(:show_reject_modal, false)
         |> assign(:reject_request_id, nil)
         |> put_flash(:info, "Request rejected successfully")
         |> assign_cheque_requests()}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Error rejecting request")}
    end
  end

  @impl true
  def handle_event("filter", params, socket) do
    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> assign_cheque_requests()}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {page, _} = Integer.parse(page)

    {:noreply,
     socket
     |> assign(:pagination, %{socket.assigns.pagination | page_number: page})
     |> assign_cheque_requests()}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Cheque Request")
    |> assign(:cheque_request, get_cheque_request!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Cheque Request")
    |> assign(:cheque_request, %ChequeRequest{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Cheque Requests")
    |> assign(:cheque_request, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "View Cheque Request")
    |> assign(:cheque_request, get_cheque_request!(id))
  end

  defp assign_cheque_requests(socket) do
    {cheque_requests, pagination} =
      list_cheque_requests(socket.assigns.filter_params, socket.assigns.pagination)

    socket
    |> assign(:cheque_requests, cheque_requests)
    |> assign(:pagination, pagination)
    |> assign_stats()
  end

  defp list_cheque_requests(params, pagination) do
    query =
      ChequeRequest
      |> filter_by_status(params["status"])
      |> filter_by_action(params["action"])
      |> filter_by_account(params["account_number"])
      |> filter_by_cheque(params["cheque_number"])
      |> order_by([r], desc: r.inserted_at)

    total_entries = Repo.aggregate(query, :count, :id)
    total_pages = ceil(total_entries / pagination.page_size)

    cheque_requests =
      query
      |> limit(^pagination.page_size)
      |> offset(^((pagination.page_number - 1) * pagination.page_size))
      |> Repo.all()
      |> Repo.preload(:user)

    {cheque_requests, %{pagination | total_pages: total_pages, total_entries: total_entries}}
  end

  defp filter_by_status(query, "all"), do: query

  defp filter_by_status(query, status) when is_binary(status) do
    from q in query, where: q.status == ^status
  end

  defp filter_by_status(query, _), do: query

  defp filter_by_action(query, action) when is_binary(action) and action != "" do
    from q in query, where: q.action == ^action
  end

  defp filter_by_action(query, _), do: query

  defp filter_by_account(query, account) when is_binary(account) and account != "" do
    from q in query, where: ilike(q.account_number, ^"%#{account}%")
  end

  defp filter_by_account(query, _), do: query

  defp filter_by_cheque(query, cheque) when is_binary(cheque) and cheque != "" do
    from q in query, where: ilike(q.cheque_number, ^"%#{cheque}%")
  end

  defp filter_by_cheque(query, _), do: query

  defp get_cheque_request!(id) do
    ChequeRequest
    |> Repo.get!(id)
    |> Repo.preload(:user)
  end

  defp update_cheque_request(%ChequeRequest{} = cheque_request, attrs) do
    cheque_request
    |> ChequeRequest.changeset(attrs)
    |> Repo.update()
  end

  defp status_color(status) do
    case status do
      "pending" -> "bg-yellow-100 text-yellow-800"
      "approved" -> "bg-green-100 text-green-800"
      "rejected" -> "bg-red-100 text-red-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end
end
