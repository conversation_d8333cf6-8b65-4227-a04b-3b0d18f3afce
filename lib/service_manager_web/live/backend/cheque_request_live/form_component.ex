defmodule ServiceManagerWeb.Backend.ChequeRequestLive.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeRequest

  @impl true
  def update(%{cheque_request: cheque_request} = assigns, socket) do
    changeset =
      case assigns.action do
        :new ->
          ChequeRequest.changeset(cheque_request, %{
            account_number: assigns.current_user.account_number,
            user_id: assigns.current_user.id
          })

        :edit ->
          ChequeRequest.changeset(cheque_request, %{
            processed_by: assigns.current_user.username
          })
      end

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:form, to_form(changeset))}
  end

  @impl true
  def handle_event("validate", %{"cheque_request" => params}, socket) do
    params = maybe_add_user_info(params, socket)

    form =
      socket.assigns.cheque_request
      |> ChequeRequest.changeset(params)
      |> Map.put(:action, :validate)
      |> to_form()

    {:noreply, assign(socket, form: form)}
  end

  @impl true
  def handle_event("save", %{"cheque_request" => params}, socket) do
    params = maybe_add_user_info(params, socket)

    save_cheque_request(socket, socket.assigns.action, params)
  end

  defp maybe_add_user_info(params, socket) do
    case socket.assigns.action do
      :edit ->
        Map.merge(params, %{
          "processed_at" => DateTime.utc_now(),
          "processed_by" => socket.assigns.current_user.username
        })

      :new ->
        Map.merge(params, %{
          "user_id" => socket.assigns.current_user.id,
          "account_number" => socket.assigns.current_user.account_number,
          "status" => "pending"
        })
    end
  end

  defp save_cheque_request(socket, :edit, params) do
    case ServiceManager.Repo.update(
           ChequeRequest.changeset(socket.assigns.cheque_request, params)
         ) do
      {:ok, _cheque_request} ->
        notify_parent()

        {:noreply,
         socket
         |> put_flash(:info, "Cheque request updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_cheque_request(socket, :new, params) do
    case ServiceManager.Repo.insert(ChequeRequest.changeset(%ChequeRequest{}, params)) do
      {:ok, _cheque_request} ->
        notify_parent()

        {:noreply,
         socket
         |> put_flash(:info, "Cheque request submitted successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent, do: send(self(), {__MODULE__, :saved})
end
