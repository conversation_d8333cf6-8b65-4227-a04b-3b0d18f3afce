defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.ReverseReasonComponent do
  use ServiceManagerWeb, :live_component
    alias ServiceManager.Transactions.WalletReversal
    alias ServiceManager.Transactions.WalletTransactions


  @impl true
  def render(assigns) do
    ~H"""
    <div >
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Reverse Transaction ID <%= @data.id %></h2>
        </div>
      </div>

      <form 
        phx-target={@myself}
        phx-submit="reverse">
        <div class="space-y-4 py-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Reason for Reversal</label>
            <div class="mt-1">
              <textarea
                name="reason"
                rows="3"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                required
              ></textarea>
            </div>
          </div>
        </div>
        <div class="mt-6 flex items-center justify-end gap-x-6">
          <button
            type="button" 
            class="text-sm font-semibold leading-6 text-gray-900"
            phx-click={JS.push("cancel_reversal")}
          >
            Cancel
          </button>
          <.button type="submit" phx-disable-with="Reversing..." class="text-sm font-semibold leading-6 text-white bg-indigo-600 hover:bg-indigo-700">
            Reverse Transaction
          </.button>
        </div>
      </form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  @impl true
  def handle_event("reverse", %{"reason" => reason}, socket) do
    IO.inspect(reason, label: "Reason for reversal")
    transaction = socket.assigns.data

    case WalletReversal.reverse_transaction(transaction, reason) do
      {:ok, _reversal} ->
        {:noreply,
         socket
         |> put_flash(:info, "Transaction reversed successfully")
         |> push_navigate(to: socket.assigns.return_to, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: socket.assigns.return_to, replace: true)}
    end
  end

  @impl true
  def handle_event("cancel_reversal", _, socket) do
    {:noreply, push_navigate(socket, to: socket.assigns.return_to, replace: true)}
  end
end
