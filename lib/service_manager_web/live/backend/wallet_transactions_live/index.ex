defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.WalletTransactionsContext, as: MainContext
  alias ServiceManager.Transactions.WalletTransactions, as: MainSchema
  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManager.Transactions.WalletReversal

  @url "/mobileBanking/WalletTransactions"

  import Ecto.Query
  import ServiceManagerWeb.Utilities.PermissionHelpers
  alias ServiceManager.Repo

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      Phoenix.PubSub.subscribe(ServiceManager.PubSub, "user:#{socket.assigns.current_user.id}")
    end

    socket =
      socket
      |> assign(:stats, get_transaction_stats())
      |> assign(:chart_data, get_chart_data())

    {:ok, socket}
  end

  defp get_chart_data do
    # Volume over time (last 7 days)
    volume_query =
      from t in MainSchema,
        group_by: fragment("DATE(inserted_at)"),
        order_by: fragment("DATE(inserted_at) DESC"),
        limit: 7,
        select: %{
          date: fragment("DATE(inserted_at)"),
          count: count(t.id)
        }

    volume_data = Repo.all(volume_query)

    # Status distribution
    status_query =
      from t in MainSchema,
        group_by: t.status,
        select: %{
          status: t.status,
          count: count(t.id)
        }

    status_data = Repo.all(status_query)

    # Amount distribution
    amount_ranges = [
      {0, 1000, "<1K"},
      {1000, 5000, "1K-5K"},
      {5000, 10000, "5K-10K"},
      {10000, 50000, "10K-50K"},
      {50000, 100_000, "50K-100K"},
      {100_000, 500_000, "100K-500K"},
      {500_000, 999_999_999, ">500K"}
    ]

    amount_data =
      Enum.map(amount_ranges, fn {min, max, label} ->
        count =
          Repo.one(
            from t in MainSchema,
              where: t.amount >= ^min and t.amount < ^max,
              select: count(t.id)
          )

        %{range: label, count: count}
      end)

    # Transaction types
    type_query =
      from t in MainSchema,
        group_by: t.type,
        select: %{
          type: t.type,
          count: count(t.id)
        }

    type_data = Repo.all(type_query)

    %{
      volume_data: volume_data |> Enum.reverse(),
      status_data: status_data,
      amount_data: amount_data,
      type_data: type_data
    }
  end

  defp get_transaction_stats do
    total_txns = Repo.aggregate(MainSchema, :count, :id)

    failed_txns_query = from(t in MainSchema, where: t.status == "failed")
    failed_txns = Repo.aggregate(failed_txns_query, :count, :id)
    failed_amount = Repo.aggregate(failed_txns_query, :sum, :amount) || Decimal.new(0)

    success_txns_query = from(t in MainSchema, where: t.status == "completed")
    success_txns = Repo.aggregate(success_txns_query, :count, :id)
    success_amount = Repo.aggregate(success_txns_query, :sum, :amount) || Decimal.new(0)

    processing_txns_query = from(t in MainSchema, where: t.status == "processing")
    processing_txns = Repo.aggregate(processing_txns_query, :count, :id)
    processing_amount = Repo.aggregate(processing_txns_query, :sum, :amount) || Decimal.new(0)

    [
      %{
        title: "Total Transactions",
        value: total_txns
      },
      %{
        title: "Failed Transactions",
        value: failed_txns,
        comparison: "MWK #{failed_amount}"
      },
      %{
        title: "Successful Transactions",
        value: success_txns,
        comparison: "MWK #{success_amount}"
      },
      %{
        title: "Processing Transactions",
        value: processing_txns,
        comparison: "MWK #{processing_amount}"
      }
    ]
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = MainContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:data, data)}
  end

  def handle_params(params, _url, socket) do
    data = MainContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:data, data)
     |> assign(pagination: pagination)}
  end

  @impl true
  def handle_info({:excel_ready, excel_data, filename}, socket) do
    IO.inspect([excel_data: excel_data, filename: filename],
      label: "========================FOR DOWNLOAD READY========================"
    )

    {:noreply,
     push_event(socket, "download-file", %{
       content: Base.encode64(excel_data),
       filename: filename,
       content_type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
     })}
  end

  def handle_info({:excel_error, message}, socket) do
    {:noreply, put_flash(socket, :error, message)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Beneficiary")
    |> assign(:data, MainContext.get_data!(id))
  end

  defp apply_action(socket, :reverse, %{"id" => id}) do
    data = MainContext.get_data!(id)
    socket
    |> assign(:page_title, "Reverse Transaction #{data.id}")
    |> assign(:data, data)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "Transfer")
    |> assign(:data, %MainSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Transactions")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    try do
      socket
      |> assign(:page_title, "Transaction Details")
      |> assign(:data, MainContext.get_data!(id))
    rescue
      Ecto.NoResultsError ->
        socket
        |> put_flash(:error, "Transaction not found")
        |> push_navigate(to: @url)
    end
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Transactions")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, nil)
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page_title, "Excel Export Transactions")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, "/mobileBankingReporting/WalletTransactions/ExcelExport")
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    data = MainContext.get_data!(id)
    {:ok, _} = MainContext.delete_data(data)

    {:noreply, stream_delete(socket, :data, data)}
  end


  def handle_event("toggle_status", %{"id" => id, "status" => status}, socket) do
    data = MainContext.get_data!(id)

    MainContext.update_data(
      data,
      %{"status" => toggle_state_status(status)},
      socket.assigns.current_user
    )
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> assign(:stats, get_transaction_stats())
         |> assign(:chart_data, get_chart_data())
         |> put_flash(:info, "Transaction status updated successfully")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: @url, replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def assess_acc(acc) do
    if is_nil(acc) do
      " - - "
    else
      "#{acc.first_name} #{acc.last_name}"
    end
  end

  def assess_number(acc) do
    if is_nil(acc) do
      " - - "
    else
      "#{acc.mobile_number}"
    end
  end
end
