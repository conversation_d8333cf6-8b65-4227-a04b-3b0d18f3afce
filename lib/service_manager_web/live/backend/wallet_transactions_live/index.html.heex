<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="transaction-stats"
  title=""
  stats={@stats}
/>

<br />
<hr />
<br />

<.header>
  Listing Wallet Transactions
</.header>

<.table
  id="dataID"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/WalletTransactions/filter"}
  export_url={~p"/mobileBanking/WalletTransactions/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.data}
  row_click={fn {_id, data} -> JS.patch(~p"/mobileBanking/WalletTransactions/#{data.id}") end}
>
  <:col :let={{_id, data}} filter_item="id" label="ID">
    <div class="text-[12px]"><%= data.id %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="reference" label="Reference">
    <div class="text-[12px] whitespace-nowrap"><%= data.reference %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="type" label="Type">
    <div class="text-[12px] text-center"><%= data.type %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="amount" label="Amount">
    <div class="text-[12px] text-right font-medium"><%= data.amount %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="sender_account" label="From Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.sender_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="receiver_account" label="To Account">
    <div class="text-[12px] whitespace-nowrap"><%= data.receiver_account %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="cbs_transaction_reference" label="CBS Reference">
    <div class="text-[12px] whitespace-nowrap"><%= data.cbs_transaction_reference %></div>
  </:col>
  <:col :let={{_id, data}} filter_item="status" label="Status">
    <.status_pill status={data.status} text={data.status} />
  </:col>
  <:col :let={{_id, data}} filter_item="inserted_at" label="Datetime">
    <div class="text-[12px] whitespace-nowrap"><%= data.inserted_at %></div>
  </:col>

  <:action :let={{id, data}}>
    <%= if can_update?(@current_user, :wallet_transactions) && data.status != "reversed" do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <.link
          patch={~p"/mobileBanking/WalletTransactions/#{data.id}/reverse"}
          class="block px-4 py-2 text-left text-sm text-orange-900 hover:bg-gray-10 hover:text-red-700"
        >
          Reverse
        </.link>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action == :show}
  id="transaction-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletTransactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletTransactionsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletTransactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletTransactionsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/WalletTransactions"}
  />
</.modal>

<.modal
  :if={@live_action in [:new, :edit]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletTransactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletTransactionsLive.FormComponent}
    id={@data.id || :new}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/transactions"}
  />
</.modal>

<.modal
  :if={@live_action == :reverse}
  id="reverse-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletTransactions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletTransactionsLive.ReverseReasonComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
    return_to={~p"/mobileBanking/WalletTransactions"}
  />
</.modal>

<!-- Charts Section -->
<div
  class="mt-8 grid grid-cols-2 gap-6"
  id="charts"
  phx-hook="Charts"
  data-chart-data={Jason.encode!(@chart_data)}
>
  <!-- Transaction Volume Over Time -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Wallet Transaction Volume Over Time</h3>
    <div id="volumeChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Status Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Status Distribution</h3>
    <div id="statusChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Amount Distribution -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Amount Distribution</h3>
    <div id="amountChart" class="w-full h-[300px]"></div>
  </div>
  <!-- Transaction Type Breakdown -->
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-semibold mb-4">Transaction Types</h3>
    <div id="typeChart" class="w-full h-[300px]"></div>
  </div>
</div>
<!-- ApexCharts Script -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts">
</script>
