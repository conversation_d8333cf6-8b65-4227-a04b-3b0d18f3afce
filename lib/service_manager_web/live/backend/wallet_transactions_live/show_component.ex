defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.ShowComponent do
  use ServiceManagerWeb, :live_component

  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Transaction Details</h2>
          <p class="text-sm text-gray-500">Transaction ID: <%= @data.id %></p>
        </div>
        <div class="flex items-center space-x-2">
          <div class={"w-3 h-3 rounded-full #{status_color(@data.status)}"} />
          <span class="text-sm font-medium"><%= @data.status %></span>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8">
        <!-- Transaction Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Transaction Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Amount</label>
              <p class="mt-1 text-lg font-semibold">MWK <%= Decimal.to_string(@data.amount) %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Transaction Type</label>
              <p class="mt-1"><%= @data.type || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Reference</label>
              <p class="mt-1"><%= @data.reference || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Description</label>
              <p class="mt-1"><%= @data.description || "--" %></p>
            </div>
          </div>
        </div>
        <!-- Account Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Sender Account</label>
              <p class="mt-1"><%= @data.sender_account || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Receiver Account</label>
              <p class="mt-1"><%= @data.receiver_account || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Opening Balance</label>
              <p class="mt-1">
                MWK <%= if @data.opening_balance,
                  do: Decimal.to_string(@data.opening_balance),
                  else: "--" %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Closing Balance</label>
              <p class="mt-1">
                MWK <%= if @data.closing_balance,
                  do: Decimal.to_string(@data.closing_balance),
                  else: "--" %>
              </p>
            </div>
          </div>
        </div>
        <!-- Additional Details -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Additional Details</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">CBS Reference</label>
              <p class="mt-1"><%= @data.cbs_transaction_reference || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">External Reference</label>
              <p class="mt-1"><%= @data.external_reference || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Value Date</label>
              <p class="mt-1"><%= @data.value_date || "--" %></p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Callback Status</label>
              <div class="mt-2 flex items-center space-x-2">
                <div class={"w-2 h-2 rounded-full #{callback_status_color(@data.callback_status)}"} />
                <span class="text-sm"><%= @data.callback_status || "--" %></span>
              </div>
            </div>
          </div>
        </div>
        <!-- System Information -->
        <div class="space-y-6">
          <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">System Information</h3>
          <div class="space-y-4">
            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1">
                <%= @data.inserted_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Last Updated</label>
              <p class="mt-1">
                <%= @data.updated_at
                |> to_string
                |> String.replace("T", " ")
                |> String.replace("Z", "") %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Credit Amount</label>
              <p class="mt-1">
                MWK <%= if @data.credit_amount,
                  do: Decimal.to_string(@data.credit_amount),
                  else: "0.00" %>
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-500">Debit Amount</label>
              <p class="mt-1">
                MWK <%= if @data.debit_amount, do: Decimal.to_string(@data.debit_amount), else: "0.00" %>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp status_color(status) do
    case status do
      "completed" -> "bg-green-500"
      "pending" -> "bg-yellow-500"
      "failed" -> "bg-red-500"
      "processing" -> "bg-blue-500"
      _ -> "bg-gray-500"
    end
  end

  defp callback_status_color(status) do
    case status do
      "success" -> "bg-green-500"
      "pending" -> "bg-yellow-500"
      "failed" -> "bg-red-500"
      _ -> "bg-gray-500"
    end
  end
end
