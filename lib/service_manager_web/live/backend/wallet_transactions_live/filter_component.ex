defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.FilterComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Schemas.Embedded.EmbeddedForm

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter transactions</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:start_date]} type="date" label="Start Date" />
            <.input field={@form[:end_date]} type="date" label="End Date" />
            <.input
              field={@form[:type]}
              type="select"
              label="Type"
              prompt=""
              options={["credit", "debit", "transfer"]}
            />
            <.input field={@form[:amount]} type="number" label="Amount" step="0.01" />
            <.input field={@form[:credit_amount]} type="number" label="Credit Amount" step="0.01" />
            <.input field={@form[:debit_amount]} type="number" label="Debit Amount" step="0.01" />
            <.input field={@form[:description]} type="text" label="Description" />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={["pending", "completed", "failed"]}
            />
            <.input field={@form[:reference]} type="text" label="Reference" />
            <.input field={@form[:value_date]} type="date" label="Value Date" />
            <.input field={@form[:opening_balance]} type="number" label="Opening Balance" step="0.01" />
            <.input field={@form[:closing_balance]} type="number" label="Closing Balance" step="0.01" />
            <.input field={@form[:first_name]} type="text" label="First Name" />
            <.input field={@form[:last_name]} type="text" label="Last Name" />
            <.input field={@form[:mobile_number]} type="text" label="Mobile Number" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:start_date]} type="date" label="Start Date" />
            <.input field={@form[:end_date]} type="date" label="End Date" />
            <.input
              field={@form[:type]}
              type="select"
              label="Type"
              prompt=""
              options={["credit", "debit", "transfer"]}
            />
            <.input field={@form[:amount]} type="number" label="Amount" step="0.01" />
            <.input field={@form[:credit_amount]} type="number" label="Credit Amount" step="0.01" />
            <.input field={@form[:debit_amount]} type="number" label="Debit Amount" step="0.01" />
            <.input field={@form[:description]} type="text" label="Description" />
            <.input
              field={@form[:status]}
              type="select"
              label="Status"
              prompt=""
              options={["pending", "completed", "failed"]}
            />
            <.input field={@form[:reference]} type="text" label="Reference" />
            <.input field={@form[:value_date]} type="date" label="Value Date" />
            <.input field={@form[:opening_balance]} type="number" label="Opening Balance" step="0.01" />
            <.input field={@form[:closing_balance]} type="number" label="Closing Balance" step="0.01" />
            <.input field={@form[:first_name]} type="text" label="First Name" />
            <.input field={@form[:last_name]} type="text" label="Last Name" />
            <.input field={@form[:mobile_number]} type="text" label="Mobile Number" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedForm{}
    changeset = EmbeddedForm.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedForm.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  def assess_dest_account_and_email(%{"to_account" => to_account, "email" => email} = params),
    do: assess_account_holder(email, to_account)

  defp save_data(socket, _any, params) do
    query = filter_pagination_link(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/WalletTransactions#{query}", replace: true)}
  end

  def get_account(email) do
    GetAccounts.retrieve(email)
    |> case do
      {:ok, accounts} -> accounts
      _anything -> []
    end
  end

  def assess_account_holder(email, acc_no) do
    case GetAccounts.retrieve(email) do
      {:ok, accounts} ->
        Enum.any?(accounts, fn {_desc, account_number} -> account_number == acc_no end)

      error ->
        error
    end
  end

  def type_options do
    [
      {"Credit", "credit"},
      {"Debit", "debit"},
      {"Transfer", "transfer"},
      {"Deposit", "deposit"},
      {"Withdrawal", "withdrawal"}
    ]
  end

  def status_options do
    [
      {"Pending", "pending"},
      {"Completed", "completed"},
      {"Failed", "failed"},
      {"Cancelled", "cancelled"},
      {"Processing", "processing"},
      {"Rejected", "rejected"}
    ]
  end
end
