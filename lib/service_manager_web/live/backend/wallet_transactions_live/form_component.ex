defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Transactions.Transaction, as: MainSchema
  alias ServiceManager.Schemas.Embedded.Transactions, as: MainContext
  alias ServiceManager.Services.GetAccounts
  alias ServiceManager.Services.TransferService

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Make fund transfer.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="main-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
          <.input
            field={@form[:from_account]}
            type="select"
            options={@accounts}
            prompt=""
            label="Choose an account to be Debited"
          />
          <.input field={@form[:amount]} type="number" label="Amount" />
          <.input field={@form[:to_account]} type="text" label="Reciepient Account Number" />
          <.input field={@form[:email]} type="email" label="Reciepints Email address" />
        </div>
        <div class="grid grid-cols-1 md:grid-cols-1 gap-5">
          <.input field={@form[:description]} type="text" label="Narration" />
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Beneficiary</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: data, current_user: current_user} = assigns, socket) do
    accounts = get_account(current_user.email)

    {:ok,
     socket
     |> assign(:accounts, accounts)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(MainContext.change_data())
     end)}
  end

  @impl true
  def handle_event("validate", %{"transactions" => params}, socket) do
    changeset = MainContext.change_data(socket.assigns.data, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"transactions" => params}, socket) do
    if assess_dest_account_and_email(params) do
      save_data(socket, socket.assigns.action, params)
    else
      {:noreply,
       socket
       |> put_flash(:error, "Destination Account number is not mapped to destination user")
       |> push_navigate(to: socket.assigns.patch, replace: true)}
    end
  end

  def assess_dest_account_and_email(%{"to_account" => to_account, "email" => email} = params),
    do: assess_account_holder(email, to_account)

  defp save_data(socket, _any, params) do
    TransferService.process_transfer(params)
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Trasfer was completed successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: socket.assigns.patch, replace: true)}
    end
  end

  def get_account(email) do
    GetAccounts.retrieve(email)
    |> case do
      {:ok, accounts} -> accounts
      _anything -> []
    end
  end

  def assess_account_holder(email, acc_no) do
    case GetAccounts.retrieve(email) do
      {:ok, accounts} ->
        Enum.any?(accounts, fn {_desc, account_number} -> account_number == acc_no end)

      error ->
        error
    end
  end
end
