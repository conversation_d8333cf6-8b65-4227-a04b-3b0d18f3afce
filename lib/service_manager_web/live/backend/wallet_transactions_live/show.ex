defmodule ServiceManagerWeb.Backend.WalletTransactionsLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManagerWeb.Backend.WalletTransactionsLive.ShowComponent
  alias ServiceManager.Contexts.WalletTransactionsContext, as: Context
  alias ServiceManager.Repo

  def mount(%{"id" => id}, _session, socket) do
    case Context.get_data(id) do
      nil ->
        {:ok,
         socket
         |> put_flash(:error, "Transaction not found")
         |> push_navigate(to: ~p"/mobileBanking/WalletTransactions")}

      transaction ->
        transaction = Repo.preload(transaction, [:from_account, :to_account])
        {:ok, assign(socket, transaction: transaction)}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="mb-6 flex justify-between items-center">
        <.link
          navigate={~p"/mobileBanking/WalletTransactions"}
          class="flex items-center text-gray-600 hover:text-gray-900"
        >
          <.icon name="hero-arrow-left-solid" class="w-4 h-4 mr-2" /> Back to Transactions
        </.link>
      </div>

      <.live_component module={ShowComponent} id="transaction-details" data={@transaction} />
    </div>
    """
  end
end
