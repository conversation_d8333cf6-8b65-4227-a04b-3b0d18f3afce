defmodule ServiceManagerWeb.Backend.RolesAndPermissionLive.FormComponent do
  @moduledoc """
  LiveComponent for managing roles and permissions form.
  Handles form updates, validation, and saving of roles and permissions.
  """

  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.RolesAndPermissionsContext

  @impl true
  def update(%{roles_and_permission: roles_and_permission} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(RolesAndPermissionsContext.change_roles_and_permission(roles_and_permission))
     end)}
  end

  @impl true
  def handle_event(
        "validate",
        %{
          "_target" => ["roles_and_permission", "rights", section, all_field],
          "roles_and_permission" => roles_and_permission_params
        },
        socket
      ) do
    # Handle validation when toggling all rights for a section
    updated_params = toggle_rights(roles_and_permission_params, section, all_field)

    changeset =
      RolesAndPermissionsContext.change_roles_and_permission(
        socket.assigns.roles_and_permission,
        updated_params
      )

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("validate", %{"roles_and_permission" => roles_and_permission_params}, socket) do
    # Handle general form validation
    changeset =
      RolesAndPermissionsContext.change_roles_and_permission(
        socket.assigns.roles_and_permission,
        roles_and_permission_params
      )

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  @impl true
  def handle_event("save", %{"roles_and_permission" => roles_and_permission_params}, socket) do
    save_roles_and_permission(socket, socket.assigns.action, roles_and_permission_params)
  end

  @doc """
  Saves the roles and permissions for editing an existing record.
  """
  defp save_roles_and_permission(socket, :edit, roles_and_permission_params) do
    case RolesAndPermissionsContext.update_roles_and_permission(
           socket.assigns.roles_and_permission,
           roles_and_permission_params
         ) do
      {:ok, roles_and_permission} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "User role and permission updated successfully",
              details: inspect(roles_and_permission)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Roles and permission updated successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "User role and permission update failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  @doc """
  Saves the roles and permissions for creating a new record.
  """
  defp save_roles_and_permission(socket, :new, roles_and_permission_params) do
    case RolesAndPermissionsContext.create_roles_and_permission(roles_and_permission_params) do
      {:ok, roles_and_permission} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "User role and permission created successfully",
              details: inspect(roles_and_permission)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Roles and permission created successfully")
         |> push_navigate(to: socket.assigns.patch, replace: true)}

      {:error, %Ecto.Changeset{} = changeset} ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "User role and permission create failed",
              error: inspect(changeset)
            },
            socket
          )
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  @doc """
  Toggles rights based on the section and field being changed.
  """
  defp toggle_rights(params, section, all_field) do
    if String.ends_with?(all_field, "_all") do
      toggle_all_rights(params, section, all_field)
    else
      update_all_checkbox(params, section)
    end
  end

  @doc """
  Toggles all rights for a specific section when the "all" checkbox is clicked.
  """
  defp toggle_all_rights(params, section, all_field) do
    all_value = params["rights"][section][all_field] == "true"
    rights = get_rights_for_group(section)
    [final_key, ""] = String.split(all_field, "_all", parts: 2)

    # Update all rights in the section to match the "all" checkbox
    updated_rights =
      Enum.reduce(rights, %{}, fn right, acc ->
        Map.put(acc, Atom.to_string(right), all_value)
      end)
      |> IO.inspect(label: "Updated rights")

    updated_section = Map.merge(params["rights"][section], updated_rights)
    updated_section = Map.put(updated_section, all_field, all_value)

    put_in(params, ["rights", section, final_key], updated_section)
  end

  @doc """
  Updates the "all" checkbox based on the state of individual rights checkboxes.
  """
  defp update_all_checkbox(params, section) do
    rights = get_rights_for_group(section)
    section_params = params["rights"][section]

    # Check if all individual rights are selected
    all_checked =
      Enum.all?(rights, fn right -> section_params[Atom.to_string(right)] == "true" end)

    updated_section = Map.put(section_params, "#{section}_all", all_checked)

    put_in(params, ["rights", section], updated_section)
  end

  @doc """
  Returns a list of all possible rights for any group.
  """
  defp get_rights_for_group(_group) do
    ~w(
       index view 
      )a
  end
end
