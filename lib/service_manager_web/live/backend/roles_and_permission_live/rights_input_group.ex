defmodule ServiceManagerWeb.Backend.RolesAndPermissionLive.RightsInputGroup do
  use Phoenix.Component
  import Phoenix.HTML.Form
  import ServiceManagerWeb.CoreComponents

  def rights_input_group(assigns) do
    ~H"""
    <div>
      <div class="flex items-center space-x-4 mb-2 cursor-pointer">
        <span class="font-semibold"><%= @title %></span>
        <.input
          field={@all_field}
          type="checkbox"
          label=""
          class="inline-flex items-center justify-end"
          phx-click="toggle_all"
          phx-value-group={@title}
        />
      </div>
      <.inputs_for :let={b} field={@field}>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5 border p-2 rounded">
          <%= for {right, label} <- @rights do %>
            <.input
              field={b[right]}
              type="checkbox"
              label={label}
              class="child-checkbox"
              data-parent={@title}
              phx-click="toggle_child"
              phx-value-right={right}
              phx-value-group={@title}
            />
          <% end %>
        </div>
      </.inputs_for>
    </div>
    """
  end

  # ... any other existing functions ...
end
