defmodule ServiceManagerWeb.Backend.RolesAndPermissionLive.Index do
  @moduledoc """
  LiveView module for managing roles and permissions in the backend.
  This module handles listing, creating, editing, and deleting roles and permissions.
  """

  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.RolesAndPermissionsContext, as: MainContext
  alias ServiceManager.Schemas.RolesAndPermission
  alias ServiceManager.Services.Security.Authorization

  # Import permission helper functions
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/ruserRoles&permissions"

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :current_path, @url)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    # Retrieve data based on params and generate pagination details
    data = MainContext.retrieve(params)
    pagination = generate_pagination_details(data)

    # Remove existing streams from socket assigns
    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:roles_and_permissions, data)}
  end

  def handle_params(params, _url, socket) do
    # Retrieve all data and generate pagination details
    data = MainContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:roles_and_permissions, data)
     |> assign(pagination: pagination)}
  end

  # Apply different actions based on the live_action
  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Roles and permission")
    |> assign(:roles_and_permission, MainContext.get_roles_and_permission!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Roles and permission")
    |> assign(:roles_and_permission, %RolesAndPermission{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Roles and permissions")
    |> assign(:roles_and_permission, nil)
  end

  @impl true
  def handle_info(
        {ServiceManagerWeb.RolesAndPermissionLive.FormComponent, {:saved, roles_and_permission}},
        socket
      ) do
    # Handle the saved event from the form component
    {:noreply, stream_insert(socket, :roles_and_permissions, roles_and_permission)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    # Delete a roles and permission entry
    roles_and_permission = RolesAndPermissionsContext.get_roles_and_permission!(id)
    {:ok, _} = RolesAndPermissionsContext.delete_roles_and_permission(roles_and_permission)

    {:noreply, stream_delete(socket, :roles_and_permissions, roles_and_permission)}
  end

  # Commented out toggle_status event handler
  # def handle_event("toggle_status", %{"id" => id, "status" => status}, socket) do
  #   data = MainContext.get_data!(id)

  #   MainContext.update_data(data, %{"status" => toggle_state_status(status)}, socket.assigns.current_user)
  #   |> case  do
  #     {:ok, _resp} ->

  #       {:noreply,
  #        socket
  #        |> put_flash(:info, "Beneficiary updated successfully")
  #        |> push_navigate(to: @url, replace: true)}

  #     {:error, error} ->
  #       {:noreply,
  #        socket
  #        |> put_flash(:error, error)
  #        |> push_navigate(to: @url, replace: true)}
  #   end
  # end

  @doc """
  Handles the search event.
  Updates the filter params and redirects to the new URL with search parameters.
  """
  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_redirect(to: "#{@url}#{endpoint}", replace: true)}
  end

  @doc """
  Handles the page size change event.
  Updates the filter params and redirects to the new URL with updated page size.
  """
  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_redirect(to: "#{@url}#{endpoint}", replace: true)}
  end

  # Permission helper functions for roles and permissions management
  defp can_create_roles?(user), do: can_create?(user, :roles_and_permissions)
  defp can_update_roles?(user), do: can_update?(user, :roles_and_permissions)
  defp can_delete_roles?(user), do: can_delete?(user, :roles_and_permissions)
  defp can_view_roles?(user), do: can_view?(user, :roles_and_permissions)
  defp has_any_roles_action_permission?(user), do: has_any_permission?(user, [:view, :update, :delete], :roles_and_permissions)
end
