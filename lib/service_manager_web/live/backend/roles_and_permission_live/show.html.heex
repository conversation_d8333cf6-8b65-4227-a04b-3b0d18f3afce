<.header>
  Roles and permission <%= @roles_and_permission.id %>
  <:subtitle>This is a roles_and_permission record from your database.</:subtitle>
  <:actions>
    <%= if can_update?(@current_user, :roles_and_permissions) do %>
      <.link
        patch={~p"/mobileBanking/ruserRoles&permissions/#{@roles_and_permission}/show/edit"}
        phx-click={JS.push_focus()}
      >
        <.button>Edit roles_and_permission</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.list>
  <:item title="Name"><%= @roles_and_permission.name %></:item>
  <:item title="Rights">Complex permission structure (view in edit mode for details)</:item>
  <:item title="Created by"><%= @roles_and_permission.created_by %></:item>
  <:item title="Updated by"><%= @roles_and_permission.updated_by %></:item>
  <:item title="Status">
    <.status_pill status={@roles_and_permission.status == "active"} text={String.capitalize(@roles_and_permission.status)} />
  </:item>
</.list>

<.back navigate={~p"/mobileBanking/ruserRoles&permissions"}>Back to roles_and_permissions</.back>

<.modal
  :if={@live_action == :edit}
  id="roles_and_permission-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/ruserRoles&permissions/#{@roles_and_permission}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.RolesAndPermissionLive.FormComponent}
    id={@roles_and_permission.id}
    title={@page_title}
    action={@live_action}
    roles_and_permission={@roles_and_permission}
    patch={~p"/mobileBanking/ruserRoles&permissions/#{@roles_and_permission}"}
  />
</.modal>
