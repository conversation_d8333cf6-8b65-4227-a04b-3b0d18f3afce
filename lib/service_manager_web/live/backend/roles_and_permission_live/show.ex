defmodule ServiceManagerWeb.Backend.RolesAndPermissionLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.RolesAndPermissionsContext

  # Import permission helper functions
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket, :current_path, Phoenix.LiveView.get_connect_params(socket)["current_path"])

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:roles_and_permission, RolesAndPermissionsContext.get_roles_and_permission!(id))}
  end

  defp page_title(:show), do: "Show Roles and permission"
  defp page_title(:edit), do: "Edit Roles and permission"
end
