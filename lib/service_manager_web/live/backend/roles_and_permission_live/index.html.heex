



<.header>
  Listing Roles and permissions
  <:actions>
    <%= if can_create?(@current_user, :roles_and_permissions) do %>
      <.link patch={~p"/mobileBanking/ruserRoles&permissions/new"}>
        <.button>New Roles and permission</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="roles_and_permissions"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  rows={@streams.roles_and_permissions}
  row_click={
    fn {_id, roles_and_permission} ->
      cond do
        can_update?(@current_user, :roles_and_permissions) ->
          JS.navigate(~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}/edit")
        can_view?(@current_user, :roles_and_permissions) ->
          JS.navigate(~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}")
        true ->
          JS.navigate("#")
      end
    end
  }
>
  <:col :let={{_id, roles_and_permission}} filter_item="id" label="ID">
    <%= roles_and_permission.id %>
  </:col>
  <:col :let={{_id, roles_and_permission}} filter_item="name" label="Name">
    <%= roles_and_permission.name %>
  </:col>
  <:col :let={{_id, roles_and_permission}} filter_item="id" label="Status">
    <.status_pill status={roles_and_permission.status} text={roles_and_permission.status} />
  </:col>

  <:action :let={{id, roles_and_permission}}>
    <%= if has_any_permission?(@current_user, [:view, :update, :delete], :roles_and_permissions) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
        <%= if can_update?(@current_user, :roles_and_permissions) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            patch={~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}/edit"}
          >
            Edit
          </.link>
        <% end %>
        <%= if can_view?(@current_user, :roles_and_permissions) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            navigate={~p"/mobileBanking/ruserRoles&permissions/#{roles_and_permission}"}
          >
            View Details
          </.link>
        <% end %>
        <%= if can_delete?(@current_user, :roles_and_permissions) do %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={JS.push("delete", value: %{id: roles_and_permission.id})}
            data-confirm="Are you sure?"
          >
            Delete
          </.link>
        <% end %>
      </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="roles_and_permission-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/ruserRoles&permissions")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.RolesAndPermissionLive.FormComponent}
    id={@roles_and_permission.id || :new}
    title={@page_title}
    action={@live_action}
    roles_and_permission={@roles_and_permission}
    patch={~p"/mobileBanking/ruserRoles&permissions"}
  />
</.modal>