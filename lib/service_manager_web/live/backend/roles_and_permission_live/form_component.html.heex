<div>
  <.header>
    <%= @title %>
    <:subtitle>Use this form to manage roles_and_permission records in your database.</:subtitle>
  </.header>

  <.simple_form
    for={@form}
    id="roles_and_permission-form"
    phx-target={@myself}
    phx-change="validate"
    phx-submit="save"
  >
    <.input field={@form[:name]} type="text" label="Name" />

    <.inputs_for :let={a} field={@form[:rights]}>
      <.collapsible_section
        title="Backend Permissions"
        phx-click="toggle_section"
        title_class="text-xl text-blue-600"
      >
        <.inputs_for :let={y} field={a[:backend]} type="checking">
          <.collapsible_section
            title="Defaults"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                phx-target={@myself}
                title="Currency"
                field={y[:currency]}
                all_field={y[:currency_all]}
                rights={[
                  index: "View",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Currencies"
                field={y[:currencies]}
                all_field={y[:currencies_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Exchange Rates"
                field={y[:exchange_rate]}
                all_field={y[:exchange_rate_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Fees & Charges"
                field={y[:fees]}
                all_field={y[:fees_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Virtual Cards APIs"
                field={y[:virtual_cards]}
                all_field={y[:virtual_cards_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Virtual Cards API Configs"
                field={y[:virtual_cards_api_configs]}
                all_field={y[:virtual_cards_api_configs_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Paylink APIs"
                field={y[:paylink]}
                all_field={y[:paylink_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  delete: "Delete",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "Download"
                ]}
              />
              <.rights_input_group
                title="Gift Card APIs"
                field={y[:gift_cards]}
                all_field={y[:gift_cards_all]}
                rights={[
                  index: "View",
                  update: "Update",
                  delete: "Delete",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "Download"
                ]}
              />
              <.rights_input_group
                title="Payment Methods"
                field={y[:payment_methods]}
                all_field={y[:payment_methods_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Exchange Rates"
                field={y[:exchange_rates]}
                all_field={y[:exchange_rates_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Cards Management"
                field={y[:cards]}
                all_field={y[:cards_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  activate: "Activate",
                  debug: "Debug"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Loan Management"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Loan Products"
                field={y[:loan_products]}
                all_field={y[:loan_products_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Loan Partnerships"
                field={y[:loan_partnership]}
                all_field={y[:loan_partnership_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Upload Products"
                field={y[:loan_upload_products]}
                all_field={y[:loan_upload_products_all]}
                rights={[
                  index: "View",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Loan Customers"
                field={y[:loan_customers]}
                all_field={y[:loan_customers_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Upload Customers"
                field={y[:loan_upload_customers]}
                all_field={y[:loan_upload_customers_all]}
                rights={[
                  index: "View",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Customer Batches"
                field={y[:loan_customers_batches]}
                all_field={y[:loan_customers_batches_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Loan Reports"
                field={y[:loan_reports]}
                all_field={y[:loan_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  download: "Download",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Transactions"
                field={y[:loan_transactions]}
                all_field={y[:loan_transactions_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Interface Panel"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="User Management"
                field={y[:users]}
                all_field={y[:users_all]}
                rights={[
                  index: "View",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Roles & Permissions"
                field={y[:roles_and_permissions]}
                all_field={y[:roles_and_permissions_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Agent Management"
                field={y[:agents]}
                all_field={y[:agents_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Merchant Managent"
                field={y[:merchants]}
                all_field={y[:merchants_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Customer Management"
                field={y[:customers]}
                all_field={y[:customers_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Beneficiaries"
                field={y[:beneficiaries]}
                all_field={y[:beneficiaries_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  delete: "Delete",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "Download"
                ]}
              />
              <.rights_input_group
                title="Accounts"
                field={y[:accounts]}
                all_field={y[:accounts_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Wallets"
                field={y[:wallets]}
                all_field={y[:wallets_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  activate: "Activate",
                  approve: "Approve",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Wallet Transactions"
                field={y[:wallet_transactions]}
                all_field={y[:wallet_transactions_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Wallet Tiers"
                field={y[:wallet_tiers]}
                all_field={y[:wallet_tiers_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Loan Management"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Loan Partnerships"
                field={y[:loan_partnerships]}
                all_field={y[:loan_partnerships_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  activate: "Activate",
                  delete: "Delete",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Products"
                field={y[:loan_products]}
                all_field={y[:loan_products_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Customers"
                field={y[:loan_customers]}
                all_field={y[:loan_customers_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Transactions"
                field={y[:loan_transactions]}
                all_field={y[:loan_transactions_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Reports"
                field={y[:loan_reports]}
                all_field={y[:loan_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Loan Charges"
                field={y[:loan_charges]}
                all_field={y[:loan_charges_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Requests & Operations"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Cheque Requests"
                field={y[:cheque_requests]}
                all_field={y[:cheque_requests_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  approve: "Approve",
                  review: "Review",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Cheque Book Requests"
                field={y[:cheque_book_requests]}
                all_field={y[:cheque_book_requests_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  delete: "Delete",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Fund Requests"
                field={y[:fund_requests]}
                all_field={y[:fund_requests_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  approve: "Approve",
                  review: "Review",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Cardless Withdraws"
                field={y[:cardless_withdraws]}
                all_field={y[:cardless_withdraws_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  approve: "Approve",
                  review: "Review",
                  debug: "Debug"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Settings"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Web Settings"
                field={y[:web_settings]}
                all_field={y[:web_settings_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="Application Settings"
                field={y[:application_settings]}
                all_field={y[:application_settings_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="APIs Settings"
                field={y[:apis_settings]}
                all_field={y[:apis_settings_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="System Configurations"
                field={y[:system_configurations]}
                all_field={y[:system_configurations_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="System Users"
                field={y[:system_users]}
                all_field={y[:system_users_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
              <.rights_input_group
                title="IP Whitelist"
                field={y[:ip_whitelist]}
                all_field={y[:ip_whitelist_all]}
                rights={[
                  index: "View",
                  create: "Create",
                  update: "Update",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="SMS Logs"
                field={y[:sms_logs]}
                all_field={y[:sms_logs_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Callbacks"
                field={y[:callbacks]}
                all_field={y[:callbacks_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Dynamic Forms"
                field={y[:dynamic_forms]}
                all_field={y[:dynamic_forms_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  debug: "Debug"
                ]}
              />
              <.rights_input_group
                title="Mobile Forms"
                field={y[:mobile_forms]}
                all_field={y[:mobile_forms_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  create: "Create",
                  update: "Update",
                  debug: "Debug",
                  delete: "Delete"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Reports"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Transactions"
                field={y[:transactions]}
                all_field={y[:transactions_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Customers Reports"
                field={y[:customer_reports]}
                all_field={y[:customer_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Agents Reports"
                field={y[:agent_reports]}
                all_field={y[:agent_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Village Banking"
                field={y[:village_banking_reports]}
                all_field={y[:village_banking_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Merchants"
                field={y[:merchants_reports]}
                all_field={y[:merchants_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Wallets"
                field={y[:wallets_reports]}
                all_field={y[:wallets_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
              <.rights_input_group
                title="Accounts"
                field={y[:accounts_reports]}
                all_field={y[:accounts_reports_all]}
                rights={[
                  index: "View",
                  view: "View Details",
                  update: "Update",
                  review: "Review",
                  approve: "Approve",
                  activate: "Activate",
                  debug: "Debug",
                  download: "DOwnload"
                ]}
              />
            </div>
          </.collapsible_section>

          <.collapsible_section
            title="Miscellaneous"
            phx-click="toggle_section"
            title_class="text-lg text-blue-600"
          >
            <div class="grid grid-cols-2 gap-5">
              <.rights_input_group
                title="Dashboard"
                field={y[:miscellaneous]}
                all_field={y[:miscellaneous_all]}
                rights={[index: "View", view: "View Details"]}
              />
            </div>
          </.collapsible_section>
        </.inputs_for>
      </.collapsible_section>

      <.collapsible_section
        title="Agents Permissions"
        phx-click="toggle_section"
        title_class="text-xl text-blue-600"
      >
      </.collapsible_section>

      <.collapsible_section
        title="Merchants Permissions"
        phx-click="toggle_section"
        title_class="text-xl text-blue-600"
      >
      </.collapsible_section>

      <.collapsible_section
        title="Village Banking Permissions"
        phx-click="toggle_section"
        title_class="text-xl text-blue-600"
      >
      </.collapsible_section>
    </.inputs_for>

    <:actions>
      <.button phx-disable-with="Saving...">Save Roles and permission</.button>
    </:actions>
  </.simple_form>
</div>
