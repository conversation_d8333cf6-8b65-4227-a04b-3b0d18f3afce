<div class="space-y-8">
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <.header>
        Function Tracking Logs
        <:subtitle>
          A detailed view of function calls, process information, and execution results.
        </:subtitle>
      </.header>

      <div class="flex items-center gap-4">
        <.button patch={~p"/mobileBanking/monitor/backend/logs/filter"}>
          <.icon name="hero-funnel" class="h-4 w-4 mr-2" />Filter
        </.button>
        <.button patch={~p"/mobileBanking/monitor/backend/logs/excel_export"}>
          <.icon name="hero-arrow-down-tray" class="h-4 w-4 mr-2" />Export
        </.button>
      </div>
    </div>
    <!-- Quick Filters -->
    <.form for={%{}} phx-submit="apply_filters" class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center gap-4">
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Module</label>
          <select
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            name="module"
            value={@filter_params["module_name"]}
          >
            <option value="">All Modules</option>
            <%= for module <- @modules do %>
              <option value={module} selected={@filter_params["module_name"] == module}>
                <%= module %>
              </option>
            <% end %>
          </select>
        </div>

        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Function</label>
          <select
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            name="function"
            value={@filter_params["function_name"]}
          >
            <option value="">All Functions</option>
            <%= for function <- @functions do %>
              <option value={function} selected={@filter_params["function_name"] == function}>
                <%= function %>
              </option>
            <% end %>
          </select>
        </div>

        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Chain ID</label>
          <select
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            name="chain_id"
            value={@filter_params["chain_id"]}
          >
            <option value="">All Chains</option>
            <%= for chain_id <- @chain_ids do %>
              <option value={chain_id} selected={@filter_params["chain_id"] == chain_id}>
                <%= chain_id %>
              </option>
            <% end %>
          </select>
        </div>

        <div class="flex items-end">
          <button
            type="submit"
            class="h-[38px] px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </.form>
  </div>

  <.live_component
    module={ServiceManagerWeb.Components.StatsComponent}
    id="log-stats"
    title=""
    stats={@stats}
  />

  <.table
    id="dataID"
    filter_params={@filter_params}
    pagination={@pagination}
    selected_column={@selected_column}
    filter_url={~p"/mobileBanking/monitor/backend/logs/filter"}
    export_url={~p"/mobileBanking/monitor/backend/logs/ExcelExportFilter"}
    show_filter={true}
    show_export={true}
    rows={@streams.data}
    row_click={
      fn {_id, data} -> JS.patch(~p"/mobileBanking/monitor/backend/logs/#{data}/show") end
    }
  >
    <:col :let={{_id, data}} filter_item="id" label="ID">
      <div class="text-[12px]"><%= data.id %></div>
    </:col>
    <:col :let={{_id, data}} filter_item="id" label="Age">
      <%= if (age(data.inserted_at |> to_string(), 1000)) == "new" do %>
        <span class="inline-flex items-center gap-x-1 rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
          <svg class="h-1.5 w-1.5 fill-green-500" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
          </svg>
          New
        </span>
      <% else %>
        <span class="inline-flex items-center gap-x-1 rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600">
          <svg class="h-1.5 w-1.5 fill-gray-400" viewBox="0 0 6 6" aria-hidden="true">
            <circle cx="3" cy="3" r="3" />
          </svg>
          Old
        </span>
      <% end %>
    </:col>
    <:col :let={{_id, data}} filter_item="chain_id" label="Chain ID">
      <div class="text-[12px] whitespace-nowrap font-mono"><%= data.chain_id %></div>
    </:col>
    <:col :let={{_id, data}} filter_item="function_name" label="Function">
      <div class="text-[12px] whitespace-nowrap font-medium"><%= data.function_name %></div>
    </:col>
    <:col :let={{_id, data}} filter_item="module_name" label="Module">
      <div class="text-[12px] text-center text-gray-600"><%= data.module_name %></div>
    </:col>
    <:col :let={{_id, data}} filter_item="process_id" label="Process ID">
      <div class="text-[12px] font-mono text-purple-600"><%= data.process_id %></div>
    </:col>
    <:col :let={{_id, data}} filter_item="inserted_at" label="Datetime">
      <div class="text-[12px] whitespace-nowrap text-gray-500"><%= data.inserted_at %></div>
    </:col>
    <:action :let={{_id, data}}>
      <div class="flex items-center gap-2">
        <.link patch={~p"/backend/logs/#{data}/show"} class="text-zinc-500 hover:text-zinc-600">
          <.icon name="hero-eye" class="h-5 w-5" />
        </.link>
      </div>
    </:action>
  </.table>
  <!-- Charts Section -->
  <div
    class="mt-8 space-y-6"
    id="charts"
    phx-hook="LogCharts"
    data-chart-data={Jason.encode!(@chart_data)}
  >
    <!-- Log Volume Over Time -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-semibold mb-4">Log Volume Over Time</h3>
      <div id="volumeChart" class="w-full h-[300px]"></div>
    </div>

    <div class="grid grid-cols-2 gap-6">
      <!-- Function Distribution -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Function Distribution</h3>
        <div id="functionChart" class="w-full h-[300px]"></div>
      </div>
      <!-- Module Distribution -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">Module Distribution</h3>
        <div id="moduleChart" class="w-full h-[300px]"></div>
      </div>
    </div>
  </div>
</div>
<!-- ApexCharts Script -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts">
</script>

<.modal
  :if={@live_action == :show}
  id="log-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/monitor/backend/logs")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LogsLive.ShowComponent}
    id={@data.id}
    title={@page_title}
    data={@data}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/monitor/backend/logs")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.LogsLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    data={@data}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/monitor/backend/logs"}
  />
</.modal>
