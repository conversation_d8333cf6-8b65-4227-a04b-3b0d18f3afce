defmodule ServiceManagerWeb.Backend.LogsLive.FilterComponent do
  use ServiceManagerWeb, :live_component
  import Ecto.Query

  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManager.Repo

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Filter logs</:subtitle>
      </.header>

      <%= if @action == :filter do %>
        <.simple_form
          for={@form}
          id="filter-form"
          phx-target={@myself}
          phx-change="validate"
          phx-submit="save"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:start_date]} type="date" label="Start Date" />
            <.input field={@form[:end_date]} type="date" label="End Date" />
            <.input field={@form[:chain_id]} type="text" label="Chain ID" />
            <.input
              field={@form[:function_name]}
              type="select"
              label="Function"
              prompt=""
              options={function_options()}
            />
            <.input
              field={@form[:module_name]}
              type="select"
              label="Module"
              prompt=""
              options={module_options()}
            />
            <.input field={@form[:process_id]} type="text" label="Process ID" />
          </div>
          <:actions>
            <.button phx-disable-with="Filtering...">Apply Filters</.button>
          </:actions>
        </.simple_form>
      <% else %>
        <.simple_form for={@form} id="filter-form" action={@url} method="post">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <.input field={@form[:start_date]} type="date" label="Start Date" />
            <.input field={@form[:end_date]} type="date" label="End Date" />
            <.input field={@form[:chain_id]} type="text" label="Chain ID" />
            <.input
              field={@form[:function_name]}
              type="select"
              label="Function"
              prompt=""
              options={function_options()}
            />
            <.input
              field={@form[:module_name]}
              type="select"
              label="Module"
              prompt=""
              options={module_options()}
            />
            <.input field={@form[:process_id]} type="text" label="Process ID" />
          </div>
          <:actions>
            <.button phx-disable-with="Exporting...">Apply Filters and Export</.button>
          </:actions>
        </.simple_form>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{filter_params: filter_params} = assigns, socket) do
    form = %EmbeddedForm{}
    changeset = EmbeddedForm.change_form(form, filter_params)

    {:ok,
     socket
     |> assign(:filter_form, form)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset, as: "form")
     end)}
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    changeset = EmbeddedForm.change_form(socket.assigns.filter_form, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate, as: "form"))}
  end

  def handle_event("save", %{"form" => params}, socket) do
    save_data(socket, socket.assigns.action, params)
  end

  defp save_data(socket, _any, params) do
    query = "?" <> URI.encode_query(params)

    {:noreply,
     socket
     |> push_navigate(to: "/mobileBanking/monitor/backend/logs#{query}", replace: true)}
  end

  def function_options do
    # Get unique function names from the database
    query =
      from l in ServiceManager.Logging.LogEntry,
        distinct: true,
        select: l.function_name,
        order_by: l.function_name

    ServiceManager.Repo.all(query)
    |> Enum.map(fn name -> {name, name} end)
  end

  def module_options do
    # Get unique module names from the database
    query =
      from l in ServiceManager.Logging.LogEntry,
        distinct: true,
        select: l.module_name,
        order_by: l.module_name

    ServiceManager.Repo.all(query)
    |> Enum.map(fn name -> {name, name} end)
  end
end
