defmodule ServiceManagerWeb.Backend.LogsLive.Show do
  @moduledoc """
  LiveView module for displaying detailed information about individual system logs (T24 and Oban jobs).
  Provides functionality to view the full details of a specific log entry.
  """

  use ServiceManagerWeb, :live_view
  alias ServiceManager.Contexts.UniversalContext

  @impl true
  @doc """
  Initializes the LiveView socket with default assigns.
  """
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  @doc """
  Handles URL parameters and routes to the appropriate view based on live_action.
  """
  def handle_params(params, _, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  @doc """
  Handles displaying T24 log details.
  Fetches the specific T24 log record and assigns relevant data to socket.
  Redirects with error message if log is not found.
  """
  defp apply_action(socket, :t24, %{"id" => id}) do
    case fetch_record(ServiceManager.Pool.FinchRequest, id) do
      {:ok, record} ->
        socket
        |> assign(:title, "T24")
        |> assign(:page_title, "Show T24 Logs")
        |> assign(:record, record)
        |> assign(:return_to, "/mobileBanking/SystemLogs/t24")

      {:error, _} ->
        socket
        |> put_flash(:error, "Log not found")
        |> push_navigate(to: ~p"/backend/SystemLogs/t24")
    end
  end

  @doc """
  Handles displaying Oban job log details.
  Fetches the specific Oban job record and assigns relevant data to socket.
  Redirects with error message if log is not found.
  """
  defp apply_action(socket, :oban_jobs, %{"id" => id}) do
    case fetch_record(ServiceManager.Schemas.ObanJobs, id) do
      {:ok, record} ->
        socket
        |> assign(:title, "Oban Jobs")
        |> assign(:page_title, "Show Oban Jobs Logs")
        |> assign(:record, record)
        |> assign(:return_to, "/mobileBanking/SystemLogs/oban")

      {:error, _} ->
        socket
        |> put_flash(:error, "Log not found")
        |> push_navigate(to: ~p"/backend/SystemLogs/oban")
    end
  end

  @doc """
  Fetches a record from the database using the provided schema and ID.
  Returns {:ok, record} if found, {:error, :not_found} otherwise.
  """
  defp fetch_record(schema, id) do
    case UniversalContext.get_data!(schema, id) do
      nil -> {:error, :not_found}
      record -> {:ok, record}
    end
  end

  @impl true
  @doc """
  Renders the log details view with header and formatted record information.
  """
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %> Log Details
        <:subtitle>View detailed information about this <%= @title %> log entry</:subtitle>
        <:actions>
          <.link navigate={@return_to}>
            <.button>Back to <%= @title %> logs</.button>
          </.link>
        </:actions>
      </.header>

      <div class="mt-8 space-y-8">
        <.custom_list style={:grid} class="bg-gray-50 p-6" items={record_to_items(@record)} />
      </div>
    </div>
    """
  end

  @doc """
  Formats map values by converting them to pretty JSON strings.
  Falls back to inspect if JSON encoding fails.
  """
  defp format_value(value) when is_map(value) do
    Jason.encode!(value, pretty: true)
  rescue
    _ -> inspect(value)
  end

  @doc """
  Formats list values by converting each element and joining with commas.
  Handles nested maps within lists.
  """
  defp format_value(value) when is_list(value) do
    value
    |> Enum.map(fn
      v when is_map(v) -> format_value(v)
      v -> to_string(v)
    end)
    |> Enum.join(", ")
  rescue
    _ -> inspect(value)
  end

  # Format datetime values to readable strings
  defp format_value(%NaiveDateTime{} = dt), do: Calendar.strftime(dt, "%Y-%m-%d %H:%M:%S")

  # Convert any other value to string
  defp format_value(value), do: to_string(value)

  @doc """
  Converts a record struct to a list of {key, formatted_value} tuples.
  Drops metadata fields and humanizes keys for display.
  """
  defp record_to_items(record) do
    record
    |> Map.drop([:__meta__, :__struct__])
    |> Enum.map(fn {key, value} ->
      {Phoenix.Naming.humanize(key), format_value(value)}
    end)
  end
end
