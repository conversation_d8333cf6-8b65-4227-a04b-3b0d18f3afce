defmodule ServiceManagerWeb.Backend.LogsLive.Index do
  use ServiceManagerWeb, :live_view
  import Ecto.Query
  import ServiceManagerWeb.Components.Utilities.RecordAge
  import ServiceManagerWeb.Utilities.Sorting
  import ServiceManagerWeb.Utilities.Utils, only: [generate_pagination_details: 1]

  alias ServiceManager.Contexts.LogEntriesContext, as: MainContext
  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManager.Logging.LogEntry, as: MainSchema
  alias ServiceManager.Repo

  @url "/mobileBanking/monitor/backend/logs"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      |> assign(:stats, get_log_stats())
      |> assign(:chart_data, get_chart_data())
      |> assign(:filter_params, %{})
      |> assign(:pagination, %{})
      |> assign(:selected_column, nil)
      |> assign(:data, nil)
      |> assign(:url, nil)
      |> assign(:expanded_logs, %{})
      |> assign(:view_modes, %{})
      |> assign(:modules, MainContext.get_distinct_modules())
      |> assign(:chain_ids, MainContext.get_distinct_chain_ids())
      |> assign(:functions, MainContext.get_distinct_functions())

    {:ok, socket}
  end

  def handle_event("apply_filters", params, socket) do
    filter_params =
      socket.assigns.filter_params
      |> Map.put("module_name", params["module"])
      |> Map.put("function_name", params["function"])
      |> Map.put("chain_id", params["chain_id"])

    {_, endpoint} = encode_url(filter_params)

    %{
      entries: entries,
      page_size: page_size,
      page_number: page_number,
      total_entries: total_entries
    } = MainContext.retrieve(filter_params)

    {:noreply,
     socket
     |> assign(:filter_params, filter_params)
     |> assign(:pagination, %{
       page_number: page_number,
       page_size: page_size,
       total_entries: total_entries,
       total_pages: max(ceil(total_entries / page_size), 1)
     })
     |> stream(:data, entries, reset: true)
     |> push_patch(to: "#{@url}#{endpoint}", replace: true)}
  end

  defp get_chart_data do
    # Volume over time (last 7 days)
    volume_query =
      from t in MainSchema,
        group_by: fragment("DATE(inserted_at)"),
        order_by: fragment("DATE(inserted_at) DESC"),
        limit: 7,
        select: %{
          date: fragment("DATE(inserted_at)"),
          count: count(t.id)
        }

    volume_data = Repo.all(volume_query)

    # Function name distribution
    function_query =
      from t in MainSchema,
        group_by: t.function_name,
        select: %{
          function: t.function_name,
          count: count(t.id)
        }

    function_data = Repo.all(function_query)

    # Module name distribution
    module_query =
      from t in MainSchema,
        group_by: t.module_name,
        select: %{
          module: t.module_name,
          count: count(t.id)
        }

    module_data = Repo.all(module_query)

    %{
      volume_data: volume_data |> Enum.reverse(),
      function_data: function_data,
      module_data: module_data
    }
  end

  defp get_log_stats do
    total_logs = Repo.aggregate(MainSchema, :count, :id)

    today_logs_query =
      from(t in MainSchema,
        where: fragment("DATE(inserted_at) = CURRENT_DATE")
      )

    today_logs = Repo.aggregate(today_logs_query, :count, :id)

    last_hour_logs_query =
      from(t in MainSchema,
        where: t.inserted_at >= ago(1, "hour")
      )

    last_hour_logs = Repo.aggregate(last_hour_logs_query, :count, :id)

    unique_chains_query =
      from(t in MainSchema,
        select: count(fragment("DISTINCT chain_id"))
      )

    unique_chains = Repo.one(unique_chains_query)

    [
      %{
        title: "Total Logs",
        value: total_logs,
        comparison: "All Time"
      },
      %{
        title: "Today's Logs",
        value: today_logs,
        comparison: "Last 24 Hours"
      },
      %{
        title: "Last Hour",
        value: last_hour_logs,
        comparison: "Last 60 Minutes"
      },
      %{
        title: "Unique Chains",
        value: unique_chains,
        comparison: "Distinct Processes"
      }
    ]
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    %{
      entries: entries,
      page_size: page_size,
      page_number: page_number,
      total_entries: total_entries
    } = MainContext.retrieve(params)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(:pagination, %{
       page_number: page_number,
       page_size: page_size,
       total_entries: total_entries,
       total_pages: max(ceil(total_entries / page_size), 1)
     })
     |> stream(:data, entries, reset: true)}
  end

  def handle_params(params, _url, socket) do
    %{
      entries: entries,
      page_size: page_size,
      page_number: page_number,
      total_entries: total_entries
    } = MainContext.retrieve(params)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:pagination, %{
       page_number: page_number,
       page_size: page_size,
       total_entries: total_entries,
       total_pages: max(ceil(total_entries / page_size), 1)
     })
     |> stream(:data, entries, reset: true)}
  end

  def handle_event("page", %{"page" => page}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> Map.put("page", page)
      |> encode_url()

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  defp encode_url(params) do
    endpoint = "?" <> URI.encode_query(params)
    {params, endpoint}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Log Entry")
    |> assign(:data, MainContext.get_data!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Log Entry")
    |> assign(:data, %MainSchema{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Log Entries")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    socket
    |> assign(:page_title, "Log Entry Details")
    |> assign(:data, MainContext.get_data!(id))
  end

  defp apply_action(socket, :filter, _params) do
    socket
    |> assign(:page_title, "Filter Log Entries")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, nil)
  end

  defp apply_action(socket, :excel_export, _params) do
    socket
    |> assign(:page_title, "Excel Export Log Entries")
    |> assign(:data, %EmbeddedForm{})
    |> assign(:url, "/backend/logs/ExcelExport")
  end

  @impl true
  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    data = MainContext.get_data!(id)
    {:ok, _} = MainContext.delete_data(data)

    {:noreply, stream_delete(socket, :data, data)}
  end

  def handle_event("toggle_details", %{"id" => id}, socket) do
    id = String.to_integer(id)
    expanded_logs = Map.get(socket.assigns, :expanded_logs, %{})
    expanded_logs = Map.put(expanded_logs, id, !Map.get(expanded_logs, id, false))
    {:noreply, assign(socket, :expanded_logs, expanded_logs)}
  end

  def handle_event("toggle_view_mode", %{"id" => id}, socket) do
    id = String.to_integer(id)
    view_modes = Map.get(socket.assigns, :view_modes, %{})
    view_modes = Map.put(view_modes, id, !Map.get(view_modes, id, false))
    {:noreply, assign(socket, :view_modes, view_modes)}
  end
end
