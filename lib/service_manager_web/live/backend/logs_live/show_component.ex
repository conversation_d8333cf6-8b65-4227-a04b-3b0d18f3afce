defmodule ServiceManagerWeb.Backend.LogsLive.ShowComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.LogEntriesContext

  @impl true
  def mount(socket) do
    {:ok, assign(socket, show_raw: false, raw_content: nil)}
  end

  @impl true
  def handle_event("toggle_raw", _, socket) do
    if socket.assigns.show_raw do
      {:noreply, assign(socket, show_raw: false, raw_content: nil)}
    else
      raw_content = get_raw_content(socket.assigns.data)
      {:noreply, assign(socket, show_raw: true, raw_content: raw_content)}
    end
  end

  defp get_raw_content(data) do
    cond do
      # First try to read from file
      data.file_path && File.exists?(data.file_path) ->
        case File.read(data.file_path) do
          {:ok, content} -> content
          _ -> data.raw_log || "Error reading log file"
        end

      # Fallback to raw_log from database
      data.raw_log ->
        data.raw_log

      # If neither exists, show error
      true ->
        "Log file not found and no raw log data available"
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-[21cm] mx-auto">
      <div class="flex justify-between items-start mb-8">
        <div class="space-y-2">
          <h2 class="text-2xl font-bold text-gray-900">Log Entry Details</h2>
          <p class="text-sm text-gray-500">ID: <%= @data.id %></p>
        </div>
        <div class="flex items-center gap-4">
          <span class="inline-flex items-center gap-x-1 rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700">
            <svg class="h-1.5 w-1.5 fill-purple-500" viewBox="0 0 6 6" aria-hidden="true">
              <circle cx="3" cy="3" r="3" />
            </svg>
            Chain: <%= @data.chain_id %>
          </span>
          <.button phx-click="toggle_raw" phx-target={@myself} class="text-sm">
            <%= if @show_raw do %>
              Show Summary
            <% else %>
              Show Raw Data
            <% end %>
          </.button>
        </div>
      </div>

      <%= if @show_raw do %>
        <!-- Raw Data View -->
        <div class="space-y-6">
          <div class="bg-gray-50 rounded-lg p-4 shadow-inner">
            <pre class="whitespace-pre-wrap font-mono text-sm text-gray-700 overflow-x-auto">
              <%= @raw_content %>
            </pre>
          </div>
        </div>
      <% else %>
        <!-- Summary View -->
        <div class="grid grid-cols-2 gap-8">
          <!-- Basic Information -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Chain ID</label>
                <p class="mt-1 font-mono text-gray-900"><%= @data.chain_id %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Function Name</label>
                <p class="mt-1 font-medium text-gray-900"><%= @data.function_name %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Module Name</label>
                <p class="mt-1 text-gray-900"><%= @data.module_name %></p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Process ID</label>
                <p class="mt-1 font-mono text-purple-600"><%= @data.process_id %></p>
              </div>
            </div>
          </div>
          <!-- Parameters Summary -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Parameters Summary</h3>
            <div class="space-y-4">
              <div class="bg-gray-50 rounded-lg p-4 shadow-inner">
                <pre class="whitespace-pre-wrap font-mono text-sm text-gray-700 overflow-x-auto max-h-40">
                  <%= LogEntriesContext.parse_json_field(@data.params) |> Jason.encode!(pretty: true) %>
                </pre>
              </div>
            </div>
          </div>
          <!-- Result Summary -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Result Summary</h3>
            <div class="space-y-4">
              <div class="bg-gray-50 rounded-lg p-4 shadow-inner">
                <pre class="whitespace-pre-wrap font-mono text-sm text-gray-700 overflow-x-auto max-h-40">
                  <%= LogEntriesContext.parse_json_field(@data.result) |> Jason.encode!(pretty: true) %>
                </pre>
              </div>
            </div>
          </div>
          <!-- Timestamps -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Timestamps</h3>
            <div class="space-y-4">
              <div>
                <label class="text-sm font-medium text-gray-500">Created At</label>
                <p class="mt-1 text-gray-900">
                  <%= @data.inserted_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                </p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-500">Last Updated</label>
                <p class="mt-1 text-gray-900">
                  <%= @data.updated_at
                  |> to_string
                  |> String.replace("T", " ")
                  |> String.replace("Z", "") %>
                </p>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
