defmodule ServiceManagerWeb.Backend.WalletTiersLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.WalletAccounts.TierManagement
  alias ServiceManager.WalletAccounts.WalletTier
  import ServiceManagerWeb.Utilities.PermissionHelpers

  @impl true
  def mount(_params, _session, socket) do
    {:ok, stream(socket, :tiers, TierManagement.list_tiers())}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Tier")
    |> assign(:tier, TierManagement.get_tier!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Tier")
    |> assign(:tier, %WalletTier{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Wallet Tiers")
    |> assign(:tier, nil)
  end

  @impl true
  def handle_info({:tier_created, tier}, socket) do
    {:noreply, stream_insert(socket, :tiers, tier)}
  end

  def handle_info({:tier_updated, tier}, socket) do
    {:noreply, stream_insert(socket, :tiers, tier)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    tier = TierManagement.get_tier!(id)

    case TierManagement.delete_tier(tier) do
      {:ok, _} ->
        {:noreply,
         socket
         |> stream_delete(:tiers, tier)
         |> put_flash(:info, "Tier deleted successfully")}

      {:error, :users_assigned} ->
        {:noreply,
         socket
         |> put_flash(:error, "Cannot delete tier with assigned users")}
    end
  end

  def handle_event("reorder", %{"positions" => positions}, socket) do
    positions = Enum.map(positions, fn {id, pos} -> %{id: id, position: pos} end)

    case TierManagement.reorder_tiers(positions) do
      {:ok, _} ->
        {:noreply,
         socket
         |> stream(:tiers, TierManagement.list_tiers())
         |> put_flash(:info, "Tiers reordered successfully")}

      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reorder tiers")}
    end
  end

  def handle_event("toggle_default", %{"id" => id}, socket) do
    tier = TierManagement.get_tier!(id)

    case TierManagement.update_tier(tier, %{is_default: !tier.is_default}) do
      {:ok, _tier} ->
        {:noreply,
         socket
         |> stream(:tiers, TierManagement.list_tiers())
         |> put_flash(:info, "Default tier updated successfully")}

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to update default tier")}
    end
  end
end
