<.header>
  Wallet Tiers
  <:actions>
    <%= if can_create?(@current_user, :wallet_tiers) do %>
      <.link
        patch={~p"/mobileBanking/WalletTiers/new"}
        class="bg-brand hover:bg-brand-600 text-white px-4 py-2 rounded-lg"
      >
        New Tier
      </.link>
    <% end %>
  </:actions>
</.header>

<br />

<div class="space-y-8">
  <div class="overflow-x-auto">
    <table
      class="min-w-full divide-y divide-gray-200"
      id="sortable-tiers"
      phx-hook="Sortable"
      data-sortable-group="tiers"
    >
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Position
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Name
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Balance Limits
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Transaction Limits
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            KYC Requirements
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Default
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody id="tiers" phx-update="stream" class="bg-white divide-y divide-gray-200">
        <tr :for={{id, tier} <- @streams.tiers} id={id} data-id={tier.id} class="hover:bg-gray-50">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 cursor-move">
            <%= tier.position %>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900"><%= tier.name %></div>
            <div class="text-sm text-gray-500"><%= tier.description %></div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              Min: <%= tier.minimum_balance %> MWK<br />
              Max: <%= tier.maximum_balance %> MWK
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">
              Per Transaction: <%= tier.min_transaction_amount %> - <%= tier.max_transaction_amount %> MWK<br />
              Credit Limit: <%= tier.maximum_credit_limit %> MWK<br />
              Debt Limit: <%= tier.maximum_debt_limit %> MWK<br />
              Daily: <%= tier.daily_transaction_limit %> MWK<br />
              Monthly: <%= tier.monthly_transaction_limit %> MWK
            </div>
          </td>
          <td class="px-6 py-4">
            <div class="text-sm text-gray-900">
              <div class="flex flex-wrap gap-1">
                <%= for field <- tier.required_kyc_fields do %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    <%= String.replace(field, "_", " ") |> String.capitalize() %>
                  </span>
                <% end %>
              </div>
              <%= if map_size(tier.kyc_rules) > 0 do %>
                <div class="mt-1 text-xs text-gray-500">
                  <%= for {rule, value} <- tier.kyc_rules do %>
                    <div>
                      <%= String.replace(rule, "_", " ") |> String.capitalize() %>: <%= value %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <button
                type="button"
                phx-click="toggle_default"
                phx-value-id={tier.id}
                class={"#{if tier.is_default, do: "bg-green-100 text-green-800", else: "bg-gray-100 text-gray-800"} relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand"}
              >
                <span class={"#{if tier.is_default, do: "translate-x-5", else: "translate-x-0"} pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"}>
                </span>
              </button>
              <span class="ml-2 text-sm text-gray-500">
                <%= if tier.is_default, do: "Default Tier", else: "Set as Default" %>
              </span>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <%= if has_any_permission?(@current_user, [:update, :delete], :wallet_tiers) do %>
              <div class="flex justify-end space-x-3">
                <%= if can_update?(@current_user, :wallet_tiers) do %>
                  <.link
                    patch={~p"/mobileBanking/WalletTiers/#{tier}/edit"}
                    class="text-brand hover:text-brand-600"
                  >
                    Edit
                  </.link>
                <% end %>
                <%= if can_delete?(@current_user, :wallet_tiers) do %>
                  <.link
                    phx-click="delete"
                    phx-value-id={tier.id}
                    data-confirm="Are you sure you want to delete this tier?"
                    class="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </.link>
                <% end %>
              </div>
            <% end %>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<.modal
  :if={@live_action in [:new, :edit]}
  id="tier-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/WalletTiers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.WalletTiersLive.FormComponent}
    id={@tier.id || :new}
    title={@page_title}
    action={@live_action}
    tier={@tier}
    patch={~p"/mobileBanking/WalletTiers"}
  />
</.modal>
