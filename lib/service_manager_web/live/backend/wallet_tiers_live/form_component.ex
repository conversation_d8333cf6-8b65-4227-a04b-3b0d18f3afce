defmodule ServiceManagerWeb.Backend.WalletTiersLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.WalletAccounts.TierManagement
  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycField
  alias ServiceManager.WalletAccounts.EmbeddedKyc.KycRule
  import ServiceManagerWeb.CoreComponents

  @available_kyc_fields [
    "first_name",
    "last_name",
    "mobile_number",
    "email",
    "date_of_birth",
    "address",
    "city",
    "occupation",
    "employer_name",
    "source_of_funds",
    "id_number",
    "id_image"
  ]

  @available_kyc_rules [
    {"minimum_age", "Minimum Age (years)", :number},
    {"id_required", "ID Required", :boolean},
    {"address_required", "Address Required", :boolean},
    {"employment_verification", "Employment Verification", :boolean},
    {"source_of_funds_required", "Source of Funds Required", :boolean},
    {"nrb_verification", "NRB Verification", :boolean}
  ]

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Configure wallet tier settings and requirements</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="tier-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="space-y-8 divide-y divide-gray-200">
          <!-- Basic Information -->
          <div class="space-y-6 pt-8">
            <div class="text-base font-medium text-gray-900">Basic Information</div>
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <.input field={@form[:name]} type="text" label="Name" required />
              <.input field={@form[:description]} type="text" label="Description" />
              <.input field={@form[:position]} type="number" label="Position" required min="1" />
              <div class="flex items-center space-x-3 pt-6">
                <.input field={@form[:is_default]} type="checkbox" label="Set as Default Tier" />
              </div>
            </div>
          </div>
          <!-- Balance Limits -->
          <div class="space-y-6 pt-8">
            <div class="text-base font-medium text-gray-900">Balance Limits</div>
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <.input
                field={@form[:minimum_balance]}
                type="number"
                label="Minimum Balance (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:maximum_balance]}
                type="number"
                label="Maximum Balance (MWK)"
                required
                step="0.01"
              />
            </div>
          </div>
          <!-- Transaction Limits -->
          <div class="space-y-6 pt-8">
            <div class="text-base font-medium text-gray-900">Transaction Limits</div>
            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <.input
                field={@form[:min_transaction_amount]}
                type="number"
                label="Minimum Transaction Amount (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:max_transaction_amount]}
                type="number"
                label="Maximum Transaction Amount (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:maximum_credit_limit]}
                type="number"
                label="Maximum Credit Limit (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:maximum_debt_limit]}
                type="number"
                label="Maximum Debt Limit (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:daily_transaction_limit]}
                type="number"
                label="Daily Transaction Limit (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:monthly_transaction_limit]}
                type="number"
                label="Monthly Transaction Limit (MWK)"
                required
                step="0.01"
              />
              <.input
                field={@form[:daily_transaction_count]}
                type="number"
                label="Daily Transaction Count"
                required
                min="1"
              />
              <.input
                field={@form[:monthly_transaction_count]}
                type="number"
                label="Monthly Transaction Count"
                required
                min="1"
              />
            </div>
          </div>
          <!-- KYC Requirements -->
          <div class="space-y-6 pt-8">
            <div class="text-base font-medium text-gray-900">KYC Requirements</div>
            <!-- Required Fields -->
            <div class="space-y-4">
              <label class="block text-sm font-medium text-gray-700">Required Fields</label>
              <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                <.inputs_for :let={f} field={@form[:kyc_fields]}>
                  <label class="inline-flex items-center">
                    <.input
                      field={f[:enabled]}
                      type="checkbox"
                      class="rounded border-gray-300 text-brand focus:ring-brand"
                      phx-click={
                        if f[:field_name].value == "date_of_birth", do: "toggle_dob", else: nil
                      }
                      phx-target={@myself}
                    />
                    <span class="ml-2 text-sm text-gray-700">
                      <%= String.replace(f[:field_name].value, "_", " ") |> String.capitalize() %>
                    </span>
                    <.input field={f[:field_name]} type="hidden" />
                  </label>
                </.inputs_for>
              </div>
            </div>
            <!-- KYC Rules -->
            <div class="space-y-4">
              <label class="block text-sm font-medium text-gray-700">Validation Rules</label>
              <div class="space-y-4">
                <.inputs_for :let={f} field={@form[:validation_rules]}>
                  <%= if f[:rule_name].value == "minimum_age" do %>
                    <div class={"#{if @show_min_age, do: "block", else: "hidden"} flex items-center space-x-4"}>
                      <label class="inline-flex items-center">
                        <.input
                          field={f[:enabled]}
                          type="checkbox"
                          class="rounded border-gray-300 text-brand focus:ring-brand"
                          checked
                          readonly
                        />
                        <span class="ml-2 text-sm text-gray-700"><%= f[:label].value %></span>
                        <.input field={f[:rule_name]} type="hidden" />
                        <.input field={f[:label]} type="hidden" />
                        <.input field={f[:type]} type="hidden" />
                      </label>

                      <div class="flex-1 max-w-[150px]">
                        <.input
                          field={f[:value]}
                          type="number"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand focus:ring-brand sm:text-sm"
                          min="1"
                        />
                      </div>
                    </div>
                  <% else %>
                    <div class="flex items-center space-x-4">
                      <!-- Regular rule display for non-minimum-age rules -->
                      <label class="inline-flex items-center">
                        <.input
                          field={f[:enabled]}
                          type="checkbox"
                          class="rounded border-gray-300 text-brand focus:ring-brand"
                          checked
                          readonly
                        />
                        <span class="ml-2 text-sm text-gray-700"><%= f[:label].value %></span>
                        <.input field={f[:rule_name]} type="hidden" />
                        <.input field={f[:label]} type="hidden" />
                        <.input field={f[:type]} type="hidden" />
                      </label>

                      <%= if f[:type].value == "number" && f[:enabled].value == "true" do %>
                        <div class="flex-1 max-w-[150px]">
                          <.input
                            field={f[:value]}
                            type="number"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-brand focus:ring-brand sm:text-sm"
                            min="1"
                            required
                          />
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </.inputs_for>
              </div>
            </div>
          </div>
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Save Tier</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  @impl true
  def update(%{tier: tier} = assigns, socket) do
    # Ensure we have properly initialized embedded schemas
    tier =
      tier
      |> Map.update(:kyc_fields, [], fn fields ->
        Enum.map(fields, fn
          %KycField{} = field -> field
          field when is_map(field) -> struct(KycField, field)
        end)
      end)
      |> Map.update(:validation_rules, [], fn rules ->
        Enum.map(rules, fn
          %KycRule{} = rule -> rule
          rule when is_map(rule) -> struct(KycRule, rule)
        end)
      end)

    changeset = TierManagement.change_tier(tier)

    dob_enabled =
      Enum.any?(tier.kyc_fields || [], fn
        %{field_name: "date_of_birth", enabled: true} -> true
        _ -> false
      end)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:show_min_age, dob_enabled)
     |> assign(:available_kyc_fields, @available_kyc_fields)
     |> assign(:available_kyc_rules, @available_kyc_rules)
     |> assign_form(changeset)}
  end

  @impl true
  def handle_event("validate", %{"wallet_tier" => tier_params}, socket) do
    changeset =
      socket.assigns.tier
      |> TierManagement.change_tier(tier_params)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, changeset)}
  end

  def handle_event("save", %{"wallet_tier" => tier_params}, socket) do
    save_tier(socket, socket.assigns.action, tier_params)
  end

  def handle_event("toggle_rule", %{"rule" => rule_name}, socket) do
    current_form = socket.assigns.form
    changeset = current_form.source

    updated_rules =
      (changeset.changes[:validation_rules] || changeset.data.validation_rules || [])
      |> Enum.map(fn
        %Ecto.Changeset{data: rule} = rule_changeset ->
          if rule.rule_name == rule_name do
            # Create a new changeset with toggled enabled state
            rule_changeset
            |> Ecto.Changeset.change(%{enabled: !rule.enabled})
          else
            rule_changeset
          end

        rule ->
          if rule.rule_name == rule_name do
            %{rule | enabled: !rule.enabled}
          else
            rule
          end
      end)

    updated_changeset =
      changeset
      |> Ecto.Changeset.put_embed(:validation_rules, updated_rules)
      |> Map.put(:action, :validate)

    {:noreply, assign_form(socket, updated_changeset)}
  end

  def handle_event("toggle_dob", params, socket) do
    IO.inspect(params, label: "Toggle DOB Params")

    value =
      if params["value"] do
        true
      else
        false
      end

    current_form = socket.assigns.form
    changeset = current_form.source

    # Find the date_of_birth field
    dob_enabled = value

    # Toggle date of birth enabled state
    updated_kyc_fields =
      (changeset.changes[:kyc_fields] || changeset.data.kyc_fields || [])
      |> Enum.map(fn
        %Ecto.Changeset{data: %{field_name: "date_of_birth"}} = cs ->
          current_enabled = Map.get(cs, :enabled) || Map.get(cs, "enabled")
          Ecto.Changeset.change(cs, %{enabled: !current_enabled})

        %{field_name: "date_of_birth"} = field ->
          %{field | enabled: !field.enabled}

        field ->
          field
      end)

    updated_rules =
      (changeset.changes[:validation_rules] || changeset.data.validation_rules || [])
      |> Enum.map(fn
        %Ecto.Changeset{data: %{rule_name: "minimum_age"}} = cs ->
          Ecto.Changeset.change(cs, %{enabled: dob_enabled})

        %{rule_name: "minimum_age"} = rule ->
          %{rule | enabled: dob_enabled}

        rule ->
          rule
      end)

    updated_changeset =
      changeset
      |> Ecto.Changeset.put_embed(:kyc_fields, updated_kyc_fields)
      |> Ecto.Changeset.put_embed(:validation_rules, updated_rules)
      |> Map.put(:action, :validate)

    {:noreply,
     socket
     |> assign(:show_min_age, dob_enabled)
     |> assign_form(updated_changeset)}
  end

  # Add these save_tier functions back
  defp save_tier(socket, :edit, tier_params) do
    case TierManagement.update_tier(socket.assigns.tier, tier_params) do
      {:ok, tier} ->
        notify_parent({:tier_updated, tier})

        {:noreply,
         socket
         |> put_flash(:info, "Tier updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp save_tier(socket, :new, tier_params) do
    case TierManagement.create_tier(tier_params) do
      {:ok, tier} ->
        notify_parent({:tier_created, tier})

        {:noreply,
         socket
         |> put_flash(:info, "Tier created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign_form(socket, changeset)}
    end
  end

  defp assign_form(socket, %Ecto.Changeset{} = changeset) do
    assign(socket, :form, to_form(changeset))
  end

  defp notify_parent(msg), do: send(self(), msg)

  defp notify_parent(msg), do: send(self(), msg)

  defp get_rule_value(%KycRule{value: value}), do: value
  defp get_rule_value(value) when is_binary(value), do: value
  defp get_rule_value(_), do: nil
end
