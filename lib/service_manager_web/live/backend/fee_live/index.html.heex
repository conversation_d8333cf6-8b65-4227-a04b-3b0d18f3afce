<div class="space-y-8">
  <.header>
    Fees and Charges Management
    <:actions>
      <%= if can_create?(@current_user, :fees) do %>
        <.link patch={~p"/mobileBanking/fees&Chargers/new"}>
          <.button>New Fee</.button>
        </.link>
      <% end %>
      <.button phx-click="toggle_comparison_view">
        <%= if @comparison_view, do: "View List", else: "Compare Fees" %>
      </.button>
    </:actions>
    <:subtitle>
      Comprehensive fee management system with real-time calculations and comparisons
    </:subtitle>
  </.header>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Fee Calculator -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-semibold mb-4">Fee Calculator</h3>
      <.form :let={f} for={%{}} as={:calculator} phx-submit="calculate_fee">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Select Fee</label>
            <select
              name="calculator[fee_id]"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
            >
              <option value="">Select Fee</option>
              <%= for {_id, fee} <- @streams.fees do %>
                <option value={fee.id}><%= fee.name %></option>
              <% end %>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Transaction Amount</label>
            <input
              type="number"
              name="calculator[amount]"
              step="0.01"
              required
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Currency</label>
            <select
              name="calculator[currency]"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
            >
              <option value="">Select Currency</option>
              <%= for {code, code} <- @currency_options do %>
                <option value={code} selected={@calculator_params["currency"] == code}>
                  <%= code %>
                </option>
              <% end %>
            </select>
          </div>
          <.button type="submit" class="w-full">Calculate Fee</.button>
        </div>
      </.form>

      <%= if @calculated_fee do %>
        <div class="mt-4 p-4 bg-gray-50 rounded">
          <h4 class="font-medium">Calculated Fee:</h4>
          <p class="text-2xl font-bold">
            <%= @calculated_fee %> <%= @calculator_params["currency"] %>
          </p>

          <div class="mt-2">
            <.form
              :let={f}
              for={%{}}
              as={:converter}
              phx-submit="convert_currency"
              class="flex gap-2"
            >
              <select
                name="target_currency"
                class="flex-1 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
              >
                <option value="">Select Target Currency</option>
                <%= for {code, code} <- @currency_options do %>
                  <option value={code}><%= code %></option>
                <% end %>
              </select>
              <.button type="submit">Convert</.button>
            </.form>
          </div>
        </div>
      <% end %>
    </div>
    <!-- Fee Categories -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-semibold mb-4">Fee Categories</h3>
      <div class="space-y-2">
        <%= for category <- @fee_categories do %>
          <button
            phx-click="select_category"
            phx-value-category={category}
            class={"w-full text-left px-4 py-2 rounded #{if @selected_category == category, do: "bg-primary text-white", else: "hover:bg-gray-100"}"}
          >
            <%= String.capitalize(category) %>
          </button>
        <% end %>
      </div>
    </div>
    <!-- Notifications -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-semibold mb-4">Upcoming Fee Changes</h3>
      <div class="space-y-4">
        <%= for {_id, fee} <- @streams.fees do %>
          <%= if fee.notification_enabled do %>
            <div class="p-4 border rounded">
              <h4 class="font-medium"><%= fee.name %></h4>
              <p class="text-sm text-gray-600">
                Effective from: <%= fee.effective_date %>
              </p>
              <%= if fee.notification_days_before do %>
                <p class="text-sm text-blue-600">
                  Reminder set for <%= fee.notification_days_before %> days before
                </p>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </div>

  <%= if @comparison_view do %>
    <!-- Fee Comparison View -->
    <div class="bg-white shadow rounded-lg p-6 mt-6">
      <h3 class="text-xl font-semibold mb-4">Fee Comparison</h3>
      <%= if @comparison_data do %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Account Type
                </th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fee Amount
                </th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Calculation Method
                </th>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conditions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for {account_type, fees} <- @comparison_data do %>
                <%= for fee <- fees do %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <%= account_type %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if fee["calculation_method"] == "percentage" do %>
                        <%= fee["percentage_rate"] %>%
                      <% else %>
                        <%= fee["amount"] %> <%= fee["currency_code"] %>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= fee["calculation_method"] %>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <%= if fee["conditions"] do %>
                        <ul class="list-disc list-inside">
                          <%= for {key, value} <- fee["conditions"] do %>
                            <li><%= key %>: <%= value %></li>
                          <% end %>
                        </ul>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <p class="text-gray-500">Select a category to compare fees across account types</p>
      <% end %>
    </div>
  <% else %>
    <!-- Fee List View -->
    <.table
      id="fees"
      filter_params={@filter_params}
      pagination={@pagination}
      selected_column={@selected_column}
      rows={@streams.fees}
      row_click={fn {_id, fee} -> JS.navigate(~p"/mobileBanking/fees&Chargers/#{fee}") end}
    >
      <:col :let={{_id, fee}} filter_item="category" label="Category"><%= fee.category %></:col>
      <:col :let={{_id, fee}} filter_item="code" label="Code"><%= fee.code %></:col>
      <:col :let={{_id, fee}} filter_item="name" label="Name"><%= fee.name %></:col>
      <:col :let={{_id, fee}} filter_item="calculation_method" label="Calculation">
        <%= if fee.calculation_method == "percentage" do %>
          <%= fee.percentage_rate %>%
        <% else %>
          <%= fee.amount %> <%= fee.currency_code %>
        <% end %>
      </:col>
      <:col :let={{_id, fee}} filter_item="currency_code" label="Currency">
        <%= fee.currency_code %>
      </:col>
      <:col :let={{_id, fee}} filter_item="account_type" label="Account Type">
        <%= fee.account_type %>
      </:col>
      <:col :let={{_id, fee}} filter_item="transaction_type" label="Transaction">
        <%= fee.transaction_type %>
      </:col>
      <:col :let={{_id, fee}} filter_item="status" label="Status">
        <.status_pill status={fee.status} text={fee.status} />
      </:col>

      <:action :let={{id, dataset}}>
        <%= if has_any_permission?(@current_user, [:update, :delete, :activate], :fees) do %>
          <.dropdown id={"dropdown-#{id}"} label="Options">
            <%= if can_activate?(@current_user, :fees) do %>
              <%= if dataset.status == "active" do %>
                <.link
                  class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  phx-click={JS.push("deactivate", value: %{id: dataset.id})}
                  data-confirm="Are you sure?"
                >
                  Deactivate
                </.link>
              <% else %>
                <.link
                  class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  phx-click={JS.push("activate", value: %{id: dataset.id})}
                  data-confirm="Are you sure?"
                >
                  Activate
                </.link>
              <% end %>
            <% end %>

            <%= if can_update?(@current_user, :fees) do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                patch={~p"/mobileBanking/fees&Chargers/#{dataset}/edit"}
              >
                Edit
              </.link>
            <% end %>
            <%= if can_delete?(@current_user, :fees) do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("delete", value: %{id: dataset.id}) |> hide("##{id}")}
                data-confirm="Are you sure?"
              >
                Delete
              </.link>
            <% end %>
          </.dropdown>
        <% end %>
      </:action>
    </.table>
  <% end %>
</div>

<.modal
  :if={@live_action in [:new, :edit]}
  id="fee-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/fees&Chargers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.FeeLive.FormComponent}
    id={@fee.id || :new}
    title={@page_title}
    action={@live_action}
    fee={@fee}
    current_user={@current_user}
    patch={~p"/mobileBanking/fees&Chargers"}
  />
</.modal>
