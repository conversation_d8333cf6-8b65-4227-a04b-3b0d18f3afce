defmodule ServiceManagerWeb.Backend.FeeLive.FormComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Contexts.FeesContext
  alias ServiceManager.Contexts.LogsContext
  alias ServiceManager.Contexts.CurrenciesContext
  import Phoenix.HTML.Form

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Configure fee details, calculations, and notifications.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="fee-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <%= if @form.errors != [] do %>
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <strong class="font-bold">Please fix the following errors:</strong>
            <ul class="mt-2 list-disc list-inside">
              <%= for {field, {message, _}} <- @form.errors do %>
                <li><%= field %>: <%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="space-y-8">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4">Basic Information</h3>
            <div class="grid grid-cols-2 gap-4">
              <.input field={@form[:code]} type="text" label="Code" />
              <.input field={@form[:name]} type="text" label="Name" />
              <div class="col-span-2">
                <.input field={@form[:description]} type="textarea" label="Description" />
              </div>
              <.input
                field={@form[:category]}
                type="select"
                options={FeesContext.get_fee_categories()}
                prompt="Select a category"
                label="Category"
              />
              <.input field={@form[:transaction_type]} type="text" label="Transaction Type" />
              <.input
                field={@form[:charge_type]}
                type="select"
                options={[
                  {"Fixed Charge", "fixed"},
                  {"Percentage Charge", "percentage"},
                  {"Tiered Charge", "tiered"},
                  {"Range Based", "range"}
                ]}
                prompt="Select charge type"
                label="Charge Type"
              />
            </div>
          </div>
          <!-- Fee Calculation -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4">Fee Calculation</h3>
            <div class="grid grid-cols-2 gap-4">
              <.input
                field={@form[:calculation_method]}
                type="select"
                options={FeesContext.get_calculation_methods()}
                prompt="Select calculation method"
                label="Calculation Method"
              />
              <%= if input_value(@form, :calculation_method) == "percentage" do %>
                <.input
                  field={@form[:percentage_rate]}
                  type="number"
                  label="Percentage Rate"
                  step="0.01"
                />
              <% else %>
                <.input field={@form[:amount]} type="number" label="Fixed Amount" step="0.01" />
              <% end %>
              <.input
                field={@form[:currency_code]}
                type="select"
                options={@currency_options}
                prompt="Select currency"
                label="Currency"
              />
              <.input
                field={@form[:exchange_rate_source]}
                type="select"
                options={[
                  {"Local", "local"},
                  {"External API", "external"},
                  {"Manual", "manual"}
                ]}
                prompt="Select rate source"
                label="Exchange Rate Source"
              />
              <.input field={@form[:min_amount]} type="number" label="Minimum Amount" step="0.01" />
              <.input field={@form[:max_amount]} type="number" label="Maximum Amount" step="0.01" />
            </div>
          </div>
          <!-- Application Rules -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4">Application Rules</h3>
            <div class="grid grid-cols-2 gap-4">
              <.input
                field={@form[:account_type]}
                type="select"
                options={[
                  {"Current", "CURRENT"},
                  {"Savings", "SAVINGS"},
                  {"Fixed Deposit", "FIXED_DEPOSIT"},
                  {"Loan", "LOAN"}
                ]}
                prompt="Select account type"
                label="Account Type"
              />
              <.input
                field={@form[:frequency]}
                type="select"
                options={FeesContext.get_frequencies()}
                prompt="Select frequency"
                label="Frequency"
              />
              <.input
                field={@form[:application_time]}
                type="select"
                options={FeesContext.get_application_times()}
                prompt="Select application time"
                label="Application Time"
              />
              <.input field={@form[:effective_date]} type="date" label="Effective Date" />
              <.input field={@form[:expiration_date]} type="date" label="Expiration Date" />
            </div>
          </div>
          <!-- Notifications -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4">Notifications</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="col-span-1">
                <.input
                  field={@form[:notification_enabled]}
                  type="checkbox"
                  label="Enable Notifications"
                />
              </div>
              <%= if input_value(@form, :notification_enabled) do %>
                <.input
                  field={@form[:notification_days_before]}
                  type="number"
                  label="Days Before to Notify"
                />
              <% end %>
            </div>
          </div>
          <!-- Additional Settings -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-medium mb-4">Additional Settings</h3>
            <div class="grid grid-cols-2 gap-4">
              <.input field={@form[:is_feature]} type="checkbox" label="Featured Fee" />
              <.input
                field={@form[:status]}
                type="select"
                options={[{"Active", "active"}, {"Inactive", "inactive"}]}
                label="Status"
              />
            </div>
          </div>
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Save Fee</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{fee: fee} = assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:currency_options, CurrenciesContext.get_currency_options())
     |> assign_new(:form, fn ->
       to_form(FeesContext.change_fee(fee))
     end)}
  end

  @impl true
  def handle_event("validate", %{"fee" => fee_params}, socket) do
    IO.inspect(fee_params, label: "Validation Params")
    changeset = FeesContext.change_fee(socket.assigns.fee, fee_params)
    IO.inspect(changeset, label: "Validation Changeset")
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"fee" => fee_params}, socket) do
    IO.inspect(fee_params, label: "Save Params")
    save_fee(socket, socket.assigns.action, fee_params)
  end

  defp save_fee(socket, :edit, fee_params) do
    case FeesContext.update_fee(socket.assigns.fee, fee_params) do
      {:ok, resp} ->
        notify_parent({:saved, resp})

        Task.start(fn ->
          LogsContext.create_log(%{
            level: "info",
            category: "fees",
            message: "Fee updated successfully",
            metadata: %{
              details: inspect(resp)
            },
            user_id: socket.assigns.current_user && socket.assigns.current_user.id
          })
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Fee updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        IO.inspect(changeset, label: "Update Error Changeset")

        Task.start(fn ->
          LogsContext.create_log(%{
            level: "error",
            category: "fees",
            message: "Fee update failed",
            metadata: %{
              details: inspect(changeset)
            },
            user_id: socket.assigns.current_user && socket.assigns.current_user.id
          })
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp save_fee(socket, :new, fee_params) do
    case FeesContext.create_fee(fee_params) do
      {:ok, fee} ->
        notify_parent({:saved, fee})

        Task.start(fn ->
          LogsContext.create_log(%{
            level: "info",
            category: "fees",
            message: "Fee created successfully",
            metadata: %{
              details: inspect(fee)
            },
            user_id: socket.assigns.current_user && socket.assigns.current_user.id
          })
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Fee created successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        IO.inspect(changeset, label: "Create Error Changeset")

        Task.start(fn ->
          LogsContext.create_log(%{
            level: "error",
            category: "fees",
            message: "Fee creation failed",
            metadata: %{
              details: inspect(changeset)
            },
            user_id: socket.assigns.current_user && socket.assigns.current_user.id
          })
        end)

        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp notify_parent(msg), do: send(self(), {__MODULE__, msg})
end
