<.header>
  Fee <%= @fee.id %>
  <:subtitle>This is a fee record from your database.</:subtitle>
  <:actions>
    <.link patch={~p"/mobileBanking/fees&Chargers/#{@fee}/show/edit"} phx-click={JS.push_focus()}>
      <.button>Edit fee</.button>
    </.link>
  </:actions>
</.header>

<.list>
  <:item title="Code"><%= @fee.code %></:item>
  <:item title="Name"><%= @fee.name %></:item>
  <:item title="Description"><%= @fee.description %></:item>
  <:item title="Amount"><%= @fee.amount %></:item>
  <:item title="Currency code"><%= @fee.currency_code %></:item>
  <:item title="Charge type"><%= @fee.charge_type %></:item>
  <:item title="Effective date"><%= @fee.effective_date %></:item>
  <:item title="Expiration date"><%= @fee.expiration_date %></:item>
  <:item title="Is feature"><%= @fee.is_feature %></:item>
  <:item title="Status"><%= @fee.status %></:item>
  <:item title="Created by"><%= @fee.created_by %></:item>
  <:item title="Updated by"><%= @fee.updated_by %></:item>
</.list>

<.back navigate={~p"/mobileBanking/fees&Chargers"}>Back to fees</.back>

<.modal
  :if={@live_action == :edit}
  id="fee-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/fees&Chargers/#{@fee}")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.FeeLive.FormComponent}
    id={@fee.id}
    title={@page_title}
    action={@live_action}
    fee={@fee}
    patch={~p"/mobileBanking/fees&Chargers/#{@fee}"}
  />
</.modal>
