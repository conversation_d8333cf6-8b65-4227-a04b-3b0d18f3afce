defmodule ServiceManagerWeb.Backend.FeeLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.FeesContext
  alias ServiceManager.Contexts.CurrenciesContext
  alias ServiceManager.Contexts.ExchangeRateContext
  alias ServiceManager.Schemas.Fee
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/fees&Chargers"

  @impl true
  def mount(_params, session, socket) do
    socket =
      socket
      |> assign(:current_path, @url)
      # |> assign(:current_user, session["current_user"])
      |> assign(:fee_categories, FeesContext.get_fee_categories())
      |> assign(:calculation_methods, FeesContext.get_calculation_methods())
      |> assign(:frequencies, FeesContext.get_frequencies())
      |> assign(:application_times, FeesContext.get_application_times())
      |> assign(:currency_options, CurrenciesContext.get_currency_options())
      |> assign(:selected_category, nil)
      |> assign(:comparison_view, false)
      |> assign(:calculator_params, %{
        "amount" => nil,
        "currency" => nil,
        "transaction_type" => nil
      })
      |> assign(:calculated_fee, nil)
      |> assign(:comparison_data, nil)

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = FeesContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:fees, data)}
  end

  def handle_params(params, _url, socket) do
    data = FeesContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:fees, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit Fee")
    |> assign(:fee, FeesContext.get_fee!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Fee")
    |> assign(:fee, %Fee{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing Fees")
    |> assign(:fee, nil)
  end

  @impl true
  def handle_info({ServiceManagerWeb.Backend.FeeLive.FormComponent, {:saved, fee}}, socket) do
    {:noreply, stream_insert(socket, :fees, fee)}
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    fee = FeesContext.get_fee!(id)
    {:ok, _} = FeesContext.delete_fee(fee)

    {:noreply, stream_delete(socket, :fees, fee)}
  end

  def handle_event("toggle_comparison_view", _, socket) do
    {:noreply, assign(socket, :comparison_view, !socket.assigns.comparison_view)}
  end

  def handle_event("select_category", %{"category" => category}, socket) do
    comparison_data =
      case FeesContext.compare_fees(category) do
        data when map_size(data) > 0 -> data
        _ -> nil
      end

    {:noreply,
     socket
     |> assign(:selected_category, category)
     |> assign(:comparison_data, comparison_data)
     |> put_flash(
       :info,
       if(comparison_data,
         do: "Showing fees for #{category}",
         else: "No fees found for #{category}"
       )
     )}
  end

  def handle_event("calculate_fee", %{"calculator" => params}, socket) do
    IO.inspect(params, label: "Calculator Params")

    with fee when not is_nil(fee) <- FeesContext.get_fee!(params["fee_id"]),
         {:ok, amount} <- Decimal.cast(params["amount"]),
         {:ok, calculated_amount} <- FeesContext.calculate_fee_amount(fee, amount) do
      # Get fresh data to maintain streams
      data = FeesContext.retrieve()
      pagination = generate_pagination_details(data)

      {:noreply,
       socket
       |> stream(:fees, data)
       |> assign(pagination: pagination)
       |> assign(:calculator_params, params)
       |> assign(:calculated_fee, Decimal.round(calculated_amount, 2))
       |> put_flash(:info, "Fee calculated successfully")}
    else
      {:error, :invalid_calculation_method} ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid calculation method for this fee.")}

      {:error, :calculation_failed} ->
        {:noreply,
         socket
         |> put_flash(:error, "Fee calculation failed. Please check the fee configuration.")}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Unable to calculate fee. Please check your inputs.")}
    end
  end

  def handle_event("convert_currency", %{"target_currency" => target_currency}, socket) do
    case socket.assigns.calculated_fee do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "Please calculate a fee first")}

      amount ->
        source_currency = socket.assigns.calculator_params["currency"]

        if source_currency == target_currency do
          {:noreply,
           socket
           |> put_flash(:info, "Already in #{target_currency}")}
        else
          case ExchangeRateContext.get_exchange_rate(source_currency, target_currency) do
            {:ok, rate} ->
              converted_amount = Decimal.mult(amount, rate)

              # Get fresh data to maintain streams
              data = FeesContext.retrieve()
              pagination = generate_pagination_details(data)

              {:noreply,
               socket
               |> stream(:fees, data)
               |> assign(pagination: pagination)
               |> assign(:calculated_fee, Decimal.round(converted_amount, 2))
               |> assign(
                 :calculator_params,
                 Map.put(socket.assigns.calculator_params, "currency", target_currency)
               )
               |> put_flash(:info, "Converted to #{target_currency}")}

            {:error, :rate_not_found} ->
              {:noreply,
               socket
               |> put_flash(
                 :error,
                 "No exchange rate found between #{source_currency} and #{target_currency}"
               )}
          end
        end
    end
  end

  def handle_event("activate", %{"id" => id}, socket) do
    data = FeesContext.get_fee!(id)

    user = socket.assigns.current_user || %{id: nil}

    FeesContext.update_data(data, %{"status" => "active"}, user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Fee activated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Fee activation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("deactivate", %{"id" => id}, socket) do
    data = FeesContext.get_fee!(id)

    user = socket.assigns.current_user || %{id: nil}

    FeesContext.update_data(data, %{"status" => "inactive"}, user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Fee inactivated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Fee inactivation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end
end
