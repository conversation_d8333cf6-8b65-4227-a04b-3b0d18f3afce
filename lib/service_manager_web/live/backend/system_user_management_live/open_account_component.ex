defmodule ServiceManagerWeb.Backend.SystemUserManagementLive.OpenAccountComponent do
  use ServiceManagerWeb, :live_component

  alias ServiceManager.Schemas.User, as: UserManagementContext
  alias ServiceManager.Services.OpenAccountService

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.flash_group flash={@flash} />

      <.header>
        <%= @title %>
        <:subtitle>Use this form to open or create an account.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="user_management-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
          <.input field={@form[:first_name]} type="text" label="First Name" />
          <.input field={@form[:last_name]} type="text" label="Last Name" />
          <.input field={@form[:email]} type="email" label="Email" />

          <div class="grid grid-cols-2 gap-6 ">
            <.input
              field={@form[:identifier_type]}
              options={account_types()}
              type="select"
              label="Identifier Type"
            />
            <.input field={@form[:identifier_number]} type="text" label="Identifier Number" />
          </div>
          <.input field={@form[:phone]} type="text" label="Phone Number" />
          <.input field={@form[:country]} type="text" label="Country" />
          <.input field={@form[:street_address]} type="text" label="Street Address" />
          <.input field={@form[:city]} type="text" label="City" />
          <.input field={@form[:region]} type="text" label="Region/Province" />
          <.input field={@form[:zip]} type="text" label="Zip Code" />
        </div>

        <div class="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
          <.input field={@form[:initial_balance]} type="text" label="Balance" />
          <.input
            field={@form[:account_number]}
            disabled="disabled"
            type="text"
            label="Account Number"
          />
        </div>

        <div class="border-b border-fdh-blue pb-12">
          <div class="mt-10  grid grid-cols-1 md:grid-cols-3 gap-10 gap-y-20">
            <.input
              field={@form[:currency]}
              options={[{"USD", "USD"}, {"MWK", "MWK"}]}
              type="radio"
              label="Currency"
            />
            <.input
              field={@form[:account_type]}
              options={[{"Current", "current"}, {"Savings", "saving"}]}
              type="radio"
              label="Account Type"
            />
            <.input
              field={@form[:notifications]}
              options={[
                {"Everything", "everything"},
                {"Same as email", "email"},
                {"No push notifications", "nothing"}
              ]}
              type="radio"
              label="Notifications"
            />
          </div>
        </div>

        <:actions>
          <.button phx-disable-with="Saving...">Submit</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{user_management: user_management} = assigns, socket) do
    user_management = %UserManagementContext{}

    {:ok,
     socket
     |> assign(:user_management, user_management)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(UserManagementContext.change_data(%{}))
     end)}
  end

  @impl true
  def handle_event("validate", %{"user" => user_management_params}, socket) do
    changeset =
      UserManagementContext.change_data(socket.assigns.user_management, user_management_params)

    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"user" => user_management_params}, socket) do
    save_user_management(socket, socket.assigns.action, user_management_params)
  end

  defp save_user_management(socket, _action, user_params) do
    user_params
    |> Map.put("name", "#{user_params["first_name"]} #{user_params["last_name"]}")
    |> Map.put("balance", user_params["initial_balance"])
    |> Map.put("role", "user")
    |> Map.put("currency", "USD")
    |> Map.put("account_type", "current")
    |> Map.put("notifications", "everything")
    |> OpenAccountService.process_account()
    |> IO.inspect(label: "=================")
    |> case do
      {:ok, res} ->
        #   {:noreply,
        #    socket
        #    |> put_flash(:info, "User management created successfully")
        #    |> push_navigate(to: socket.assigns.patch, replace: true)}

        changeset =
          UserManagementContext.change_data(
            socket.assigns.user_management,
            Map.put(user_params, "account_number", res["account_number"] || res[:account_number])
          )

        {:noreply,
         socket
         |> put_flash(:info, "User management created successfully")
         |> assign(form: to_form(changeset, action: :validate))}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)
         |> push_navigate(to: socket.assigns.patch, replace: true)}
    end
  end

  def account_types() do
    [
      {"National Registration Number", "NRC"},
      {"Passport", "PASSPORT"}
    ]
  end
end
