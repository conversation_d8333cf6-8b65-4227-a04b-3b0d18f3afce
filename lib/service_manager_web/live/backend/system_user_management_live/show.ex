defmodule ServiceManagerWeb.Backend.SystemUserManagementLive.Show do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.SystemUserManagementContext, as: UserManagementContext
  alias ServiceManagerWeb.Api.Services.ProfileServiceController

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket, :current_path, Phoenix.LiveView.get_connect_params(socket)["current_path"])

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"id" => id}, _, socket) do
    {:noreply,
     socket
     |> assign(:page_title, page_title(socket.assigns.live_action))
     |> assign(:user_management, UserManagementContext.get_data!(id))}
  end

  defp page_title(:show), do: "Show User management"
  defp page_title(:edit), do: "Edit User management"

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    user_management = UserManagementContext.get_data!(id)
    {:ok, _} = UserManagementContext.delete_data(user_management)

    {:noreply, stream_delete(socket, :user_managements, user_management)}
  end

  def handle_event("password_reset", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.password_reset(socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password has been reset!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end

  def handle_event("approve", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.user_activation(socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "user Enabled and Password has been reset!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end

  def handle_event("perform", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.status_change(socket.assigns.current_user, params)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "user updated!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end
end
