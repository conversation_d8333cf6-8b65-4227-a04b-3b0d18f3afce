defmodule ServiceManagerWeb.Backend.SystemUserManagementLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.SystemUserManagementContext, as: UserManagementContext
  alias ServiceManager.Schemas.AdminUsers, as: UserManagement
  alias ServiceManagerWeb.Api.Services.ProfileServiceController
  alias ServiceManager.Services.Security.Authorization
  @url "/mobileBanking/SystemUsers"

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket =
      assign(socket, :current_path, @url)
      |> assign(:stats, get_card_stats())

    {:ok, socket}
  end

  @impl true
  def handle_params(%{"sort_field" => sort_field} = params, _url, socket) do
    data = UserManagementContext.retrieve(params)
    pagination = generate_pagination_details(data)

    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> assign(:selected_column, sort_field)
     |> assign(:filter_params, params)
     |> assign(pagination: pagination)
     |> stream(:user_managements, data)}
  end

  def handle_params(params, url, socket) do
    data = UserManagementContext.retrieve()
    pagination = generate_pagination_details(data)

    {:noreply,
     apply_action(socket, socket.assigns.live_action, params)
     |> stream(:user_managements, data)
     |> assign(pagination: pagination)}
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:page_title, "Edit User management")
    |> assign(:user_management, UserManagementContext.get_data!(id))
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New User management")
    |> assign(:user_management, %UserManagement{})
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Listing User managements")
    |> assign(:user_management, nil)
  end

  defp apply_action(socket, :open_account, _params) do
    socket
    |> assign(:page_title, "Open An Account")
    |> assign(:user_management, %ServiceManager.Schemas.User{})
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    user_management = UserManagementContext.get_data!(id)
    {:ok, _} = UserManagementContext.delete_data(user_management)

    {:noreply, stream_delete(socket, :user_managements, user_management)}
  end

  def handle_event("password_reset", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.password_reset(socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "Password has been reset!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end

  def handle_event("approve", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.user_activation(socket.assigns.current_user)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "user Enabled and Password has been reset!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end

  def handle_event("perform", %{"id" => id} = params, socket) do
    UserManagementContext.get_data!(id)
    |> UserManagementContext.status_change(socket.assigns.current_user, params)
    |> case do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "user updated!")
         |> push_navigate(to: @url, replace: true)}

      {:error, error} ->
        {:noreply,
         socket
         |> put_flash(:error, error)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_approval(is_approved) do
    String.capitalize(is_approved)
  end

  defp get_card_stats do
    stats = UserManagementContext.stats()

    [
      %{
        title: "Total Users",
        value: stats.total
      },
      %{
        title: "User Status",
        value: "#{stats.active} / #{stats.inactive}",
        comparison: "active / inactive"
      },
      %{
        title: "Activation Status",
        value: "#{stats.activated} / #{stats.pending}",
        comparison: "activated / pending"
      },
      %{
        title: "Deleted Users",
        value: "#{stats.blocked} / #{stats.deleted}",
        comparison: "blocked / deleted"
      }
    ]
  end

  # Helper functions for permission checks
  defp can_create?(user), do: Authorization.can?(user, :create, :system_users)
  defp can_update?(user), do: Authorization.can?(user, :update, :system_users)
  defp can_delete?(user), do: Authorization.can?(user, :delete, :system_users)
  defp can_approve?(user), do: Authorization.can?(user, :approve, :system_users)
  defp can_activate?(user), do: Authorization.can?(user, :activate, :system_users)
  defp has_any_action_permission?(user), do: can_update?(user) or can_delete?(user) or can_approve?(user) or can_activate?(user)
end
