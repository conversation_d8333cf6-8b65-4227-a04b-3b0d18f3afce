<.live_component
  module={ServiceManagerWeb.Components.StatsComponent}
  id="card-stats"
  title=""
  stats={@stats}
/>
<br />
<hr />

<.header>
  System Users
  <:actions>
    <%= if can_create?(@current_user) do %>
      <.link patch={~p"/mobileBanking/SystemUsers/new"}>
        <.button>Add a System User</.button>
      </.link>
    <% end %>
  </:actions>
</.header>

<.table
  id="system_users"
  filter_params={@filter_params}
  pagination={@pagination}
  selected_column={@selected_column}
  filter_url={~p"/mobileBanking/SystemUsers/filter"}
  export_url={~p"/mobileBanking/SystemUsers/ExcelExportFilter"}
  show_filter={true}
  show_export={true}
  rows={@streams.user_managements}
  row_click={
    fn {_id, user_management} ->
      JS.navigate(~p"/mobileBanking/SystemUsers/#{user_management}")
    end
  }
  class="max-w-full"
>
  <:col :let={{_id, user_management}} filter_item="id" label="ID">
    <div class="text-[12px]"><%= user_management.id || "--" %></div>
  </:col>
  <:col :let={{_id, user_management}} filter_item="email" label="Email">
    <div class="text-[12px]">
      <%= if is_nil(user_management.email) || String.trim(user_management.email) == "",
        do: "--",
        else: user_management.email %>
    </div>
  </:col>
  <:col :let={{_id, user_management}} filter_item="first_name" label="First name">
    <div class="text-[12px]">
      <%= if is_nil(user_management.first_name) || String.trim(user_management.first_name) == "",
        do: "--",
        else: user_management.first_name %>
    </div>
  </:col>
  <:col :let={{_id, user_management}} filter_item="last_name" label="Last name">
    <div class="text-[12px]">
      <%= if is_nil(user_management.last_name) || String.trim(user_management.last_name) == "",
        do: "--",
        else: user_management.last_name %>
    </div>
  </:col>
  <:col :let={{_id, user_management}} filter_item="phone_number" label="Phone number">
    <div class="text-[12px]">
      <%= if is_nil(user_management.phone_number) ||
               String.trim(user_management.phone_number) == "",
             do: "--",
             else: user_management.phone_number %>
    </div>
  </:col>
  <:col :let={{_id, user_management}} filter_item="status" label="Status">
    <.status_pill status={user_management.status} text={handle_approval(user_management.status)} />
  </:col>

  <:col :let={{_id, user_management}} filter_item="inserted_at" label="Created at">
    <%= format_date1(user_management.inserted_at) %>
  </:col>

  <:col :let={{_id, user_management}} filter_item="updated_at" label="Updated at">
    <%= format_date1(user_management.updated_at) %>
  </:col>

  <:action :let={{id, user_management}}>
    <%= if has_any_action_permission?(@current_user) do %>
      <.dropdown id={"dropdown-#{id}"} label="Options">
      <%= case user_management.status do %>
        <% "active" -> %>
          <%= if can_activate?(@current_user) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("perform",
                  value: %{
                    status: "inactive",
                    approved: "false",
                    activation_status: "inactive",
                    id: user_management.id,
                    email: user_management.email
                  }
                )
              }
              data-confirm="Are you sure?"
            >
              Disable
            </.link>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("perform",
                  value: %{
                    status: "blocked",
                    approved: "false",
                    activation_status: "blocked",
                    id: user_management.id,
                    email: user_management.email
                  }
                )
              }
              data-confirm="Are you sure?"
            >
              Block
            </.link>
          <% end %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={
              JS.push("password_reset",
                value: %{id: user_management.id, email: user_management.email}
              )
            }
            data-confirm="Are you sure?"
          >
            Reset Password
          </.link>
        <% "inactive" -> %>
          <.link
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            phx-click={
              JS.push("perform",
                value: %{
                  status: "active",
                  approved: "true",
                  activation_status: "active",
                  id: user_management.id,
                  email: user_management.email
                }
              )
            }
            data-confirm="Are you sure?"
          >
            Approve
          </.link>
          <.link
            class="block px-4 py-2 text-left text-sm text-pink-500 hover:bg-gray-100"
            phx-click={
              JS.push("perform",
                value: %{
                  status: "blocked",
                  approved: "false",
                  activation_status: "blocked",
                  id: user_management.id,
                  email: user_management.email
                }
              )
            }
            data-confirm="Are you sure?"
          >
            Block
          </.link>
        <% _any -> %>
          <%= if can_approve?(@current_user) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={
                JS.push("perform",
                  value: %{
                    status: "active",
                    approved: "true",
                    activation_status: "active",
                    id: user_management.id,
                    email: user_management.email
                  }
                )
              }
              data-confirm="Are you sure?"
            >
              Approve
            </.link>
          <% end %>
      <% end %>

      <%= if can_update?(@current_user) do %>
        <.link
          class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          patch={~p"/mobileBanking/SystemUsers/#{user_management}/edit"}
        >
          Edit
        </.link>
      <% end %>
      <%= if can_delete?(@current_user) do %>
        <.link
          class="block px-4 py-2 text-left text-sm text-rose-700 hover:bg-gray-100"
          phx-click={
            JS.push("perform",
              value: %{id: user_management.id, status: "deleted", activation_status: "deleted"}
            )
          }
          data-confirm="Are you sure?"
        >
          Delete
        </.link>
      <% end %>
    </.dropdown>
    <% end %>
  </:action>
</.table>

<.modal
  :if={@live_action in [:new, :edit]}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/SystemUsers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.SystemUserManagementLive.FormComponent}
    id={@user_management.id || :new}
    title={@page_title}
    action={@live_action}
    current_user={@current_user}
    user_management={@user_management}
    patch={~p"/mobileBanking/SystemUsers"}
  />
</.modal>

<.modal
  :if={@live_action in [:filter, :excel_export]}
  id="data-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/SystemUsers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.SystemUserManagementLive.FilterComponent}
    id={:filters}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    current_user={@current_user}
    filter_params={@filter_params}
    url={@url}
    patch={~p"/mobileBanking/SystemUsers"}
  />
</.modal>

<.modal
  :if={@live_action in [:open_account]}
  id="user_management-modal"
  show
  on_cancel={JS.patch(~p"/mobileBanking/SystemUsers")}
>
  <.live_component
    module={ServiceManagerWeb.Backend.SystemUserManagementLive.OpenAccountComponent}
    id={:open_account}
    title={@page_title}
    action={@live_action}
    user_management={@user_management}
    current_user={@current_user}
    patch={~p"/mobileBanking/SystemUsers"}
  />
</.modal>
