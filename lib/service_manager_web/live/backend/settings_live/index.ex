defmodule ServiceManagerWeb.Backend.SettingsLive.Index do
  use ServiceManagerWeb, :live_view

  alias ServiceManager.Contexts.SystemConfiguarationsContext
  import ServiceManagerWeb.Utilities.PermissionHelpers
  @url "/mobileBanking/systemSettings/More"

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true

  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "System Settings")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :t24, _params) do
    socket
    |> assign(:page_title, "T24 Configuration")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :general_settings, _params) do
    socket
    |> assign(:page_title, "General Settings")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :notifications, _params) do
    socket
    |> assign(:page_title, "Notification Settings")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :images, _params) do
    socket
    |> assign(:page_title, "Image Settings")
    |> assign(:data, nil)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :more_new, _) do
    data = SystemConfiguarationsContext.data_schema()
    pagination = generate_pagination_details(%{})

    socket
    |> assign(:page_title, "New Config")
    #      |> stream(:data, [])
    |> assign(:form_data, data)
    |> assign(:pagination, pagination)
    |> assign(:url, nil)
  end

  defp apply_action(socket, :more_edit, %{"id" => id}) do
    data = SystemConfiguarationsContext.get_setting_by_id(id)
    pagination = generate_pagination_details(%{})

    socket
    |> assign(:page_title, "Edit #{get_key(data)} Config")
    #      |> stream(:data, [])
    |> assign(:form_data, data)
    |> assign(:pagination, pagination)
    |> assign(:url, nil)
  end

  defp apply_action(socket, _, _params) do
    data = SystemConfiguarationsContext.retrieve()
    pagination = generate_pagination_details(data)
    # Remove existing streams from socket assigns
    assigns =
      socket.assigns
      |> Map.delete(:streams)

    socket =
      socket
      |> Map.update(:assigns, assigns, fn _existing_value -> assigns end)

    socket
    |> assign(:page_title, "More Configs")
    |> stream(:data, data)
    |> assign(:data, data)
    |> assign(:form_data, nil)
    |> assign(:url, nil)
    |> assign(pagination: pagination)
  end

  @impl true
  def handle_event("delete", %{"id" => id}, socket) do
    data = SystemConfiguarationsContext.get_setting_by_id(id)
    {:ok, _} = SystemConfiguarationsContext.delete_settings(data)

    Task.start(fn ->
      LogsContext.insert_log(
        "fatal",
        %{
          message: "Config deleted successfully"
        },
        socket
      )
    end)

    {:noreply, stream_delete(socket, :data, data)}
  end

  def handle_event("activate", %{"id" => id}, socket) do
    data = SystemConfiguarationsContext.get_setting_by_id(id)

    SystemConfiguarationsContext.update_data(
      data,
      %{"status" => "active"},
      socket.assigns.current_user
    )
    |> case do
      {:ok, resp} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Config activation completed successfully",
              details: inspect(resp)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Config activated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      error ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Config activation failed",
              error: error
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:error, "Config activation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("deactivate", %{"id" => id}, socket) do
    data = SystemConfiguarationsContext.get_setting_by_id(id)

    SystemConfiguarationsContext.update_data(
      data,
      %{"status" => "inactive"},
      socket.assigns.current_user
    )
    |> case do
      {:ok, resp} ->
        Task.start(fn ->
          LogsContext.insert_log(
            %{
              message: "Config inactivated successfully",
              details: inspect(resp)
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:info, "Config inactivated successfully")
         |> push_navigate(to: "#{@url}", replace: true)}

      error ->
        Task.start(fn ->
          LogsContext.insert_log(
            "error",
            %{
              message: "Config inactivation failed",
              error: error
            },
            socket
          )
        end)

        {:noreply,
         socket
         |> put_flash(:error, "Config inactivation failed")
         |> push_navigate(to: "#{@url}", replace: true)}
    end
  end

  def handle_event("search", %{"isearch" => search}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> search_encode_url(search)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  def handle_event("page_size", %{"page_size" => page_size}, socket) do
    {params, endpoint} =
      socket.assigns.filter_params
      |> page_size_encode_url(page_size)

    {:noreply,
     socket
     |> assign(:filter_params, params)
     |> push_navigate(to: "#{@url}#{endpoint}", replace: true)}
  end

  defp get_key(data) do
    if data do
      data.key
    else
      ""
    end
  end
end
