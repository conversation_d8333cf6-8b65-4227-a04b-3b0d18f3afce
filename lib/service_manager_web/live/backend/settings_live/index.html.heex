<%= if @live_action in [:more, :more_new, :more_edit] do %>
  <.header>
    Configurations
    <:subtitle></:subtitle>

    <:actions>
      <%= if can_create?(@current_user, :system_configurations) do %>
        <.link patch={~p"/mobileBanking/systemSettings/More/new"}>
          <.button>New Config</.button>
        </.link>
      <% end %>
    </:actions>
  </.header>
  <%= if @live_action in [:more] do %>
    <.table
      id="currencies"
      filter_params={@filter_params}
      pagination={@pagination}
      selected_column={@selected_column}
      rows={@streams.data}
      row_click={
        fn {_id, data} -> JS.navigate(~p"/mobileBanking/systemSettings/More/#{data}") end
      }
    >
      <:col :let={{_id, data}} filter_item="id" label="ID"><%= data.id %></:col>
      <:col :let={{_id, data}} filter_item="key" label="Key"><%= data.key %></:col>
      <:col :let={{_id, data}} filter_item="value" label="Value"><%= data.value %></:col>
      <:col :let={{_id, data}} filter_item="status" label="Status">
        <.status_pill status={data.status} text={data.status} />
      </:col>

      <:action :let={{id, dataset}}>
        <%= if has_any_permission?(@current_user, [:update, :delete, :activate], :system_configurations) do %>
          <.dropdown id={"dropdown-#{id}"} label="Options">
          <%= if can_activate?(@current_user, :system_configurations) do %>
            <%= if dataset.status == "active" do %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("deactivate", value: %{id: dataset.id})}
                data-confirm="Are you sure?"
              >
                Deactivate
              </.link>
            <% else %>
              <.link
                class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                phx-click={JS.push("activate", value: %{id: dataset.id})}
                data-confirm="Are you sure?"
              >
                Activate
              </.link>
            <% end %>
          <% end %>

          <%= if can_update?(@current_user, :system_configurations) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              patch={~p"/mobileBanking/systemSettings/More/#{dataset}/edit"}
            >
              Edit
            </.link>
          <% end %>
          <%= if can_delete?(@current_user, :system_configurations) do %>
            <.link
              class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
              phx-click={JS.push("delete", value: %{id: dataset.id}) |> hide("##{id}")}
              data-confirm="Are you sure?"
            >
              Delete
            </.link>
          <% end %>
        </.dropdown>
        <% end %>
      </:action>
    </.table>
  <% end %>
  <.modal
    :if={@live_action in [:more_new, :more_edit]}
    id="data-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/systemSettings/More")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.SettingsLive.Components.FormComponent}
      id={@form_data.id || :new}
      title={@page_title}
      action={@live_action}
      data={@form_data}
      current_user={@current_user}
      patch={~p"/mobileBanking/systemSettings/More"}
    />
  </.modal>
<% else %>
  <.live_component
    module={ServiceManagerWeb.Backend.SettingsLive.Components.Home}
    id={:home}
    title={@page_title}
    action={@live_action}
    data={@data}
    current_user={@current_user}
    patch={~p"/mobileBanking/systemSettings"}
  />

  <.modal
    :if={@live_action in [:t24]}
    id="t24-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/systemSettings")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.SettingsLive.Components.T24}
      id={:t24}
      title={@page_title}
      action={@live_action}
      current_user={@current_user}
      patch={~p"/mobileBanking/systemSettings"}
    />
  </.modal>

  <.modal
    :if={@live_action in [:general_settings]}
    id="generalSettings-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/systemSettings")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.SettingsLive.Components.GeneralSettings}
      id={:general_settings}
      title={@page_title}
      action={@live_action}
      current_user={@current_user}
      patch={~p"/mobileBanking/systemSettings"}
    />
  </.modal>

  <.modal
    :if={@live_action in [:notifications]}
    id="notifications-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/systemSettings")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.SettingsLive.Components.Notifications}
      id={:notifications}
      title={@page_title}
      action={@live_action}
      current_user={@current_user}
      patch={~p"/mobileBanking/systemSettings"}
    />
  </.modal>

  <.modal
    :if={@live_action in [:images]}
    id="images-modal"
    show
    on_cancel={JS.patch(~p"/mobileBanking/systemSettings")}
  >
    <.live_component
      module={ServiceManagerWeb.Backend.SettingsLive.Components.Images}
      id={:images}
      title={@page_title}
      action={@live_action}
      current_user={@current_user}
      patch={~p"/mobileBanking/systemSettings"}
    />
  </.modal>
<% end %>
