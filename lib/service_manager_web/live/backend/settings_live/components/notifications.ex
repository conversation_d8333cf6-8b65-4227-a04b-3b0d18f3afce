defmodule ServiceManagerWeb.Backend.SettingsLive.Components.Notifications do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.NotificationsConfigContext, as: Notifications

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>
          Configure notification settings including email, SMS and push notifications.
        </:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="notifications-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.inputs_for :let={config} field={@form[:config]}>
            <div class="col-span-2 border-b pb-4 mb-4">
              <h3 class="text-lg font-medium">Email Settings</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input
                  field={config[:email_notifications_enabled]}
                  type="checkbox"
                  label="Enable Email Notifications"
                />
                <.input field={config[:email_from_name]} type="text" label="From Name" />
                <.input field={config[:email_from_address]} type="email" label="From Email Address" />
                <.input field={config[:smtp_host]} type="text" label="SMTP Host" />
                <.input field={config[:smtp_port]} type="number" label="SMTP Port" />
                <.input
                  field={config[:smtp_encryption]}
                  type="select"
                  label="SMTP Encryption"
                  options={["tls", "ssl", "none"]}
                />
                <.input field={config[:smtp_username]} type="text" label="SMTP Username" />
                <.input field={config[:smtp_password]} type="password" label="SMTP Password" />
              </div>
            </div>

            <div class="col-span-2 border-b pb-4 mb-4">
              <h3 class="text-lg font-medium">SMS Settings</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input
                  field={config[:sms_notifications_enabled]}
                  type="checkbox"
                  label="Enable SMS Notifications"
                />
                <.input field={config[:sms_provider]} type="text" label="SMS Provider" />
                <.input field={config[:sms_api_key]} type="password" label="API Key" />
                <.input field={config[:sms_sender_id]} type="text" label="Sender ID" />
              </div>
            </div>

            <div class="col-span-2">
              <h3 class="text-lg font-medium">Push Notification Settings</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input
                  field={config[:push_notifications_enabled]}
                  type="checkbox"
                  label="Enable Push Notifications"
                />
                <.input field={config[:fcm_server_key]} type="password" label="FCM Server Key" />
                <.input field={config[:apn_key_id]} type="text" label="APN Key ID" />
                <.input field={config[:apn_team_id]} type="text" label="APN Team ID" />
                <.input field={config[:apn_bundle_id]} type="text" label="APN Bundle ID" />
                <.input field={config[:apn_key_file]} type="text" label="APN Key File Path" />
              </div>
            </div>
          </.inputs_for>
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Settings</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    schema = Notifications.get_setting_schema()
    changeset = Notifications.change_setting(schema)

    {:ok,
     socket
     |> assign(:schema, schema)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset)
     end)}
  end

  @impl true
  def handle_event("validate", %{"notifications" => params}, socket) do
    changeset = Notifications.change_setting(socket.assigns.schema, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"notifications" => params}, socket) do
    case socket.assigns.schema.id do
      nil -> create_setting(socket, params)
      _id -> update_setting(socket, params, socket.assigns.schema)
    end
  end

  defp create_setting(socket, params) do
    case Notifications.insert_data(params, socket.assigns.current_user) do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Notification settings saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp update_setting(socket, params, schema) do
    case Notifications.update_data(schema, params, socket.assigns.current_user) do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Notification settings updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
