defmodule ServiceManagerWeb.Backend.SettingsLive.Components.FormComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.SystemConfiguarationsContext, as: GeneralSettingsContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage your configuration in the database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="general-settings-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.input field={@form[:key]} type="text" label="Key" />
          <.input field={@form[:value]} type="text" label="Value" />
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Settings</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(%{data: data} = assigns, socket) do
    schema = data
    changeset = GeneralSettingsContext.change_setting(schema)

    {:ok,
     socket
     |> assign(:schema, schema)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset)
     end)}
  end

  @impl true
  def handle_event("validate", %{"config" => params}, socket) do
    changeset = GeneralSettingsContext.change_setting(socket.assigns.schema, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"config" => params}, socket) do
    case socket.assigns.schema.id do
      nil -> create_setting(socket, params)
      _id -> update_setting(socket, params, socket.assigns.schema)
    end
  end

  defp create_setting(socket, params) do
    params
    |> Map.put("created_by", socket.assigns.current_user.id)
    |> GeneralSettingsContext.create_setting()
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Settings saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp update_setting(socket, params, schema) do
    new_params =
      params
      |> Map.put("updated_by", socket.assigns.current_user.id)

    GeneralSettingsContext.update_setting(schema, new_params)
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Settings updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
