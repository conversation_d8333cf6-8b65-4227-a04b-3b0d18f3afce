defmodule ServiceManagerWeb.Backend.SettingsLive.Components.Home do
  use ServiceManagerWeb, :live_component
  alias ServiceManagerWeb.Components.Utilities.CustomIcons

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
        <%= for option <- @options do %>
          <div class="relative flex items-center space-x-3 rounded-lg border border-gray-50 bg-white px-6 py-5 shadow-xl overflow-hidden">
            <div class="shrink-0">
              <CustomIcons.icon key={option.icon} class="ml-1 w-20 h-20 text-fdhNavyBlue" />
            </div>
            <div class="min-w-0 flex-1">
              <.link navigate={option.url} class="focus:outline-none">
                <span class="absolute inset-0" aria-hidden="true"></span>
                <p class="text-xl font-medium text-gray-900"><%= option.name %></p>
                <p class="truncate text-sm text-gray-500"><%= option.description %></p>
              </.link>
            </div>
            <!-- Background SVG -->
            <div class="absolute bottom-0 right-0 opacity-10">
              <CustomIcons.icon key={option.icon} class="w-32 h-32 -mb-8 -mr-8 -rotate-45" />
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(:options, options())
     |> assign(assigns)}
  end

  def options do
    [
      %{
        name: "General Settings",
        url: "/mobileBanking/systemSettings/generalSettings",
        description: "Configure Fundamental information of the site.",
        icon: "cog"
      },
      %{
        name: "System Settings",
        url: "/",
        description: "Control all the basic modules of the system.",
        icon: "cog-8-tooth"
      },
      %{
        name: "Notification Settings",
        url: "/mobileBanking/systemSettings/notifications",
        description: "Configure Fundamental information of the site.",
        icon: "bell"
      },
      %{
        name: "T-24 Settings",
        url: "/mobileBanking/systemSettings/t24Configs",
        description: "Configure all Fundamentals for the T24 integration.",
        icon: "puzzle-piece"
      },
      %{
        name: "Logo & Favicon",
        url: "/mobileBanking/systemSettings/images",
        description: "upload your logo and Favicon here.",
        icon: "photo"
      },
      %{
        name: "Mobile Forms",
        url: "/mobileBanking/mobile-forms",
        description: "Manage dynamic forms for mobile applications.",
        icon: "document"
      },
      %{
        name: "Dynamic Forms",
        url: "/mobileBanking/dynamic-forms",
        description: "Configure dynamic API forms and processes.",
        icon: "document"
      },
      %{
        name: "More Configurations",
        url: "/mobileBanking/systemSettings/More",
        description: "Add more configurations.",
        icon: "plus"
      }
    ]
  end
end
