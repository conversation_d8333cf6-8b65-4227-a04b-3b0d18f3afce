defmodule ServiceManagerWeb.Backend.SettingsLive.Components.GeneralSettings do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.GeneralSettingsContext

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Use this form to manage general settings configuration in the database.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="general-settings-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.inputs_for :let={config} field={@form[:config]}>
            <.input field={config[:site_title]} type="text" label="Site Title" />
            <.input field={config[:currency]} type="text" label="Currency" />
            <.input field={config[:currency_symbol]} type="text" label="Currency Symbol" />
            <.input field={config[:timezone]} type="text" label="Timezone" />
            <.input field={config[:base_color]} type="color" label="Base Color" />
            <.input field={config[:secondary_color]} type="color" label="Secondary Color" />
            <.input field={config[:opt_expiration_time]} type="text" label="OTP Expiration Time" />
            <.input field={config[:user_idle_time]} type="text" label="User Idle Time" />
            <.input field={config[:account_number_length]} type="text" label="Account Number Length" />
            <.input field={config[:minimum_limit]} type="text" label="Minimum Limit" />
            <.input field={config[:daily_limit]} type="text" label="Daily Limit" />
            <.input field={config[:monthly_limit]} type="number" label="Monthly Limit" />
          </.inputs_for>
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Settings</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    schema = GeneralSettingsContext.get_setting_schema()
    changeset = GeneralSettingsContext.change_setting(schema)

    {:ok,
     socket
     |> assign(:schema, schema)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset)
     end)}
  end

  @impl true
  def handle_event("validate", %{"general_settings" => params}, socket) do
    changeset = GeneralSettingsContext.change_setting(socket.assigns.schema, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"general_settings" => params}, socket) do
    case socket.assigns.schema.id do
      nil -> create_setting(socket, params)
      _id -> update_setting(socket, params, socket.assigns.schema)
    end
  end

  defp create_setting(socket, params) do
    params
    |> Map.put("created_by", socket.assigns.current_user.id)
    |> GeneralSettingsContext.create_setting()
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Settings saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp update_setting(socket, params, schema) do
    new_params =
      params
      |> Map.put("updated_by", socket.assigns.current_user.id)

    GeneralSettingsContext.update_setting(schema, new_params)
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Settings updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
