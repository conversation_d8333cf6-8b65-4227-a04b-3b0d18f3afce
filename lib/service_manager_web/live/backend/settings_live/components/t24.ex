defmodule ServiceManagerWeb.Backend.SettingsLive.Components.T24 do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.T24Context

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>
          Use this form to manage t24 integration configurations record in the database.
        </:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="currency-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.inputs_for :let={a} field={@form[:config]}>
            <.input field={a[:base_url]} type="text" label="Base URL" />
            <.input field={a[:username]} type="text" label="Username" />
            <.input field={a[:token]} type="text" label="Token" />
            <.input field={a[:password]} type="text" label="Password" />

            <.inputs_for :let={b} field={a[:urls]}>
              <.input field={b[:create_fund_transfer]} type="text" label="Create Fund Transfer URL" />
              <.input field={b[:get_customer_profile]} type="text" label="Get Customer Profile URL" />
              <.input field={b[:get_account_balance]} type="text" label="Get Account Balance URL" />
              <.input field={b[:open_current_account]} type="text" label="Open Current Account URL" />
              <.input field={b[:get_book_balance]} type="text" label="Get Book Balance URL" />
              <.input field={b[:reserve_funds]} type="text" label="Reserve Funds URL" />
              <.input
                field={b[:release_reserved_funds]}
                type="text"
                label="Release Reserved Funds URL"
              />
              <.input field={b[:get_transaction]} type="text" label="Get Transactions URL" />
              <.input
                field={b[:get_account_transaction]}
                type="text"
                label="Get Account Transaction URL"
              />
              <.input field={b[:get_currency_exchange]} type="text" label="Get Currency Exchange URL" />
            </.inputs_for>
          </.inputs_for>
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Currency</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    schema = T24Context.get_setting_schema()
    changeset = T24Context.change_setting(schema)

    {:ok,
     socket
     |> assign(:schema, schema)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset)
     end)}
  end

  @impl true
  def handle_event("validate", %{"t24" => params}, socket) do
    changeset = T24Context.change_setting(socket.assigns.schema, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"t24" => params}, socket) do
    case socket.assigns.schema.id do
      nil -> new_setting(socket, params)
      schema -> update(socket, params, schema)
    end
  end

  def new_setting(socket, params) do
    params
    |> Map.put("created_by", socket.assigns.current_user.id)
    |> T24Context.create_setting()
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  def update_setting(socket, params, schema) do
    new_params =
      params
      |> Map.put("updated_by", socket.assigns.current_user.id)

    T24Context.update_setting(schema, params)
    |> case do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
