defmodule ServiceManagerWeb.Backend.SettingsLive.Components.Images do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Contexts.ImagesContext, as: Images

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <.header>
        <%= @title %>
        <:subtitle>Configure image settings including logo, favicon and storage settings.</:subtitle>
      </.header>

      <.simple_form
        for={@form}
        id="images-form"
        phx-target={@myself}
        phx-change="validate"
        phx-submit="save"
      >
        <div class="grid grid-cols-2 gap-5">
          <.inputs_for :let={config} field={@form[:config]}>
            <div class="col-span-2 border-b pb-4 mb-4">
              <h3 class="text-lg font-medium">Branding Images</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input field={config[:logo_url]} type="text" label="Logo URL" />
                <.input field={config[:favicon_url]} type="text" label="Favicon URL" />
                <.input
                  field={config[:login_background_url]}
                  type="text"
                  label="Login Background URL"
                />
                <.input
                  field={config[:dashboard_banner_url]}
                  type="text"
                  label="Dashboard Banner URL"
                />
              </div>
            </div>

            <div class="col-span-2 border-b pb-4 mb-4">
              <h3 class="text-lg font-medium">Image Settings</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input field={config[:max_file_size]} type="number" label="Max File Size (bytes)" />
                <.input field={config[:allowed_formats]} type="text" label="Allowed Formats" />
                <.input
                  field={config[:compression_enabled]}
                  type="checkbox"
                  label="Enable Compression"
                />
                <.input
                  field={config[:compression_quality]}
                  type="number"
                  label="Compression Quality (1-100)"
                />
              </div>
            </div>

            <div class="col-span-2">
              <h3 class="text-lg font-medium">Storage Settings</h3>
              <div class="grid grid-cols-2 gap-4">
                <.input
                  field={config[:storage_provider]}
                  type="select"
                  label="Storage Provider"
                  options={["local", "s3", "cloudinary"]}
                />
                <.input field={config[:storage_bucket]} type="text" label="Storage Bucket" />
                <.input field={config[:storage_region]} type="text" label="Storage Region" />
                <.input field={config[:storage_access_key]} type="password" label="Access Key" />
                <.input field={config[:storage_secret_key]} type="password" label="Secret Key" />
              </div>
            </div>
          </.inputs_for>
        </div>
        <:actions>
          <.button phx-disable-with="Saving...">Save Settings</.button>
        </:actions>
      </.simple_form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    schema = Images.get_setting_schema()
    changeset = Images.change_setting(schema)

    {:ok,
     socket
     |> assign(:schema, schema)
     |> assign(assigns)
     |> assign_new(:form, fn ->
       to_form(changeset)
     end)}
  end

  @impl true
  def handle_event("validate", %{"images" => params}, socket) do
    changeset = Images.change_setting(socket.assigns.schema, params)
    {:noreply, assign(socket, form: to_form(changeset, action: :validate))}
  end

  def handle_event("save", %{"images" => params}, socket) do
    case socket.assigns.schema.id do
      nil -> create_setting(socket, params)
      _id -> update_setting(socket, params, socket.assigns.schema)
    end
  end

  defp create_setting(socket, params) do
    case Images.create_setting(params) do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Image settings saved successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end

  defp update_setting(socket, params, schema) do
    case Images.update_setting(schema, params) do
      {:ok, _resp} ->
        {:noreply,
         socket
         |> put_flash(:info, "Image settings updated successfully")
         |> push_patch(to: socket.assigns.patch)}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, form: to_form(changeset))}
    end
  end
end
