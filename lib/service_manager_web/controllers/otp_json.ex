defmodule ServiceManagerWeb.OTPJSON do
  @doc """
  Renders OTP data.
  """
  def generate(%{otp: otp, expires_at: expires_at}) do
    %{
      data: %{
        otp: otp,
        expires_at: expires_at
      },
      status: true,
      message: "<PERSON>TP processed"
    }
  end

  def validate(%{message: message}) do
    %{
      data: %{
        message: message
      },
      status: true,
      message: "OTP processed"
    }
  end

  def error(%{error: error}) do
    %{
      error: %{
        message: error
      },
      status: false,
      message: "OTP not processed"
    }
  end
end
