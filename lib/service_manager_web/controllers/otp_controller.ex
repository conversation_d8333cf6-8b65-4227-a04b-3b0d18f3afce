defmodule ServiceManagerWeb.OTPController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Accounts
  alias ServiceManagerWeb.OTPJSON
  alias ServiceManager.WalletAccounts

  @otp_length 6
  @otp_expiry_minutes 5

  def generate(conn, _params) do
    user = Accounts.get_user!(conn.assigns.user.id)

    case Accounts.generate_otp_for_user(user) do
      {:ok, otp, expires_at} ->
        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: user.phone_number,
          message: "#{user.first_name} #{user.last_name}, your OTP code is #{otp}"
        })

        conn
        |> put_status(:created)
        |> render(:generate, %{otp: otp, expires_at: expires_at})

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, %{error: reason})
    end
  end

  def validate(conn, %{"otp" => otp}) do
    user = Accounts.get_user!(conn.assigns.user.id)

    case Accounts.validate_otp_for_user(user, otp) do
      {:ok, :valid} ->
        render(conn, :validate, %{message: "OTP is valid"})

      {:error, :invalid_otp} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, %{error: "Invalid OTP"})
    end
  end

  def generate_wallet_otp(conn, params) do
    user = WalletAccounts.get_wallet_user_by_mobile_number(params["mobile_number"])

    case WalletAccounts.generate_otp_for_user(user) do
      {:ok, otp, expires_at} ->
        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: user.mobile_number,
          message:
            "#{user.first_name || user.mobile_number} #{user.last_name}, your OTP code is #{otp}"
        })

        conn
        |> put_status(:created)
        |> render(:generate, %{otp: otp, expires_at: expires_at})

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, %{error: reason})
    end
  end

  def validate_wallet_otp(conn, %{"otp" => otp, "mobile_number" => mobile_number}) do
    user = WalletAccounts.get_wallet_user_by_mobile_number(mobile_number)

    case WalletAccounts.validate_otp_for_user(user, otp) do
      {:ok, :valid} ->
        render(conn, :validate, %{message: "OTP is valid"})

      {:error, :invalid_otp} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, %{error: "Invalid OTP"})
    end
  end

  def generate_general_otp(conn, %{"mobile_number" => mobile_number} = params) do
    otp =
      :rand.uniform(:math.pow(10, @otp_length) |> round())
      |> Integer.to_string()
      |> String.pad_leading(@otp_length, "0")

    expires_at = DateTime.utc_now() |> DateTime.add(@otp_expiry_minutes * 60, :second)

    Cachex.put(:otp_cache, mobile_number, otp)
    Cachex.expire(:otp_cache, mobile_number, :timer.seconds(@otp_expiry_minutes * 60))

    ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
      msisdn: mobile_number,
      message: "#{mobile_number}, your OTP code is #{otp}"
    })

    conn
    |> put_status(:created)
    |> render(:generate, %{otp: otp, expires_at: expires_at})
  end

  def validate_general_otp(conn, %{"mobile_number" => mobile_number, "otp" => otp}) do
    case Cachex.get(:otp_cache, mobile_number) do
      {:ok, cache_otp} ->
        if cache_otp == otp do
          Cachex.del(:otp_cache, mobile_number)
          render(conn, :validate, %{message: "OTP is valid"})
        else
          conn
          |> put_status(:unprocessable_entity)
          |> render(:error, %{error: "Invalid OTP"})
        end

      {:error, _} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, %{error: "Invalid OTP"})
    end
  end
end
