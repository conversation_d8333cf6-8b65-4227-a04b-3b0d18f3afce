defmodule ServiceManagerWeb.Reports.TransactionsExportReportsController do
  @moduledoc """
  Controller for handling transaction report exports in Excel and CSV formats.

  Provides endpoints for:
  - Exporting transaction data to Excel format
  - Exporting transaction data to CSV format
  - Exporting wallet transaction data to Excel format

  Handles form validation, data retrieval, and file generation with appropriate error handling.
  """

  use ServiceManagerWeb, :controller

  alias ServiceManager.Contexts.TransactionsContext
  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManagerWeb.Utilities.Utils
  alias ServiceManagerWeb.Reports.ExcelProcessor
  alias ServiceManager.Contexts.WalletTransactionsContext

  @doc """
  Handles Excel export requests for wallet transactions.

  Takes form parameters, validates them using the EmbeddedForm schema, retrieves wallet transaction data
  and generates an Excel file.

  ## Parameters
    - conn: Plug connection
    - form_params: Map containing form parameters for filtering wallet transactions

  ## Returns
    - Excel file download if validation passes and data is found
    - Redirects with error message if no data found or validation fails
  """

  #  def wallet_excel(conn, %{"form" => form_params}) do
  #    user_id = conn.assigns.current_user.id
  #
  #    Task.start(fn ->
  #      case wallet_validate_form(form_params) do
  #        {:ok, entries} ->
  #          case ExcelProcessor.render(
  #            entries,
  #            conn.assigns.current_user,
  #            "Transactions",
  #            excluded_fields: [],
  #            title_format: [size: 12, bold: true],
  #            col_width: 25
  #          ) do
  #            {:ok, excel_data} ->
  #              Phoenix.PubSub.broadcast(
  #                ServiceManager.PubSub,
  #                "user:#{user_id}",
  #                {:excel_ready, excel_data, "Transactions Report #{Utils.custom_datetime}.xlsx"}
  #              )
  #              |> IO.inspect(label: "")
  #            {:error, reason} ->
  #              Phoenix.PubSub.broadcast(
  #                ServiceManager.PubSub,
  #                "user:#{user_id}",
  #                {:excel_error, reason}
  #              )
  #          end
  #        {:error, _} ->
  #          Phoenix.PubSub.broadcast(
  #            ServiceManager.PubSub,
  #            "user:#{user_id}",
  #            {:excel_error, "No data found for your selected search criteria"}
  #          )
  #      end
  #    end)
  #
  #    conn
  #    |> put_flash(:info, "Processing")
  #    |> redirect(to: ~p"/mobileBanking/WalletTransactions")
  #  end

  def wallet_excel(conn, %{"form" => form_params}) do
    case wallet_validate_form(form_params) do
      {:ok, entries} ->
        generate_excel_report(conn, entries, conn.assigns.current_user)

      {:error, :no_data} ->
        conn
        |> put_flash(:error, "No data found for your selected search criteria")
        |> redirect(to: ~p"/mobileBanking/WalletTransactions")

      {:error, _} ->
        handle_invalid_form(conn)
    end
  end

  @doc """
  Validates wallet transaction form parameters and retrieves matching data.

  Validates the form parameters using EmbeddedForm schema and retrieves filtered wallet transactions
  if validation passes.

  ## Parameters
    - form_params: Map containing form parameters to validate

  ## Returns
    - {:ok, entries} if validation passes and data is found
    - {:error, :no_data} if validation passes but no data matches criteria
    - {:error, :invalid_form} if validation fails
  """
  defp wallet_validate_form(form_params) do
    changeset = EmbeddedForm.change_form(%EmbeddedForm{}, form_params)

    if changeset.valid? do
      entries = WalletTransactionsContext.retrieve_report_data(form_params)

      if Enum.empty?(entries) do
        {:error, :no_data}
      else
        {:ok, entries}
      end
    else
      {:error, :invalid_form}
    end
  end

  @doc """
  Handles Excel export requests for regular transactions.

  Takes form parameters, validates them, retrieves transaction data and generates an Excel file.
  Returns the Excel file for download or redirects with error message if validation fails.

  ## Parameters
    - conn: Plug connection
    - form_params: Map containing form parameters for filtering transactions

  ## Returns
    - Excel file download if validation passes and data is found
    - Redirects with error message if no data found or validation fails
  """
  def excel(conn, %{"form" => form_params}) do
    case validate_form(form_params) do
      {:ok, entries} ->
        generate_excel_report(conn, entries, conn.assigns.current_user)

      {:error, :no_data} ->
        conn
        |> put_flash(:error, "No data found for your selected search criteria")
        |> redirect(to: ~p"/mobileBanking/transactions")

      {:error, _} ->
        handle_invalid_form(conn)
    end
  end

  @doc """
  Handles CSV export requests for regular transactions.

  Takes form parameters, validates them, retrieves transaction data and generates a CSV file.
  Returns the CSV file for download or redirects with error message if validation fails.

  ## Parameters
    - conn: Plug connection
    - form_params: Map containing form parameters for filtering transactions

  ## Returns
    - CSV file download if validation passes and data is found
    - Redirects with error message if no data found or validation fails
  """
  def csv(conn, %{"form" => form_params}) do
    case validate_form(form_params) do
      {:ok, entries} ->
        generate_csv_report(conn, entries)

      {:error, :no_data} ->
        conn
        |> put_flash(:error, "No data found for the selected filters")
        |> redirect(to: ~p"/mobileBanking/transactions")

      {:error, _} ->
        handle_invalid_form(conn)
    end
  end

  @doc """
  Validates the form parameters using EmbeddedForm schema.

  ## Parameters
    - form_params: Map containing form parameters to validate

  ## Returns
    - {:ok, entries} if validation passes and data is found
    - {:error, :no_data} if validation passes but no data matches criteria
    - {:error, :invalid_form} if validation fails
  """
  defp validate_form(form_params) do
    changeset = EmbeddedForm.change_form(%EmbeddedForm{}, form_params)

    if changeset.valid? do
      entries = TransactionsContext.retrieve_report_data(form_params)

      if Enum.empty?(entries) do
        {:error, :no_data}
      else
        {:ok, entries}
      end
    else
      {:error, :invalid_form}
    end
  end

  @doc """
  Generates Excel report using ExcelProcessor.

  Configures Excel formatting options and returns file for download.
  Handles errors by redirecting with flash message.

  ## Parameters
    - conn: Plug connection
    - entries: List of transaction entries to include in report
    - user: Current user generating the report

  ## Returns
    - Excel file download response if successful
    - Redirects with error message if generation fails
  """
  defp generate_excel_report(conn, entries, user) do
    name = "Transactions"

    case ExcelProcessor.render(
           entries,
           user,
           name,
           excluded_fields: [],
           title_format: [size: 12, bold: true],
           col_width: 25
         ) do
      {:ok, excel_data} ->
        conn
        |> put_resp_content_type(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        |> put_resp_header(
          "content-disposition",
          "attachment; filename=#{name} Report #{Utils.custom_datetime()}.xlsx"
        )
        |> send_resp(200, excel_data)

      {:error, reason} ->
        conn
        |> put_flash(:error, reason)
        |> redirect(to: ~p"/mobileBanking/transactions")
    end
  end

  @doc """
  Generates CSV report from transaction data.

  Sets appropriate headers and returns file for download.

  ## Parameters
    - conn: Plug connection
    - entries: List of transaction entries to include in report

  ## Returns
    - CSV file download response
  """
  defp generate_csv_report(conn, entries) do
    conn
    |> put_resp_content_type("text/csv")
    |> put_resp_header(
      "content-disposition",
      "attachment; filename=transactions_#{Utils.custom_datetime()}.csv"
    )
    |> send_resp(200, generate_csv_content(entries))
  end

  @doc """
  Handles invalid form submission by redirecting with error message.

  ## Parameters
    - conn: Plug connection

  ## Returns
    - Redirects to transactions page with error message
  """
  defp handle_invalid_form(conn) do
    conn
    |> put_flash(:error, "Invalid filter parameters")
    |> redirect(to: ~p"/mobileBanking/transactions")
  end

  @doc """
  Generates CSV content from transaction data.

  Creates headers and rows from transaction fields and encodes to CSV format.

  ## Parameters
    - transactions: List of transaction records to convert to CSV

  ## Returns
    - String containing CSV formatted data
  """
  defp generate_csv_content(transactions) do
    headers = [
      "ID",
      "Reference",
      "Type",
      "Amount",
      "Status",
      "From Account",
      "To Account",
      "Created At"
    ]

    rows =
      Enum.map(transactions, fn t ->
        [
          t.id,
          t.reference,
          t.type,
          t.amount,
          t.status,
          t.from_account,
          t.to_account,
          t.inserted_at
        ]
      end)

    ([headers] ++ rows)
    |> CSV.encode()
    |> Enum.join()
  end
end
