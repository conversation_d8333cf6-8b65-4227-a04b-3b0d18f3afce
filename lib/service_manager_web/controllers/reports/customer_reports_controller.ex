defmodule ServiceManagerWeb.Reports.CustomerReportsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Schemas.Embedded.EmbeddedForm
  alias ServiceManager.Contexts.CustomersContext, as: Accounts
  alias ServiceManagerWeb.Utilities.Utils
  alias ServiceManagerWeb.Reports.ExcelProcessor

  def excel(conn, %{"form" => form_params}) do
    case validate_form(form_params) do
      {:ok, entries} ->
        generate_excel_report(conn, entries, conn.assigns.current_user)

      {:error, :no_data} ->
        conn
        |> put_flash(:error, "No data found for the selected filters")
        |> redirect(to: ~p"/mobileBanking/customers")

      {:error, _} ->
        handle_invalid_form(conn)
    end
  end

  def csv(conn, %{"form" => form_params}) do
    case validate_form(form_params) do
      {:ok, entries} ->
        generate_csv_report(conn, entries)

      {:error, :no_data} ->
        conn
        |> put_flash(:error, "No data found for the selected filters")
        |> redirect(to: ~p"/mobileBanking/customers")

      {:error, _} ->
        handle_invalid_form(conn)
    end
  end

  defp validate_form(form_params) do
    changeset = EmbeddedForm.change_form(%EmbeddedForm{}, form_params)

    if changeset.valid? do
      entries = Accounts.retrieve_report_data(form_params)

      if Enum.empty?(entries) do
        {:error, :no_data}
      else
        {:ok, entries}
      end
    else
      {:error, :invalid_form}
    end
  end

  defp generate_excel_report(conn, entries, user) do
    name = "Customers"

    case ExcelProcessor.render(
           entries,
           user,
           name,
           excluded_fields: [:accounts, :hashed_password, :roles, :user_permissions, :otp],
           title_format: [size: 12, bold: true],
           col_width: 25
         ) do
      {:ok, excel_data} ->
        conn
        |> put_resp_content_type(
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        |> put_resp_header(
          "content-disposition",
          "attachment; filename=#{name} Report #{Utils.custom_datetime()}.xlsx"
        )
        |> send_resp(200, excel_data)

      {:error, reason} ->
        conn
        |> put_flash(:error, reason)
        |> redirect(to: ~p"/mobileBanking/customers")
    end
  end

  defp generate_csv_report(conn, entries) do
    conn
    |> put_resp_content_type("text/csv")
    |> put_resp_header("content-disposition", "attachment; filename=customers.csv")
    |> send_resp(200, generate_csv_content(entries))
  end

  defp handle_invalid_form(conn) do
    conn
    |> put_flash(:error, "Invalid filter parameters")
    |> redirect(to: ~p"/mobileBanking/customers")
  end

  defp generate_csv_content(customers) do
    headers = [
      "ID",
      "Email",
      "Name",
      "Nickname",
      "First Name",
      "Last Name",
      "Phone Number",
      "Approved",
      "Created At"
    ]

    rows =
      Enum.map(customers, fn c ->
        [
          c.id,
          c.email,
          c.name,
          c.nickname,
          c.first_name,
          c.last_name,
          c.phone_number,
          c.approved,
          c.inserted_at
        ]
      end)

    [headers | rows]
    |> CSV.encode()
    |> Enum.join()
  end
end
