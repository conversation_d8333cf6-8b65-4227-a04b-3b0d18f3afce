defmodule ServiceManagerWeb.Reports.ExcelProcessor do
  @moduledoc """
  Generic Excel report processor that dynamically generates reports based on input data.
  Handles Ecto schemas and various data types for Excel report generation.

  This module provides functionality to:
  - Generate Excel reports from lists of maps or Ecto schemas
  - Format different data types (DateTime, Decimal, etc.) appropriately
  - Add metadata and styling to reports
  - Handle chunked processing for large datasets
  - Track telemetry metrics for report generation
  """

  import ServiceManagerWeb.Utilities.Utils
  alias Elixlsx.{Workbook, Sheet}

  @type report_options :: [
          # Fields to exclude from report
          excluded_fields: [atom()],
          # Formatting options for title
          title_format: keyword(),
          # Name of the Excel sheet
          sheet_name: String.t(),
          # Custom styles for cells
          styles: keyword(),
          # Width of columns
          col_width: pos_integer()
        ]

  # Default styles for different cell types
  @default_styles %{
    title: [bold: true],
    header: [bold: true],
    cell: []
  }

  # Default column width in characters
  @default_col_width 20
  # Row number where to freeze the pane
  @freeze_pane_row 8
  # Number of entries to process at once
  @chunk_size 1000

  # Fields to always exclude from processing
  @excluded_fields [:__meta__, :__struct__, :__cardinality__, :__field__, :__owner__]

  defmodule Options do
    @moduledoc """
    Configuration options for Excel report generation.
    Provides a structured way to pass configuration to the report generator.
    """

    defstruct excluded_fields: [],
              title_format: [],
              sheet_name: "default",
              styles: [],
              col_width: 20

    @doc "Creates a new Options struct with given options"
    def new(opts \\ []) do
      struct(__MODULE__, opts)
    end
  end

  defmodule Error do
    @moduledoc """
    Error handling for Excel report generation.
    Provides structured error reporting with optional reason details.
    """

    defexception [:message, :reason]

    @type t :: %__MODULE__{
            message: String.t(),
            reason: any()
          }

    @doc "Creates a new Error struct with message and optional reason"
    def new(message, reason \\ nil) do
      %__MODULE__{message: message, reason: reason}
    end
  end

  defmodule Formatter do
    @moduledoc """
    Handles formatting of different data types for Excel cells.
    Provides consistent formatting for dates, times, decimals, and other types.
    """

    @doc "Formats a value based on its type and optional format specification"
    def format_cell_value(value, type \\ :default)

    def format_cell_value(%DateTime{} = dt, _), do: format_datetime(dt)
    def format_cell_value(%NaiveDateTime{} = dt, _), do: format_datetime(dt)
    def format_cell_value(%Date{} = date, :date), do: format_date_value(date)

    def format_cell_value(%{calendar: Calendar.ISO, year: y, month: m, day: d}, :date) do
      {:ok, date} = Date.new(y, m, d)
      format_date_value(date)
    end

    def format_cell_value(%Decimal{} = value, :amount), do: format_amount(value)
    def format_cell_value(value, :amount) when is_number(value), do: format_amount(value)

    def format_cell_value(value, :amount) when is_binary(value) do
      case Decimal.parse(value) do
        {:ok, decimal} -> format_amount(decimal)
        _ -> value
      end
    end

    def format_cell_value(nil, _), do: ""
    def format_cell_value(value, _), do: to_string(value)

    # Formats datetime with full details including seconds
    defp format_datetime(dt), do: Timex.format!(dt, "%A, %d %B %Y  -  %H:%M:%S:%M", :strftime)

    # Formats date in a human-readable format
    defp format_date_value(date), do: Timex.format!(date, "%A, %d %B %Y", :strftime)

    # Formats numeric amounts with 2 decimal places and thousand separators
    defp format_amount(value) do
      Number.Delimit.number_to_delimited(value, precision: 2)
    end
  end

  @doc """
  Renders an Excel report from the given entries.

  ## Parameters
    - entries: List of maps or structs to include in report
    - user: Map containing user details (requires :first_name and :last_name)
    - name: Name for the report (default: "default")
    - opts: Keyword list of options (see @type report_options)
    
  ## Returns
    - {:ok, binary} - Excel file as binary
    - {:error, reason} - Error message if generation fails
  """
  @spec render([map()], map(), String.t(), report_options()) ::
          {:ok, binary()} | {:error, String.t()}
  def render(entries, user, name \\ "default", opts \\ []) do
    options = Options.new(Keyword.merge([sheet_name: name], opts))
    filename = "#{name}_report.xlsx"

    with :ok <- validate_entries(entries),
         {:ok, user} <- validate_user(user),
         {:ok, report_data} <- process_report_data(entries, options),
         {:ok, workbook} <- generate_report(report_data, user, options),
         {:ok, {_charlist, binary}} <- Elixlsx.write_to_memory(workbook, filename) do
      # Track telemetry metrics for report generation
      :telemetry.execute(
        [:service_manager, :excel_processor, :report_generation],
        %{row_count: length(entries)},
        %{report_type: name}
      )

      {:ok, binary}
    else
      {:error, %Error{} = error} -> {:error, error.message}
      error -> {:error, "Failed to generate report: #{inspect(error)}"}
    end
  end

  @doc "Extracts and formats headers from the first entry"
  @spec extract_headers([map()], [atom()]) ::
          {:ok, [[any()]]} | {:error, Error.t()}
  defp extract_headers([], _), do: {:error, Error.new("No entries provided")}

  defp extract_headers([first | _] = entries, excluded_fields) do
    first = if Map.has_key?(first, :__struct__), do: Map.from_struct(first), else: first

    headers =
      first
      |> Map.keys()
      |> Enum.reject(&(&1 in (@excluded_fields ++ (excluded_fields || []))))
      |> Enum.map(&format_header/1)

    {:ok, [headers]}
  end

  # Formats header field names for display
  defp format_header(field) do
    field
    |> to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  # Processes raw data into format suitable for Excel
  defp process_report_data(entries, opts) do
    with {:ok, headers} <- extract_headers(entries, opts.excluded_fields),
         {:ok, formatted_data} <- format_entries(entries, headers, opts),
         {:ok, metadata} <- prepare_metadata(entries) do
      {:ok, %{headers: headers, data: formatted_data, metadata: metadata}}
    end
  end

  # Formats entries in chunks to handle large datasets efficiently
  defp format_entries(entries, headers, opts) do
    formatted_data =
      entries
      |> Stream.chunk_every(@chunk_size)
      |> Stream.flat_map(&process_chunk(&1, headers, opts))
      |> Enum.to_list()

    {:ok, formatted_data}
  end

  # Processes a chunk of entries, converting them to Excel-friendly format
  defp process_chunk(entries, [headers], opts) do
    entries
    |> Enum.map(fn entry ->
      entry =
        case entry do
          %{__struct__: _} ->
            entry
            |> Map.from_struct()
            |> Map.drop(@excluded_fields)

          _ ->
            entry
        end

      headers
      |> Enum.map(fn header ->
        field = header |> String.downcase() |> String.replace(" ", "_") |> String.to_atom()
        value = Map.get(entry, field)
        format_field_value(value, field)
      end)
    end)
  end

  # Formats field values based on field name and value type
  defp format_field_value(value, field) do
    cond do
      is_struct(value) ->
        format_struct_value(value)

      field in [:amount, :credit_amount, :debit_amount] ->
        Formatter.format_cell_value(value, :amount)

      field in [:date, :created_at, :updated_at, :deleted_at] ->
        Formatter.format_cell_value(value, :date)

      true ->
        Formatter.format_cell_value(value)
    end
  end

  # Handles formatting of struct values based on their type
  defp format_struct_value(value) do
    cond do
      is_struct(value, DateTime) or is_struct(value, NaiveDateTime) ->
        Formatter.format_cell_value(value)

      is_struct(value, Date) ->
        Formatter.format_cell_value(value, :date)

      is_struct(value, Decimal) ->
        Formatter.format_cell_value(value, :amount)

      true ->
        case value do
          %{__struct__: _} -> Map.from_struct(value) |> inspect()
          _ -> inspect(value)
        end
    end
  end

  # Prepares metadata about the report
  defp prepare_metadata(entries) do
    {:ok,
     %{
       total_count: length(entries),
       total_amount: calculate_total_amount(entries),
       generated_at: DateTime.utc_now()
     }}
  end

  # Generates the final Excel workbook with timing metrics
  defp generate_report(report_data, user, opts) do
    start_time = System.monotonic_time()

    workbook =
      prepare_sheet(
        report_data.headers,
        user,
        report_data.metadata.total_count,
        report_data.metadata.total_amount,
        opts.sheet_name,
        report_data.data
      )

    end_time = System.monotonic_time()
    duration = System.convert_time_unit(end_time - start_time, :native, :millisecond)

    # Track report generation duration
    :telemetry.execute(
      [:service_manager, :excel_processor, :report_generation],
      %{duration: duration},
      %{report_type: opts.sheet_name}
    )

    {:ok, workbook}
  end

  # Prepares the Excel sheet with headers and spacing
  defp prepare_sheet(headers, user, item_count, total_amount, name, rows) do
    # Create empty rows for spacing
    empty_rows = [
      # Row 1
      [],
      # Row 2
      [],
      # Row 3
      [],
      # Row 4
      [],
      # Row 5
      [],
      # Row 6
      [],
      # Row 7
      []
    ]

    sheet = %Sheet{
      name: "#{name} Report",
      show_grid_lines: false,
      rows: empty_rows ++ headers ++ rows,
      merge_cells: [{"A2", "F2"}]
    }

    sheet
    |> add_metadata(user, item_count, total_amount)
    |> Sheet.set_pane_freeze(@freeze_pane_row, 0)
    |> set_col_widths()
    |> then(&%Workbook{sheets: [&1]})
  end

  # Adds metadata to the sheet including timing, user info, and stats
  defp add_metadata(sheet, user, item_count, total_amount) do
    sheet
    |> add_report_timing()
    |> add_user_info(user)
    |> add_report_stats(item_count, total_amount)
  end

  # Adds report generation timing information
  defp add_report_timing(sheet) do
    now = Timex.local()

    sheet
    |> Sheet.set_cell("B4", "Report Date:")
    |> Sheet.set_cell("C4", Calendar.strftime(now, "%A, %d %B %Y"))
    |> Sheet.set_cell("B3", "Report Time:")
    |> Sheet.set_cell("C3", Calendar.strftime(now, "%H:%M:%S %P"))
  end

  # Adds user information to the report
  defp add_user_info(sheet, user) do
    sheet
    |> Sheet.set_cell("B5", "Generated By:")
    |> Sheet.set_cell(
      "C5",
      "#{String.capitalize(user.first_name)} #{String.capitalize(user.last_name)}"
    )
  end

  # Adds statistical information to the report
  defp add_report_stats(sheet, item_count, total_amount) do
    sheet
    |> Sheet.set_cell("E4", "Item Count:")
    |> Sheet.set_cell("F4", to_string(item_count))
    |> Sheet.set_cell("E5", "Total Value:")
    |> Sheet.set_cell("F5", total_amount)
  end

  # Sets uniform column widths across the sheet
  defp set_col_widths(sheet) do
    Enum.reduce(?A..?U, sheet, fn col, acc ->
      Sheet.set_col_width(acc, <<col>>, @default_col_width)
    end)
  end

  # Calculates total amount across all entries
  defp calculate_total_amount(entries) do
    Enum.reduce(entries, Decimal.new(0), fn entry, acc ->
      amount = Map.get(entry, :amount, "0")

      case amount do
        %Decimal{} = decimal ->
          Decimal.add(decimal, acc)

        number when is_number(number) ->
          Decimal.add(Decimal.new(number), acc)

        string when is_binary(string) ->
          case Decimal.parse(string) do
            {:ok, decimal} -> Decimal.add(decimal, acc)
            _ -> acc
          end

        _ ->
          acc
      end
    end)
    |> Number.Delimit.number_to_delimited(precision: 2)
  end

  # Validates that entries is a non-empty list
  defp validate_entries(entries) when is_list(entries) and length(entries) > 0, do: :ok
  defp validate_entries(_), do: {:error, Error.new("Invalid entries format")}

  # Validates that user has required fields
  defp validate_user(%{first_name: _, last_name: _} = user), do: {:ok, user}
  defp validate_user(_), do: {:error, Error.new("Invalid user format")}

  # Test helper function only available in test environment
  if Mix.env() == :test do
    def test_format_cell_value(value, type \\ :default) do
      Formatter.format_cell_value(value, type)
    end
  end
end
