defmodule ServiceManagerWeb.Api.MobileAppFormsV2Controller do
  use ServiceManagerWeb, :controller

  alias ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service, as: V2
  alias ServiceManagerWeb.Api.MobileAppFormsJSON

  action_fallback ServiceManagerWeb.FallbackController

  # Discovery
  def list_screens(conn, params) do
    with {:ok, screens} <- V2.list_screens(params) do
      json(conn, %{success: true, data: %{screens: screens, count: length(screens)}})
    end
  end

  def list_pages(conn, params) do
    with {:ok, pages} <- V2.list_pages(params) do
      json(conn, %{success: true, data: %{pages: pages, count: length(pages)}})
    end
  end

  def list_forms(conn, params) do
    with {:ok, forms} <- V2.list_forms(params) do
      json(conn, %{success: true, data: %{forms: forms, count: length(forms)}})
    end
  end

  def structure(conn, params) do
    with {:ok, tree} <- V2.structure(params) do
      json(conn, %{success: true, data: tree})
    end
  end

  # Retrieval
  def get_form(conn, params) do
    with {:ok, form} <- V2.get_view_form(params),
         {:ok, fields} <- V2.get_form_fields(form.id) do
      # Reuse the existing JSON shape; include form_submit_to so buttons inherit action
      json(conn, MobileAppFormsJSON.mobile_response(%{fields: fields, form_submit_to: form.submit_to}))
    else
      {:error, message} ->
        conn |> put_status(:not_found) |> json(%{success: false, error: message})
    end
  end

  # CRUD
  def create_screen(conn, params) do
    case V2.create_screen(params) do
      {:ok, s} -> json(conn, %{success: true, data: s})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def update_screen(conn, %{"id" => id} = params) do
    case V2.update_screen(id, Map.delete(params, "id")) do
      {:ok, s} -> json(conn, %{success: true, data: s})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def delete_screen(conn, %{"id" => id}) do
    case V2.delete_screen(id) do
      {:ok, _} -> json(conn, %{success: true})
      {:error, _} -> conn |> put_status(:not_found) |> json(%{success: false, error: "Not found"})
    end
  end

  def create_page(conn, params) do
    case V2.create_page(params) do
      {:ok, p} -> json(conn, %{success: true, data: p})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def update_page(conn, %{"id" => id} = params) do
    case V2.update_page(id, Map.delete(params, "id")) do
      {:ok, p} -> json(conn, %{success: true, data: p})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def delete_page(conn, %{"id" => id}) do
    case V2.delete_page(id) do
      {:ok, _} -> json(conn, %{success: true})
      {:error, _} -> conn |> put_status(:not_found) |> json(%{success: false, error: "Not found"})
    end
  end

  def create_form(conn, params) do
    case V2.create_form(params) do
      {:ok, f} -> json(conn, %{success: true, data: f})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def update_form(conn, %{"id" => id} = params) do
    case V2.update_form(id, Map.delete(params, "id")) do
      {:ok, f} -> json(conn, %{success: true, data: f})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def delete_form(conn, %{"id" => id}) do
    case V2.delete_form(id) do
      {:ok, _} -> json(conn, %{success: true})
      {:error, _} -> conn |> put_status(:not_found) |> json(%{success: false, error: "Not found"})
    end
  end

  def create_field(conn, params) do
    case V2.create_field(params) do
      {:ok, ff} -> json(conn, %{success: true, data: ff})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def update_field(conn, %{"id" => id} = params) do
    case V2.update_field(id, Map.delete(params, "id")) do
      {:ok, ff} -> json(conn, %{success: true, data: ff})
      {:error, cs} -> conn |> put_status(:unprocessable_entity) |> json(%{success: false, errors: cs})
    end
  end
  def delete_field(conn, %{"id" => id}) do
    case V2.delete_field(id) do
      {:ok, _} -> json(conn, %{success: true})
      {:error, _} -> conn |> put_status(:not_found) |> json(%{success: false, error: "Not found"})
    end
  end
end
