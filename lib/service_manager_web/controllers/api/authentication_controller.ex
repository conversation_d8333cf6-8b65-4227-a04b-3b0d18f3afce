defmodule ServiceManagerWeb.AuthenticationController do
  use ServiceManagerWeb, :controller
  require Logger

  alias ServiceManagerWeb.Api.Services.AuthenticationService
  alias ServiceManager.Schemas.ActiveDevice, as: Device
  alias ServiceManager.Accounts.User

  # track do
  def sign_in(conn, params) do
    # chain_id = "SIGN-IN: #{params["user-id"]}"
    res = AuthenticationService.authenticate(params)

    case res do
      %{"status" => true, "data" => %{"user" => %{"profile" => user}}} = response ->
        track_user_presence(conn, user)
        json(conn, response)

      response ->
        json(conn, response)
    end
  end

  def check_device_status(conn, params) do
    user = User.find_by(account_number: params["account_number"])
    device = Device.find_by(device_id: params["device_id"])

    case user do
      nil -> 
        
        response = %{
          "message" => "Device not authorized, No user with entered account number",
          "device_on_hold" => true,
          "status" => false,
          "data" => %{
          }
        }
        json(conn, response)
      user -> 
        case device do
          nil -> 
            
            response = %{
              "message" => "No user with this account number #{params["account_number"]} and device id exists",
              "device_on_hold" => true,
              "status" => false,
              "data" => %{
              }
            }
            json(conn, response)
          device -> 
            verify_device = Device.find_by([device_id: device.device_id, user_id: user.id, static_access_token: params["access_token"]])
            IO.inspect device.id, label: "Device-id"
            IO.inspect user.id, label: "User-id"
            IO.inspect params["token"], label: "Token"

            if verify_device != nil do
              response = %{
                "message" => verify_device.device_screen_message,
                "device_on_hold" => verify_device.on_hold,
                "status" => true,
                "data" => %{
                }
              }

              json(conn, response)
            else
              response = %{
                "message" => "Device not authorized, could not identify device",
                "device_on_hold" => true,
                "status" => false,
                "data" => %{
                }
              }
              json(conn, response)
            end
        end
      
    end

  end

  defp respond({:error, error_info}) do
    %{
      "message" => "#{error_info}",
      "status" => false,
      "data" => %{
        "errors" => error_info
      }
    }
  end

  defp respond({:ok, profile}, message \\ "") do
    %{
      "message" => message,
      "status" => true,
      "data" => %{
        "profile" => profile,
        "accounts" => profile.accounts
      }
    }
  end


  # end

  # track do
  defp track_user_presence(conn, user) do
    device_id = get_device_id(conn)
    now = DateTime.utc_now()

    # ServiceManager.Schemas.ActiveDevice.all()
    device = %{
      device_id: device_id,
      last_seen_at: now,
      user_id: user.id,
      user_type: "mobile_banking"
    }

    ServiceManagerWeb.Presence.update(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )

    ServiceManagerWeb.Presence.track(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )

    time =
      DateTime.utc_now()
      |> DateTime.truncate(:second)
      |> to_string
      |> String.replace("Z", "")

    params =
      conn.params
      |> Map.put("last_seen_at", DateTime.utc_now())
      |> Map.put("user_id", user.id)

    case ServiceManager.Repo.get_by(ServiceManager.Schemas.ActiveDevice,
           device_id: conn.params["device_id"],
           user_id: user.id
         ) do
      nil ->
        %ServiceManager.Schemas.ActiveDevice{}
        |> ServiceManager.Schemas.ActiveDevice.changeset(params)
        |> ServiceManager.Repo.insert()

        message =
          "#{user.first_name} #{user.last_name}, A new device login has happened at #{time}"

        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: user.phone_number,
          message: message
        })

      device ->
        message =
          "#{user.first_name} #{user.last_name}, Device login has happened at #{time} on your profile"

        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: user.phone_number,
          message: message
        })

        device
        |> ServiceManager.Schemas.ActiveDevice.changeset(params |> Map.delete("user_id"))
        |> ServiceManager.Repo.update()
    end
  end

  # end

  # track do
  defp get_device_id(conn) do
    # Generate a unique device ID based on user agent and IP
    user_agent = conn.params["device_id"] || get_req_header(conn, "user-agent") |> List.first()
    remote_ip = conn.remote_ip |> :inet.ntoa() |> to_string()
    :crypto.hash(:sha256, "#{user_agent}:#{conn.params["user-id"]}") |> Base.encode16()
  end

  # end

  track do
    def sign_out(conn, %{"access_token" => token} = params) do
      res = AuthenticationService.invalidate(token)
      json(conn, res)
    end
  end

  track do
    def sign_up(conn, _params) do
      json(conn, %{})
    end
  end

  track do
    def refresh(conn, %{"access_token" => token} = params) do
      res = AuthenticationService.refresh_token(token)
      json(conn, res)
    end
  end

  track do
    def forgot_password(
          conn,
          %{"username" => username, "memorable_word" => memorable_word} = params
        ) do
      case AuthenticationService.verify_memorable_word(username, memorable_word) do
        {:ok, _} ->
          # Generate temporary password and send to user's registered contact
          case AuthenticationService.reset_password(username) do
            {:ok, response} -> json(conn, response)
            {:error, reason} -> json(conn, %{error: reason})
          end

        {:error, reason} ->
          error_response = %{
            data: %{},
            message: "Both the username and memorable word were entered incorrectly.",
            status: false
          }

          json(conn, error_response)
      end
    end
  end
end
