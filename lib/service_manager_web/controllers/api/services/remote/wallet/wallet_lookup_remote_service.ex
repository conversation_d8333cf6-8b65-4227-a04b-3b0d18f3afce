defmodule ServiceManagerWeb.Api.Services.Remote.WalletLookupRemoteService do
  def lookup_wallet(params) do
    # url = "http://localhost:4000/api/accounts"
    url =
      url =
      Application.get_env(:service_manager, :urls)[
        Application.get_env(:service_manager, :urls)[:active] |> String.to_atom()
      ] <> "/api/wallet/accounts/lookup"

    headers = [{"Content-type", "application/json"}]

    payload =
      %{
        mobile_number: params["phone_number"] || "",
        account_type: "wallet"
      }
      |> Jason.encode!()

    case HTTPoison.post(url, payload, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 201, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end
end
