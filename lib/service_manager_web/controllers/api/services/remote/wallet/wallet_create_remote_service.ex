defmodule ServiceManagerWeb.Api.Services.Remote.WalletCreateRemoteService do
  def create_wallet(params) do
    # url = "http://localhost:4000/api/wallet/accounts"
    url =
      url =
      Application.get_env(:service_manager, :urls)[
        Application.get_env(:service_manager, :urls)[:active] |> String.to_atom()
      ] <> "/api/wallet/accounts"

    headers = [{"Content-type", "application/json"}]

    payload =
      %{
        email: params["email"] || "",
        mobile_number: params["phone_number"] || "",
        name: params["name"],
        initial_balance: params["initial_balance"] || 0.0,
        currency: params["currency"] || "MWK",
        account_type: "WALLET",
        role: "wallet",
        customer_number: params["customer_number"]
      }
      |> IO.inspect()
      |> Jason.encode!()

    case HTTPoison.post(url, payload, headers) |> IO.inspect() do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 201, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end
end
