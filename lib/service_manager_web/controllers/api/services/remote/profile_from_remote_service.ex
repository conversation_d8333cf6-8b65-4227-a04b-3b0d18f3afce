defmodule ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService do
  alias ServiceManager.Services.T24.Messages.GetAccountTransactions
  alias ServiceManager.Services.T24.Messages.GetCustomerProfile
  alias ServiceManager.Services.T24.Messages.GetAccountDetails
  alias ServiceManager.MapKeyFinder, as: Find

  import ServiceManager.Logging.FunctionTracker

  def get_profile_by_account_number(account_number) do
    # url = "http://localhost:4000/api/accounts/account_lookup"
    url =
      Application.get_env(:service_manager, :urls)[
        Application.get_env(:service_manager, :urls)[:active] |> String.to_atom()
      ] <> "/api/accounts/account_lookup"

    headers = [{"Content-type", "application/json"}]
    payload = %{account_number: account_number} |> Jason.encode!()

    case HTTPoison.post(url, payload, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, body |> Jason.decode!()}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: The requested account number does not exist"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}"}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}"}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason}
    end
  end

  # ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService.test
  def test() do
    "*************" |> get_profile_by_account_number_v4() |> IO.inspect()
  end

  def get_profile_by_account_number_v2(account_number) do
    account_number
    |> GetAccountTransactions.get_transactions()
    |> case do
      {:error, reason} ->
        {:error, reason}

      {:ok, %{status: _status, body: body, headers: _headers}} ->
        get_transaction_response = body |> Jason.decode!()

        customer_number = find(get_transaction_response, "customerId")

        get_profile_response = GetCustomerProfile.get_profile_parsed(customer_number)

        profile = %{}

        %{
          "body" => %{
            "accountOfficer" => "100",
            "companyId" => "MW0010001",
            "customerNameAdditionals" => [%{"customerNameAdditional" => "OWEN MAZOZO"}],
            "customerNames" => [%{"customerName" => "OWEN MAZOZO"}],
            "customerShortNames" => [%{"customerShortName" => "OWEN MAZOZO"}],
            "customerStatus" => "1",
            "dateofBirth" => "********",
            "gender" => "MALE",
            "industryName" => "113",
            "languageId" => "1",
            "nationalityId" => "ZW",
            "residenceId" => "ZW",
            "sectorId" => "2",
            "street" => "BLANTYRE",
            "target" => "1",
            "title" => "MR",
            "townCountry" => "BLANTYRE"
          },
          "header" => %{
            "audit" => %{
              "T24_time" => 682,
              "requestParse_time" => 15,
              "responseParse_time" => 5,
              "versionNumber" => "2"
            },
            "id" => "********",
            "status" => "success",
            "transactionStatus" => "Unapproved",
            "uniqueIdentifier" => "IRFX243268420775237.02"
          }
        }

        user_params = %{
          "email" => profile["profile"]["email"],
          "name" => find(get_profile_response, "customerNames") |> find("customerName"),
          "account_number" => find(get_transaction_response, "accountId"),
          "customer_no" => customer_number,
          # "account_balance" => profile["balance"],
          "first_name" => profile["profile"]["first_name"] || profile["first_name"],
          "last_name" => profile["profile"]["last_name"] || profile["last_name"],
          "phone_number" => profile["profile"]["phone"] || profile["phone"],
          "date_of_birth" => find(get_profile_response, "dateofBirth"),
          "address" => find(get_profile_response, "street"),
          "city" => profile["profile"]["city"] || profile["city"],
          "state" => profile["profile"]["state"] || profile["state"],
          "zip" => profile["profile"]["zip"] || profile["zip"],
          "country" => find(get_profile_response, "townCountry"),
          "region" => profile["profile"]["region"] || profile["region"],
          "postal_code" => profile["profile"]["postal_code"] || profile["postal_code"],
          "identifier_type" =>
            profile["profile"]["identifier_type"] || profile["identifier_type"],
          "identifier_number" =>
            profile["profile"]["identifier_number"] || profile["identifier_number"],
          "currency" => find(get_transaction_response, "accountCurrency"),
          # :crypto.strong_rand_bytes(12) |> Base.encode64() |> to_string() |> IO.inspect
          "password" => "Qwerty123456"
        }

        {:ok, user_params}
    end
  end

  # track do
  def get_profile_by_account_number_v3(account_number) do
    sync_with_bank = false

    if sync_with_bank do
      user_error_params = %{
        "account_number" => "nosync",
        "address" => "nosync",
        "city" => "nosync",
        "country" => "nosync",
        "currency" => "nosync",
        "customer_no" => "nosync",
        "date_of_birth" => "nosync",
        "email" => "nosync",
        "first_name" => "nosync",
        "identifier_number" => "nosync",
        "identifier_type" => "nosync",
        "last_name" => "nosync",
        "name" => "nosync",
        "phone_number" => "nosync",
        "postal_code" => "nosync",
        "region" => "nosync",
        "state" => "nosync",
        "username" => "nosync",
        "zip" => "nosync"
      }

      {:ok, user_error_params}
    else
      account_number
      |> GetAccountDetails.get_account_details_parsed()
      |> case do
        {:ok, body} ->
          account = body

          username_generator = fn first_name, last_name ->
            # Convert inputs to lowercase and format the username
            first_name_clean = String.downcase(first_name)
            last_name_initial = String.first(String.downcase(last_name))

            # Generate a random number between 1000 and 9999
            random_number = :rand.uniform(9000) + 1000

            # Create the username with the standard format
            "#{first_name_clean}.#{last_name_initial}.#{random_number}"
          end

          firstname = account["first_name"]

          lastname = account["last_name"]

          user_params = %{
            "username" => "",
            "email" => account["email"],
            "name" => account["holder_name"],
            "account_number" => account["account_id"],
            "customer_no" => account["customer_id"] |> to_string(),
            "first_name" => firstname,
            "last_name" => lastname,
            "other_names" => account["other_names"],
            "phone_number" => account["phone_number"],
            "date_of_birth" => account["date_of_birth"],
            "address" => account["street"],
            "city" => "",
            "state" => "",
            "zip" => "",
            "country" => account["town_country"],
            "region" => "",
            "postal_code" => account["post_code"],
            "identifier_type" => "",
            "identifier_number" => "",
            "currency" => account["account_currency"]
          }

          {:ok, user_params}

        {:error, _reason} ->
          user_error_params = %{
            "account_number" => "noconnection",
            "address" => "noconnection",
            "city" => "noconnection",
            "country" => "noconnection",
            "currency" => "noconnection",
            "customer_no" => "noconnection",
            "date_of_birth" => Date.utc_today() |> to_string,
            "email" => "noconnection",
            "first_name" => "noconnection",
            "identifier_number" => "noconnection",
            "identifier_type" => "noconnection",
            "last_name" => "noconnection",
            "name" => "noconnection",
            "phone_number" => "noconnection",
            "postal_code" => "noconnection",
            "region" => "noconnection",
            "state" => "noconnection",
            "username" => "noconnection",
            "zip" => "noconnection"
          }

          {:error, user_error_params}
      end
    end
  end

  # end

  track do
    def get_profile_by_account_number_v4(account_number) do
      account_number
      |> GetAccountDetails.get_account_details_parsed()
      |> case do
        {:ok, account} ->
          username_generator = fn first_name, last_name ->
            first_name_clean = String.downcase(first_name)
            last_name_initial = String.first(String.downcase(last_name || "0"))
            random_number = :rand.uniform(9000) + 1000
            "#{first_name_clean}.#{last_name_initial}.#{random_number}"
          end

          user_params = %{
            "username" => "",
            "email" => account["email"],
            "name" => account["holder_name"],
            "account_number" => account["account_id"],
            "customer_no" => account["customer_id"] |> to_string(),
            "first_name" => account["first_name"],
            "last_name" => account["last_name"],
            "phone_number" => account["phone_number"],
            "date_of_birth" => account["date_of_birth"],
            "address" => account["street"],
            "city" => nil,
            "state" => nil,
            "zip" => nil,
            "country" => account["town_country"],
            "region" => nil,
            "postal_code" => account["post_code"],
            "identifier_type" => nil,
            "identifier_number" => nil,
            "currency" => account["account_currency"],
            "account_status" => account["account_status"],
            "available_balance" => account["available_balance"],
            "cleared_balance" => account["cleared_balance"],
            "account_balance" => account["working_balance"],
            "online_actual_balance" => account["online_actual_balance"],
            "currency_id" => account["currency_id"],
            "category_name" => account["category_name"],
            "employment_status" => account["employment_status"],
            "marital_status" => account["marital_status"],
            "nick_name" => account["nick_name"],
            "online_limit" => account["online_limit"],
            "opening_date" => account["opening_date"]
          }

          {:ok, user_params}

        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  def find(data, key) do
    Find.find_key(data, key, mode: :first)
  end

  def test_get_profile_by_account_number() do
    test_account_number = "ACCT89FBEA27972F109B"

    case get_profile_by_account_number(test_account_number) do
      {:ok, profile} ->
        IO.puts("Test passed: Received profile for account number #{test_account_number}")

      {:error, reason} ->
        IO.puts("Test failed: #{reason}")
    end
  end
end

# ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService.get_profile_by_account_number_v3("*************")
