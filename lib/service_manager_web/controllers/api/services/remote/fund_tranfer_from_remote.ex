defmodule ServiceManagerWeb.Api.Services.Remote.FundTransferFromRemoteService do
  require Logger
  alias ServiceManager.Pool.RequestPool
  alias ServiceManager.Utilities.TransactionHandler, as: ResponseHandler

  def transfer(params) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Starting remote fund transfer")

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Transfer params: #{inspect(params)}"
    )

    payload = get_params(params)

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Generated payload: #{inspect(payload)}"
    )

    case RequestPool.create_request(%{
           method: "POST",
           url: "https://fdh-esb.ngrok.dev/api/esb/transfers/v1/payments/generic/1",
           headers: %{
             "Content-Type" => "application/json",
             "Authorization" => "Basic YWRtaW46YWRtaW4="
           },
           body: payload,
           name: "fund_transfer_ofs",
           reference: "fund_transfer_ofs_#{:os.system_time(:millisecond)}"
         }) do
      {:ok, request} ->
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Request created successfully with ID: #{request.id}"
        )

        wait_for_completion(request)

      error ->
        Logger.error(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create request: #{inspect(error)}"
        )

        error
    end
  end

  def get_params(%{
        "from_account" => debit_account,
        "amount" => amount,
        "to_account" => credit_account,
        "reference" => external_reference,
        "description" => description
      }) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Generating transfer parameters"
    )

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * From account: #{debit_account}"
    )

    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * To account: #{credit_account}")
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Amount: #{amount}")

    %{
      "header" => %{},
      "body" => %{
        "transactionType" => "AC",
        "debitAccountId" => debit_account,
        "debitAmount" => amount,
        "debitCurrencyId" => "MWK",
        "creditCurrencyId" => "MWK",
        "creditAccountId" => credit_account,
        "externalReference" => external_reference,
        "description" => description
      }
    }
  end

  defp wait_for_completion(request, attempts \\ 0) do
    # 60 seconds total
    max_attempts = 30
    # 2 seconds
    check_interval = 2000

    if attempts >= max_attempts do
      Logger.error(
        "[#{DateTime.utc_now() |> DateTime.to_string()}] * Request timeout after #{attempts} attempts"
      )

      {:error, "Request timeout"}
    else
      Logger.info(
        "[#{DateTime.utc_now() |> DateTime.to_string()}] * Checking request status (attempt #{attempts + 1}/#{max_attempts})"
      )

      case RequestPool.get_request(request.id) do
        {:ok, req} ->
          case req.status do
            "completed" ->
              Logger.info(
                "[#{DateTime.utc_now() |> DateTime.to_string()}] * Request completed successfully"
              )

              Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Response: REDUCTED")

              {:ok, req.response}
              |> ResponseHandler.transaction_response_parser()

            "failed" ->
              error_message = req.response["error"] || "Transfer failed"

              Logger.error(
                "[#{DateTime.utc_now() |> DateTime.to_string()}] * Request failed: #{error_message}"
              )

              {:error, error_message}
              |> ResponseHandler.transaction_response_parser()

            _ ->
              Logger.info(
                "[#{DateTime.utc_now() |> DateTime.to_string()}] * Request still processing, waiting #{check_interval}ms"
              )

              Process.sleep(check_interval)
              wait_for_completion(request, attempts + 1)
          end

        error ->
          Logger.error(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to get request status: #{inspect(error)}"
          )

          error
      end
    end
  end
end
