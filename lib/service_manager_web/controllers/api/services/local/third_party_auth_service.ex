defmodule ServiceManagerWeb.Api.Services.ThirdPartyAuthService do
  # Alias the Accounts, UserAuth, Token and Repo modules for easier access
  alias ServiceManager.Contexts.SystemUserManagementContext, as: Accounts
  alias ServiceManagerWeb.Plugs.SystemUserAuth, as: UserAuth
  alias ServiceManager.Schemas.SystemUserToken, as: Token
  alias ServiceManager.Repo

  # Function to authenticate a user
  def authenticate(user_params) do
    # Extract user id, password and auth type from the user parameters
    %{"phone_number" => userid, "password" => password} = user_params

    # Check if the user exists in the database with the provided email and password
    if user = Accounts.ser_by_phone_and_password(userid, password) do
      # Generate extra claims for the user
      claims = extra_claims(user)
      # Generate a token for the user
      token = generate_token(claims)

      # Save the token to the database and get the token object
      {:ok, db_token} = create_token(token, user.id)

      # Respond with the token added
      {:ok, db_token.token}
      |> respond(user)
    else
      # In case of failure, respond with an error message
      {:error, "Invalid API credentials"}
      |> respond()
    end
  end

  def verify_api_key(api_key) do
    api_key |> Accounts.verify_api_key()
  end

  # Function to invalidate a token
  def invalidate(token) do
    # Check if the token exists in the database
    case Repo.get_by(Token, token: token) do
      nil ->
        # If the token does not exist, respond with an error message

        %{
          "message" => "Token not found",
          "status" => false,
          "data" => %{
            "user" => %{},
            "auth" => %{}
          }
        }

      db_token ->
        # If the token exists, update its revoked_at field and respond with a success message
        Repo.update(Token.changeset(db_token, %{revoked_at: DateTime.utc_now()}))

        %{
          "message" => "Token invalidated",
          "status" => true,
          "data" => %{
            "user" => %{},
            "auth" => %{}
          }
        }
    end
  end

  # Private function to generate extra claims for a user
  defp extra_claims(user) do
    # Currently, no extra claims are generated
    %{}
  end

  # Private function to create a token for a user
  defp create_token(token, user_id) do
    # Create a new token with the provided token, user id and expiry time
    %Token{}
    |> Token.changeset(%{
      token: token,
      user_id: user_id,
      # Token expires in 1 hour
      expires_at: DateTime.add(DateTime.utc_now(), 3600, :second)
    })
    |> Repo.insert()
  end

  # Function to check if a token is valid
  def token_valid?(token) do
    # Check if the token exists in the database
    case Repo.get_by(Token, token: token) do
      nil ->
        false

      db_token ->
        # If the token exists, check if it has not expired and has not been revoked
        now = DateTime.utc_now()
        db_token.expires_at > now && is_nil(db_token.revoked_at)
    end
  end

  def refresh_token(token) do
    # Check if the token is valid
    case token_valid?(token) do
      false ->
        # If the token is not valid, respond with an error message
        respond({:error, "Invalid token"})

      true ->
        # If the token is valid, generate a new token
        new_token = generate_token(extra_claims(%{}))

        # Create the new token in the database
        create_token(new_token, Repo.get_by(Token, token: token).user_id)

        # Invalidate the old token
        invalidate(token)

        # Respond with the new token
        respond(
          {:ok, new_token},
          Repo.get_by(ServiceManager.WalletAccounts.WalletUser,
            id: Repo.get_by(Token, token: new_token).user_id
          )
        )
    end
  end

  def verify_token(token) do
    case token_valid?(token) do
      false ->
        # If the token is not valid, respond with an error message
        {:error, "Invalid token"}

      true ->
        # If the token is valid, get the user associated with the token
        user =
          Repo.get_by(ServiceManager.WalletAccounts.WalletUser,
            id: Repo.get_by(Token, token: token).user_id
          )

        # Respond with the user
        {:ok, user}
    end
  end

  # Private function to generate a token
  defp generate_token(claims) do
    # Generate and sign a token with the provided claims
    ServiceManager.Token.generate_and_sign!(claims)
  end

  # Private function to respond with an error message
  defp respond({:error, error_info}, _user \\ %{}) do
    # Respond with a message indicating invalid API credentials
    %{
      "message" => error_info,
      "status" => false,
      "data" => %{
        "user" => %{},
        "auth" => %{}
      }
    }
  end

  # Private function to respond with a success message
  defp respond({:ok, token_info}, user) do
    # Respond with a message indicating success and the user's email and access token
    %{
      "message" => "success",
      "status" => true,
      "data" => %{
        "user" => %{
          "user-id" => user.mobile_number
        },
        "auth" => %{
          "access_token" => token_info
        }
      }
    }
  end
end
