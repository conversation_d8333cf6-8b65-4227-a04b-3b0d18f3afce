defmodule ServiceManagerWeb.Controllers.Api.Services.Local.ChequeBookService do
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeBookRequest
  import Ecto.Query

  def create_request(params) do
    # Check for existing pending request
    case get_pending_request(params["account_number"]) do
      nil ->
        params = Map.put_new(params, "number_of_leaves", params["cheque_leaves"])
        params = Map.delete(params, "cheque_leaves")

        %ChequeBookRequest{}
        |> ChequeBookRequest.changeset(params)
        |> Repo.insert()

      _existing ->
        {:error, "A pending request already exists for this account"}
    end
  end

  def list_requests(params) do
    ChequeBookRequest
    |> filter_by_account(params["account_number"])
    |> filter_by_status(params["request_status"])
    |> Repo.all()
  end

  @spec get_request(String.t()) :: {:error, String.t()} | {:ok, ChequeBookRequest.t()}
  def get_request(uid) do
    case Repo.get_by(ChequeBookRequest, uid: uid) do
      nil -> {:error, "Cheque book request not found"}
      request -> {:ok, request}
    end
  end

  def update_request(uid, params) do
    with {:ok, request} <- get_request(uid),
         {:ok, updated_request} <- do_update_request(request, params) do
      {:ok, updated_request}
    end
  end

  def delete_request(uid) do
    with {:ok, request} <- get_request(uid),
         true <- can_delete?(request),
         {:ok, _} <- Repo.delete(request) do
      {:ok, "Cheque book request deleted successfully"}
    else
      false -> {:error, "Cannot delete processed request"}
      error -> error
    end
  end

  # Private functions

  defp get_pending_request(account_number) do
    ChequeBookRequest
    |> where([r], r.account_number == ^account_number and r.request_status == "pending")
    |> Repo.one()
  end

  defp filter_by_account(query, nil), do: query

  defp filter_by_account(query, account_number) do
    where(query, [r], r.account_number == ^account_number)
  end

  defp filter_by_status(query, nil), do: query

  defp filter_by_status(query, status) do
    where(query, [r], r.request_status == ^status)
  end

  defp do_update_request(request, params) do
    request
    |> ChequeBookRequest.changeset(params)
    |> maybe_set_timestamps(params["request_status"])
    |> Repo.update()
  end

  defp maybe_set_timestamps(changeset, "approved") do
    Ecto.Changeset.put_change(changeset, :issued_at, DateTime.utc_now())
  end

  defp maybe_set_timestamps(changeset, "fulfilled") do
    Ecto.Changeset.put_change(changeset, :fulfilled_at, DateTime.utc_now())
  end

  defp maybe_set_timestamps(changeset, _), do: changeset

  defp can_delete?(request) do
    request.request_status in ["pending", "rejected"]
  end
end
