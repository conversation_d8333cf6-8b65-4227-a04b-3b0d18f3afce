defmodule ServiceManager.Services.TransferService do
  require <PERSON>gger
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.Transaction

  @doc """
  Creates a minimal transaction record with only essential fields.
  Used when one or both accounts don't exist in the system.
  """

  alias ServiceManagerWeb.Api.Services.Remote.FundTransferFromRemoteService
  alias ServiceManager.Accounts.User
  alias ServiceManager.Schemas.Accounts.BankAccount
  alias ServiceManager.Services.TransferCallbackServer
  alias ServiceManager.Accounts.FundAccounts
  alias ServiceManager.Services.AccountValidationService
  alias ServiceManager.Services.AccountingService


  import Ecto.Query

  def process_minimal_transfer(params) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Creating minimal transaction record"
    )

    # Generate bank type reference
    reference = "BNK#{:os.system_time(:millisecond)}"

    # Prepare transaction attributes
    transaction_attrs = %{
      type: "direct-transfer",
      amount: params["amount"],
      credit_amount: params["amount"],
      debit_amount: params["amount"],
      status: "pending",
      reference: reference,
      description:
        "Direct Transfer from account #{params["from_account"]} to account #{params["to_account"]}",
      value_date: DateTime.utc_now() |> DateTime.to_date() |> Date.to_string(),
      sender_account: params["from_account"],
      receiver_account: params["to_account"],
      transaction_details:
        Map.take(params, [
          "from_account_exists",
          "to_account_exists",
          "from_account",
          "to_account"
        ])
    }

    # Create transaction with accounting entries
    case AccountingService.create_transaction_with_accounting(transaction_attrs) do
      {:ok, %{transaction: transaction}} ->
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Minimal transaction created successfully with accounting entries: #{transaction.id}"
        )

        {:ok, transaction}

      {:error, reason} ->
        Logger.error(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create minimal transaction: #{inspect(reason)}"
        )

        {:error, "Failed to create transaction record"}
    end
  end

  def process_external_transfer(params) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Creating external transfer transaction record"
    )

    # Generate bank type reference
    reference = "BNK#{:os.system_time(:millisecond)}"

    # Prepare transaction attributes
    transaction_attrs = %{
      type: "external-transfer",
      amount: params["amount"],
      credit_amount: params["amount"],
      debit_amount: params["amount"],
      status: "pending",
      reference: reference,
      description:
        "External Transfer from account #{params["from_account"]} to account #{params["to_account"]}",
      value_date: DateTime.utc_now() |> DateTime.to_date() |> Date.to_string(),
      sender_account: params["from_account"],
      receiver_account: params["to_account"],
      transaction_details:
        Map.take(params, [
          "from_account_exists",
          "to_account_exists",
          "from_account",
          "to_account"
        ])
    }

    # Create transaction with accounting entries
    case AccountingService.create_transaction_with_accounting(transaction_attrs) do
      {:ok, %{transaction: transaction}} ->
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * External transfer transaction created successfully with accounting entries: #{transaction.id}"
        )

        {:ok, transaction}

      {:error, reason} ->
        Logger.error(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create external transfer transaction: #{inspect(reason)}"
        )

        {:error, "Failed to create transaction record"}
    end
  end

  def process_transfer(params) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] - Received transfer request")

    # Get fund account IDs for the transaction and preload user for notifications
    from_account =
      Repo.get_by(FundAccounts, account_number: params["from_account"]) |> Repo.preload(:user)

    to_account =
      Repo.get_by(FundAccounts, account_number: params["to_account"]) |> Repo.preload(:user)

    case {from_account, to_account} do
      {nil, _} ->
        {:error, %{error: "From account not found"}}

      {_, nil} ->
        {:error, %{error: "To account not found"}}

      {from_account, to_account} ->
        # Validate account thresholds before processing
        case AccountValidationService.validate_account_thresholds(
               from_account,
               params["amount"],
               from_account.last_transaction_date
             ) do
          :ok ->
            # Create initial pending transaction with fund account IDs
            case create_initial_transaction(params, from_account, to_account) do
              {:ok, transaction} ->
                # Start background processing
                #                Task.start(fn ->
                case process_transfer_async(params, transaction) do
                  {:ok, {:ok, txn}} ->
                    txn = txn |> Map.put(:currency, "MWK")

                    # Initiate SMS to sender and receiver
                    if txn.status == "completed" do
                      transaction_time =
                        txn.updated_at
                        |> to_string
                        |> String.replace("T", " ")
                        |> String.replace("Z", "")

                      mask_fn = fn input ->
                        input_str = to_string(input)
                        len = String.length(input_str)

                        if len > 7 do
                          first = String.slice(input_str, 0, 3)
                          last = String.slice(input_str, -4, 4)
                          "#{first}XXXX#{last}"
                        else
                          # return as-is if too short to mask
                          input_str
                        end
                      end

                      sender_message = """
                      FDH BANK
                      Account #{from_account.account_number |> mask_fn.()} has moved funds to #{to_account.account_number |> mask_fn.()}.
                      Amount: #{txn.currency}#{txn.amount}. Fee: #{txn.currency}#{}. Dbt Ref: #{txn.external_reference}.
                      Date: #{transaction_time}. Ref: #{txn.cbs_transaction_reference}. Bal: #{txn.currency}-
                      """

                      receiver_message = """
                      FDH BANK
                      Account #{to_account.account_number |> mask_fn.()} has received #{txn.currency}#{txn.amount}.
                      Sender: #{from_account.account_number |> mask_fn.()}, #{from_account.user.first_name} #{from_account.user.last_name}. Date: #{transaction_time}. Narr: Funds Transfer.
                      Ref: #{txn.cbs_transaction_reference}. Bal: #{txn.currency}-
                      """

                      ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
                        msisdn: from_account.user.phone_number,
                        message: sender_message
                      })

                      ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
                        msisdn: to_account.user.phone_number,
                        message: receiver_message
                      })
                    end

                    # Preload associations and return in format expected by TransfersJSON.show/1
                    preloaded_transaction = Repo.preload(txn, [:from_account, :to_account])
                    {:ok, %{transaction: preloaded_transaction}}

                  {:error, error_message} ->
                    {:error, %{error: error_message}}
                end

              #                end)
              {:error, error} ->
                Logger.info(
                  "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create initial transaction: #{inspect(error)}"
                )

                {:error,
                 %{
                   error:
                     "Transfer request failed, Please try again after some time or contact support",
                    error_details: "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create initial transaction: #{inspect(error)}"
                 }}
            end

          {:error, message} ->
            {:error, %{error: message}}
        end
    end
  end

  # Returns nil since we don't need the result in the controller
  defp process_transfer_async(params, initial_transaction) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] - Processing transfer in background"
    )

    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Processing transfer with params: #{inspect(params)}"
    )

    with {:ok, from_user_id} <- get_user_id(params["from_account"]),
         {:ok, to_user_id} <- get_user_id(params["to_account"]),
         {:ok, response} <- FundTransferFromRemoteService.transfer(params) do
      Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Transfer response: REDACTED")

      # Update transaction with reference from header
      result =
        case response do
          %{
            "body" => %{
              "header" => %{
                "id" => tx_id,
                "uniqueIdentifier" => uniqueIdentifier,
                "status" => status
              }
            }
          } ->
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Updating transaction reference: #{tx_id}"
            )

            sanitize_status = fn status ->
              if status == "success" do
                "completed"
              else
                status
              end
            end

            updated_transaction =
              Transaction.changeset(initial_transaction, %{
                transaction_details: response,
                status: sanitize_status.(status),
                cbs_transaction_reference: tx_id,
                external_reference: uniqueIdentifier
              })
              |> Repo.update()
              |> IO.inspect(label: "txn_UPDATE")

            {:ok, updated_transaction}

          _ ->
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * No transaction reference in response header"
            )

            {:error, "Could not complete transaction at this time, please try again later"}
        end

      Logger.info(
        "[#{DateTime.utc_now() |> DateTime.to_string()}] * Transfer process completed successfully"
      )

      result

      #      update_transaction(initial_transaction, response)
    else
      {:error, error} ->
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Transfer process failed: #{inspect(error)}"
        )

        # Update transaction with error status
        initial_transaction
        |> Transaction.changeset(%{
          status: "failed",
          description: "#{inspect(error.error.errorDetails)}",
          cbs_transaction_reference: error.header.id,
          external_reference: error.header.uniqueIdentifier
        })
        |> Repo.update()

        {:error, error}
    end
  end

  defp get_user_id(account_number) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Getting user ID for account: #{account_number}"
    )

    case get_user_by_account_number(account_number) do
      nil ->
        create_user_and_bank_account(account_number)

      user ->
        {:ok, user.id}
    end
  end

  defp get_user_by_account_number(account_number) do
    query =
      from u in User,
        join: ba in BankAccount,
        on: ba.user_id == u.id,
        where: ba.number == ^account_number,
        select: u

    Repo.one(query)
  end

  defp create_user_and_bank_account(account_number) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Creating user and bank account for: #{account_number}"
    )

    Repo.transaction(fn ->
      with {:ok, user} <- get_user(account_number),
           {:ok, _bank_account} <- create_bank_account(user.id, account_number) do
        {:ok, user.id}
      else
        {:error, _} = error ->
          Logger.info(
            "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create user and bank account: #{inspect(error)}"
          )

          Repo.rollback(error)

          {:error, "Account number #{account_number} does not exist [S-2]"}
      end
    end)
  end

  defp get_user(account_number) do
    case User.find_by(account_number: account_number) do
      nil ->
        case FundAccounts.find_by(account_number: account_number) do
          nil ->
            {:error, "Account number #{account_number} does not exist [S-1]"}

          fund_account ->
            case User.find(fund_account.user_id) do
              nil ->
                {:error, "Account number #{account_number} does not exist [S-1-2]"}

              user ->
                {:ok, user}
            end
        end

      user ->
        {:ok, user}
    end
  end

  defp create_bank_account(user_id, account_number) do
    %BankAccount{}
    |> BankAccount.changeset(%{
      user_id: user_id,
      number: account_number,
      name: "Default Account",
      type: "Checking",
      currency: "MWK",
      balance: Decimal.new("0.00")
    })
    |> Repo.insert()
  end

  defp create_initial_transaction(params, from_account, to_account) do
    Logger.info("[#{DateTime.utc_now() |> DateTime.to_string()}] * Creating initial transaction with accounting entries")

    # Prepare transaction attributes
    transaction_attrs = %{
      type: "transfer",
      amount: params["amount"],
      sender_account: params["from_account"],
      receiver_account: params["to_account"],
      from_account_id: from_account.id,
      to_account_id: to_account.id,
      owner_id: Repo.preload(from_account, :user).user_id,
      status: "pending",
      reference: params["reference"],
      description: params["description"] || "Transfer",
      value_date: DateTime.utc_now() |> DateTime.to_date() |> Date.to_string(),
      credit_amount: params["amount"],
      debit_amount: params["amount"]
    }

    # Create transaction with accounting entries
    case AccountingService.create_transaction_with_accounting(transaction_attrs, from_account.user_id) |> IO.inspect(label: "TXN-WITH-ACCOUNT") do
      {:ok, %{transaction: transaction}} ->
        {:ok, transaction}

      {:error, reason} ->
        Logger.error(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to create initial transaction with accounting entries: #{inspect(reason)}"
        )

        {:error, reason}
    end
  end

  defp update_transaction(transaction, response) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Updating transaction with response"
    )

    case response do
      %{"body" => %{"body" => %{"debitAmount" => amount}}} ->
        result =
          Repo.transaction(fn ->
            # Update transaction status
            {:ok, updated_transaction} =
              transaction
              |> Transaction.changeset(%{
                status: "completed",
                description: "Transfer completed",
                value_date: DateTime.utc_now() |> DateTime.to_date() |> Date.to_string(),
                opening_balance: nil,
                closing_balance: nil,
                credit_amount: amount,
                debit_amount: amount,
                amount: amount
              })
              |> Repo.update()

            # Update last_transaction_date for source account
            from_account = Repo.get!(FundAccounts, updated_transaction.from_account_id)

            {:ok, _updated_account} =
              from_account
              |> FundAccounts.changeset(%{
                last_transaction_date: NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)
              })
              |> Repo.update()

            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Transaction and account updated successfully"
            )

            # Preload associations before sending callback
            preloaded_transaction =
              Repo.preload(updated_transaction, [:from_account, :to_account])

            TransferCallbackServer.notify_transfer_update(preloaded_transaction)
            {:ok, updated_transaction}
          end)

        case result do
          {:ok, updated_transaction} ->
            {:ok, updated_transaction}

          {:error, error} ->
            Logger.info(
              "[#{DateTime.utc_now() |> DateTime.to_string()}] * Failed to update transaction: #{inspect(error)}"
            )

            {:error, error}
        end

      _ ->
        Logger.info(
          "[#{DateTime.utc_now() |> DateTime.to_string()}] * Invalid response format from transfer service"
        )

        {:error, "Invalid response format from transfer service"}
    end
  end
end
