defmodule ServiceManagerWeb.Api.Services.Local.UssdService do
  @moduledoc """
  Service module for USSD menu and option management operations.
  
  Provides functions for creating, reading, updating, and deleting USSD menus and options.
  Handles the business logic for USSD management including hierarchical relationships.
  """

  import Ecto.Query, warn: false
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Api.UssdMenuSchema
  alias ServiceManagerWeb.Api.UssdOptionSchema

  # ========== MENU OPERATIONS ==========

  @doc """
  Returns the list of USSD menus with their options preloaded.
  """
  def list_menus do
    try do
      menus = 
        UssdMenuSchema
        |> where([m], m.active == true)
        |> order_by([m], [asc: m.menu_order, asc: m.title])
        |> preload([:options])
        |> Repo.all()
        |> Enum.map(&preload_sorted_options/1)

      {:ok, menus}
    rescue
      e -> {:error, "Failed to fetch menus: #{Exception.message(e)}"}
    end
  end

  @doc """
  Gets a single USSD menu by ID with options preloaded.
  """
  def get_menu(id) do
    try do
      case Repo.get(UssdMenuSchema, id) do
        nil -> {:error, "Menu not found"}
        menu -> 
          menu = preload_sorted_options(menu)
          {:ok, menu}
      end
    rescue
      e -> {:error, "Failed to fetch menu: #{Exception.message(e)}"}
    end
  end

  @doc """
  Creates a new USSD menu.
  """
  def create_menu(attrs \\ %{}) do
    try do
      %UssdMenuSchema{}
      |> UssdMenuSchema.create_changeset(attrs)
      |> Repo.insert()
      |> case do
        {:ok, menu} -> {:ok, menu}
        {:error, changeset} -> {:error, format_changeset_errors(changeset)}
      end
    rescue
      e -> {:error, "Failed to create menu: #{Exception.message(e)}"}
    end
  end

  @doc """
  Updates an existing USSD menu.
  """
  def update_menu(%UssdMenuSchema{} = menu, attrs) do
    try do
      menu
      |> UssdMenuSchema.update_changeset(attrs)
      |> Repo.update()
      |> case do
        {:ok, menu} -> {:ok, menu}
        {:error, changeset} -> {:error, format_changeset_errors(changeset)}
      end
    rescue
      e -> {:error, "Failed to update menu: #{Exception.message(e)}"}
    end
  end

  @doc """
  Updates a USSD menu by ID.
  """
  def update_menu(id, attrs) do
    case get_menu(id) do
      {:ok, menu} -> update_menu(menu, attrs)
      error -> error
    end
  end

  @doc """
  Deletes a USSD menu by ID.
  """
  def delete_menu(id) do
    try do
      case Repo.get(UssdMenuSchema, id) do
        nil -> {:error, "Menu not found"}
        menu -> 
          case Repo.delete(menu) do
            {:ok, _} -> {:ok, "Menu deleted successfully"}
            {:error, changeset} -> {:error, format_changeset_errors(changeset)}
          end
      end
    rescue
      e -> {:error, "Failed to delete menu: #{Exception.message(e)}"}
    end
  end

  @doc """
  Soft deletes a USSD menu by setting active to false.
  """
  def deactivate_menu(id) do
    update_menu(id, %{active: false})
  end

  # ========== OPTION OPERATIONS ==========

  @doc """
  Returns the list of USSD options for a specific menu.
  """
  def list_options_for_menu(menu_id) do
    try do
      options = 
        UssdOptionSchema
        |> where([o], o.menu_id == ^menu_id and o.active == true)
        |> order_by([o], [asc: o.option_order, asc: o.text])
        |> Repo.all()

      {:ok, options}
    rescue
      e -> {:error, "Failed to fetch options: #{Exception.message(e)}"}
    end
  end

  @doc """
  Gets a single USSD option by ID.
  """
  def get_option(id) do
    try do
      case Repo.get(UssdOptionSchema, id) do
        nil -> {:error, "Option not found"}
        option -> {:ok, option}
      end
    rescue
      e -> {:error, "Failed to fetch option: #{Exception.message(e)}"}
    end
  end

  @doc """
  Creates a new USSD option.
  """
  def create_option(attrs \\ %{}) do
    try do
      %UssdOptionSchema{}
      |> UssdOptionSchema.create_changeset(attrs)
      |> Repo.insert()
      |> case do
        {:ok, option} -> {:ok, option}
        {:error, changeset} -> {:error, format_changeset_errors(changeset)}
      end
    rescue
      e -> {:error, "Failed to create option: #{Exception.message(e)}"}
    end
  end

  @doc """
  Updates an existing USSD option.
  """
  def update_option(%UssdOptionSchema{} = option, attrs) do
    try do
      option
      |> UssdOptionSchema.update_changeset(attrs)
      |> Repo.update()
      |> case do
        {:ok, option} -> {:ok, option}
        {:error, changeset} -> {:error, format_changeset_errors(changeset)}
      end
    rescue
      e -> {:error, "Failed to update option: #{Exception.message(e)}"}
    end
  end

  @doc """
  Updates a USSD option by ID.
  """
  def update_option(id, attrs) do
    case get_option(id) do
      {:ok, option} -> update_option(option, attrs)
      error -> error
    end
  end

  @doc """
  Deletes a USSD option by ID.
  """
  def delete_option(id) do
    try do
      case Repo.get(UssdOptionSchema, id) do
        nil -> {:error, "Option not found"}
        option -> 
          case Repo.delete(option) do
            {:ok, _} -> {:ok, "Option deleted successfully"}
            {:error, changeset} -> {:error, format_changeset_errors(changeset)}
          end
      end
    rescue
      e -> {:error, "Failed to delete option: #{Exception.message(e)}"}
    end
  end

  @doc """
  Soft deletes a USSD option by setting active to false.
  """
  def deactivate_option(id) do
    update_option(id, %{active: false})
  end

  # ========== HELPER FUNCTIONS ==========

  @doc """
  Creates a complete USSD menu with options in a single transaction.
  """
  def create_menu_with_options(menu_attrs, options_list) do
    try do
      Repo.transaction(fn ->
        with {:ok, menu} <- create_menu(menu_attrs),
             {:ok, _options} <- create_options_for_menu(menu.menu_id, options_list) do
          get_menu(menu.menu_id)
        else
          {:error, reason} -> Repo.rollback(reason)
        end
      end)
      |> case do
        {:ok, {:ok, menu}} -> {:ok, menu}
        {:ok, {:error, reason}} -> {:error, reason}
        {:error, reason} -> {:error, reason}
      end
    rescue
      e -> {:error, "Failed to create menu with options: #{Exception.message(e)}"}
    end
  end

  @doc """
  Creates multiple options for a menu.
  """
  def create_options_for_menu(menu_id, options_list) when is_list(options_list) do
    try do
      options_with_menu_id = 
        options_list
        |> Enum.with_index()
        |> Enum.map(fn {option_attrs, index} ->
          option_attrs
          |> Map.put("menu_id", menu_id)
          |> Map.put("option_order", Map.get(option_attrs, "option_order", index))
        end)

      results = Enum.map(options_with_menu_id, &create_option/1)
      
      case Enum.find(results, fn {status, _} -> status == :error end) do
        nil -> {:ok, Enum.map(results, fn {:ok, option} -> option end)}
        {:error, reason} -> {:error, reason}
      end
    rescue
      e -> {:error, "Failed to create options: #{Exception.message(e)}"}
    end
  end

  @doc """
  Gets the target menu for a submenu option.
  """
  def get_target_menu_for_option(option_id) do
    try do
      case get_option(option_id) do
        {:ok, %{action: "submenu", target_menu_id: target_id}} when not is_nil(target_id) ->
          get_menu(target_id)
        {:ok, _option} ->
          {:error, "Option is not a submenu type"}
        error ->
          error
      end
    rescue
      e -> {:error, "Failed to get target menu: #{Exception.message(e)}"}
    end
  end

  @doc """
  Validates that a target menu exists for submenu options.
  """
  def validate_target_menu(target_menu_id) when is_nil(target_menu_id), do: {:ok, nil}
  def validate_target_menu(target_menu_id) do
    case get_menu(target_menu_id) do
      {:ok, menu} -> {:ok, menu}
      {:error, _} -> {:error, "Target menu does not exist"}
    end
  end

  # ========== PRIVATE FUNCTIONS ==========

  defp preload_sorted_options(menu) do
    options = 
      UssdOptionSchema
      |> where([o], o.menu_id == ^menu.menu_id and o.active == true)
      |> order_by([o], [asc: o.option_order, asc: o.text])
      |> Repo.all()
    
    %{menu | options: options}
  end

  defp format_changeset_errors(changeset) do
    changeset
    |> Ecto.Changeset.traverse_errors(fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
    |> Enum.map(fn {field, errors} ->
      "#{field}: #{Enum.join(errors, ", ")}"
    end)
    |> Enum.join("; ")
  end

  # ========== FORM INTEGRATION FUNCTIONS ==========

  @doc """
  Gets form fields for a specific form ordered by field order
  """
  def get_form_fields(form_name) do
    try do
      alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
      
      case MobileAppFormsService.list_fields(%{"form" => form_name}) do
        {:ok, fields} ->
          # Sort fields by field_order for proper step sequence
          sorted_fields = 
            fields
            |> Enum.sort_by(fn field -> 
              field.field_order || 0
            end)
          
          {:ok, sorted_fields}
        
        {:error, reason} ->
          {:error, reason}
      end
    rescue
      e -> {:error, "Failed to fetch form fields: #{Exception.message(e)}"}
    end
  end

  @doc """
  Creates a USSD input session for collecting form data
  """
  def create_form_session(option_id, form_name, user_session_id \\ nil) do
    try do
      case get_form_fields(form_name) do
        {:ok, fields} ->
          session_data = %{
            option_id: option_id,
            form_name: form_name,
            user_session_id: user_session_id,
            current_field_index: 0,
            total_fields: length(fields),
            field_definitions: fields,
            collected_data: %{},
            field_history: [],
            started_at: DateTime.utc_now(),
            status: "active"
          }
          
          {:ok, session_data}
        
        {:error, reason} ->
          {:error, reason}
      end
    rescue
      e -> {:error, "Failed to create form session: #{Exception.message(e)}"}
    end
  end

  @doc """
  Gets the next field to collect in a form session
  """
  def get_next_form_field(session_data) do
    try do
      fields = session_data.field_definitions
      current_index = session_data.current_field_index
      
      if current_index < length(fields) do
        field = Enum.at(fields, current_index)
        {:ok, field, current_index + 1 < length(fields)}
      else
        {:completed, session_data.collected_data}
      end
    rescue
      e -> {:error, "Failed to get next field: #{Exception.message(e)}"}
    end
  end

  @doc """
  Processes user input for the current form field
  """
  def process_form_input(session_data, user_input) do
    try do
      current_field = Enum.at(session_data.field_definitions, session_data.current_field_index)
      
      # Handle button fields differently
      if current_field.field_type == "button" do
        # For button fields, check if user entered the continue number (field_order)
        expected_number = current_field.field_order || (session_data.current_field_index + 1)
        
        case user_input do
          ^expected_number when is_integer(expected_number) ->
            process_button_field(session_data, current_field)
          input when is_binary(input) ->
            case Integer.parse(input) do
              {^expected_number, _} -> process_button_field(session_data, current_field)
              _ -> {:error, "Enter #{expected_number} to continue"}
            end
          _ ->
            {:error, "Enter #{expected_number} to continue"}
        end
      else
        # Validate input based on field type for non-button fields
        case validate_field_input(current_field, user_input) do
          {:ok, validated_value} ->
            # Store the validated input and add to history
            updated_data = Map.put(session_data.collected_data, current_field.field_name, validated_value)
            updated_history = [session_data.current_field_index | session_data.field_history]
            
            # Move to next field
            updated_session = %{session_data |
              current_field_index: session_data.current_field_index + 1,
              collected_data: updated_data,
              field_history: updated_history
            }
            
            {:ok, updated_session}
          
          {:error, reason} ->
            {:error, reason}
        end
      end
    rescue
      e -> {:error, "Failed to process form input: #{Exception.message(e)}"}
    end
  end

  defp process_button_field(session_data, _button_field) do
    # For button fields, just move to next field without storing data
    updated_history = [session_data.current_field_index | session_data.field_history]
    
    updated_session = %{session_data |
      current_field_index: session_data.current_field_index + 1,
      field_history: updated_history
    }
    
    {:ok, updated_session}
  end

  @doc """
  Handles going back to previous field in form
  """
  def go_back_form_field(session_data) do
    try do
      case session_data.field_history do
        [] ->
          {:error, "Cannot go back further"}
        
        [previous_index | remaining_history] ->
          updated_session = %{session_data |
            current_field_index: previous_index,
            field_history: remaining_history,
            # Remove the data for the current field if it was collected
            collected_data: remove_field_data(session_data, session_data.current_field_index)
          }
          
          {:ok, updated_session}
      end
    rescue
      e -> {:error, "Failed to go back: #{Exception.message(e)}"}
    end
  end

  defp remove_field_data(session_data, field_index) do
    field = Enum.at(session_data.field_definitions, field_index)
    if field && field.field_type != "button" do
      Map.delete(session_data.collected_data, field.field_name)
    else
      session_data.collected_data
    end
  end

  @doc """
  Validates user input based on field type and constraints
  """
  def validate_field_input(field, input) do
    try do
      case field.field_type do
        "email" ->
          if String.contains?(input, "@") do
            {:ok, String.trim(input)}
          else
            {:error, "Please enter a valid email address"}
          end
        
        "phone" ->
          # Basic phone validation - digits only
          cleaned = String.replace(input, ~r/[^\d]/, "")
          if String.length(cleaned) >= 10 do
            {:ok, cleaned}
          else
            {:error, "Please enter a valid phone number"}
          end
        
        "number" ->
          case Float.parse(input) do
            {number, _} -> {:ok, number}
            :error -> {:error, "Please enter a valid number"}
          end
        
        "integer" ->
          case Integer.parse(input) do
            {number, _} -> {:ok, number}
            :error -> {:error, "Please enter a valid whole number"}
          end
        
        "password" ->
          if String.length(input) >= 6 do
            {:ok, input}
          else
            {:error, "Password must be at least 6 characters long"}
          end
        
        _ ->
          # Text field - basic validation
          trimmed = String.trim(input)
          if String.length(trimmed) > 0 do
            {:ok, trimmed}
          else
            {:error, "This field is required"}
          end
      end
    rescue
      e -> {:error, "Validation error: #{Exception.message(e)}"}
    end
  end

  @doc """
  Generates USSD prompt text for a form field
  """
  def generate_field_prompt(field, field_number, total_fields) do
    field_label = field.label || field.field_name
    
    if field.field_type == "button" do
      # For button fields: "Enter 4 to continue"
      expected_number = field.field_order || field_number
      "CON Enter #{expected_number} to continue"
    else
      # For other fields: "Label-1. Enter account number"
      "CON #{field_label}-#{field_number}. Enter #{String.downcase(field_label)}"
    end
  end

  @doc """
  Completes a form session and returns the collected data
  """
  def complete_form_session(session_data) do
    try do
      # Mark session as completed
      completed_session = %{session_data |
        status: "completed",
        completed_at: DateTime.utc_now()
      }
      
      {:ok, completed_session}
    rescue
      e -> {:error, "Failed to complete form session: #{Exception.message(e)}"}
    end
  end
end