defmodule ServiceManagerWeb.Services.Local.BeneficiaryService do
  @moduledoc """
  Service module for handling beneficiary-related operations.
  """

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Accounts.SchemaBeneficiary
  import Ecto.Query

  @doc """
  Creates a new beneficiary.
  """
  def create_beneficiary(params) do
    case %SchemaBeneficiary{}
         |> SchemaBeneficiary.changeset(params)
         |> Repo.insert() do
      {:ok, beneficiary} ->
        {:ok, beneficiary, "Beneficiary created successfully"}

      {:error, changeset} ->
        {:error, format_changeset_errors(changeset)}
    end
  end

  @doc """
  Retrieves a beneficiary by ID.
  """
  def get_beneficiary(beneficiary_id, user_id) do
    case Repo.get(SchemaBeneficiary, beneficiary_id) do
      nil ->
        {:error, "Beneficiary not found"}

      beneficiary ->
        # if beneficiary.user_id == user_id do
        {:ok, beneficiary, "Beneficiary fetched successfully"}
        # else
        #   {:error, "Beneficiary not found"}
        # end
    end
  end

  @doc """
  Lists all beneficiaries, with pagination.
  """
  def list_beneficiaries(user_id, beneficiary_type, page \\ 1, page_size \\ 10) do
    query =
      case beneficiary_type do
        "wallet" ->
          SchemaBeneficiary
          |> where([b], b.wallet_id == ^user_id and b.beneficiary_type == ^"wallet")

        _ ->
          SchemaBeneficiary
          |> where([b], b.user_id == ^user_id and b.beneficiary_type == ^beneficiary_type)
      end

    page = query |> Repo.paginate(page: page, page_size: page_size)
    {:ok, page, "Beneficiaries fetched successfully"}
  end

  @doc """
  Updates an existing beneficiary.
  """
  def update_beneficiary(beneficiary_id, params) do
    case Repo.get(SchemaBeneficiary, beneficiary_id) do
      nil ->
        {:error, "Beneficiary not found"}

      beneficiary ->
        case beneficiary
             |> SchemaBeneficiary.changeset(params)
             |> Repo.update() do
          {:ok, updated_beneficiary} ->
            {:ok, updated_beneficiary, "Beneficiary updated successfully"}

          {:error, changeset} ->
            {:error, format_changeset_errors(changeset)}
        end
    end
  end

  @doc """
  Removes a beneficiary.
  """
  def remove_beneficiary(beneficiary_id, user_id) do
    case Repo.get(SchemaBeneficiary, beneficiary_id) do
      nil ->
        {:error, "Beneficiary not found"}

      beneficiary ->
        # if beneficiary.user_id == user_id do
        case Repo.delete(beneficiary) do
          {:ok, _} ->
            {:ok, nil, "Beneficiary removed successfully"}

          {:error, changeset} ->
            {:error, format_changeset_errors(changeset)}
        end

        # else
        #   {:error, "Beneficiary not found"}
        # end
    end
  end

  @doc """
  Validates a beneficiary's account details.
  """
  def validate_beneficiary(account_number, bank_code) do
    # This is a placeholder. In a real-world scenario, you would integrate
    # with a bank verification service or your own validation logic.
    {:ok, %{is_valid: true, account_name: "John Doe"}, "Beneficiary validated successfully"}
  end

  @doc """
  Retrieves the status of a beneficiary.
  """
  def get_beneficiary_status(user_id, beneficiary_id) do
    case Repo.get(SchemaBeneficiary, beneficiary_id) do
      nil ->
        {:error, "Beneficiary not found"}

      beneficiary ->
        # if beneficiary.user_id == user_id do
        {:ok, %{status: beneficiary.status}, "Beneficiary status fetched successfully"}
        # else
        #   {:error, "Beneficiary not found"}
        # end
    end
  end

  @doc """
  Sets a beneficiary as the default.
  """
  def set_default_beneficiary(beneficiary_id) do
    case Repo.get(SchemaBeneficiary, beneficiary_id) do
      nil ->
        {:error, "Beneficiary not found"}

      beneficiary ->
        # First, unset any existing default beneficiary
        Repo.update_all(SchemaBeneficiary, set: [is_default: false])

        # Then set the new default
        case beneficiary
             |> SchemaBeneficiary.changeset(%{is_default: true})
             |> Repo.update() do
          {:ok, updated_beneficiary} ->
            {:ok, updated_beneficiary, "Default beneficiary set successfully"}

          {:error, changeset} ->
            {:error, format_changeset_errors(changeset)}
        end
    end
  end

  @doc """
  Searches for beneficiaries based on given criteria.
  """
  def search_beneficiaries(criteria, page \\ 1, page_size \\ 10) do
    query = from(b in SchemaBeneficiary)

    query =
      Enum.reduce(criteria, query, fn
        {:name, name}, query ->
          where(query, [b], ilike(b.name, ^"%#{name}%"))

        {:account_number, account_number}, query ->
          where(query, [b], b.account_number == ^account_number)

        _, query ->
          query
      end)

    page = Repo.paginate(query, page: page, page_size: page_size)
    {:ok, page, "Beneficiaries search completed successfully"}
  end

  defp format_changeset_errors(changeset) do
    changeset
    |> Ecto.Changeset.traverse_errors(fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
    |> Enum.map(fn {key, errors} ->
      Enum.map(errors, fn error -> "#{key}: #{error}" end)
    end)
    |> List.flatten()
    |> Enum.join(", ")
  end
end
