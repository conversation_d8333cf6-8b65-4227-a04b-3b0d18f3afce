defmodule ServiceManagerWeb.Api.Services.Local.TransactionsLookupService do
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Transactions.Transaction, as: MainSchema
  alias Decimal
  import Ecto.Query

  def get_transactions_by_reference(reference) do
    # Get wallet transactions
    wallet_transactions =
      WalletTransactions
      |> where([t], t.reference == ^reference)
      |> Repo.all()

    # Get standard transactions using exact reference match
    account_transactions =
      MainSchema
      |> where([t], t.reference == ^reference)
      # |> preload([:from_account, :to_account])
      |> Repo.all()

    # Combine results and determine overall status
    transaction_status =
      case {wallet_transactions, account_transactions} do
        {[], []} ->
          "not_found"

        {wallet_txns, account_txns} ->
          all_complete = Enum.all?(wallet_txns ++ account_txns, &(&1.status == "completed"))
          if all_complete, do: "success", else: "failed"
      end

    %{
      wallet_transactions: wallet_transactions,
      account_transactions: account_transactions,
      reference: reference,
      transaction_status: transaction_status
    }
  end

  def get_transactions_by_references(references) when is_list(references) do
    # Get wallet transactions
    wallet_transactions =
      WalletTransactions
      |> where([t], t.reference in ^references)
      |> Repo.all()
      |> group_by_reference()

    # Get standard transactions using exact reference match
    account_transactions =
      MainSchema
      |> where([t], t.reference in ^references)
      |> preload([:from_account, :to_account])
      |> Repo.all()
      |> Enum.group_by(& &1.reference)

    # Return grouped results with status
    references
    |> Enum.map(fn ref ->
      wallet_txns = Map.get(wallet_transactions, ref, [])
      account_txns = Map.get(account_transactions, ref, [])

      transaction_status =
        case {wallet_txns, account_txns} do
          {[], []} ->
            "not_found"

          {w_txns, a_txns} ->
            all_complete = Enum.all?(w_txns ++ a_txns, &(&1.status == "complete"))
            if all_complete, do: "success", else: "pending"
        end

      %{
        reference: ref,
        wallet_transactions: wallet_txns,
        account_transactions: account_txns,
        transaction_status: transaction_status
      }
    end)
  end

  defp group_by_reference(transactions) do
    transactions
    |> Enum.group_by(& &1.reference)
  end

  def get_transaction_pair(reference) do
    case get_transactions_by_reference(reference) do
      %{wallet_transactions: [wallet_txn], account_transactions: [account_txn]} ->
        transaction_status =
          if wallet_txn.status == "complete" && account_txn.status == "complete" do
            "success"
          else
            "pending"
          end

        {:ok,
         %{
           wallet_transaction: wallet_txn,
           account_transaction: account_txn,
           transaction_status: transaction_status
         }}

      %{wallet_transactions: [], account_transactions: []} ->
        {:error, "No transactions found for reference #{reference}"}

      _ ->
        {:error, "Invalid transaction pair for reference #{reference}"}
    end
  end

  def get_reversal_chain(original_transaction_id) when is_binary(original_transaction_id) do
    # Get original wallet transaction
    original_wallet_txn =
      WalletTransactions
      |> where([t], t.id == ^original_transaction_id)
      |> Repo.one()

    case original_wallet_txn do
      nil ->
        {:error, "Original transaction not found"}

      txn ->
        # Get reversal wallet transaction
        reversal_wallet_txn =
          WalletTransactions
          |> where(
            [t],
            t.original_transaction_id == ^original_transaction_id and t.is_reversal == true
          )
          |> Repo.one()

        # Get original account transaction using exact reference match
        original_account_txn =
          MainSchema
          |> where([t], t.reference == ^txn.reference)
          |> preload([:from_account, :to_account])
          |> Repo.one()

        # Get reversal account transaction if exists
        reversal_account_txn =
          case reversal_wallet_txn do
            nil ->
              nil

            rev_txn ->
              MainSchema
              |> where([t], t.reference == ^rev_txn.reference)
              |> preload([:from_account, :to_account])
              |> Repo.one()
          end

        # Determine status for original and reversal transactions
        original_status =
          if original_wallet_txn.status == "complete" && original_account_txn.status == "complete" do
            "success"
          else
            "pending"
          end

        reversal_status =
          case {reversal_wallet_txn, reversal_account_txn} do
            {nil, nil} ->
              nil

            {w_txn, a_txn} ->
              if w_txn.status == "complete" && a_txn.status == "complete" do
                "success"
              else
                "pending"
              end
          end

        {:ok,
         %{
           original: %{
             wallet_transaction: original_wallet_txn,
             account_transaction: original_account_txn,
             transaction_status: original_status
           },
           reversal:
             case reversal_wallet_txn do
               nil ->
                 nil

               _ ->
                 %{
                   wallet_transaction: reversal_wallet_txn,
                   account_transaction: reversal_account_txn,
                   transaction_status: reversal_status
                 }
             end
         }}
    end
  end

  def get_mini_statement(account_number, date_range \\ nil, transaction_count \\ 10)

  def get_mini_statement(account_number, nil, count) when is_binary(account_number) do
    do_get_mini_statement(account_number, nil, nil, count)
  end

  def get_mini_statement(account_number, {start_date, end_date}, count)
      when is_binary(account_number) do
    # Convert dates to start and end of day as NaiveDateTime
    start_datetime = NaiveDateTime.new!(start_date, ~T[00:00:00])
    end_datetime = NaiveDateTime.new!(end_date, ~T[23:59:59])

    do_get_mini_statement(account_number, start_datetime, end_datetime, count)
  end

  def get_mini_statement(_, _, _), do: {:error, "Invalid account number format"}

  defp do_get_mini_statement(account_number, start_datetime, end_datetime, count) do
    # Build base queries
    account_query =
      MainSchema
      |> where([t], t.sender_account == ^account_number or t.receiver_account == ^account_number)
      |> order_by([t], desc: t.inserted_at)

    wallet_query =
      WalletTransactions
      |> where([t], t.sender_account == ^account_number or t.receiver_account == ^account_number)
      |> order_by([t], desc: t.inserted_at)

    # Add date filters if provided
    {account_query, wallet_query} =
      if start_datetime && end_datetime do
        {
          account_query
          |> where([t], t.inserted_at >= ^start_datetime and t.inserted_at <= ^end_datetime),
          wallet_query
          |> where([t], t.inserted_at >= ^start_datetime and t.inserted_at <= ^end_datetime)
        }
      else
        {account_query, wallet_query}
      end

    # Execute queries with limit
    account_transactions = account_query |> limit(^count) |> Repo.all()
    wallet_transactions = wallet_query |> limit(^count) |> Repo.all()

    case {account_transactions, wallet_transactions} do
      {[], []} ->
        {:error, "No transactions found for account #{account_number}"}

      {account_txns, wallet_txns} ->
        # Combine and sort all transactions by date
        all_transactions =
          (account_txns ++ wallet_txns)
          |> Enum.sort_by(&NaiveDateTime.to_erl(&1.inserted_at), :desc)
          |> Enum.take(count)

        # Calculate total value of transactions
        statement_value = calculate_statement_value(all_transactions, account_number)

        {:ok,
         %{
           account_number: account_number,
           transactions: all_transactions,
           data: all_transactions,
           total_count: length(all_transactions),
           statement_value: statement_value,
           status: true,
           message: "Mini statement pulled"
         }}
    end
  end

  defp calculate_statement_value(transactions, account_number) do
    transactions
    |> Enum.filter(&(&1.status == "completed"))
    |> Enum.reduce(Decimal.new(0), fn transaction, acc ->
      amount = Decimal.new(transaction.amount)

      cond do
        transaction.sender_account == account_number ->
          # Debit (money sent)
          Decimal.add(acc, amount)

        transaction.receiver_account == account_number ->
          # Credit (money received)
          Decimal.add(acc, amount)

        true ->
          acc
      end
    end)
  end
end
