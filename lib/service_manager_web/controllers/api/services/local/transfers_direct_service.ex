defmodule ServiceManager.Service.TransfersDirectService do
  @moduledoc """

  """

  def transfer_to_other_account(params) do
    # create database entry

    # when entry is saved make api call

    # update transaction to processing

    # get api result

    # update database entry with result and respond
  end

  def transfer_to_wallet(params) do
  end

  def transfer_to_other_bank(params) do
  end
end
