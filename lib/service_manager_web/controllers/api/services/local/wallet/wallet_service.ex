defmodule ServiceManagerWeb.Api.Services.WalletService do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Repo
  alias ServiceManager.WalletAccounts.WalletUser, as: Profile
  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService, as: RemoteProfile
  alias ServiceManager.WalletAccounts, as: Account
  alias ServiceManagerWeb.Api.Services.WalletService, as: Service

  def approve(params) do
    user = Repo.get_by(Profile, email: params["email"]) |> Repo.preload(:accounts)

    case user
         |> Profile.update_user(%{approved: true})
         |> Repo.update() do
      {:ok, profile} ->
        {:ok, profile} |> respond("profile approved")

      {:error, changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              # Convert list to string with Enum.join
              String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
            end)
          end)

        respond({:error, errors})
    end
  end

  def disable(params) do
    user = Repo.get_by(Profile, email: params["email"]) |> Repo.preload(:accounts)

    case user
         |> Profile.update_user(%{approved: false})
         |> Repo.update() do
      {:ok, profile} ->
        {:ok, profile} |> respond("profile disabled")

      {:error, changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              # Convert list to string with Enum.join
              String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
            end)
          end)

        respond({:error, errors})
    end
  end

  def register(_user, profile_params) do
    case Repo.get_by(Profile, email: profile_params["email"]) do
      nil ->
        case %Profile{}
             |> Profile.registration_changeset(profile_params)
             |> Repo.insert() do
          {:ok, profile} ->
            profile = Repo.preload(profile, :accounts)
            {:ok, profile} |> respond("profile created")

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  # Convert list to string with Enum.join
                  String.replace(acc, "%{#{key}}", Enum.join(to_string(value), ", "))
                end)
              end)

            respond({:error, errors})
        end

      _user ->
        respond({:error, "Email already exists"})
    end
  end

  def update(user, profile_params) do
    profile = Repo.get!(Profile, user.id)

    case Repo.update(update_profile(profile, profile_params)) do
      {:ok, profile} ->
        profile = Repo.preload(profile, :accounts)
        respond({:ok, profile}, "profile updated")

      {:error, %Ecto.Changeset{} = changeset} ->
        errors =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
            Enum.reduce(opts, msg, fn {key, value}, acc ->
              String.replace(acc, "%{#{key}}", to_string(value))
            end)
          end)

        respond({:error, errors})
    end
  end

  def update_password(user, password_params) do
    old_password = password_params["old_password"]
    new_password = password_params["new_password"]
    confirm_new_password = password_params["confirm_new_password"]

    if Bcrypt.verify_pass(old_password, user.hashed_password) do
      if new_password == confirm_new_password do
        changeset =
          Profile.update_password_changeset(user, %{
            password: new_password,
            first_time_login: false
          })

        case Repo.update(changeset) do
          {:ok, user} ->
            ServiceManagerWeb.Api.Services.WalletAuthenticationService.invalidate(
              password_params["access_token"]
            )

            respond({:ok, user}, "Password updated successfully")

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  # Convert value to list with List.wrap
                  String.replace(acc, "%{#{key}}", to_string(Enum.join(List.wrap(value), ", ")))
                end)
              end)

            respond({:error, errors})
        end
      else
        respond({:error, "New password and confirm new password do not match"})
      end
    else
      respond({:error, "Old password is incorrect"})
    end
  end

  def reset_password(mobile_number, new_password) do
    case Account.get_wallet_user_by_mobile_number(mobile_number) do
      nil ->
        respond({:error, "User not found"})

      user ->
        changeset =
          Profile.update_password_changeset(user, %{
            password: new_password,
            first_time_login: true
          })

        case Repo.update(changeset) do
          {:ok, user} ->
            # Invalidate any existing sessions
            # ServiceManagerWeb.Api.Services.WalletAuthenticationService.invalidate_all(user.id)
            {:ok, user}

          {:error, changeset} ->
            errors =
              Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                Enum.reduce(opts, msg, fn {key, value}, acc ->
                  String.replace(acc, "%{#{key}}", to_string(Enum.join(List.wrap(value), ", ")))
                end)
              end)

            {:error, errors}
        end
    end
  end

  def details(user, _profile_params) do
    {:ok, remote_profile} = user.account_number |> RemoteProfile.get_profile_by_account_number()

    updated_balance =
      user.account_balance
      |> to_string()
      |> Decimal.new()
      |> Decimal.to_string(:normal)

    local_profile =
      user
      |> Map.put(:account_balance, updated_balance)
      |> Map.put(:name, "#{user.first_name} #{user.last_name}")
      |> Map.put(:created_at, user.inserted_at)

    # |> Map.put(:updated_at, user.updated_at)

    %{
      "message" => "Profile details fetched",
      "status" => true,
      "data" => %{
        "bank_profile" => remote_profile["profile"] |> Map.delete("user_accounts"),
        "profile" => local_profile,
        "wallet_accounts" =>
          Enum.filter(remote_profile["profile"]["user_accounts"], fn account ->
            account["account_type"] == "wallet"
          end),
        "bank_accounts" =>
          Enum.filter(remote_profile["profile"]["user_accounts"], fn account ->
            account["account_type"] != "wallet"
          end)
      }
    }
  end

  # Public function to get an account by its number and password
  def get_user_by_email(email) do
    # Query the UserAccounts table to get the account by its number
    Repo.get_by(UserAccounts, email: email)
  end

  defp update_profile(profile, attrs) do
    Profile.update_user(profile, attrs)
  end

  defp change_profile(profile, attrs) do
    Profile.registration_changeset(profile, attrs)
  end

  defp respond({:error, error_info}) do
    %{
      "message" => "Profile update failed",
      "status" => false,
      "data" => %{
        "errors" => error_info
      }
    }
  end

  defp respond({:ok, profile}, message \\ "") do
    %{
      "message" => message,
      "status" => true,
      "data" => %{
        "wallet" => profile
      }
    }
  end
end
