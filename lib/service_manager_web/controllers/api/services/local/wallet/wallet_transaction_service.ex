defmodule ServiceManager.Service.WalletTransactionService do
  # Import necessary modules and aliases
  alias ServiceManager.Repo
  alias ServiceManager.Transactions.WalletTransactions, as: Transaction
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.WalletAccounts.WalletUser, as: UserAccounts
  alias ServiceManager.WalletAccounts.WalletUser, as: AccountUsers
  alias ServiceManager.Services.AccountingService
  import Ecto.Query

  # Function to create a new account
  # @spec create_account(any()) :: any()
  # def create_account(params) do

  #   params = Enum.into(params, %{}, fn {k, v} -> {String.to_atom(k), v} end)
  #   %{customer_number: customer_number, email: email, name: name, initial_balance: initial_balance, currency: currency, account_type: account_type} = params

  #   # Start a database transaction
  #   Repo.transaction(fn ->
  #     # Attempt to find or create an account user, then create an account, and finally create an initial deposit transaction
  #     with {:ok, account_user} <- find_or_create_account_user(email, name, params),
  #          {:ok, user_account} <- do_create_account(customer_number, account_user, initial_balance, currency, account_type),
  #          {:ok, _transaction} <- create_transaction(nil, user_account, initial_balance, "initial_deposit") do
  #       # Return the created user account if all operations succeed
  #       user_account
  #     else
  #       # Rollback the transaction if any operation fails
  #       {:error, changeset} -> Repo.rollback(changeset)
  #     end
  #   end)
  # end

  # Function to credit an account
  def credit_account(account_number, amount, description \\ "Credit") do
    # Initiate a database transaction
    text_message = "You have been credited MWK #{amount} into your wallet."

    {:ok, response} =
      Repo.transaction(fn ->
        # Try to fetch the account by its number, update its balance, and create a credit transaction
        with {:ok, account, message} <- get_account_by_number(account_number),
             {:ok, updated_account, message} <-
               do_update_balance(
                 account,
                 Decimal.add(account.balance || Decimal.new(0), Decimal.new(amount)),
                 amount,
                 message,
                 text_message
               ),
             {:ok, transaction} <- create_transaction(nil, account, amount, description, message) do
          # If all operations are successful, return the updated account and the transaction
          {updated_account, transaction}
        else
          # If the account is not found or any operation fails, rollback the transaction
          nil -> Repo.rollback(:account_not_found)
          {:error, changeset} -> Repo.rollback(changeset)
        end
      end)

    response
  end

  # Function to debit an account
  def debit_account(account_number, amount, description \\ "Debit") do
    text_message = "You have been debited MWK #{amount} from your wallet."
    # Start a database transaction
    {:ok, response} =
      Repo.transaction(fn ->
        # Attempt to get the account by its number, check sufficient balance, update its balance, and create a debit transaction
        with {:ok, account, message} <- get_account_by_number(account_number),
             :ok <- check_sufficient_balance(account, amount),
             {:ok, updated_account, message} <-
               do_update_balance(
                 account,
                 Decimal.sub(account.balance, Decimal.new(amount)),
                 amount,
                 message,
                 text_message
               ),
             {:ok, transaction} <- create_transaction(account, nil, amount, description, message) do
          # Return the updated account and the transaction if all operations succeed
          {updated_account, transaction}
        else
          # Rollback the transaction if the account is not found, insufficient funds, or any operation fails
          nil -> Repo.rollback(:account_not_found)
          {:error, :insufficient_funds} -> Repo.rollback(:insufficient_funds)
          {:error, changeset} -> Repo.rollback(changeset)
        end
      end)

    response
  end

  # Function to lookup accounts by email
  def lookup_accounts_by_email(email) do
    # Query the AccountUsers table to find accounts by email and preload associated user accounts
    AccountUsers
    |> where([au], au.email == ^email)
    |> preload(:user_accounts)
    |> Repo.one()
  end

  # Function to lookup accounts by email
  def lookup_accounts_by_phone(phone_number) do
    # Query the AccountUsers table to find accounts by email and preload associated user accounts
    AccountUsers
    |> where([au], au.phone == ^phone_number)
    |> preload(:wallet_accounts)
    |> Repo.one()
  end

  # Function to transfer funds between accounts
  @spec transfer_funds(any(), any(), any(), any()) :: any()
  @spec transfer_funds(any(), any(), any()) :: any()
  def transfer_funds(from_account_number, to_account_number, amount, description \\ "Transfer") do
    from_text_message = "You have transferred MWK #{amount} to #{to_account_number}."
    to_text_message = "You have received MWK #{amount} from #{from_account_number}."
    # Start a database transaction
    Repo.transaction(fn ->
      # Attempt to get both accounts by their numbers, check sufficient balance, update balances, and create a transfer transaction
      with {:ok, from_account, from_message} <- get_account_by_number(from_account_number),
           {:ok, to_account, to_message} <- get_account_by_number(to_account_number),
           :ok <- check_sufficient_balance(from_account, amount),
           {:ok, updated_from_account, message_from} <-
             do_update_balance_with_message(
               from_account,
               Decimal.sub(from_account.balance, Decimal.new(amount)),
               from_message,
               from_text_message
             ),
           {:ok, updated_to_account, message_to} <-
             do_update_balance_with_message(
               to_account,
               Decimal.add(to_account.balance, Decimal.new(amount)),
               to_message,
               to_text_message
             ),
           {:ok, transaction} <-
             create_transaction(
               from_account,
               to_account,
               amount,
               description,
               message_to,
               message_from
             ) do
        # Return the updated accounts and the transaction if all operations succeed
        {updated_from_account, updated_to_account, transaction}
      else
        # Rollback the transaction if any account is not found, insufficient funds, or any operation fails
        nil -> Repo.rollback(:account_not_found)
        {:error, :insufficient_funds} -> Repo.rollback(:insufficient_funds)
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  # Function to get transactions for an account
  def get_account_transactions(account_number) do
    # Get the account by its number
    {:ok, account, _message} = get_account_by_number(account_number)

    # If the account exists, query the transactions table for transactions involving the account
    transactions =
      if account do
        Transaction
        |> where([t], t.from_wallet_id == ^account.id or t.to_wallet_id == ^account.id)
        |> order_by([t], desc: t.inserted_at)
        |> Repo.all()
      else
        # Return an empty list if the account does not exist
        []
      end

    {:ok, transactions}
  end

  # Function to get an account by its number
  def get_by_account_number(account_number) do
    # Call the private function to get the account by its number
    get_account_by_number(account_number)
  end

  # Private function to find or create an account user
  defp find_or_create_account_user(email, name, params \\ %{}) do
    # Attempt to get the account user by email
    case Repo.get_by(AccountUsers, email: email, phone: params.mobile_number) do
      # If the account user does not exist, create a new account user
      nil -> create_account_user(email, name, params)
      # If the account user exists, return it
      account_user -> {:ok, account_user}
    end
  end

  # Private function to create an account user
  defp create_account_user(_email, _name, params) do
    # Create a new AccountUsers struct and insert it into the database
    %AccountUsers{}
    |> AccountUsers.changeset(params)
    |> Repo.insert()
  end

  # Private function to create an account
  defp do_create_account(customer_number, account_user, initial_balance, currency, account_type) do
    # Create a new UserAccounts struct and insert it into the database
    %UserAccounts{}
    |> UserAccounts.changeset(%{
      account_number:
        if(account_type == "wallet",
          do: generate_wallet_number(),
          else: generate_account_number()
        ),
      customer_number: customer_number,
      balance: initial_balance,
      currency: currency,
      account_type: "WALLET",
      status: "active",
      account_users_id: account_user.id
    })
    |> Repo.insert()
  end

  # Private function to get an account by its number
  defp get_account_by_number(account_number) do
    # Query the UserAccounts table to get the account by its number
    case Repo.get_by(UserAccounts, mobile_number: account_number) do
      nil ->
        # Account doesn't exist, create a new one
        password = generate_random_password()

        wallet_user_params = %{
          "mobile_number" => account_number,
          "password" => password
        }

        case ServiceManager.WalletAccounts.register_wallet_user(wallet_user_params, password) do
          {:ok, wallet_user, message} ->
            # Create a new account for the wallet user
            {:ok, wallet_user, message}

          {:error, error, message} ->
            {:error, error, message}
        end

      user_wallet ->
        # {:ok, match} = user_wallet
        {:ok, user_wallet, SMSMessage.new(%SMSMessage{receiver: user_wallet.mobile_number})}
    end
  end

  defp generate_random_password do
    # Generate a random string of 8 characters
    for _ <- 1..12,
        into: "",
        do: <<Enum.random(~c"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")>>
  end

  # Private function to update the balance of an account
  defp do_update_balance(account, new_balance) do
    # Update the balance and last transaction date of the account
    account =
      account
      |> UserAccounts.update_balance(%{
        balance: new_balance,
        last_transaction_date: NaiveDateTime.utc_now()
      })
      |> Repo.update()

    account
  end

  # Private function to update the balance of an account
  defp do_update_balance_with_message(account, new_balance, message, message_text \\ "") do
    # Update the balance and last transaction date of the account
    account =
      account
      |> UserAccounts.update_balance(%{
        # balance: new_balance,
        last_transaction_date: NaiveDateTime.utc_now()
      })
      |> Repo.update()

    {_, account} = account

    message =
      cond do
        message.completed == false and message.message == "" ->
          message
          |> SMSMessage.new()
          |> SMSMessage.new_message("Dear #{account.account_number} #{message_text}")

        message.completed == false and message.message != "" ->
          message |> SMSMessage.new_message(message_text)

        true ->
          message
      end

    {:ok, account, message}
  end

  # Private function to update the balance of an account
  defp do_update_balance(account, new_balance, amount) do
    # Update the balance and last transaction date of the account
    account =
      account
      |> UserAccounts.update_balance(%{
        # balance: new_balance,
        last_transaction_date: NaiveDateTime.utc_now()
      })
      |> Repo.update()

    account
  end

  # Private function to update the balance of an account
  defp do_update_balance(account, new_balance, amount, message, message_text \\ "") do
    # Update the balance and last transaction date of the account
    account =
      account
      |> UserAccounts.update_balance(%{
        # balance: new_balance,
        last_transaction_date: NaiveDateTime.utc_now()
      })
      |> Repo.update()

    {:ok, wallet} = account

    message =
      cond do
        message.completed == false and message.message == "" ->
          message
          |> SMSMessage.new()
          |> SMSMessage.new_message("Dear #{wallet.mobile_number} #{message_text}")

        message.completed == false and message.message != "" ->
          message |> SMSMessage.new_message(message_text)

        true ->
          message
      end

    {:ok, wallet, message}
  end

  # Private function to check if an account has sufficient balance
  defp check_sufficient_balance(account, amount) do
    # Compare the account balance with the amount to check for sufficient funds
    if Decimal.compare(account.balance, Decimal.new(amount)) == :lt do
      # Return an error if the balance is insufficient
      {:error, :insufficient_funds}
    else
      # Return :ok if the balance is sufficient
      :ok
    end
  end

  # Private function to create a transaction
  defp create_transaction(from_account, to_account, amount, description) do
    # Determine the type of transaction based on the presence of from_account and to_account
    transaction_type = determine_transaction_type(from_account, to_account)
    reference = generate_reference()
    now = DateTime.utc_now()

    # Define the attributes for the main transaction
    transaction_attrs = %{
      type: transaction_type,
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_account_id:
        if(transaction_type == "debit", do: nil, else: from_account && from_account.id),
      to_account_id: if(transaction_type == "credit", do: nil, else: to_account && to_account.id),
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Define the attributes for the wallet transaction
    wallet_transaction_attrs = %{
      type: transaction_type,
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_wallet_id:
        if(transaction_type == "credit", do: nil, else: from_account && from_account.id),
      to_wallet_id: if(transaction_type == "debit", do: nil, else: to_account && to_account.id),
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Create both transactions with accounting entries
    Repo.transaction(fn ->
      # Create the main transaction with accounting entries
      with {:ok, %{transaction: transaction}} <-
             AccountingService.create_transaction_with_accounting(transaction_attrs),
           # Create the wallet transaction with accounting entries
           {:ok, %{wallet_transaction: wallet_transaction}} <-
             AccountingService.create_wallet_transaction_with_accounting(wallet_transaction_attrs) do
        {:ok, transaction}
      else
        {:error, reason} -> Repo.rollback(reason)
      end
    end)
  end

  # Private function to create a transaction
  defp create_transaction(from_account, to_account, amount, description, message) do
    # Determine the type of transaction based on the presence of from_account and to_account
    transaction_type = determine_transaction_type(from_account, to_account)
    reference = generate_reference()
    now = DateTime.utc_now()

    # Define the attributes for the main transaction
    transaction_attrs = %{
      type: transaction_type,
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_account_id:
        if(transaction_type == "debit", do: nil, else: from_account && from_account.id),
      to_account_id: if(transaction_type == "credit", do: nil, else: to_account && to_account.id),
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Define the attributes for the wallet transaction
    wallet_transaction_attrs = %{
      type: transaction_type,
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_wallet_id:
        if(transaction_type == "credit", do: nil, else: from_account && from_account.id),
      to_wallet_id: if(transaction_type == "debit", do: nil, else: to_account && to_account.id),
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Create both transactions with accounting entries
    Repo.transaction(fn ->
      # Create the main transaction with accounting entries
      with {:ok, %{transaction: transaction}} <-
             AccountingService.create_transaction_with_accounting(transaction_attrs),
           # Create the wallet transaction with accounting entries
           {:ok, %{wallet_transaction: wallet_transaction}} <-
             AccountingService.create_wallet_transaction_with_accounting(wallet_transaction_attrs) do
        if transaction.status == "completed" do
          message_text =
            "REF: #{transaction.reference} was successful. Your new balance is MWK #{from_account.balance}"

          complete_message =
            message
            |> SMSMessage.new_message(message_text)

          complete_message
          |> SMSMessage.complete_message()
          |> SMSMessage.send_message()

          {:ok, transaction, complete_message}
        else
          {:ok, transaction}
        end
      else
        {:error, reason} -> Repo.rollback(reason)
      end
    end)
  end

  # Private function to create a transaction
  defp create_transaction(from_account, to_account, amount, description, to_message, from_message) do
    # Determine the type of transaction based on the presence of from_account and to_account
    transaction_type = determine_transaction_type(from_account, to_account)
    reference = generate_reference()
    now = DateTime.utc_now()

    # Define the attributes for the main transaction
    transaction_attrs = %{
      type: "direct-transfer",
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_account_id: nil,
      to_account_id: nil,
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Define the attributes for the wallet transaction
    wallet_transaction_attrs = %{
      type: transaction_type,
      amount: amount,
      credit_amount:
        if(transaction_type in ["credit", "transfer"], do: amount, else: Decimal.new(0)),
      debit_amount:
        if(transaction_type in ["debit", "transfer"], do: amount, else: Decimal.new(0)),
      description: description,
      status: "pending",
      reference: reference,
      value_date: DateTime.to_date(now),
      opening_balance: get_opening_balance(from_account || to_account),
      closing_balance: get_closing_balance(to_account || from_account, amount, transaction_type),
      from_wallet_id:
        if(transaction_type == "credit", do: nil, else: from_account && from_account.id),
      to_wallet_id: if(transaction_type == "debit", do: nil, else: to_account && to_account.id),
      sender_account: from_account && from_account.account_number,
      receiver_account: to_account && to_account.account_number
    }

    # Create both transactions with accounting entries
    Repo.transaction(fn ->
      # Create the main transaction with accounting entries
      with {:ok, %{transaction: transaction}} <-
             AccountingService.create_transaction_with_accounting(transaction_attrs),
           # Create the wallet transaction with accounting entries
           {:ok, %{wallet_transaction: wallet_transaction}} <-
             AccountingService.create_wallet_transaction_with_accounting(wallet_transaction_attrs) do
        if transaction.status == "completed" do
          to_message_text =
            "REF: #{transaction.reference} was successful. Your new balance is MWK #{Decimal.add(to_account.balance, amount) |> to_string}"

          from_message_text =
            "REF: #{transaction.reference} was successful. Your new balance is MWK #{Decimal.sub(from_account.balance, amount) |> to_string}"

          to_complete_message =
            to_message
            |> SMSMessage.new_message(to_message_text)

          from_complete_message =
            from_message
            |> SMSMessage.new_message(from_message_text)

          to_complete_message
          |> SMSMessage.complete_message()
          |> SMSMessage.send_message()

          from_complete_message
          |> SMSMessage.complete_message()
          |> SMSMessage.send_message()

          {:ok, transaction, to_complete_message}
        else
          {:ok, transaction}
        end
      else
        {:error, reason} -> Repo.rollback(reason)
      end
    end)
  end

  # Private function to determine the type of transaction
  defp determine_transaction_type(nil, _to_account), do: "credit"
  defp determine_transaction_type(_from_account, nil), do: "debit"
  defp determine_transaction_type(_from_account, _to_account), do: "transfer"

  # Private function to generate a unique account number
  def generate_account_number do
    # Generate a unique account number using timestamp, date and 12 random numbers

    number =
      :rand.uniform(999_999)
      |> Integer.to_string()

    "1850000#{String.slice(number, 0, 6)}"
  end

  def generate_wallet_number do
    # Generate a unique account number using timestamp, date and 12 random numbers

    number =
      :rand.uniform(999_999)
      |> Integer.to_string()

    "2659999#{String.slice(number, 0, 5)}"
  end

  # Private function to generate a unique transaction reference
  defp generate_reference do
    # Generate a unique transaction reference using random bytes
    "TXN" <> (:crypto.strong_rand_bytes(8) |> Base.encode16())
  end

  # Private function to get the opening balance of an account
  defp get_opening_balance(account) do
    # Return the current balance of the account
    account.balance
  end

  # Private function to get the closing balance of an account
  defp get_closing_balance(account, amount, transaction_type) do
    amount = Decimal.new(to_string(amount || 0))

    case transaction_type do
      "credit" -> Decimal.add(account.balance || 0, amount)
      "debit" -> Decimal.sub(account.balance || 0, amount)
      "transfer" -> account.balance
    end
  end

  # Function to update the nickname for an account
  def set_nickname(account_number, new_nickname) do
    # Retrieve the account using its number
    account = get_account_by_number(account_number)

    # If the account is found, proceed to update the nickname
    if account do
      updated_account =
        account
        |> UserAccounts.changeset(%{nickname: new_nickname})
        |> Repo.update()

      {:ok, updated_account} = updated_account
      updated_account
    else
      # If the account is not found, return an error
      nil
    end
  end
end
