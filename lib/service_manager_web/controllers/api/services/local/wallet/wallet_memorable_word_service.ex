defmodule ServiceManagerWeb.Services.Local.WalletMemorableWordService do
  alias ServiceManager.WalletAccounts.WalletUser, as: User
  alias ServiceManager.Repo

  # added memorable word to the wallet
  def set_memorable_word(user_id, memorable_word) do
    user = Repo.get(User, user_id)

    case user do
      nil ->
        {:error, :user_not_found}

      user ->
        user
        |> User.update_wallet(%{memorable_word: memorable_word})
        |> Repo.update()
    end
  end

  def verify_memorable_word(user_id, memorable_word) do
    user = Repo.get(User, user_id)

    case user do
      nil ->
        {:error, :wallet_not_found}

      user ->
        if user.memorable_word == memorable_word do
          {:ok, :verified}
        else
          {:error, :invalid_memorable_word}
        end
    end
  end
end
