defmodule ServiceManagerWeb.Api.Services.Local.CrossTransfersService do
  alias ServiceManager.Repo
  alias ServiceManager.Accounts.FundAccounts, as: Account
  alias ServiceManager.WalletAccounts.WalletUser, as: Wallet
  alias ServiceManager.Transactions.WalletTransactions
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: <PERSON><PERSON>
  alias ServiceManager.Services.AccountValidationService

  def process_transfer(params) do
    IO.inspect(params)
  end

  def test_transfer do
    test_params = %{
      "access_token" => "test_token",
      "from_account" => "ACCTD2F3228C3B91A4D6",
      "to_wallet" => "************",
      "amount" => "10"
    }

    account_to_wallet(test_params)
  end

  def test_bank_transfer do
    test_params = %{
      "access_token" => "test_token",
      "from_account" => "ACCTD2F3228C3B91A4D6",
      "to_account" => "ACCTD2F3228C3B91A4D7",
      "amount" => "10"
    }

    bank_to_bank(test_params)
  end

  def account_to_wallet(params) do
    Logger.info("Starting account to wallet transfer validation")
    Logger.info("Validating source account: #{params["from_account"]}")

    validation_result =
      case Account.find_by(account_number: params["from_account"]) |> Repo.preload(:user) do
        nil ->
          Logger.error("Source account not found: #{params["from_account"]}")
          {:error, "Account not found"}

        account ->
          Logger.info("Validating destination wallet: #{params["to_wallet"]}")

          case Wallet.find_by(mobile_number: params["to_wallet"]) do
            nil ->
              Logger.error("Destination wallet not found: #{params["to_wallet"]}")
              {:error, "Wallet not found"}

            wallet ->
              Logger.info("Validating transfer amount: #{params["amount"]}")

              with {:ok, amount} <- validate_amount(params["amount"], account.balance),
                   :ok <-
                     AccountValidationService.validate_account_thresholds(
                       account,
                       params["amount"],
                       account.last_transaction_date
                     ) do
                Logger.info("Amount and threshold validation successful: #{amount}")
                {:ok, account, wallet, amount}
              else
                {:error, reason} ->
                  Logger.error("Validation failed: #{reason}")
                  {:error, reason}
              end
          end
      end

    case validation_result do
      {:ok, account, wallet, amount} ->
        Logger.info("Starting transaction process for account to wallet transfer")

        Repo.transaction(fn ->
          try do
            Logger.info("Updating account balance")
            account_current_balance = Decimal.new(account.balance)
            account_new_balance = Decimal.sub(account_current_balance, Decimal.new(amount))

            case account
                 |> Account.changeset(%{
                   # balance: account_new_balance,
                   last_transaction_date: NaiveDateTime.utc_now()
                 })
                 |> Repo.update() do
              {:ok, updated_account} ->
                Logger.info(
                  "Account balance updated successfully. Expected New balance (NOT COMMITED): #{account_new_balance}"
                )

                Logger.info("Creating account transaction record")
                common_ref = generate_reference()
                reference = common_ref
                now = DateTime.utc_now()

                account_transaction = %{
                  type: "debit",
                  amount: amount,
                  credit_amount: Decimal.new(0),
                  debit_amount: amount,
                  description:
                    "Transfer from account #{account.account_number} to wallet #{wallet.mobile_number}",
                  status: "pending",
                  reference: reference,
                  value_date: DateTime.to_date(now),
                  opening_balance: account_current_balance,
                  closing_balance: account_new_balance,
                  from_account_id: updated_account.id,
                  to_account_id: nil,
                  from_wallet_id: nil,
                  to_wallet_id: wallet.id,
                  sender_account: account.account_number,
                  receiver_account: wallet.account_number
                }

                case ServiceManager.Contexts.TransactionsContext.create_method(
                       account_transaction
                     ) do
                  {:ok, account_txn} ->
                    Logger.info("Account transaction record created successfully")
                    Logger.info("Updating wallet balance")
                    current_balance = Decimal.new(wallet.balance)
                    new_balance = Decimal.add(current_balance, Decimal.new(amount))

                    case wallet
                         |> Wallet.update_balance(
                           %{
                             # balance: new_balance
                           }
                         )
                         |> Repo.update() do
                      {:ok, updated_wallet} ->
                        Logger.info(
                          "Wallet balance updated successfully.Expected New balance (NOT COMMITTED): #{new_balance}"
                        )

                        Logger.info("Creating wallet transaction record")

                        wallet_transaction = %{
                          type: "credit",
                          amount: amount,
                          credit_amount: amount,
                          debit_amount: Decimal.new(0),
                          description:
                            "Transfer from account #{account.account_number} to wallet #{wallet.mobile_number}",
                          status: "pending",
                          reference: reference,
                          value_date: DateTime.to_date(now),
                          opening_balance: current_balance,
                          closing_balance: new_balance,
                          from_account_id: updated_account.id,
                          to_account_id: nil,
                          from_wallet_id: nil,
                          to_wallet_id: updated_wallet.id,
                          sender_account: account.account_number,
                          receiver_account: wallet.account_number
                        }

                        case WalletTransactions.changeset(
                               %WalletTransactions{},
                               wallet_transaction
                             )
                             |> Repo.insert() do
                          {:ok, wallet_txn} ->
                            Logger.info("Wallet transaction record created successfully")

                            Logger.info(
                              "Transaction records created successfully. Reference: #{reference}"
                            )

                            {:ok,
                             %{
                               account: updated_account,
                               wallet: updated_wallet,
                               amount: amount,
                               account_transaction: account_txn,
                               wallet_transaction: wallet_txn
                             }}

                          {:error, changeset} ->
                            error_msg =
                              "Failed to create wallet transaction record: #{inspect(changeset.errors)}"

                            Logger.error(error_msg)
                            Repo.rollback(error_msg)
                        end

                      {:error, changeset} ->
                        error_msg =
                          "Failed to update wallet balance: #{inspect(changeset.errors)}"

                        Logger.error(error_msg)
                        Repo.rollback(error_msg)
                    end

                  {:error, changeset} ->
                    error_msg =
                      "Failed to create account transaction record: #{inspect(changeset.errors)}"

                    Logger.error(error_msg)
                    Repo.rollback(error_msg)
                end

              {:error, changeset} ->
                error_msg = "Failed to update account balance: #{inspect(changeset.errors)}"
                Logger.error(error_msg)
                Repo.rollback(error_msg)
            end
          rescue
            e ->
              error_msg = "Transaction failed: #{inspect(e)}"
              Logger.error(error_msg)
              Repo.rollback(error_msg)
          end
        end)

      {:error, error} ->
        {:error, error}
    end
  end

  def wallet_to_account(params) do
    Logger.info("Starting wallet to account transfer validation")
    Logger.info("Validating source wallet: #{params["from_wallet"]}")

    validation_result =
      case Wallet.find_by(mobile_number: params["from_wallet"]) do
        nil ->
          Logger.error("Source wallet not found: #{params["from_wallet"]}")
          {:error, "Wallet not found"}

        wallet ->
          Logger.info("Validating destination account: #{params["to_account"]}")

          case Account.find_by(account_number: params["to_account"]) do
            nil ->
              Logger.error("Destination account not found: #{params["to_account"]}")
              {:error, "Account not found"}

            account ->
              Logger.info("Validating transfer amount: #{params["amount"]}")

              case validate_amount(params["amount"], wallet.balance) do
                {:ok, amount} ->
                  Logger.info("Amount validation successful: #{amount}")
                  {:ok, wallet, account, amount}

                {:error, reason} ->
                  Logger.error("Amount validation failed: #{reason}")
                  {:error, reason}
              end
          end
      end

    case validation_result do
      {:ok, wallet, account, amount} ->
        Logger.info("Starting transaction process for wallet to account transfer")

        Repo.transaction(fn ->
          try do
            Logger.info("Updating wallet balance")
            current_balance = Decimal.new(wallet.balance)
            new_balance = Decimal.sub(current_balance, Decimal.new(amount))

            case wallet
                 |> Wallet.update_balance(
                   %{
                     # balance: new_balance
                   }
                 )
                 |> Repo.update() do
              {:ok, updated_wallet} ->
                Logger.info("Wallet balance updated successfully. New balance: #{new_balance}")
                Logger.info("Creating wallet transaction record")
                common_reference = generate_reference()
                reference = common_reference
                now = DateTime.utc_now()

                wallet_transaction = %{
                  type: "debit",
                  amount: amount,
                  credit_amount: Decimal.new(0),
                  debit_amount: amount,
                  description:
                    "Transfer from wallet #{wallet.mobile_number} to account #{account.account_number}",
                  status: "pending",
                  reference: reference,
                  value_date: DateTime.to_date(now),
                  opening_balance: current_balance,
                  closing_balance: new_balance,
                  from_account_id: nil,
                  to_account_id: account.id,
                  from_wallet_id: updated_wallet.id,
                  to_wallet_id: nil,
                  sender_account: wallet.account_number,
                  receiver_account: account.account_number
                }

                case WalletTransactions.changeset(%WalletTransactions{}, wallet_transaction)
                     |> Repo.insert() do
                  {:ok, wallet_txn} ->
                    Logger.info("Wallet transaction record created successfully")
                    Logger.info("Updating account balance")
                    account_current_balance = Decimal.new(account.balance)

                    account_new_balance =
                      Decimal.add(account_current_balance, Decimal.new(amount))

                    case account
                         |> Account.changeset(
                           %{
                             # balance: account_new_balance
                           }
                         )
                         |> Repo.update() do
                      {:ok, updated_account} ->
                        Logger.info(
                          "Account balance updated successfully. New balance: #{account_new_balance}"
                        )

                        Logger.info("Creating account transaction record")

                        account_transaction = %{
                          type: "credit",
                          amount: amount,
                          credit_amount: amount,
                          debit_amount: Decimal.new(0),
                          description:
                            "Transfer from wallet #{wallet.mobile_number} to account #{account.account_number}",
                          status: "pending",
                          reference: reference,
                          value_date: DateTime.to_date(now),
                          opening_balance: account_current_balance,
                          closing_balance: account_new_balance,
                          from_account_id: nil,
                          to_account_id: updated_account.id,
                          from_wallet_id: updated_wallet.id,
                          to_wallet_id: nil,
                          sender_account: wallet.account_number,
                          receiver_account: account.account_number
                        }

                        case ServiceManager.Contexts.TransactionsContext.create_method(
                               account_transaction
                             ) do
                          {:ok, account_txn} ->
                            Logger.info("Account transaction record created successfully")

                            Logger.info(
                              "Transaction records created successfully. Reference: #{reference}"
                            )

                            {:ok,
                             %{
                               account: updated_account,
                               wallet: updated_wallet,
                               amount: amount,
                               account_transaction: account_txn,
                               wallet_transaction: wallet_txn
                             }}

                          {:error, changeset} ->
                            error_msg =
                              "Failed to create account transaction record: #{inspect(changeset.errors)}"

                            Logger.error(error_msg)
                            Repo.rollback(error_msg)
                        end

                      {:error, changeset} ->
                        error_msg =
                          "Failed to update account balance: #{inspect(changeset.errors)}"

                        Logger.error(error_msg)
                        Repo.rollback(error_msg)
                    end

                  {:error, changeset} ->
                    error_msg =
                      "Failed to create wallet transaction record: #{inspect(changeset.errors)}"

                    Logger.error(error_msg)
                    Repo.rollback(error_msg)
                end

              {:error, changeset} ->
                error_msg = "Failed to update wallet balance: #{inspect(changeset.errors)}"
                Logger.error(error_msg)
                Repo.rollback(error_msg)
            end
          rescue
            e ->
              error_msg = "Transaction failed: #{inspect(e)}"
              Logger.error(error_msg)
              Repo.rollback(error_msg)
          end
        end)

      {:error, error} ->
        {:error, error}
    end
  end

  defp validate_amount(amount, wallet_balance) when is_binary(amount) do
    case Decimal.parse(amount) do
      {decimal, ""} ->
        cond do
          Decimal.cmp(decimal, Decimal.new(0)) != :gt ->
            {:error, "Amount must be greater than 0"}

          Decimal.cmp(decimal, Decimal.new(wallet_balance)) == :gt ->
            {:error, "Insufficient wallet balance"}

          true ->
            {:ok, decimal}
        end

      _ ->
        {:error, "Invalid amount format"}
    end
  end

  defp validate_amount(amount, wallet_balance) when is_number(amount) do
    decimal_amount = Decimal.new(amount)

    cond do
      amount <= 0 ->
        {:error, "Amount must be greater than 0"}

      Decimal.cmp(decimal_amount, Decimal.new(wallet_balance)) == :gt ->
        {:error, "Insufficient balance"}

      true ->
        {:ok, decimal_amount}
    end
  end

  defp validate_amount(_, _), do: {:error, "Invalid amount format"}

  defp get_or_create_account(account_number) do
    Logger.info("Looking up account: #{account_number}")

    case Account.find_by(account_number: account_number) |> Repo.preload(:user) do
      nil ->
        Logger.info("Account not found, looking up user by account number")

        case ServiceManager.Accounts.User |> Repo.get_by(account_number: account_number) do
          nil ->
            Logger.error("User not found for account number: #{account_number}")
            {:error, "User not found for account number"}

          user ->
            Logger.info("User found, creating new account")

            account_params = %{
              account_number: account_number,
              user_id: user.id,
              balance: Decimal.new(0),
              last_transaction_date: NaiveDateTime.utc_now()
            }

            case %Account{} |> Account.changeset(account_params) |> Repo.insert() do
              {:ok, account} ->
                Logger.info("Account created successfully")
                {:ok, account |> Repo.preload(:user)}

              {:error, changeset} ->
                Logger.error("Failed to create account: #{inspect(changeset.errors)}")
                {:error, "Failed to create account"}
            end
        end

      account ->
        Logger.info("Account found")
        {:ok, account}
    end
  end

  def bank_to_bank(params) do
    Logger.info("Starting bank to bank transfer validation")
    Logger.info("Validating source account: #{params["from_account"]}")

    validation_result =
      case get_or_create_account(params["from_account"]) do
        {:error, reason} ->
          Logger.error("Error with source account: #{reason}")
          {:error, reason}

        {:ok, source_account} ->
          Logger.info("Validating destination account: #{params["to_account"]}")

          case get_or_create_account(params["to_account"]) do
            {:error, reason} ->
              Logger.error("Error with destination account: #{reason}")
              {:error, reason}

            {:ok, dest_account} ->
              Logger.info("Validating transfer amount: #{params["amount"]}")

              with {:ok, amount} <- validate_amount(params["amount"], source_account.balance),
                   :ok <-
                     AccountValidationService.validate_account_thresholds(
                       source_account,
                       params["amount"],
                       source_account.last_transaction_date
                     ) do
                Logger.info("Amount and threshold validation successful: #{amount}")
                {:ok, source_account, dest_account, amount}
              else
                {:error, reason} ->
                  Logger.error("Validation failed: #{reason}")
                  {:error, reason}
              end
          end
      end

    case validation_result do
      {:ok, source_account, dest_account, amount} ->
        Logger.info("Starting transaction process for bank to bank transfer")

        Repo.transaction(fn ->
          try do
            Logger.info("Updating source account balance")
            source_current_balance = Decimal.new(source_account.balance)
            source_new_balance = Decimal.sub(source_current_balance, Decimal.new(amount))

            case source_account
                 |> Account.changeset(%{
                   balance: source_new_balance,
                   last_transaction_date: NaiveDateTime.utc_now()
                 })
                 |> Repo.update() do
              {:ok, updated_source_account} ->
                Logger.info(
                  "Source account balance updated successfully. New balance: #{source_new_balance}"
                )

                Logger.info("Updating destination account balance")
                dest_current_balance = Decimal.new(dest_account.balance)
                dest_new_balance = Decimal.add(dest_current_balance, Decimal.new(amount))

                case dest_account
                     |> Account.changeset(%{balance: dest_new_balance})
                     |> Repo.update() do
                  {:ok, updated_dest_account} ->
                    Logger.info(
                      "Destination account balance updated successfully. New balance: #{dest_new_balance}"
                    )

                    Logger.info("Creating transaction record")
                    reference = generate_reference()
                    now = DateTime.utc_now()

                    transaction = %{
                      type: "transfer",
                      amount: amount,
                      credit_amount: amount,
                      debit_amount: amount,
                      description:
                        "Transfer from account #{source_account.account_number} to account #{dest_account.account_number}",
                      status: "pending",
                      reference: reference,
                      value_date: DateTime.to_date(now),
                      opening_balance: source_current_balance,
                      closing_balance: source_new_balance,
                      from_account_id: updated_source_account.id,
                      to_account_id: updated_dest_account.id,
                      sender_account: source_account.account_number,
                      receiver_account: dest_account.account_number
                    }

                    Logger.info("Inserting transaction record into database")

                    with {:ok, txn} <-
                           ServiceManager.Contexts.TransactionsContext.create_method(transaction) do
                      Logger.info(
                        "Transaction record created successfully. Reference: #{reference}"
                      )

                      {:ok,
                       %{
                         source_account: updated_source_account,
                         dest_account: updated_dest_account,
                         amount: amount,
                         transaction: txn
                       }}
                    else
                      {:error, changeset} ->
                        error_msg =
                          "Failed to create transaction records: #{inspect(changeset.errors)}"

                        Logger.error(error_msg)
                        Repo.rollback(error_msg)
                    end

                  {:error, changeset} ->
                    error_msg =
                      "Failed to update destination account balance: #{inspect(changeset.errors)}"

                    Logger.error(error_msg)
                    Repo.rollback(error_msg)
                end

              {:error, changeset} ->
                error_msg =
                  "Failed to update source account balance: #{inspect(changeset.errors)}"

                Logger.error(error_msg)
                Repo.rollback(error_msg)
            end
          rescue
            e ->
              error_msg = "Transaction failed: #{inspect(e)}"
              Logger.error(error_msg)
              Repo.rollback(error_msg)
          end
        end)

      {:error, error} ->
        {:error, error}
    end
  end

  defp generate_reference do
    :crypto.strong_rand_bytes(8)
    |> Base.encode16()
    |> String.slice(0, 16)
  end

  def reverse_wallet_to_account_transfer(params) do
    Logger.info(
      "Starting wallet to account reversal validation for transaction #{params["original_transaction_id"]}"
    )

    Logger.info("Validating source account: #{params["to_wallet"]}")

    validation_result =
      case Account.find_by(account_number: params["to_wallet"]) do
        nil ->
          Logger.error("Source account not found: #{params["to_wallet"]}")
          {:error, "Account not found"}

        account ->
          Logger.info("Validating destination wallet: #{params["from_account"]}")

          case Wallet.find_by(mobile_number: params["from_account"]) do
            nil ->
              Logger.error("Destination wallet not found: #{params["from_account"]}")
              {:error, "Wallet not found"}

            wallet ->
              Logger.info("Validating reversal amount: #{params["amount"]}")

              case validate_amount(params["amount"], account.balance) do
                {:ok, amount} ->
                  Logger.info("Amount validation successful: #{amount}")
                  {:ok, account, wallet, amount}

                {:error, reason} ->
                  Logger.error("Amount validation failed: #{reason}")
                  {:error, reason}
              end
          end
      end

    case validation_result do
      {:ok, account, wallet, amount} ->
        Logger.info("Starting transaction process for wallet to account reversal")

        Repo.transaction(fn ->
          try do
            Logger.info("Updating account balance")
            account_current_balance = Decimal.new(account.balance)
            account_new_balance = Decimal.sub(account_current_balance, Decimal.new(amount))

            case account
                 |> Account.changeset(
                   %{
                     # balance: account_new_balance
                   }
                 )
                 |> Repo.update() do
              {:ok, updated_account} ->
                Logger.info(
                  "Account balance updated successfully. New balance: #{account_new_balance}"
                )

                Logger.info("Updating wallet balance")
                wallet_current_balance = Decimal.new(wallet.balance)
                wallet_new_balance = Decimal.add(wallet_current_balance, Decimal.new(amount))

                case wallet
                     |> Wallet.update_balance(
                       %{
                         # balance: wallet_new_balance
                       }
                     )
                     |> Repo.update() do
                  {:ok, updated_wallet} ->
                    Logger.info(
                      "Wallet balance updated successfully.Expected New balance (NOT COMMITTED): #{wallet_new_balance}"
                    )

                    Logger.info("Creating reversal transaction records")
                    reference = generate_reference()
                    now = DateTime.utc_now()

                    account_transaction = %{
                      type: "debit",
                      amount: amount,
                      credit_amount: Decimal.new(0),
                      debit_amount: amount,
                      description: "Reversal: #{params["description"]}",
                      status: "pending",
                      reference: reference,
                      value_date: DateTime.to_date(now),
                      opening_balance: account_current_balance,
                      closing_balance: account_new_balance,
                      from_account_id: updated_account.id,
                      to_account_id: nil,
                      from_wallet_id: nil,
                      to_wallet_id: updated_wallet.id,
                      original_transaction_id: params["original_transaction_id"],
                      is_reversal: true,
                      sender_account: account.account_number,
                      receiver_account: wallet.account_number
                    }

                    wallet_transaction = %{
                      type: "credit",
                      amount: amount,
                      credit_amount: amount,
                      debit_amount: Decimal.new(0),
                      description: "Reversal: #{params["description"]}",
                      status: "pending",
                      reference: reference,
                      value_date: DateTime.to_date(now),
                      opening_balance: wallet_current_balance,
                      closing_balance: wallet_new_balance,
                      from_account_id: updated_account.id,
                      to_account_id: nil,
                      from_wallet_id: nil,
                      to_wallet_id: updated_wallet.id,
                      original_transaction_id: params["original_transaction_id"],
                      is_reversal: true,
                      sender_account: account.account_number,
                      receiver_account: wallet.account_number
                    }

                    Logger.info("Inserting reversal transaction records into database")

                    with {:ok, account_txn} <-
                           ServiceManager.Contexts.TransactionsContext.create_method(
                             account_transaction
                           ),
                         {:ok, wallet_txn} <-
                           WalletTransactions.changeset(%WalletTransactions{}, wallet_transaction)
                           |> Repo.insert() do
                      Logger.info(
                        "Reversal transaction records created successfully. Reference: #{reference}, Original Transaction: #{params["original_transaction_id"]}"
                      )

                      #  {:ok, wallet_txn_copy} <- ServiceManager.Contexts.TransactionsContext.create_method(wallet_transaction) do
                      {:ok,
                       %{
                         account: updated_account,
                         wallet: updated_wallet,
                         amount: amount,
                         account_transaction: account_txn,
                         wallet_transaction: wallet_txn
                       }}
                    else
                      {:error, changeset} ->
                        error_msg =
                          "Failed to create reversal transaction records: #{inspect(changeset.errors)}"

                        Logger.error(error_msg)
                        Repo.rollback(error_msg)
                    end

                  {:error, changeset} ->
                    error_msg = "Failed to update wallet balance: #{inspect(changeset.errors)}"
                    Logger.error(error_msg)
                    Repo.rollback(error_msg)
                end

              {:error, changeset} ->
                error_msg = "Failed to update account balance: #{inspect(changeset.errors)}"
                Logger.error(error_msg)
                Repo.rollback(error_msg)
            end
          rescue
            e ->
              error_msg = "Reversal transaction failed: #{inspect(e)}"
              Logger.error(error_msg)
              Repo.rollback(error_msg)
          end
        end)

      {:error, error} ->
        {:error, error}
    end
  end

  def reverse_account_to_wallet_transfer(params) do
    Logger.info(
      "Starting account to wallet reversal validation for transaction #{params["original_transaction_id"]}"
    )

    Logger.info("Validating source wallet: #{params["from_wallet"]}")

    validation_result =
      case Wallet.find_by(mobile_number: params["from_wallet"]) do
        nil ->
          Logger.error("Source wallet not found: #{params["from_wallet"]}")
          {:error, "Wallet not found"}

        wallet ->
          Logger.info("Validating destination account: #{params["to_account"]}")

          case Account.find_by(account_number: params["to_account"]) do
            nil ->
              Logger.error("Destination account not found: #{params["to_account"]}")
              {:error, "Account not found"}

            account ->
              Logger.info("Validating reversal amount: #{params["amount"]}")

              case validate_amount(params["amount"], wallet.balance) do
                {:ok, amount} ->
                  Logger.info("Amount validation successful: #{amount}")
                  {:ok, wallet, account, amount}

                {:error, reason} ->
                  Logger.error("Amount validation failed: #{reason}")
                  {:error, reason}
              end
          end
      end

    case validation_result do
      {:ok, wallet, account, amount} ->
        Logger.info("Starting transaction process for account to wallet reversal")

        Repo.transaction(fn ->
          try do
            Logger.info("Updating wallet balance")
            current_balance = Decimal.new(wallet.balance)
            new_balance = Decimal.sub(current_balance, Decimal.new(amount))

            case wallet
                 |> Wallet.update_balance(
                   %{
                     # balance: new_balance
                   }
                 )
                 |> Repo.update() do
              {:ok, updated_wallet} ->
                Logger.info("Wallet balance updated successfully. New balance: #{new_balance}")
                Logger.info("Updating account balance")
                account_current_balance = Decimal.new(account.balance)
                account_new_balance = Decimal.add(account_current_balance, Decimal.new(amount))

                case account
                     |> Account.changeset(%{
                       # balance: account_new_balance,
                       last_transaction_date: NaiveDateTime.utc_now()
                     })
                     |> Repo.update() do
                  {:ok, updated_account} ->
                    Logger.info(
                      "Account balance updated successfully. Expected New balance (NOT COMMITED): #{account_new_balance}"
                    )

                    Logger.info("Creating reversal transaction records")
                    reference = generate_reference()
                    now = DateTime.utc_now()

                    wallet_transaction = %{
                      type: "debit",
                      amount: amount,
                      credit_amount: Decimal.new(0),
                      debit_amount: amount,
                      description: "Reversal: #{params["description"]}",
                      status: "pending",
                      reference: reference,
                      value_date: DateTime.to_date(now),
                      opening_balance: current_balance,
                      closing_balance: new_balance,
                      from_account_id: nil,
                      to_account_id: updated_account.id,
                      from_wallet_id: updated_wallet.id,
                      to_wallet_id: nil,
                      original_transaction_id: params["original_transaction_id"],
                      is_reversal: true,
                      sender_account: wallet.account_number,
                      receiver_account: account.account_number
                    }

                    account_transaction = %{
                      type: "credit",
                      amount: amount,
                      credit_amount: amount,
                      debit_amount: Decimal.new(0),
                      description: "Reversal: #{params["description"]}",
                      status: "pending",
                      reference: reference,
                      value_date: DateTime.to_date(now),
                      opening_balance: account_current_balance,
                      closing_balance: account_new_balance,
                      from_account_id: nil,
                      to_account_id: updated_account.id,
                      from_wallet_id: updated_wallet.id,
                      to_wallet_id: nil,
                      original_transaction_id: params["original_transaction_id"],
                      is_reversal: true,
                      sender_account: wallet.account_number,
                      receiver_account: account.account_number
                    }

                    Logger.info("Inserting reversal transaction records into database")

                    with {:ok, wallet_txn} <-
                           WalletTransactions.changeset(%WalletTransactions{}, wallet_transaction)
                           |> Repo.insert(),
                         {:ok, account_txn} <-
                           ServiceManager.Contexts.TransactionsContext.create_method(
                             account_transaction
                           ) do
                      Logger.info(
                        "Reversal transaction records created successfully. Reference: #{reference}, Original Transaction: #{params["original_transaction_id"]}"
                      )

                      {:ok,
                       %{
                         account: updated_account,
                         wallet: updated_wallet,
                         amount: amount,
                         account_transaction: account_txn,
                         wallet_transaction: wallet_txn
                       }}
                    else
                      {:error, changeset} ->
                        error_msg =
                          "Failed to create reversal transaction records: #{inspect(changeset.errors)}"

                        Logger.error(error_msg)
                        Repo.rollback(error_msg)
                    end

                  {:error, changeset} ->
                    error_msg = "Failed to update account balance: #{inspect(changeset.errors)}"
                    Logger.error(error_msg)
                    Repo.rollback(error_msg)
                end

              {:error, changeset} ->
                error_msg = "Failed to update wallet balance: #{inspect(changeset.errors)}"
                Logger.error(error_msg)
                Repo.rollback(error_msg)
            end
          rescue
            e ->
              error_msg = "Reversal transaction failed: #{inspect(e)}"
              Logger.error(error_msg)
              Repo.rollback(error_msg)
          end
        end)

      {:error, error} ->
        {:error, error}
    end
  end
end
