defmodule ServiceManager.MapKeyFinder do
  @doc """
  Recursively searches through a nested structure (map, list of maps) to find a specified key.

  ## Parameters:
  - data: The map, list of maps, or nested structure to search.
  - key: The key to search for.
  - opts: A keyword list of options:
    - `:mode` - :all (default), :first, or {:limit, n} for first n results.

  ## Examples:
      iex> data = %{
      ...>   "body" => [
      ...>     %{
      ...>       "CUSTOMER" => 1617214,
      ...>       "accountDetails" => [
      ...>         %{"accountCurrency" => ["MWK"], "accountID" => ["*************"]}
      ...>       ]
      ...>     }
      ...>   ]
      ...> }
      iex> ServiceManager.MapKeyFinder.find_key(data, "CUSTOMER")
      [1617214]

      iex> ServiceManager.MapKeyFinder.find_key(data, "accountCurrency", mode: :first)
      ["MWK"]

      iex> ServiceManager.MapKeyFinder.find_key(data, "accountCurrency", mode: {:limit, 1})
      ["MWK"]
  """
  def find_key(data, key, opts \\ [mode: :all]) do
    results = do_find_key(data, key)

    case opts[:mode] do
      :all ->
        results

      :first ->
        List.first(results) || nil

      {:limit, n} ->
        limited_results = Enum.take(results, n)
        if length(limited_results) == 1, do: List.first(limited_results), else: limited_results

      _ ->
        results
    end
  end

  defp do_find_key(data, key) when is_map(data) do
    Enum.flat_map(data, fn
      {^key, value} -> [value]
      {_, nested} -> do_find_key(nested, key)
    end)
  end

  defp do_find_key(data, key) when is_list(data) do
    Enum.flat_map(data, &do_find_key(&1, key))
  end

  defp do_find_key(_data, _key), do: []
end
