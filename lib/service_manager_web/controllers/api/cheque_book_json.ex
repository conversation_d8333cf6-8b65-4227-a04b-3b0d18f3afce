defmodule ServiceManagerWeb.ChequeBookJson do
  def cheque_book_request(%{request: request}) do
    %{
      data: request_data(request),
      message: "Cheque book request retrieved successfully",
      status: true
    }
  end

  def cheque_book_requests(%{requests: requests}) do
    %{
      data: Enum.map(requests, &request_data/1),
      message: "Cheque book requests retrieved successfully",
      status: true
    }
  end

  def error(%{changeset: changeset}) do
    errors = Ecto.Changeset.traverse_errors(changeset, fn {msg, _opts} -> msg end)

    %{
      status: false,
      message: "Validation failed",
      errors: errors
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    %{
      status: false,
      message: inspect(error),
      errors: %{error: inspect(error)}
    }
  end

  # Helper function to format cheque book request data
  defp request_data(request) do
    %{
      uid: request.uid,
      account_number: request.account_number,
      branch_code: request.branch_code,
      cheque_leaves: request.number_of_leaves,
      request_status: request.request_status,
      issued_at: request.issued_at,
      fulfilled_at: request.fulfilled_at,
      inserted_at: request.inserted_at,
      updated_at: request.updated_at
    }
  end
end
