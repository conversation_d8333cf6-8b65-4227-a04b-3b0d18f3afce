defmodule ServiceManagerWeb.WalletMemorableWordController do
  use ServiceManagerWeb, :controller

  alias ServiceManagerWeb.Services.Local.WalletMemorableWordService, as: MemorableWordService
  alias ServiceManager.WalletAccounts.WalletUser, as: Account

  # Set memorable word for wallet
  def set_wallet_memorable_word(conn, %{"memorable_word" => memorable_word}) do
    user_id = conn.assigns.user.id

    case MemorableWordService.set_memorable_word(user_id, memorable_word) do
      {:ok, _user} ->
        conn
        |> put_status(:ok)
        |> json(%{
          message: "Wallet memorable word set successfully",
          status: true,
          data: %{
            memorable_word_hint: memorable_word |> String.slice(-3, 3)
          }
        })

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to set memorable word",
          status: false,
          data: %{
            memorable_word_hint: nil
          }
        })
    end
  end

  def wallet_verify_memorable_word(conn, %{
        "memorable_word" => memorable_word,
        "mobile_number" => mobile_number
      }) do
    user = Account.find_by(mobile_number: mobile_number)

    case MemorableWordService.verify_memorable_word(user.id, memorable_word) do
      {:error, :invalid_memorable_word} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{
          message: "Invalid memorable word",
          status: false,
          data: %{}
        })

      {:ok, :verified} ->
        conn
        |> put_status(:ok)
        |> json(%{
          message: "Memorable word verified successfully",
          status: true,
          data: %{}
        })

      {:error, :user_not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          message: "User not found",
          status: false,
          data: %{}
        })
    end
  end
end
