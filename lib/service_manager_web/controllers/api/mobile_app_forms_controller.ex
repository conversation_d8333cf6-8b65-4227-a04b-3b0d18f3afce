defmodule ServiceManagerWeb.Api.MobileAppFormsController do
  use ServiceManagerWeb, :controller
  alias ServiceManagerWeb.Api.Services.Local.MobileAppFormsService
  alias ServiceManagerWeb.Api.MobileAppFormsJSON

  action_fallback ServiceManagerWeb.FallbackController

  # Mobile App Form Retrieval API
  def get_form(conn, %{"form_type" => form_type} = params) do
    # Extract query parameters from request body
    query_params = Map.take(params, ["screen", "page", "version", "type"])
    
    # Use 'type' parameter if provided, otherwise use form_type from URL
    actual_form_type = Map.get(query_params, "type", form_type)
    
    with {:ok, fields} <- MobileAppFormsService.get_form_fields(actual_form_type, query_params),
         {:ok, form_submit_to} <- MobileAppFormsService.get_form_submit_to(actual_form_type, query_params) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.mobile_response(%{fields: fields, form_submit_to: form_submit_to}))
    else
      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  # Field Management APIs

  def list_fields(conn, params) do
    with {:ok, fields} <- MobileAppFormsService.list_fields(params) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.admin_list_response(%{fields: fields}))
    end
  end

  def get_field(conn, %{"field_id" => field_id}) do
    with {:ok, field} <- MobileAppFormsService.get_field(field_id) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.admin_show_response(%{field: field}))
    else
      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  def create_field(conn, params) do
    with {:ok, field} <- MobileAppFormsService.create_field(params) do
      conn
      |> put_status(:created)
      |> json(MobileAppFormsJSON.admin_show_response(%{field: field}))
    else
      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  def update_field(conn, %{"field_id" => field_id} = params) do
    field_params = Map.drop(params, ["field_id"])
    
    with {:ok, field} <- MobileAppFormsService.update_field(field_id, field_params) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.admin_show_response(%{field: field}))
    else
      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  def delete_field(conn, %{"field_id" => field_id}) do
    with {:ok, message} <- MobileAppFormsService.delete_field(field_id) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.success_response(message))
    else
      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  # Hierarchy Management APIs

  def create_form(conn, %{"form_name" => form_name} = params) do
    version = Map.get(params, "version", "1.0")
    
    with {:ok, result} <- MobileAppFormsService.create_form(form_name, version) do
      conn
      |> put_status(:created)
      |> json(MobileAppFormsJSON.hierarchy_response(result))
    else
      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  def create_screen(conn, %{"form_name" => form_name, "screen_name" => screen_name} = params) do
    version = Map.get(params, "version", "1.0")
    
    with {:ok, result} <- MobileAppFormsService.create_screen(form_name, screen_name, version) do
      conn
      |> put_status(:created)
      |> json(MobileAppFormsJSON.hierarchy_response(result))
    else
      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  def create_page(conn, %{"form_name" => form_name, "screen_name" => screen_name, "page_name" => page_name} = params) do
    version = Map.get(params, "version", "1.0")
    
    with {:ok, result} <- MobileAppFormsService.create_page(form_name, screen_name, page_name, version) do
      conn
      |> put_status(:created)
      |> json(MobileAppFormsJSON.hierarchy_response(result))
    else
      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(MobileAppFormsJSON.error_response(message))
    end
  end

  # Listing Hierarchy APIs

  def list_forms(conn, _params) do
    with {:ok, forms} <- MobileAppFormsService.list_forms() do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.forms_list_response(%{forms: forms}))
    end
  end

  def list_screens(conn, %{"form_name" => form_name} = params) do
    version = Map.get(params, "version")
    
    with {:ok, screens} <- MobileAppFormsService.list_screens(form_name, version) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.screens_list_response(%{screens: screens}))
    end
  end

  def list_pages(conn, %{"form_name" => form_name, "screen_name" => screen_name} = params) do
    version = Map.get(params, "version")
    
    with {:ok, pages} <- MobileAppFormsService.list_pages(form_name, screen_name, version) do
      conn
      |> put_status(:ok)
      |> json(MobileAppFormsJSON.pages_list_response(%{pages: pages}))
    end
  end
end