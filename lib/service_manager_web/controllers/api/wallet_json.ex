defmodule ServiceManagerWeb.WalletJSON do
  def show(%{transaction: transaction}) do
    %{
      data: data(transaction),
      message: "Transaction Complete",
      status: true
    }
  end

  def show(%{from: from_account, to: to_account, transaction: transaction}) do
    %{
      data: %{
        from_account: %{
          mobile_number: from_account.mobile_number,
          balance: from_account.balance,
          currency: from_account.currency
        },
        to_account: %{
          mobile_number: to_account.mobile_number,
          balance: to_account.balance,
          currency: to_account.currency
        },
        transaction: transaction_data(transaction)
      },
      message: "Transfer completed successfully",
      status: true
    }
  end

  def account_with_transaction(%{account: account, transaction: transaction}) do
    %{
      data: %{
        account: %{
          mobile_number: account.mobile_number,
          balance: account.balance,
          currency: account.currency
        },
        transaction: transaction_data(transaction)
      },
      message: "Transaction completed successfully",
      status: true
    }
  end

  def data({:ok, result}) do
    data(result)
  end

  # Handle single WalletTransactions struct
  def data(%ServiceManager.Transactions.WalletTransactions{} = transaction) do
    transaction_data(transaction)
  end

  # Handle ServiceManager.Transactions.Transaction struct
  def data(%ServiceManager.Transactions.Transaction{} = transaction) do
    transaction_data(transaction)
  end

  def data(%{
        account: account,
        wallet: wallet,
        amount: amount,
        account_transaction: account_txn,
        wallet_transaction: wallet_txn
      }) do
    %{
      amount: amount,
      account: %{
        account_number: account.account_number,
        balance: account.balance,
        currency: account.currency
      },
      wallet: %{
        mobile_number: wallet.mobile_number,
        balance: wallet.balance,
        currency: wallet.currency
      },
      account_transaction: transaction_data(account_txn),
      wallet_transaction: transaction_data(wallet_txn)
    }
  end

  # Helper function to format transaction data consistently
  defp transaction_data(transaction) do
    %{
      sender_account: transaction.sender_account,
      receiver_account: transaction.receiver_account,
      type: transaction.type,
      amount: transaction.amount,
      description: transaction.description,
      status: transaction.status,
      reference: transaction.reference,
      value_date: transaction.value_date,
      opening_balance: transaction.opening_balance,
      closing_balance: transaction.closing_balance
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    formatted_error = format_error_message(error)

    %{
      status: false,
      message: formatted_error,
      errors: %{error: formatted_error}
    }
  end

  # Helper function to format error messages into clean strings
  defp format_error_message(error) do
    case error do
      # Handle map errors with nested error key
      %{error: inner_error} when is_binary(inner_error) ->
        inner_error

      # Handle map errors with nested error key (recursive)
      %{error: inner_error} ->
        format_error_message(inner_error)

      # Handle timeout errors
      {:error, :timeout} ->
        "Request timeout. Please try again."

      # Handle connection errors
      {:error, :econnrefused} ->
        "Connection refused. Service may be unavailable."

      # Handle other {:error, reason} tuples
      {:error, reason} when is_binary(reason) ->
        reason

      {:error, reason} ->
        format_error_message(reason)

      # Handle atom errors
      error when is_atom(error) ->
        error |> Atom.to_string() |> String.replace("_", " ") |> String.capitalize()

      # Handle binary strings
      error when is_binary(error) ->
        error

      # Fallback to inspect for complex structures
      _ ->
        inspect(error)
    end
  end

  def wallet(%{wallet: wallet}) do
    %{
      data: %{
        mobile_number: wallet.mobile_number,
        balance: wallet.balance,
        currency: wallet.currency,
        status: wallet.status,
        email: wallet.email,
        first_name: wallet.first_name,
        last_name: wallet.last_name,
        id_number: wallet.id_number,
        id_image: wallet.id_image,
        frozen: wallet.frozen,
        locked: wallet.locked,
        blocked: wallet.blocked,
        nickname: wallet.nickname,
        working_balance: wallet.balance,
        cleared_balance: wallet.balance,
        first_time_login: wallet.first_time_login,
        hidden: wallet.hidden,
        large_transaction_alert: wallet.large_transaction_alert,
        large_transaction_threshold: wallet.large_transaction_threshold,
        low_balance_alert: wallet.low_balance_alert,
        low_balance_threshold: wallet.low_balance_threshold,
        suspicous_activity_alert: wallet.suspicous_activity_alert,
        suspicous_activity_seconds_between_transactions:
          wallet.suspicous_activity_seconds_between_transactions,
        bank_code: wallet.bank_code,
        branch_code: wallet.branch_code,
        bank_name: wallet.bank_name,
        enable_alerts: wallet.enable_alerts,
        email_notifications: wallet.email_notifications,
        sms_notifications: wallet.sms_notifications,
        push_notifications: wallet.push_notifications
      },
      message: "Wallet details retrieved successfully",
      status: true
    }
  end

  def transactions(%{transactions: transactions}) do
    %{
      data: %{
        transactions:
          Enum.map(transactions, fn transaction ->
            transaction_data(transaction)
          end)
      },
      message: "Transactions retrieved successfully",
      status: true
    }
  end
end
