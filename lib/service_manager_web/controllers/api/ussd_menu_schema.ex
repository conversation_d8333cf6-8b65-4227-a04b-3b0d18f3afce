defmodule ServiceManagerWeb.Api.UssdMenuSchema do
  @moduledoc """
  Schema for USSD menu management with hierarchical organization.
  
  USSD menus support hierarchical navigation with sub-menus and options.
  - title: Required - the menu title displayed to users
  - menu_order: Optional - order of menus for display purposes
  - version: Required - version control for menu definitions
  - active: Boolean - whether this menu is currently active
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:menu_id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:menu_id, :title, :menu_order, :version, :active]}

  schema "ussd_menus" do
    field :title, :string
    field :menu_order, :integer, default: 0
    field :version, :string
    field :active, :boolean, default: true

    has_many :options, ServiceManagerWeb.Api.UssdOptionSchema, foreign_key: :menu_id, on_delete: :delete_all

    timestamps()
  end

  @doc false
  def changeset(ussd_menu, attrs) do
    ussd_menu
    |> cast(attrs, [:title, :menu_order, :version, :active])
    |> validate_required([:title, :version])
    |> validate_length(:title, min: 1, max: 200)
    |> validate_length(:version, min: 1, max: 20)
    |> validate_number(:menu_order, greater_than_or_equal_to: 0)
    |> unique_constraint([:title, :version], name: :ussd_menus_unique_title_version_constraint)
  end

  @doc """
  Returns a changeset for creating a new USSD menu
  """
  def create_changeset(ussd_menu \\ %__MODULE__{}, attrs) do
    ussd_menu
    |> changeset(attrs)
  end

  @doc """
  Returns a changeset for updating an existing USSD menu
  """
  def update_changeset(ussd_menu, attrs) do
    ussd_menu
    |> changeset(attrs)
  end
end