defmodule ServiceManagerWeb.Api.CardsJSON do
  @doc """
  Renders a card when created or retrieved
  """
  def show(%{card: card}) do
    %{
      data: data(card),
      message: "Card details retrieved successfully",
      status: true
    }
  end

  @doc """
  Renders card validation response
  """
  def validate(%{valid: true}) do
    %{
      data: %{},
      message: "Card details are valid",
      status: true
    }
  end

  def validate(%{valid: false}) do
    %{
      data: %{},
      message: "Invalid card details",
      status: false
    }
  end

  @doc """
  Renders card status response
  """
  def status(%{card: card}) do
    %{
      data: %{
        status: card.status,
        activation_status: card.activation_status,
        is_virtual: card.is_virtual,
        last_used_at: card.last_used_at,
        daily_limit: card.daily_limit,
        monthly_limit: card.monthly_limit
      },
      message: "Card status retrieved successfully",
      status: true
    }
  end

  @doc """
  Renders card linking response
  """
  def link(%{card: card}) do
    %{
      data: data(card),
      message: "Card linked successfully",
      status: true
    }
  end

  @doc """
  Renders card block response
  """
  def block(%{card: card}) do
    %{
      data: data(card),
      message: "Card blocked successfully",
      status: true
    }
  end

  @doc """
  Renders card activation response
  """
  def activate(%{card: card}) do
    %{
      data: data(card),
      message: "Card activated successfully",
      status: true
    }
  end

  @doc """
  Renders error responses
  """
  def error(%{error: :account_not_found}) do
    %{
      status: false,
      message: "Account not found",
      errors: %{error: "account_not_found"}
    }
  end

  def error(%{error: :wallet_user_not_found}) do
    %{
      status: false,
      message: "Wallet user not found",
      errors: %{error: "wallet_user_not_found"}
    }
  end

  def error(%{error: :invalid_api_key}) do
    %{
      status: false,
      message: "Invalid API key",
      errors: %{error: "invalid_api_key"}
    }
  end

  def error(%{error: :invalid_params}) do
    %{
      status: false,
      message:
        "Invalid parameters. Must provide either account_number, mobile_number, or api_key",
      errors: %{error: "invalid_params"}
    }
  end

  def error(%{error: :card_already_activated}) do
    %{
      status: false,
      message: "Card is already activated",
      errors: %{error: "card_already_activated"}
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    %{
      status: false,
      message: inspect(error),
      errors: %{error: inspect(error)}
    }
  end

  # Private functions

  defp data(%ServiceManager.Schemas.Accounts.SchemaCard{} = card) do
    %{
      card_number: card.card_number,
      card_type: card.card_type,
      expiry_date: card.expiry_date,
      status: card.status,
      currency: card.currency,
      daily_limit: card.daily_limit,
      monthly_limit: card.monthly_limit,
      activation_status: card.activation_status,
      is_virtual: card.is_virtual,
      last_used_at: card.last_used_at
    }
  end

  defp data({:ok, card}), do: data(card)
end
