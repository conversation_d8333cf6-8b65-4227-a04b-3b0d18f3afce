defmodule ServiceManagerWeb.Api.BillersJSON do
  @moduledoc """
  JSON responses for Billers API
  """

  @doc """
  Renders account details response
  """
  def account_details(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        account_number: transaction.account_number,
        account_type: transaction.account_type,
        status: transaction.status,
        response_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Account details retrieved successfully"
    }
  end

  @doc """
  Renders payment success response
  """
  def payment_success(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        account_number: transaction.account_number,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        customer_account_name: transaction.customer_account_name,
        response_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Payment processed successfully"
    }
  end

  @doc """
  Renders invoice details response
  """
  def invoice_details(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        account_number: transaction.account_number,
        status: transaction.status,
        invoice_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Invoice details retrieved successfully"
    }
  end

  @doc """
  Renders invoice confirmation response
  """
  def invoice_confirmation(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        account_number: transaction.account_number,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        customer_account_name: transaction.customer_account_name,
        confirmation_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Invoice payment confirmed successfully"
    }
  end

  @doc """
  Renders bundle details response
  """
  def bundle_details(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        bundle_id: transaction.bundle_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        status: transaction.status,
        bundle_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Bundle details retrieved successfully"
    }
  end

  @doc """
  Renders bundle confirmation response
  """
  def bundle_confirmation(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        bundle_id: transaction.bundle_id,
        phone_number: transaction.account_number,
        amount: transaction.amount,
        currency: transaction.currency,
        status: transaction.status,
        confirmation_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Bundle purchase confirmed successfully"
    }
  end

  @doc """
  Renders validation result response
  """
  def validation_result(%{transaction: transaction}) do
    %{
      data: %{
        transaction_id: transaction.our_transaction_id,
        account_number: transaction.account_number,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        status: transaction.status,
        validation_data: transaction.response_payload,
        processed_at: transaction.processed_at
      },
      status: true,
      message: "Account validation completed successfully"
    }
  end

  @doc """
  Renders transaction details response
  """
  def transaction_details(%{transaction: transaction}) do
    %{
      data: %{
        id: transaction.id,
        transaction_id: transaction.our_transaction_id,
        biller_type: transaction.biller_type,
        biller_name: transaction.biller_name,
        account_number: transaction.account_number,
        account_type: transaction.account_type,
        amount: transaction.amount,
        currency: transaction.currency,
        transaction_type: transaction.transaction_type,
        status: transaction.status,
        credit_account: transaction.credit_account,
        debit_account: transaction.debit_account,
        customer_account_number: transaction.customer_account_number,
        customer_account_name: transaction.customer_account_name,
        bundle_id: transaction.bundle_id,
        api_endpoint: transaction.api_endpoint,
        request_payload: transaction.request_payload,
        response_payload: transaction.response_payload,
        error_message: transaction.error_message,
        processed_at: transaction.processed_at,
        inserted_at: transaction.inserted_at,
        updated_at: transaction.updated_at
      },
      status: true,
      message: "Transaction details retrieved successfully"
    }
  end

  @doc """
  Renders transactions list response
  """
  def transactions_list(%{transactions: transactions}) do
    %{
      data: Enum.map(transactions, fn transaction ->
        %{
          id: transaction.id,
          transaction_id: transaction.our_transaction_id,
          biller_type: transaction.biller_type,
          biller_name: transaction.biller_name,
          account_number: transaction.account_number,
          amount: transaction.amount,
          currency: transaction.currency,
          transaction_type: transaction.transaction_type,
          status: transaction.status,
          customer_account_name: transaction.customer_account_name,
          bundle_id: transaction.bundle_id,
          error_message: transaction.error_message,
          processed_at: transaction.processed_at,
          inserted_at: transaction.inserted_at
        }
      end),
      status: true,
      message: "Transactions retrieved successfully",
      count: length(transactions)
    }
  end

  @doc """
  Renders retry success response
  """
  def retry_success(%{response: response}) do
    %{
      data: %{
        result: response
      },
      status: true,
      message: "Transaction retry initiated successfully"
    }
  end

  @doc """
  Renders available biller types
  """
  def biller_types(%{biller_types: biller_types}) do
    %{
      data: biller_types,
      status: true,
      message: "Biller types retrieved successfully"
    }
  end

  @doc """
  Renders error response
  """
  def error(%{error: error}) when is_binary(error) do
    %{
      error: %{
        message: error
      },
      status: false,
      message: "Request failed"
    }
  end

  def error(%{changeset: changeset}) do
    %{
      error: %{
        message: "Validation failed",
        details: translate_errors(changeset)
      },
      status: false,
      message: "Request failed"
    }
  end

  def error(%{error: error}) do
    %{
      error: %{
        message: inspect(error)
      },
      status: false,
      message: "Request failed"
    }
  end

  # Private helper to translate changeset errors
  defp translate_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end
end