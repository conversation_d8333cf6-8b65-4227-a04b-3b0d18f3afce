defmodule ServiceManagerWeb.Api.NotificationSettingsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Accounts.User
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Endpoint

  action_fallback ServiceManagerWeb.FallbackController

  def show(conn, _params) do
    user = conn.assigns.user
    render(conn, :show, user: user)
  end

  def update(conn, %{"settings" => settings}) do
    user = conn.assigns.user

    changeset =
      User.notification_settings_changeset(user, %{
        "email_notifications" => settings["email_notifications"],
        "sms_notifications" => settings["sms_notifications"],
        "push_notifications" => settings["push_notifications"]
      })

    case Repo.update(changeset) do
      {:ok, updated_user} ->
        render(conn, :show, user: updated_user)

      {:error, _changeset} ->
        conn
        |> put_status(:bad_request)
        |> render(:error, error: "Failed to update notification settings")
    end
  end

  def update(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> render(:error, error: "Invalid request parameters")
  end

  def send_notification(conn, %{"type" => type, "content" => content, "title" => title} = params) do
    user = conn.assigns.user
    timestamp = DateTime.utc_now()

    # Split type by | and validate each type
    notification_types = String.split(type, "|")
    valid_types = ["sms", "push", "email"]

    if Enum.all?(notification_types, &(&1 in valid_types)) do
      # Filter enabled notification types for the user
      enabled_notifications =
        notification_types
        |> Enum.filter(fn type ->
          case type do
            "email" -> user.email_notifications
            "sms" -> user.sms_notifications
            "push" -> user.push_notifications
          end
        end)

      if Enum.empty?(enabled_notifications) do
        conn
        |> put_status(:bad_request)
        |> render(:error, error: "All specified notification types are disabled for this user")
      else
        # Create and broadcast a notification for each enabled type
        notifications =
          Enum.map(enabled_notifications, fn notification_type ->
            notification = %{
              title: title,
              type: notification_type,
              content: content,
              timestamp: timestamp
            }

            case notification_type do
              "email" ->
                []

              # ServiceManager.Notifications.EmailNotification.deliver(user, content)
              "sms" ->
                ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
                  msisdn: user.phone_number,
                  message: content
                })

              "push" ->
                # Broadcast to the channel for push notifications
                Endpoint.broadcast(
                  "notifications:#{user.session_id}",
                  "notification",
                  Map.put(notification, "delivery_type", "push")
                )
            end

            notification
          end)

        conn
        |> put_status(:ok)
        |> render(:send, notifications: notifications)
      end
    else
      conn
      |> put_status(:bad_request)
      |> render(:error, error: "Invalid notification type. Must be one of: sms, push, email")
    end
  end

  def send_notification(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> render(:error, error: "Invalid notification parameters")
  end
end
