defmodule ServiceManagerWeb.Api.WalletNotificationSettingsJSON do
  def show(%{user: user}) do
    %{
      data: %{
        email_notifications: user.email_notifications,
        sms_notifications: user.sms_notifications,
        push_notifications: user.push_notifications,
        limits: %{
          daily_limit: user.daily_notification_limit,
          weekly_limit: user.weekly_notification_limit,
          monthly_limit: user.monthly_notification_limit,
          current_count: user.notification_count,
          last_reset: user.last_notification_reset
        }
      },
      status: true,
      message: "Wallet notification settings retrieved successfully"
    }
  end

  def send(%{notifications: notifications}) do
    %{
      data: %{
        notifications:
          Enum.map(notifications, fn notification ->
            %{
              type: notification.type,
              content: notification.content,
              timestamp: notification.timestamp
            }
          end)
      },
      status: true,
      message: "Notifications sent successfully"
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    message = inspect(error)

    %{
      status: false,
      message: message,
      errors: %{error: message}
    }
  end
end
