defmodule ServiceManagerWeb.CallbackSchemaController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Validators.CallbackSchemaValidator

  @doc """
  Lists available fields for mapping schemas.
  """
  def list_fields(conn, %{"api_key" => _api_key, "type" => type}) do
    case CallbackSchemaValidator.list_available_fields(type) do
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: reason})

      fields ->
        json(conn, %{
          data: %{
            type: type,
            available_fields: fields,
            example_schema: generate_example_schema(type)
          }
        })
    end
  end

  @doc """
  Validates a mapping schema.
  """
  def validate_schema(conn, %{"api_key" => _api_key, "schema" => schema, "type" => type}) do
    case CallbackSchemaValidator.validate_schema(schema, type) do
      {:ok, validated_schema} ->
        json(conn, %{data: validated_schema})

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: reason})
    end
  end

  @doc """
  Tests a schema mapping with a sample payload.
  """
  def test_mapping(conn, %{
        "api_key" => _api_key,
        "schema" => schema,
        "type" => type,
        "payload" => payload
      }) do
    case CallbackSchemaValidator.validate_and_map(payload, schema, type) do
      {:ok, mapped_payload} ->
        json(conn, %{data: mapped_payload})

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: reason})
    end
  end

  # Private Functions

  defp generate_example_schema("transaction") do
    %{
      "reference" => "transaction_id",
      "transaction.credit_account" => "credit_account",
      "transaction.debit_account" => "debit_account",
      "transaction.description" => "narration",
      "transaction.type" => "transaction_type",
      "transaction.transaction_details.merchant_id" => "merchant_id",
      "transaction.transaction_details.customer_name" => "customer_name",
      "transaction.transaction_details.payment_method" => "payment_method",
      "transaction_status" => "status"
    }
  end

  defp generate_example_schema("wallet") do
    %{
      "reference" => "transaction_id",
      "transaction_status" => "status",
      "transaction.credit_account" => "destination_wallet",
      "transaction.debit_account" => "source_wallet",
      "transaction.description" => "description",
      "transaction.type" => "transaction_type",
      "transaction.transaction_details.merchant_id" => "merchant_id",
      "transaction.transaction_details.customer_name" => "customer_name",
      "transaction.transaction_details.payment_method" => "payment_method"
    }
  end

  defp generate_example_schema(_), do: %{}
end
