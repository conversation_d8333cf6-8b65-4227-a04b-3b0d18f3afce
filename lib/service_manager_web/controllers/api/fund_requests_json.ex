defmodule ServiceManagerWeb.API.FundRequestsJSON do
  @doc """
  Renders fund request data.
  """
  def create(%{fund_request: fund_request}) do
    %{
      data: %{
        id: fund_request.id,
        amount: fund_request.amount,
        status: fund_request.status,
        transaction_reference: fund_request.transaction_reference,
        description: fund_request.description,
        receiver_account: fund_request.receiver_account,
        sender_account: fund_request.sender_account,
        inserted_at: fund_request.inserted_at,
        updated_at: fund_request.updated_at
      },
      status: true,
      message: "Fund request created successfully"
    }
  end

  def index(%{fund_requests: fund_requests}) do
    %{
      data: %{
        requests:
          Enum.map(fund_requests, fn request ->
            %{
              id: request.id,
              amount: request.amount,
              status: request.status,
              transaction_reference: request.transaction_reference,
              description: request.description,
              receiver_account: request.receiver_account,
              sender_account: request.sender_account,
              inserted_at: request.inserted_at,
              updated_at: request.updated_at
            }
          end)
      },
      status: true,
      message: "Fund requests retrieved successfully"
    }
  end

  def approve(%{fund_request: fund_request}) do
    %{
      data: %{
        id: fund_request.id,
        status: fund_request.status,
        transaction_reference: fund_request.transaction_reference
      },
      status: true,
      message: "Fund request approved successfully"
    }
  end

  def reject(%{fund_request: fund_request}) do
    %{
      data: %{
        id: fund_request.id,
        status: fund_request.status,
        transaction_reference: fund_request.transaction_reference
      },
      status: true,
      message: "Fund request rejected successfully"
    }
  end

  def error(%{error: error}) do
    %{
      error: %{
        message: error
      },
      status: false,
      message: "Fund request operation failed"
    }
  end
end
