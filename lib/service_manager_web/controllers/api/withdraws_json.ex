defmodule ServiceManagerWeb.Api.WithdrawsJSON do
  def create(%{withdraw: withdraw}) do
    %{
      data: %{
        reference_number: withdraw.reference_number,
        amount: withdraw.amount,
        status: withdraw.status,
        beneficiary_name: withdraw.beneficiary_name,
        beneficiary_phone: withdraw.beneficiary_phone
      },
      message: "Cardless withdraw created successfully",
      status: true
    }
  end

  def verify(%{withdraw: withdraw}) do
    %{
      data: %{
        reference_number: withdraw.reference_number,
        amount: withdraw.amount,
        status: withdraw.status,
        beneficiary_name: withdraw.beneficiary_name,
        beneficiary_phone: withdraw.beneficiary_phone,
        source_account: withdraw.source_account,
        source_wallet: withdraw.source_wallet
      },
      message: "Withdrawal completed successfully",
      status: true
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    %{
      status: false,
      message: inspect(error),
      errors: %{error: inspect(error)}
    }
  end
end
