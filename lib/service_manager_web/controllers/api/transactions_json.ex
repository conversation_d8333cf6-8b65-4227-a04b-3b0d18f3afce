defmodule ServiceManagerWeb.TransactionsJSON do
  def transactions_report(%{transactions: transactions}) do
    %{
      data: Enum.map(transactions, &transaction_data/1),
      message: "Transactions retrieved successfully",
      status: true
    }
  end

  def wallet_transactions_report(%{transactions: transactions}) do
    %{
      data: Enum.map(transactions, &wallet_transaction_data/1),
      message: "Wallet transactions retrieved successfully",
      status: true
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: error,
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    %{
      status: false,
      message: inspect(error),
      errors: %{error: inspect(error)}
    }
  end

  # Helper function to format regular transaction data
  defp transaction_data(transaction) do
    %{
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      credit_amount: transaction.credit_amount,
      debit_amount: transaction.debit_amount,
      description: transaction.description,
      status: transaction.status,
      reference: transaction.reference,
      value_date: transaction.value_date,
      opening_balance: transaction.opening_balance,
      closing_balance: transaction.closing_balance,
      transaction_details: transaction.transaction_details,
      callback_status: transaction.callback_status,
      cbs_transaction_reference: transaction.cbs_transaction_reference,
      external_reference: transaction.external_reference,
      from_account: render_account(transaction.from_account),
      to_account: render_account(transaction.to_account),
      inserted_at: transaction.inserted_at,
      updated_at: transaction.updated_at
    }
  end

  # Helper function to format wallet transaction data
  defp wallet_transaction_data(transaction) do
    %{
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      credit_amount: transaction.credit_amount,
      debit_amount: transaction.debit_amount,
      description: transaction.description,
      status: transaction.status,
      reference: transaction.reference,
      value_date: transaction.value_date,
      opening_balance: transaction.opening_balance,
      closing_balance: transaction.closing_balance,
      callback_status: transaction.callback_status,
      cbs_transaction_reference: transaction.cbs_transaction_reference,
      external_reference: transaction.external_reference,
      from_account: render_wallet_account(transaction.from_account),
      to_account: render_wallet_account(transaction.to_account),
      inserted_at: transaction.inserted_at,
      updated_at: transaction.updated_at
    }
  end

  defp render_account(nil), do: nil

  defp render_account(account) do
    %{
      id: account.id,
      account_number: account.account_number,
      email: account.email,
      account_type: account.account_type,
      balance: account.balance,
      currency: account.currency,
      status: account.status
    }
  end

  defp render_wallet_account(nil), do: nil

  defp render_wallet_account(account) do
    %{
      id: account.id,
      mobile_number: account.mobile_number,
      first_name: account.first_name,
      last_name: account.last_name,
      balance: account.balance
    }
  end
end
