defmodule ServiceManagerWeb.Api.MobileAppFormDefSchema do
  @moduledoc """
  Form-level definition for mobile app forms, primarily to store submit URL
  at the form scope rather than per field.

  Keyed by (form, screen, page, version) to stay compatible with legacy
  hierarchy during transition.
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :form, :screen, :page, :version, :submit_to, :active]}

  schema "mobile_app_form_defs" do
    field :form, :string
    field :screen, :string
    field :page, :string
    field :version, :string
    field :submit_to, :string
    field :active, :boolean, default: true

    timestamps()
  end

  @doc false
  def changeset(form_def, attrs) do
    form_def
    |> cast(attrs, [:form, :screen, :page, :version, :submit_to, :active])
    |> validate_required([:form, :version])
    |> validate_length(:form, min: 1, max: 100)
    |> validate_length(:screen, max: 100)
    |> validate_length(:page, max: 100)
    |> validate_length(:version, min: 1, max: 20)
    |> validate_length(:submit_to, max: 500)
    |> unique_constraint([:form, :screen, :page, :version], name: :mobile_app_form_defs_unique_form_scope)
  end
end

