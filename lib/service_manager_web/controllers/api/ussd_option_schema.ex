defmodule ServiceManagerWeb.Api.UssdOptionSchema do
  @moduledoc """
  Schema for USSD menu options with action definitions.
  
  USSD options belong to menus and define user interaction choices.
  - text: Required - the option text displayed to users
  - action: Required - action type (submenu, function, end)
  - target_menu_id: Optional - target menu for submenu navigation
  - option_order: Optional - order of options within a menu
  - active: Boolean - whether this option is currently active
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:option_id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:option_id, :menu_id, :text, :action, :target_menu_id, :form_id, :form_name, :option_order, :active]}

  schema "ussd_options" do
    field :text, :string
    field :action, :string
    field :option_order, :integer, default: 0
    field :active, :boolean, default: true
    field :form_id, Ecto.UUID
    field :form_name, :string

    belongs_to :menu, ServiceManagerWeb.Api.UssdMenuSchema, foreign_key: :menu_id, type: Ecto.UUID
    belongs_to :target_menu, ServiceManagerWeb.Api.UssdMenuSchema, foreign_key: :target_menu_id, type: Ecto.UUID

    timestamps()
  end

  @doc false
  def changeset(ussd_option, attrs) do
    ussd_option
    |> cast(attrs, [:text, :action, :option_order, :active, :menu_id, :target_menu_id, :form_id, :form_name])
    |> validate_required([:text, :action, :menu_id])
    |> validate_length(:text, min: 1, max: 200)
    |> validate_length(:form_name, max: 100)
    |> validate_inclusion(:action, ["submenu", "function", "end", "none", "form"])
    |> validate_number(:option_order, greater_than_or_equal_to: 0)
    |> validate_target_menu_for_submenu()
    |> validate_form_for_form_action()
    |> foreign_key_constraint(:menu_id)
    |> foreign_key_constraint(:target_menu_id)
    |> unique_constraint([:menu_id, :option_order], name: :ussd_options_unique_menu_order_constraint)
  end

  @doc """
  Returns a changeset for creating a new USSD option
  """
  def create_changeset(ussd_option \\ %__MODULE__{}, attrs) do
    ussd_option
    |> changeset(attrs)
  end

  @doc """
  Returns a changeset for updating an existing USSD option
  """
  def update_changeset(ussd_option, attrs) do
    ussd_option
    |> changeset(attrs)
  end

  # Custom validation to ensure submenu actions have target_menu_id
  defp validate_target_menu_for_submenu(changeset) do
    action = get_field(changeset, :action)
    target_menu_id = get_field(changeset, :target_menu_id)

    case {action, target_menu_id} do
      {"submenu", nil} -> add_error(changeset, :target_menu_id, "is required when action is submenu")
      {"submenu", ""} -> add_error(changeset, :target_menu_id, "is required when action is submenu")
      _ -> changeset
    end
  end

  # Custom validation to ensure form actions have form_name
  defp validate_form_for_form_action(changeset) do
    action = get_field(changeset, :action)
    form_name = get_field(changeset, :form_name)

    case {action, form_name} do
      {"form", nil} -> add_error(changeset, :form_name, "is required when action is form")
      {"form", ""} -> add_error(changeset, :form_name, "is required when action is form")
      _ -> changeset
    end
  end

  @doc """
  Returns list of valid action types
  """
  def valid_actions do
    ["submenu", "function", "end", "none", "form"]
  end
end