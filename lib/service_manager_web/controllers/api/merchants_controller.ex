defmodule ServiceManagerWeb.MerchantsController do
  use ServiceManagerWeb, :controller
  alias ServiceManager.Contexts.MerchantsContext

  def index(conn, _params) do
    merchants = MerchantsContext.list_merchants()
    json(conn, %{data: merchants})
  end

  def register(conn, params) do
    with {:ok, validated_params} <- validate_registration_params(params),
         {:ok, merchant} <- MerchantsContext.create_merchant(validated_params) do
      conn
      |> put_status(:created)
      |> json(%{
        status: "success",
        data: %{
          merchant_id: merchant.merchant_code,
          name: merchant.name,
          email: merchant.email,
          address: merchant.address,
          phone_number: merchant.phone_number,
          is_active: merchant.is_active,
          balance: merchant.balance
        }
      })
    else
      {:error, :invalid_params, errors} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid parameters", details: errors})

      {:error, changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error: "Failed to register merchant",
          details: error_details(changeset)
        })
    end
  end

  def pay(conn, params) do
    user = conn.assigns.user

    with {:ok, validated_params} <- validate_payment_params(params),
         {:ok, %{transaction: transaction, user: updated_user, merchant: merchant}} <-
           MerchantsContext.process_payment(user, validated_params) |> IO.inspect() do
      json(conn, %{
        status: "success",
        data: %{
          transaction_id: transaction.transaction_id,
          merchant_id: merchant.merchant_code,
          amount: transaction.amount,
          currency: transaction.currency,
          status: transaction.status,
          user_balance: updated_user.account_balance |> Decimal.from_float() |> Decimal.round(2),
          merchant_balance: merchant.balance |> to_string()
        }
      })
    else
      {:error, :merchant_not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "Merchant not found"})

      {:error, :user_not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "User not found"})

      {:error, :invalid_amount} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid transaction amount"})

      {:error, :insufficient_funds} ->
        conn
        |> put_status(:payment_required)
        |> json(%{error: "Insufficient funds"})

      {:error, :invalid_params, errors} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid parameters", details: errors})

      {:error, {failed_operation, failed_value}} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          error: "Failed to process payment",
          operation: failed_operation,
          details: error_details(failed_value)
        })
    end
  end

  defp validate_registration_params(params) do
    required_fields = ~w(name email)
    optional_fields = ~w(address phone_number is_active)

    with :ok <- validate_required_fields(params, required_fields),
         :ok <- validate_email(params["email"]) do
      {:ok, params}
    end
  end

  defp validate_payment_params(params) do
    required_fields = ~w(merchant_id merchant_name transaction_id transaction_amount currency)
    optional_fields = ~w(qr_code_data payment_description callback_url)

    with :ok <- validate_required_fields(params, required_fields),
         :ok <- validate_amount(params["transaction_amount"]) do
      {:ok, params}
    end
  end

  defp validate_required_fields(params, required_fields) do
    missing_fields =
      Enum.filter(
        required_fields,
        &(not Map.has_key?(params, &1) or is_nil(params[&1]) or params[&1] == "")
      )

    case missing_fields do
      [] -> :ok
      fields -> {:error, :invalid_params, %{missing_fields: fields}}
    end
  end

  defp validate_email(email) when is_binary(email) do
    case Regex.match?(~r/^[^\s]+@[^\s]+$/, email) do
      true -> :ok
      false -> {:error, :invalid_params, %{email: "must be a valid email address"}}
    end
  end

  defp validate_email(_), do: {:error, :invalid_params, %{email: "must be a valid email address"}}

  defp validate_amount(amount) when is_binary(amount) do
    case Decimal.parse(amount) do
      {_decimal, ""} -> :ok
      _ -> {:error, :invalid_params, %{amount: "must be a valid number"}}
    end
  end

  defp validate_amount(amount) when is_number(amount), do: :ok
  defp validate_amount(_), do: {:error, :invalid_params, %{amount: "must be a valid number"}}

  defp error_details(%Ecto.Changeset{} = changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end

  defp error_details(error), do: %{message: inspect(error)}
end
