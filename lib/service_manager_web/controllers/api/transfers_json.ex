defmodule ServiceManagerWeb.TransfersJSON do
  require Logger

  alias ServiceManager.Transactions.Transaction

  def index(%{transactions: transactions}) do
    %{data: for(transaction <- transactions, do: data(transaction))}
  end

  def show(%{transaction: %{transaction: transaction}}) do
    Logger.info(
      "[#{DateTime.utc_now() |> DateTime.to_string()}] * Rendering transaction: #{inspect(transaction)}"
    )

    %{
      data: %{
        amount: transaction.amount,
        status: transaction.status,
        value_date: transaction.value_date,
        inserted_at: transaction.inserted_at,
        type: transaction.type,
        credit_amount: transaction.credit_amount,
        debit_amount: transaction.debit_amount,
        reference: transaction.reference
      },
      status: true,
      message: "Transaction processed successfully"
    }
  end

  def show(%{transaction: {:ok, transaction}}) do
    %{
      data: data(transaction),
      status: true,
      message: "Transaction processed successfully"
    }
  end

  def show(%{transaction: transaction}) do
    %{
      data: data(transaction),
      status: true,
      message: "Transaction processed successfully"
    }
  end

  def data(%Transaction{} = transaction) do
    %{
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      status: transaction.status,
      description: transaction.description,
      value_date: transaction.value_date,
      credit_amount: transaction.credit_amount,
      debit_amount: transaction.debit_amount,
      reference: transaction.reference,
      opening_balance: transaction.opening_balance,
      closing_balance: transaction.closing_balance
    }
  end

  def data(%{
        account: account,
        wallet: wallet,
        amount: amount,
        account_transaction: account_txn,
        wallet_transaction: wallet_txn
      }) do
    %{
      amount: amount,
      account: %{
        account_number: account.account_number,
        balance: account.balance,
        currency: account.currency
      },
      wallet: %{
        mobile_number: wallet.mobile_number,
        balance: wallet.balance,
        currency: wallet.currency
      },
      account_transaction: %{
        type: account_txn.type,
        amount: account_txn.amount,
        description: account_txn.description,
        status: account_txn.status,
        reference: account_txn.reference,
        value_date: account_txn.value_date,
        opening_balance: account_txn.opening_balance,
        closing_balance: account_txn.closing_balance
      },
      wallet_transaction: %{
        type: wallet_txn.type,
        amount: wallet_txn.amount,
        description: wallet_txn.description,
        status: wallet_txn.status,
        reference: wallet_txn.reference,
        value_date: wallet_txn.value_date,
        opening_balance: wallet_txn.opening_balance,
        closing_balance: wallet_txn.closing_balance
      }
    }
  end

  def data(%{
        source_account: source_account,
        dest_account: dest_account,
        amount: amount,
        transaction: transaction
      }) do
    %{
      amount: amount,
      source_account: %{
        account_number: source_account.account_number,
        balance: source_account.balance,
        currency: source_account.currency
      },
      destination_account: %{
        account_number: dest_account.account_number,
        balance: dest_account.balance,
        currency: dest_account.currency
      },
      transaction: %{
        type: transaction.type,
        amount: transaction.amount,
        description: transaction.description,
        status: transaction.status,
        reference: transaction.reference,
        value_date: transaction.value_date,
        opening_balance: transaction.opening_balance,
        closing_balance: transaction.closing_balance,
        credit_amount: transaction.credit_amount,
        debit_amount: transaction.debit_amount
      }
    }
  end

  def error(%{error: {:error, message}}) when is_binary(message) do
    %{
      status: false,
      message: "Transaction Failed.",
      errors: %{error: message}
    }
  end

  def error(%{error: error}) when is_binary(error) do
    %{
      status: false,
      message: "Transaction Failed.",
      errors: %{error: error}
    }
  end

  def error(%{error: error}) do
    message = format_error_message(error)

    %{
      status: false,
      message: "Transaction Failed.",
      errors: %{error: message}
    }
  end

  # Helper function to format error messages into clean strings
  defp format_error_message(error) do
    case error do
      # Handle map errors with nested error key
      %{error: inner_error} when is_binary(inner_error) ->
        inner_error

      # Handle map errors with nested error key (recursive)
      %{error: inner_error} ->
        format_error_message(inner_error)

      # Handle TLS/SSL errors
      {:tls_alert, details} ->
        "SSL/TLS Certificate Error: #{inspect(details)}"

      {:error, {:tls_alert, details}} ->
        "SSL/TLS Certificate Error: #{inspect(details)}"

      # Handle timeout errors
      {:error, :timeout} ->
        "Request timeout. Please try again."

      # Handle connection errors
      {:error, :econnrefused} ->
        "Connection refused. Service may be unavailable."

      # Handle other {:error, reason} tuples
      {:error, reason} when is_binary(reason) ->
        reason

      {:error, reason} ->
        format_error_message(reason)

      # Handle atom errors
      error when is_atom(error) ->
        error |> Atom.to_string() |> String.replace("_", " ") |> String.capitalize()

      # Handle binary strings
      error when is_binary(error) ->
        error

      # Fallback to inspect for complex structures
      _ ->
        inspect(error)
    end
  end
end
