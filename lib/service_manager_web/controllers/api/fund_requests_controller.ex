defmodule ServiceManagerWeb.API.FundRequestsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Contexts.FundRequestsContext
  alias ServiceManager.Accounts
  alias ServiceManagerWeb.API.FundRequestsJSON

  def create(conn, %{"amount" => amount, "receiver_account" => receiver_account} = params) do
    user = conn.assigns.user

    attrs = %{
      amount: amount,
      receiver_account: receiver_account,
      sender_account: user.account_number,
      sender_id: user.id,
      description: Map.get(params, "description")
    }

    case FundRequestsContext.create_fund_request(attrs) do
      {:ok, request} ->
        conn
        |> put_status(:created)
        |> json(FundRequestsJSON.create(%{fund_request: request}))

      {:error, %Ecto.Changeset{} = changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(FundRequestsJSON.error(%{error: format_changeset_errors(changeset)}))

      {:error, error_message} when is_binary(error_message) ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(FundRequestsJSON.error(%{error: error_message}))
    end
  end

  def index(conn, _params) do
    user = conn.assigns.user
    requests = FundRequestsContext.list_user_requests(user.id)

    json(conn, FundRequestsJSON.index(%{fund_requests: requests}))
  end

  def pending_requests(conn, _params) do
    user = conn.assigns.user
    requests = FundRequestsContext.list_pending_requests(user.account_number)

    json(conn, FundRequestsJSON.index(%{fund_requests: requests}))
  end

  def approve(conn, %{"reference" => reference}) do
    user = conn.assigns.user

    with %{status: "pending"} = request <-
           FundRequestsContext.get_fund_request_by_reference(reference),
         {:ok, updated_request} <- FundRequestsContext.approve_request(request, user.id) do
      json(conn, FundRequestsJSON.approve(%{fund_request: updated_request}))
    else
      nil ->
        conn
        |> put_status(:not_found)
        |> json(FundRequestsJSON.error(%{error: "Fund request not found"}))

      %{status: status} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(
          FundRequestsJSON.error(%{error: "Request cannot be approved. Current status: #{status}"})
        )

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(FundRequestsJSON.error(%{error: "Failed to approve request: #{inspect(reason)}"}))
    end
  end

  def reject(conn, %{"reference" => reference}) do
    user = conn.assigns.user

    with %{status: "pending", receiver_account: account} = request <-
           FundRequestsContext.get_fund_request_by_reference(reference),
         true <- account == user.account_number,
         {:ok, updated_request} <- FundRequestsContext.reject_request(request) do
      json(conn, FundRequestsJSON.reject(%{fund_request: updated_request}))
    else
      nil ->
        conn
        |> put_status(:not_found)
        |> json(FundRequestsJSON.error(%{error: "Fund request not found"}))

      false ->
        conn
        |> put_status(:forbidden)
        |> json(FundRequestsJSON.error(%{error: "Not authorized to reject this request"}))

      %{status: status} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(
          FundRequestsJSON.error(%{error: "Request cannot be rejected. Current status: #{status}"})
        )

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(FundRequestsJSON.error(%{error: "Failed to reject request: #{inspect(reason)}"}))
    end
  end

  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
    |> Enum.map_join(", ", fn {key, value} -> "#{key} #{value}" end)
  end
end
