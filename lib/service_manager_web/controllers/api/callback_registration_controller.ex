defmodule ServiceManagerWeb.CallbackRegistrationController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Repo
  alias ServiceManager.Schemas.CallbackRegistry
  alias ServiceManager.Schemas.Callback
  alias ServiceManager.Validators.CallbackSchemaValidator

  import Ecto.Query, warn: false

  action_fallback ServiceManagerWeb.FallbackController

  @doc """
  Register a new callback URL with optional validation scheme
  """
  def register(conn, %{"validation_scheme" => scheme} = params) do
    # Validate schema before registration
    case CallbackSchemaValidator.validate_schema(scheme, params["callback_type"]) do
      {:ok, _validated_schema} ->
        with {:ok, callback_registry} <- create_callback_registry(params) do
          conn
          |> put_status(:created)
          |> json(%{data: format_callback_registry(callback_registry)})
        end

      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid validation scheme: #{reason}"})
    end
  end

  def register(conn, params) do
    # Registration without schema
    with {:ok, callback_registry} <- create_callback_registry(params) do
      conn
      |> put_status(:created)
      |> json(%{data: format_callback_registry(callback_registry)})
    end
  end

  @doc """
  List all registered callbacks
  """
  def list(conn, _params) do
    callbacks = Repo.all(CallbackRegistry)
    json(conn, %{data: Enum.map(callbacks, &format_callback_registry/1)})
  end

  @doc """
  Get a specific callback registration
  """
  def show(conn, %{"api_key" => api_key, "id" => id}) do
    case Repo.get(CallbackRegistry, id) do
      nil ->
        {:error, :not_found}

      callback_registry ->
        json(conn, %{data: format_callback_registry(callback_registry)})
    end
  end

  @doc """
  Update a callback registration with optional validation scheme update
  """
  def update(conn, %{"api_key" => api_key, "id" => id, "validation_scheme" => scheme} = params) do
    with {:ok, callback_registry} <- get_callback_registry(id),
         {:ok, _} <-
           CallbackSchemaValidator.validate_schema(scheme, callback_registry.callback_type),
         {:ok, updated_registry} <- update_callback_registry(callback_registry, params) do
      json(conn, %{data: format_callback_registry(updated_registry)})
    else
      {:error, :invalid_schema, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "Invalid validation scheme: #{reason}"})

      error ->
        error
    end
  end

  def update(conn, %{"api_key" => api_key, "id" => id} = params) do
    with {:ok, callback_registry} <- get_callback_registry(id),
         {:ok, updated_registry} <- update_callback_registry(callback_registry, params) do
      json(conn, %{data: format_callback_registry(updated_registry)})
    end
  end

  @doc """
  Delete a callback registration
  """
  def delete(conn, %{"api_key" => api_key, "id" => id}) do
    with {:ok, callback_registry} <- get_callback_registry(id),
         {:ok, _deleted} <- Repo.delete(callback_registry) do
      send_resp(conn, :no_content, "")
    end
  end

  @doc """
  Get callback status and statistics
  """
  def status(conn, %{"api_key" => api_key, "id" => id}) do
    with {:ok, callback_registry} <- get_callback_registry(id) do
      stats = get_callback_stats(callback_registry)
      json(conn, %{data: format_stats(stats)})
    end
  end

  @doc """
  Get detailed callback history/report
  """
  def report(conn, params) do
    query = build_report_query(params)
    callbacks = Repo.all(query)
    json(conn, %{data: Enum.map(callbacks, &format_callback/1)})
  end

  # Formatting functions

  defp format_callback_registry(registry) do
    %{
      id: registry.id,
      callback_url: registry.callback_url,
      callback_type: registry.callback_type,
      status: registry.status,
      api_key: registry.api_key,
      headers: registry.headers,
      validation_scheme: registry.validation_scheme,
      inserted_at: registry.inserted_at,
      updated_at: registry.updated_at
    }
  end

  defp format_stats(stats) do
    %{
      total_callbacks: stats.total,
      successful_callbacks: stats.success,
      failed_callbacks: stats.failed,
      pending_callbacks: stats.pending,
      average_duration_ms: stats.avg_duration
    }
  end

  defp format_callback(callback) do
    %{
      id: callback.id,
      callback_type: callback.callback_type,
      callback_url: callback.callback_url,
      request_headers: callback.request_headers,
      request_body: callback.request_body,
      response_status: callback.response_status,
      response_headers: callback.response_headers,
      response_body: callback.response_body,
      duration_ms: callback.duration_ms,
      status: callback.status,
      error_message: callback.error_message,
      retry_count: callback.retry_count,
      inserted_at: callback.inserted_at
    }
  end

  # Private functions

  defp create_callback_registry(params) do
    %CallbackRegistry{}
    |> CallbackRegistry.changeset(params)
    |> Repo.insert()
  end

  defp get_callback_registry(id) do
    case Repo.get(CallbackRegistry, id) do
      nil -> {:error, :not_found}
      registry -> {:ok, registry}
    end
  end

  defp update_callback_registry(registry, params) do
    registry
    |> CallbackRegistry.changeset(params)
    |> Repo.update()
  end

  defp get_callback_stats(registry) do
    import Ecto.Query

    callbacks_query =
      from c in Callback,
        where: c.callback_registry_id == ^registry.id,
        select: %{
          total: count(c.id),
          success: count(fragment("case when ? = 'success' then 1 else null end", c.status)),
          failed: count(fragment("case when ? = 'failed' then 1 else null end", c.status)),
          pending: count(fragment("case when ? = 'pending' then 1 else null end", c.status)),
          avg_duration: avg(c.duration_ms)
        }

    Repo.one(callbacks_query)
  end

  defp build_report_query(params) do
    import Ecto.Query

    base_query =
      from c in Callback,
        order_by: [desc: c.inserted_at]

    base_query
    |> maybe_filter_by_status(params["status"])
    |> maybe_filter_by_date_range(params["from"], params["to"])
    |> maybe_filter_by_registry(params["registry_id"])
    |> maybe_limit(params["limit"])
  end

  defp maybe_filter_by_status(query, status) when is_nil(status), do: query

  defp maybe_filter_by_status(query, status) do
    from(c in query, where: c.status == ^status)
  end

  defp maybe_filter_by_date_range(query, nil, nil), do: query

  defp maybe_filter_by_date_range(query, from, to) when not is_nil(from) and not is_nil(to) do
    from c in query,
      where: c.inserted_at >= ^from and c.inserted_at <= ^to
  end

  defp maybe_filter_by_registry(query, nil), do: query

  defp maybe_filter_by_registry(query, registry_id) do
    from c in query, where: c.callback_registry_id == ^registry_id
  end

  defp maybe_limit(query, nil), do: query

  defp maybe_limit(query, limit) do
    from c in query, limit: ^limit
  end
end
