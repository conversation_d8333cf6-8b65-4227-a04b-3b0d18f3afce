defmodule ServiceManagerWeb.Api.FormFieldV2Schema do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :form_id, :field_name, :field_type, :label, :is_required, :field_order, :active, :options]}

  schema "mobile_form_fields_v2" do
    field :field_name, :string
    field :field_type, :string
    field :label, :string
    field :is_required, :boolean, default: false
    field :field_order, :integer, default: 0
    field :active, :boolean, default: true
    field :options, :string

    belongs_to :form, ServiceManagerWeb.Api.FormV2Schema, type: Ecto.UUID

    timestamps()
  end

  @valid_field_types ["string", "number", "integer", "boolean", "date", "datetime", "email", "password", "phone", "select", "multiselect", "textarea"]

  def changeset(field, attrs) do
    field
    |> cast(attrs, [:form_id, :field_name, :field_type, :label, :is_required, :field_order, :active, :options])
    |> validate_required([:form_id, :field_name, :field_type, :label])
    |> validate_inclusion(:field_type, @valid_field_types)
    |> validate_length(:field_name, min: 1, max: 100)
    |> validate_length(:label, min: 1, max: 200)
    |> validate_number(:field_order, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:form_id)
    |> unique_constraint([:form_id, :field_name], name: :mobile_form_fields_v2_unique_form_field)
  end
end

