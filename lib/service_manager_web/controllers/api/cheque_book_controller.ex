defmodule ServiceManagerWeb.ChequeBookController do
  use ServiceManagerWeb, :controller
  alias ServiceManagerWeb.Controllers.Api.Services.Local.ChequeBookService
  alias ServiceManagerWeb.ChequeBookJson

  def create(conn, params) do
    params = Map.put_new(params, "user_id", conn.assigns.user.id)

    case ChequeBookService.create_request(params) do
      {:ok, request} ->
        conn
        |> put_status(:created)
        |> json(ChequeBookJson.cheque_book_request(%{request: request}))

      {:error, %Ecto.Changeset{} = changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(ChequeBookJson.error(%{changeset: changeset}))

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(ChequeBookJson.error(%{error: message}))
    end
  end

  def list(conn, params) do
    requests = ChequeBookService.list_requests(params)
    json(conn, ChequeBookJson.cheque_book_requests(%{requests: requests}))
  end

  def detail(conn, %{"cheque_id" => uid}) do
    case ChequeBookService.get_request(uid) do
      {:ok, request} ->
        json(conn, ChequeBookJson.cheque_book_request(%{request: request}))

      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: message})
    end
  end

  def update(conn, %{"cheque_id" => uid} = params) do
    case ChequeBookService.update_request(uid, params) do
      {:ok, request} ->
        json(conn, %{
          data: %{
            uid: request.uid,
            request_status: request.request_status
          },
          message: "Request updated successfully",
          status: true
        })

      {:error, %Ecto.Changeset{} = changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(ChequeBookJson.error(%{changeset: changeset}))

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(ChequeBookJson.error(%{error: message}))
    end
  end

  def delete(conn, %{"cheque_id" => uid}) do
    case ChequeBookService.delete_request(uid) do
      {:ok, message} ->
        json(conn, %{
          data: nil,
          message: message,
          status: true
        })

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(ChequeBookJson.error(%{error: message}))
    end
  end
end
