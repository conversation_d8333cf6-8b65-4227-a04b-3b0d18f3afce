defmodule ServiceManagerWeb.Api.ChequeRequestController do
  use ServiceManagerWeb, :controller
  alias ServiceManager.Schemas.Accounts.Cheques.ChequeRequest
  alias ServiceManager.Repo

  def create(conn, %{"action" => action, "cheque_number" => cheque_number})
      when action in ["stop", "cancel"] do
    user = conn.assigns.user

    attrs = %{
      action: action,
      cheque_number: cheque_number,
      account_number: user.account_number,
      user_id: user.id,
      status: "pending"
    }

    case create_cheque_request(attrs) do
      {:ok, cheque_request} ->
        conn
        |> put_status(:created)
        |> json(%{
          status: true,
          message: "Cheque #{action} request created successfully",
          data: %{
            id: cheque_request.id,
            action: cheque_request.action,
            cheque_number: cheque_request.cheque_number,
            status: cheque_request.status
          }
        })

      {:error, changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          data: %{},
          status: false,
          message: "Could not submit your cancellation request at this time.",
          errors: format_changeset_errors(changeset)
        })
    end
  end

  def create(conn, %{"action" => action}) when action not in ["stop", "cancel"] do
    conn
    |> put_status(:bad_request)
    |> json(%{
      data: %{},
      status: false,
      message: "Invalid action. Must be either 'stop' or 'cancel'"
    })
  end

  def create(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{
      data: %{},
      status: false,
      message: "Missing required parameters: action and cheque_number"
    })
  end

  defp create_cheque_request(attrs) do
    %ChequeRequest{}
    |> ChequeRequest.changeset(attrs)
    |> Repo.insert()
  end

  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end
end
