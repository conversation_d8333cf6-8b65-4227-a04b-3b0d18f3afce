defmodule ServiceManagerWeb.Api.PageSchema do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :screen_id, :name, :order, :active]}

  schema "mobile_pages" do
    field :name, :string
    field :order, :integer, default: 0
    field :active, :boolean, default: true

    belongs_to :screen, ServiceManagerWeb.Api.ScreenSchema, type: Ecto.UUID
    has_many :forms, ServiceManagerWeb.Api.FormV2Schema, foreign_key: :page_id

    timestamps()
  end

  def changeset(page, attrs) do
    page
    |> cast(attrs, [:name, :order, :active, :screen_id])
    |> validate_required([:name, :screen_id])
    |> validate_length(:name, min: 1, max: 100)
    |> validate_number(:order, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:screen_id)
    |> unique_constraint([:screen_id, :name], name: :mobile_pages_unique_screen_name)
  end
end

