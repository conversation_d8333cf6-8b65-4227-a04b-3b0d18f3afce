defmodule ServiceManagerWeb.Api.CardsController do
  use ServiceManagerWeb, :controller
  alias ServiceManager.Schemas.Accounts.SchemaCard
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Api.CardsJSON

  action_fallback ServiceManagerWeb.FallbackController

  # Card Creation
  def create(conn, params) do
    with {:ok, card} <- create_card(params) do
      conn
      |> put_status(:created)
      |> json(CardsJSON.show(%{card: card}))
    end
  end

  # Card Validation Service
  def validate(conn, %{"card_number" => card_number} = params) do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, _} <- validate_card_details(card, params) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.validate(%{valid: true}))
    else
      {:error, _reason} ->
        conn
        |> put_status(:ok)
        |> json(CardsJSON.validate(%{valid: false}))
    end
  end

  # Card Status Service
  def status(conn, %{"card_number" => card_number}) do
    with {:ok, card} <- get_card_by_number(card_number) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.status(%{card: card}))
    end
  end

  # Card Account Linking Service
  def link_account(conn, %{"card_number" => card_number} = params) do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, updated_card} <- link_card_to_account(card, params) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.link(%{card: updated_card}))
    end
  end

  # Update Card Limits
  def update_limits(conn, %{
        "card_number" => card_number,
        "daily_limit" => daily_limit,
        "monthly_limit" => monthly_limit
      }) do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, updated_card} <-
           update_card_limits(card, %{daily_limit: daily_limit, monthly_limit: monthly_limit}) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.show(%{card: updated_card}))
    end
  end

  # Change Card Status
  def change_status(conn, %{"card_number" => card_number, "status" => status})
      when status in ["active", "blocked", "expired"] do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, updated_card} <- update_card_status(card, status) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.show(%{card: updated_card}))
    end
  end

  def change_status(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(CardsJSON.error(%{error: "Invalid status. Must be one of: active, blocked, expired"}))
  end

  # Block Card
  def block(conn, %{"card_number" => card_number}) do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, updated_card} <- update_card_status(card, "blocked") do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.block(%{card: updated_card}))
    end
  end

  # Activate Card
  def activate(conn, %{"card_number" => card_number, "pin" => pin}) do
    with {:ok, card} <- get_card_by_number(card_number),
         {:ok, updated_card} <- activate_card(card, pin) do
      conn
      |> put_status(:ok)
      |> json(CardsJSON.activate(%{card: updated_card}))
    else
      {:error, :card_already_activated} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(CardsJSON.error(%{error: :card_already_activated}))

      error ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(CardsJSON.error(%{error: error}))
    end
  end

  # Private Functions

  defp create_card(params) do
    %SchemaCard{}
    |> SchemaCard.changeset(params)
    |> Repo.insert()
  end

  defp get_card_by_number(card_number) do
    case Repo.get_by(SchemaCard, card_number: card_number) do
      nil -> {:error, :card_not_found}
      card -> {:ok, card}
    end
  end

  defp validate_card_details(card, %{"cvv" => cvv, "expiry_date" => expiry_date}) do
    cond do
      card.status != "active" ->
        {:error, :card_inactive}

      card.cvv != cvv ->
        {:error, :invalid_cvv}

      Date.compare(card.expiry_date, Date.from_iso8601!(expiry_date)) != :eq ->
        {:error, :invalid_expiry_date}

      true ->
        {:ok, card}
    end
  end

  defp update_card_status(card, status) do
    card
    |> SchemaCard.update_changeset(%{status: status})
    |> Repo.update()
  end

  defp activate_card(card, pin) do
    if card.activation_status == "pending" do
      card
      |> SchemaCard.validate_pin_changeset(%{
        pin_hash: Bcrypt.hash_pwd_salt(pin),
        activation_status: "activated"
      })
      |> Repo.update()
    else
      {:error, :card_already_activated}
    end
  end

  defp update_card_limits(
         card,
         %{daily_limit: daily_limit, monthly_limit: monthly_limit} = params
       ) do
    card
    |> SchemaCard.update_changeset(params)
    |> Repo.update()
  end

  defp link_card_to_account(card, %{"account_number" => account_number} = _params) do
    case ServiceManager.Accounts.user_by_account_number(account_number) do
      nil ->
        {:error, :account_not_found}

      user ->
        card
        |> Ecto.Changeset.change(%{user_id: user.id})
        |> Repo.update()
    end
  end

  defp link_card_to_account(card, %{"mobile_number" => mobile_number} = _params) do
    try do
      wallet_user =
        ServiceManager.WalletAccounts.get_wallet_user_by_mobile_number(mobile_number)
        |> IO.inspect()

      card
      |> Ecto.Changeset.change(%{wallet_user_id: wallet_user.id})
      |> Repo.update()
    rescue
      Ecto.NoResultsError -> {:error, :wallet_user_not_found}
    end
  end

  defp link_card_to_account(card, %{"api_key" => api_key} = _params) do
    alias ServiceManager.ThirdParty.ThirdPartyApiKey

    case ThirdPartyApiKey.validate_api_key(api_key) do
      {:ok, api_key_record} ->
        card
        |> Ecto.Changeset.change(%{third_party_api_key_id: api_key_record.id})
        |> Repo.update()

      {:error, _} ->
        {:error, :invalid_api_key}
    end
  end

  defp link_card_to_account(_card, _params) do
    {:error, :invalid_params}
  end
end
