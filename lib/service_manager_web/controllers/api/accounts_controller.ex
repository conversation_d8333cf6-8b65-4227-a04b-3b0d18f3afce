defmodule ServiceManagerWeb.AccountsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Service.TransactionService, as: AccountTransactionService
  alias ServiceManagerWeb.Api.Services.Remote.AccountsFromRemoteService, as: RemoteAccounts
  alias ServiceManager.Accounts.FundAccounts, as: Account

  def index(conn, _params) do
    # The home page is often custom made,
    # so skip the default app layout.
    json(conn, %{})
  end

  def create(conn, %{
        "initial_balance" => initial_balance,
        "currency" => currency,
        "account_type" => account_type
      }) do
    user = conn.assigns.user

    account_details = %{
      email: user.email,
      name: user.last_name <> " " <> user.first_name,
      initial_balance: initial_balance,
      currency: currency,
      account_type: account_type,
      account_number: ""
    }

    res = AccountTransactionService.create_account(account_details)

    res =
      case res do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Account created",
            "account" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to create account",
            "account" => %{}
          }
      end

    json(conn, res)
  end

  def new_wallet(conn, params) do
    case params |> Map.put("email", conn.assigns.user.email) |> RemoteAccounts.create_wallet() do
      {:ok, res} ->
        res = %{
          "data" => %{
            "wallet_account" => res
          },
          "status" => true,
          "message" => "Wallet created successfully"
        }

        json(conn, res)

      {:error, reason} ->
        res = %{
          "data" => %{
            "errors" => reason
          },
          "status" => false,
          "message" => "Wallet creation failed"
        }

        json(conn, res)
    end
  end

  def freeze_account(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"])
      |> Account.update(is_frozen: true, frozen: true)
      |> case do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Account frozen",
            "data" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to freeze account",
            "data" => %{}
          }
      end

    json(conn, res)
  end

  def unfreeze_account(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"])
      |> Account.update(is_frozen: false, frozen: false)
      |> case do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Account unfrozen",
            "data" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to unfreeze account",
            "data" => %{}
          }
      end

    json(conn, res)
  end

  def hide_account(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"])
      |> Account.update(is_hidden: true, hidden: true)
      |> case do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Account hidden",
            "data" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to hide account",
            "data" => %{}
          }
      end

    json(conn, res)
  end

  def unhide_account(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"])
      |> Account.update(is_hidden: false, hidden: false)
      |> case do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Account unhidden",
            "data" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to unhide account",
            "data" => %{}
          }
      end

    json(conn, res)
  end

  def set_account_nickname(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"], user_id: conn.assigns.user.id)
      |> Account.update(nickname: params["nickname"] || "")
      |> case do
        {:ok, account} ->
          %{
            "status" => true,
            "message" => "Nickname updated",
            "data" => account
          }

        {:error} ->
          %{
            "status" => false,
            "message" => "Failed to update nickname",
            "data" => %{}
          }
      end

    json(conn, res)
  end

  # def get_account_by_account_number(conn, params) do
  #   Account.find_by([account_number: params["account_number"]])
  #   |> case do
  #     account -> json(conn, account)
  #     {:error, _error} -> json(conn, %{"message" => "account not found"})
  #   end
  # end

  def get_account_by_account_number(conn, params) do
    res =
      Account.find_by(account_number: params["account_number"])
      |> case do
        account ->
          %{
            "status" => true,
            "message" => "Account found",
            "account" => account
          }

        {:error, _error} ->
          %{
            "status" => false,
            "message" => "Account not found",
            "account" => %{}
          }
      end

    json(conn, res)
  end

  def list_accounts(conn, _params) do
    user_id = conn.assigns.user.id

    res =
      case Account.where(user_id: user_id) do
        account ->
          %{
            "status" => true,
            "message" => "Accounts list",
            "accounts" => account
          }

        {:error, _error} ->
          %{
            "status" => false,
            "message" => "Accounts list not found",
            "account" => %{}
          }
      end

    json(conn, res)
  end
end
