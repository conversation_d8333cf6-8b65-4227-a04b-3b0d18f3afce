defmodule ServiceManagerWeb.Api.BillersController do
  use ServiceManagerWeb, :controller
  
  alias ServiceManager.Billers.BillersService

  action_fallback ServiceManagerWeb.FallbackController

  @doc """
  Get account details for any biller type
  """
  def account_details(conn, %{"biller_type" => biller_type, "account_number" => account_number} = params) do
    opts = []
    opts = if params["account_type"], do: [account_type: params["account_type"]], else: opts
    
    case BillersService.process_account_details(biller_type, account_number, opts) do
      {:ok, %{transaction: transaction}} ->
        conn
        |> put_status(:ok)
        |> render(:account_details, transaction: transaction)
      
      {:error, %{transaction: changeset}} ->
        conn
        |> put_status(:bad_request)
        |> render(:error, changeset: changeset)
        
      {:error, error} ->
        conn
        |> put_status(:internal_server_error)
        |> render(:error, error: error)
    end
  end

  @doc """
  Process payment for any biller type
  """
  def process_payment(conn, %{"biller_type" => biller_type} = params) do
    payment_attrs = %{
      account_number: params["account_number"],
      amount: params["amount"],
      currency: params["currency"] || "MWK",
      credit_account: params["credit_account"],
      credit_account_type: params["credit_account_type"],
      debit_account: params["debit_account"],
      debit_account_type: params["debit_account_type"],
      customer_account_number: params["customer_account_number"],
      customer_account_name: params["customer_account_name"],
      account_type: params["account_type"] # For MASM
    }
    
    with {:ok, %{transaction: transaction}} <- BillersService.process_payment(biller_type, payment_attrs) do
      conn
      |> put_status(:ok)
      |> render(:payment_success, transaction: transaction)
    end
  end

  @doc """
  Get invoice for Register General or SRWB Prepaid
  """
  def get_invoice(conn, %{"biller_type" => biller_type, "account_number" => account_number}) do
    with {:ok, %{transaction: transaction}} <- BillersService.process_invoice_request(biller_type, account_number) do
      conn
      |> put_status(:ok)
      |> render(:invoice_details, transaction: transaction)
    end
  end

  @doc """
  Confirm invoice payment for Register General or SRWB Prepaid
  """
  def confirm_invoice(conn, %{"biller_type" => biller_type} = params) do
    confirmation_attrs = %{
      account_number: params["account_number"],
      amount: params["amount"],
      currency: params["currency"] || "MWK",
      credit_account: params["credit_account"],
      credit_account_type: params["credit_account_type"],
      debit_account: params["debit_account"],
      debit_account_type: params["debit_account_type"],
      customer_account_number: params["customer_account_number"],
      customer_account_name: params["customer_account_name"]
    }
    
    with {:ok, %{transaction: transaction}} <- BillersService.process_invoice_confirmation(biller_type, confirmation_attrs) do
      conn
      |> put_status(:ok)
      |> render(:invoice_confirmation, transaction: transaction)
    end
  end

  @doc """
  Get bundle details for TNM
  """
  def bundle_details(conn, %{"bundle_id" => bundle_id}) do
    with {:ok, %{transaction: transaction}} <- BillersService.process_bundle_details(bundle_id) do
      conn
      |> put_status(:ok)
      |> render(:bundle_details, transaction: transaction)
    end
  end

  @doc """
  Confirm bundle purchase for TNM
  """
  def confirm_bundle(conn, %{"bundle_id" => bundle_id} = params) do
    bundle_attrs = %{
      bundle_id: bundle_id,
      account_number: params["phone_number"], # phone number for TNM
      amount: params["amount"],
      currency: params["currency"] || "MWK"
    }
    
    with {:ok, %{transaction: transaction}} <- BillersService.process_bundle_confirmation(bundle_attrs) do
      conn
      |> put_status(:ok)
      |> render(:bundle_confirmation, transaction: transaction)
    end
  end

  @doc """
  Validate Airtel Money account
  """
  def validate_account(conn, %{"account_number" => account_number}) do
    with {:ok, %{transaction: transaction}} <- BillersService.process_validation(account_number) do
      conn
      |> put_status(:ok)
      |> render(:validation_result, transaction: transaction)
    end
  end

  @doc """
  Get transaction by ID
  """
  def get_transaction(conn, %{"id" => id}) do
    case BillersService.get_transaction(id) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Transaction not found")
      
      transaction ->
        conn
        |> put_status(:ok)
        |> render(:transaction_details, transaction: transaction)
    end
  end

  @doc """
  Get transaction by reference
  """
  def get_transaction_by_reference(conn, %{"reference" => reference}) do
    case BillersService.get_transaction_by_reference(reference) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Transaction not found")
      
      transaction ->
        conn
        |> put_status(:ok)
        |> render(:transaction_details, transaction: transaction)
    end
  end

  @doc """
  List transactions with filters
  """
  def list_transactions(conn, params) do
    opts = [
      limit: params["limit"] || 50,
      offset: params["offset"] || 0
    ]
    
    transactions = cond do
      params["biller_type"] ->
        BillersService.get_transactions_by_biller(params["biller_type"], opts)
      
      params["status"] ->
        BillersService.get_transactions_by_status(params["status"], opts)
      
      params["account_number"] ->
        BillersService.get_transactions_by_account(params["account_number"], opts)
      
      true ->
        BillersService.get_all_transactions(opts)
    end
    
    conn
    |> put_status(:ok)
    |> render(:transactions_list, transactions: transactions)
  end

  @doc """
  Retry a failed transaction
  """
  def retry_transaction(conn, %{"id" => id}) do
    with {:ok, response} <- BillersService.retry_transaction(id) do
      conn
      |> put_status(:ok)
      |> render(:retry_success, response: response)
    end
  end

  @doc """
  Get available biller types
  """
  def get_biller_types(conn, _params) do
    biller_types = [
      %{
        type: "register_general",
        name: "Register General",
        description: "General registration and invoice services",
        features: ["invoice", "payment"]
      },
      %{
        type: "bwb_postpaid",
        name: "BWB Postpaid",
        description: "Blantyre Water Board - Postpaid",
        features: ["account_lookup", "payment"]
      },
      %{
        type: "lwb_postpaid",
        name: "LWB Postpaid",
        description: "Lilongwe Water Board - Postpaid",
        features: ["account_lookup", "payment"]
      },
      %{
        type: "srwb_postpaid",
        name: "SRWB Postpaid",
        description: "Southern Region Water Board - Postpaid",
        features: ["account_lookup", "payment"]
      },
      %{
        type: "srwb_prepaid",
        name: "SRWB Prepaid",
        description: "Southern Region Water Board - Prepaid",
        features: ["invoice", "payment"]
      },
      %{
        type: "masm",
        name: "MASM",
        description: "Malawi Savings Bank",
        features: ["account_lookup", "payment"]
      },
      %{
        type: "airtel_validation",
        name: "Airtel Validation",
        description: "Airtel Money Validation",
        features: ["validation"]
      },
      %{
        type: "tnm_bundles",
        name: "TNM Bundles",
        description: "TNM Internet Bundles",
        features: ["bundle_lookup", "bundle_purchase"]
      }
    ]
    
    conn
    |> put_status(:ok)
    |> render(:biller_types, biller_types: biller_types)
  end
end