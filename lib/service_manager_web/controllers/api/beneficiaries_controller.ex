defmodule ServiceManagerWeb.BeneficiariesController do
  use ServiceManagerWeb, :controller

  alias ServiceManagerWeb.Services.Local.BeneficiaryService
  alias ServiceManagerWeb.BeneficiariesJSON

  action_fallback ServiceManagerWeb.FallbackController

  def index(conn, params) do
    # ... existing code ...
    result = ServiceManagerWeb.BeneficiariesJSON.index(params)
    json(conn, result)
    # ... existing code ...
  end

  def create(conn, %{"beneficiary_type" => beneficiary_type} = params) do
    user_id = conn.assigns.user.id

    params =
      if beneficiary_type == "wallet" do
        Map.put(params, "wallet_id", user_id)
      else
        Map.put(params, "user_id", user_id)
      end

    case BeneficiaryService.create_beneficiary(params) do
      {:ok, beneficiary, message} ->
        conn
        |> put_status(:created)
        |> render(:show, beneficiary: beneficiary)

      {:error, errors} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: errors)
    end
  end

  def get(conn, %{"id" => beneficiary_id}) do
    user_id = conn.assigns.user.id

    case BeneficiaryService.get_beneficiary(beneficiary_id, user_id) do
      {:ok, beneficiary, _message} ->
        render(conn, :show, beneficiary: beneficiary)

      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> render(:error, message: message)
    end
  end

  def list(conn, params) do
    page = Map.get(params, "page", 1)
    page_size = Map.get(params, "page_size", 10)
    user_id = conn.assigns.user.id
    beneficiary_type = Map.get(params, "beneficiary_type")

    case BeneficiaryService.list_beneficiaries(user_id, beneficiary_type, page, page_size) do
      {:ok, page, _message} ->
        render(conn, :index,
          beneficiaries: page.entries,
          page_number: page.page_number,
          page_size: page.page_size,
          total_pages: page.total_pages,
          total_entries: page.total_entries
        )

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end

  def update(conn, %{"id" => beneficiary_id, "beneficiary_type" => beneficiary_type} = params) do
    user_id = conn.assigns.user.id

    params =
      if beneficiary_type == "wallet" do
        params = Map.put(params, "wallet_id", user_id)
      else
        params = Map.put(params, "user_id", user_id)
      end

    case BeneficiaryService.update_beneficiary(beneficiary_id, params) do
      {:ok, beneficiary, _message} ->
        render(conn, :show, beneficiary: beneficiary)

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end

  def remove(conn, %{"id" => beneficiary_id}) do
    user_id = conn.assigns.user.id

    case BeneficiaryService.remove_beneficiary(beneficiary_id, user_id) do
      {:ok, _, message} ->
        json(conn, %{message: message, status: true})

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end

  def validate(conn, %{"account_number" => account_number, "bank_code" => bank_code}) do
    case BeneficiaryService.validate_beneficiary(account_number, bank_code) do
      {:ok, result, message} ->
        json(conn, %{message: message, status: true, data: result})

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end

  def status(conn, %{"id" => beneficiary_id}) do
    user_id = conn.assigns.user.id

    case BeneficiaryService.get_beneficiary_status(user_id, beneficiary_id) do
      {:ok, status, message} ->
        json(conn, %{message: message, status: true, data: status})

      {:error, message} ->
        conn
        |> put_status(:not_found)
        |> render(:error, message: message)
    end
  end

  def set_default(conn, %{"id" => beneficiary_id}) do
    case BeneficiaryService.set_default_beneficiary(beneficiary_id) do
      {:ok, beneficiary, _message} ->
        render(conn, :show, beneficiary: beneficiary)

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end

  def search(conn, params) do
    page = Map.get(params, "page", 1)
    page_size = Map.get(params, "page_size", 10)
    criteria = Map.take(params, ["name", "account_number", "bank_code", "currency", "status"])

    case BeneficiaryService.search_beneficiaries(criteria, page, page_size) do
      {:ok, page, _message} ->
        render(conn, :index,
          beneficiaries: page.entries,
          page_number: page.page_number,
          page_size: page.page_size,
          total_pages: page.total_pages,
          total_entries: page.total_entries
        )

      {:error, message} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, message: message)
    end
  end
end
