defmodule ServiceManagerWeb.Api.ScreenSchema do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :name, :version, :order, :active]}

  schema "mobile_screens" do
    field :name, :string
    field :version, :string
    field :order, :integer, default: 0
    field :active, :boolean, default: true

    has_many :pages, ServiceManagerWeb.Api.PageSchema,
      foreign_key: :screen_id

    timestamps()
  end

  def changeset(screen, attrs) do
    screen
    |> cast(attrs, [:name, :version, :order, :active])
    |> validate_required([:name, :version])
    |> validate_length(:name, min: 1, max: 100)
    |> validate_length(:version, min: 1, max: 20)
    |> validate_number(:order, greater_than_or_equal_to: 0)
    |> unique_constraint([:name, :version], name: :mobile_screens_unique_name_version)
  end
end

