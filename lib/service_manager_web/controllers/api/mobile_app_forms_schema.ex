defmodule ServiceManagerWeb.Api.MobileAppFormsSchema do
  @moduledoc """
  Schema for mobile app form fields with hierarchical organization.
  
  Fields are organized hierarchically as: Form → Screen → Page → Field
  - form: Required - the main form identifier (e.g., "transfer", "beneficiary")
  - screen: Optional - screen within a form (e.g., "main", "confirmation")
  - page: Optional - page within a screen (e.g., "step1", "step2")
  - field_*: Required - individual form field definition
  - submit_to: Optional - endpoint where form data should be submitted (URL, path, or identifier)
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:field_id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:field_id, :form, :screen, :page, :version, :field_name, :field_type, :label, :is_required, :field_order, :active, :submit_to]}

  schema "mobile_app_forms" do
    field :form, :string
    field :screen, :string
    field :page, :string
    field :version, :string
    field :field_name, :string
    field :field_type, :string
    field :label, :string
    field :is_required, :boolean, default: false
    field :field_order, :integer, default: 0
    field :active, :boolean, default: true
    field :submit_to, :string

    timestamps()
  end

  @doc false
  def changeset(mobile_app_form, attrs) do
    mobile_app_form
    |> cast(attrs, [:form, :screen, :page, :version, :field_name, :field_type, :label, :is_required, :field_order, :active, :submit_to])
    |> validate_required([:form, :version, :field_name, :field_type, :label])
    |> validate_inclusion(:field_type, ["string", "number", "integer", "boolean", "date", "datetime", "email", "password", "phone", "select", "multiselect", "textarea", "button"])
    |> validate_length(:form, min: 1, max: 100)
    |> validate_length(:screen, max: 100)
    |> validate_length(:page, max: 100)
    |> validate_length(:version, min: 1, max: 20)
    |> validate_length(:field_name, min: 1, max: 100)
    |> validate_length(:label, min: 1, max: 200)
    |> validate_length(:submit_to, max: 500)
    |> validate_number(:field_order, greater_than_or_equal_to: 0)
    |> validate_page_requires_screen()
    |> validate_submit_to_format()
    |> unique_constraint([:form, :screen, :page, :field_name, :version], name: :mobile_app_forms_unique_field_constraint)
  end


  # Custom validation to ensure page requires screen
  defp validate_page_requires_screen(changeset) do
    page = get_field(changeset, :page)
    screen = get_field(changeset, :screen)

    case {page, screen} do
      {nil, _} -> changeset
      {_, nil} -> add_error(changeset, :page, "cannot be set without a screen")
      {_, _} -> changeset
    end
  end

  # Custom validation for submit_to field format
  defp validate_submit_to_format(changeset) do
    submit_to = get_field(changeset, :submit_to)

    case submit_to do
      nil -> changeset
      "" -> changeset
      value when is_binary(value) ->
        if valid_submit_to_format?(value) do
          changeset
        else
          add_error(changeset, :submit_to, "must be a valid URL or endpoint identifier")
        end
      _ -> add_error(changeset, :submit_to, "must be a string")
    end
  end

  # Check if submit_to value is in valid format
  defp valid_submit_to_format?(value) do
    # Allow URLs or relative paths/endpoints
    url_regex = ~r{^https?://[^\s/$.?#].[^\s]*$}
    endpoint_regex = ~r{^/[a-zA-Z0-9/_-]+$}
    identifier_regex = ~r{^[a-zA-Z0-9_-]+$}

    Regex.match?(url_regex, value) or 
    Regex.match?(endpoint_regex, value) or
    Regex.match?(identifier_regex, value)
  end

  @doc """
  Returns list of valid field types
  """
  def valid_field_types do
    ["string", "number", "integer", "boolean", "date", "datetime", "email", "password", "phone", "select", "multiselect", "textarea", "button"]
  end
end