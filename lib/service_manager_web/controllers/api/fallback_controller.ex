defmodule ServiceManagerWeb.FallbackControllers do
  @moduledoc """
  Translates controller action results into valid `Plug.Conn` responses.

  See `Phoenix.Controller.action_fallback/1` for more details.
  """
  use ServiceManagerWeb, :controller

  # This clause is an example of how to handle resources that cannot be found.
  def call(conn, {:error, :not_found}) do
    conn
    |> put_status(:not_found)
    |> put_view(html: ServiceManagerWeb.ErrorHTML, json: ServiceManagerWeb.ErrorJSON)
    |> render(:"404")
  end

  def call(conn, error) do
    conn
    |> put_status(:internal_server_error)
    |> put_view(html: AppWeb.ErrorHTML, json: AppWeb.ErrorJSON)
    |> render(:"500")
  end
end
