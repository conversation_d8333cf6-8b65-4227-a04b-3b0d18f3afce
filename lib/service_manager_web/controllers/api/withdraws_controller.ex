defmodule ServiceManagerWeb.Api.WithdrawsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Contexts.CardlessWithdrawsContext
  alias ServiceManager.Notifications.SMSNotification
  alias ServiceManager.Repo
  alias ServiceManager.Broadcasters.LoggerBroadcaster, as: <PERSON><PERSON>
  alias ServiceManagerWeb.TransfersController
  alias ServiceManagerWeb.Api.WithdrawsJSON

  action_fallback ServiceManagerWeb.FallbackController

  def cardless_withdraw(conn, %{"amount" => amount} = params) do
    Logger.info("Starting cardless withdraw request")

    source_account = conn.assigns.user.account_number
    phone = conn.assigns.user.phone_number
    beneficiary_name = "#{conn.assigns.user.first_name} #{conn.assigns.user.last_name}"
    user_id = conn.assigns.user.id

    withdraw_params = %{
      "amount" => amount,
      "source_account" => source_account,
      "beneficiary_phone" => phone,
      "beneficiary_name" => beneficiary_name,
      "user_id" => user_id,
      # Suspense account for cardless withdrawals
      "suspense_account" => "*************"
    }

    case CardlessWithdrawsContext.create_cardless_withdraw(withdraw_params) |> IO.inspect() do
      {:ok, withdraw} ->
        # Send OTP via SMS
        case Repo.insert(%SMSNotification{
               msisdn: phone,
               message:
                 "Your cardless withdrawal OTP is #{withdraw.otp}. Reference: #{withdraw.reference_number}"
             }) do
          {:ok, _notification} ->
            Logger.info(
              "Cardless withdraw created successfully. Reference: #{withdraw.reference_number}"
            )

            conn
            |> put_status(:created)
            |> render(:create, withdraw: withdraw)

          {:error, error} ->
            IO.inspect(error)
            Logger.error("Failed to send OTP SMS: #{inspect(error)}")
            CardlessWithdrawsContext.update_cardless_withdraw(withdraw, %{status: "failed"})

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to send OTP")
        end

      {:error, error} ->
        Logger.error("Failed to create cardless withdraw: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to create cardless withdraw")
    end
  end

  def cardless_wallet_withdraw(conn, %{"amount" => amount} = params) do
    Logger.info("Starting cardless wallet withdraw request")

    source_wallet = conn.assigns.user.mobile_number
    wallet_user_id = conn.assigns.user.id
    name = "#{conn.assigns.user.first_name} #{conn.assigns.user.last_name}"
    phone = conn.assigns.user.mobile_number

    withdraw_params = %{
      "amount" => amount,
      "source_wallet" => source_wallet,
      "beneficiary_phone" => phone,
      "beneficiary_name" => name,
      "wallet_user_id" => wallet_user_id,
      # Suspense account for cardless withdrawals
      "suspense_account" => "*************"
    }

    case CardlessWithdrawsContext.create_cardless_wallet_withdraw(withdraw_params) do
      {:ok, withdraw} ->
        # Send OTP via SMS
        case Repo.insert(%SMSNotification{
               msisdn: phone,
               message:
                 "Your cardless wallet withdrawal OTP is #{withdraw.otp}. Reference: #{withdraw.reference_number}"
             }) do
          {:ok, _notification} ->
            Logger.info(
              "Cardless wallet withdraw created successfully. Reference: #{withdraw.reference_number}"
            )

            conn
            |> put_status(:created)
            |> render(:create, withdraw: withdraw)

          {:error, error} ->
            Logger.error("Failed to send OTP SMS: #{inspect(error)}")

            CardlessWithdrawsContext.update_cardless_wallet_withdraw(withdraw, %{status: "failed"})

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to send OTP")
        end

      {:error, error} ->
        Logger.error("Failed to create cardless wallet withdraw: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to create cardless wallet withdraw")
    end
  end

  def verify_withdraw(conn, %{"reference_number" => reference_number, "withdraw_voucher_code" => otp}) do
    Logger.info("Verifying cardless withdraw OTP")

    case CardlessWithdrawsContext.get_cardless_withdraw_by_reference(reference_number) do
      nil ->
        Logger.error("Withdraw request not found: #{reference_number}")

        conn
        |> put_status(:not_found)
        |> render(:error, error: "Withdraw request not found")

      withdraw ->
        case CardlessWithdrawsContext.verify_withdraw_otp(withdraw, otp) do
          {:ok, verified_withdraw} ->
            # Process the bank transfer
            transfer_params = %{
              "from_account" => verified_withdraw.source_account,
              "to_account" => verified_withdraw.suspense_account,
              "amount" => Decimal.to_string(verified_withdraw.amount),
              "description" => "Cardless withdrawal: #{reference_number}"
            }

            case TransfersController.bank_to_bank(conn, transfer_params) do
              %{status: 201} ->
                Logger.info("Cardless withdraw completed successfully: #{reference_number}")

                CardlessWithdrawsContext.update_cardless_withdraw(verified_withdraw, %{
                  status: "completed"
                })

                conn
                |> put_status(:ok)
                |> render(:verify, withdraw: verified_withdraw)

              _ ->
                Logger.error("Failed to process transfer for withdraw: #{reference_number}")

                CardlessWithdrawsContext.update_cardless_withdraw(verified_withdraw, %{
                  status: "failed"
                })

                conn
                |> put_status(:unprocessable_entity)
                |> render(:error, error: "Failed to process withdrawal")
            end

          {:error, "Invalid OTP"} ->
            Logger.error("Invalid OTP provided for withdraw: #{reference_number}")

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Invalid OTP")

          {:error, error} ->
            Logger.error("Error verifying withdraw OTP: #{inspect(error)}")

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to verify OTP")
        end
    end
  end

  def verify_wallet_withdraw(conn, %{"reference_number" => reference_number, "otp" => otp}) do
    Logger.info("Verifying cardless wallet withdraw OTP")

    case CardlessWithdrawsContext.get_cardless_wallet_withdraw_by_reference(reference_number) do
      nil ->
        Logger.error("Wallet withdraw request not found: #{reference_number}")

        conn
        |> put_status(:not_found)
        |> render(:error, error: "Withdraw request not found")

      withdraw ->
        case CardlessWithdrawsContext.verify_wallet_withdraw_otp(withdraw, otp) do
          {:ok, verified_withdraw} ->
            # Process the wallet transfer
            transfer_params = %{
              "from_wallet" => verified_withdraw.source_wallet,
              "to_account" => verified_withdraw.suspense_account,
              "amount" => Decimal.to_string(verified_withdraw.amount),
              "description" => "Cardless wallet withdrawal: #{reference_number}"
            }

            case TransfersController.wallet_to_account_v2(transfer_params) |> IO.inspect() do
              {:ok, {:ok, _}} ->
                Logger.info(
                  "Cardless wallet withdraw completed successfully: #{reference_number}"
                )

                CardlessWithdrawsContext.update_cardless_wallet_withdraw(verified_withdraw, %{
                  status: "completed"
                })

                conn
                |> put_status(:ok)
                |> render(:verify, withdraw: verified_withdraw)

              {:error, _error} ->
                Logger.error(
                  "Failed to process transfer for wallet withdraw: #{reference_number}"
                )

                CardlessWithdrawsContext.update_cardless_wallet_withdraw(verified_withdraw, %{
                  status: "failed"
                })

                conn
                |> put_status(:unprocessable_entity)
                |> render(:error, error: "Failed to process withdrawal")
            end

          {:error, "Invalid OTP"} ->
            Logger.error("Invalid OTP provided for wallet withdraw: #{reference_number}")

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Invalid OTP")

          {:error, error} ->
            Logger.error("Error verifying wallet withdraw OTP: #{inspect(error)}")

            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to verify OTP")
        end
    end
  end

  def funds_request(conn, params) do
    # fund request schema
  end

  def funds_wallet_request(conn, params) do
    # fund request wallet schema
  end

  def agent_cashout(conn, params) do
    # cash agent checkout requests schema
  end

  def agent_wallet_cashout(conn, params) do
    # cash agent wallet checkout requests schema
  end

  def merchant_cashout(conn, params) do
    # cash merchant wallet checkout requests schema
  end

  def merchant_wallet_cashout(conn, params) do
    # cash merchant wallet checkout requests schema
  end
end
