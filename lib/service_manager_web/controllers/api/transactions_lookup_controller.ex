defmodule ServiceManagerWeb.TransactionsLookupController do
  use ServiceManagerWeb, :controller
  alias ServiceManagerWeb.Api.Services.Local.TransactionsLookupService

  def get_by_reference(conn, %{"reference" => reference}) do
    transactions = TransactionsLookupService.get_transactions_by_reference(reference)
    json(conn, transactions)
  end

  def get_by_references(conn, %{"reference" => references}) do
    case references do
      refs when is_list(refs) ->
        transactions = TransactionsLookupService.get_transactions_by_references(refs)
        json(conn, transactions)

      _ ->
        json(conn, %{error: "references must be an array"})
    end
  end

  def get_by_references(conn, _params) do
    json(conn, %{error: "reference parameter is required and must be an array"})
  end

  def get_transaction_pair(conn, %{"reference" => reference}) do
    case TransactionsLookupService.get_transaction_pair(reference) do
      {:ok, transactions} -> json(conn, transactions)
      {:error, message} -> json(conn, %{error: message})
    end
  end

  def get_reversal_chain(conn, %{"original_transaction_id" => transaction_id}) do
    case TransactionsLookupService.get_reversal_chain(transaction_id) do
      {:ok, chain} -> json(conn, chain)
      {:error, message} -> json(conn, %{error: message})
    end
  end

  def get_reversal_chain(conn, _params) do
    json(conn, %{error: "original_transaction_id parameter is required"})
  end

  def get_mini_statement(conn, %{"account_number" => account_number} = params) do
    transaction_count = Map.get(params, "transaction_count", 10)

    with {:ok, count} <- validate_transaction_count(transaction_count),
         {:ok, date_range} <- validate_date_params(params) do
      case TransactionsLookupService.get_mini_statement(account_number, date_range, count) do
        {:ok, transactions} -> json(conn, transactions)
        {:error, message} -> json(conn, %{error: message})
      end
    else
      {:error, message} -> json(conn, %{error: message})
    end
  end

  def get_mini_statement_mobile_banking(conn, %{"account_number" => account_number} = params) do
    transaction_count = Map.get(params, "transaction_count", 10)

    with {:ok, count} <- validate_transaction_count(transaction_count),
         {:ok, date_range} <- validate_date_params(params) do
      case TransactionsLookupService.get_mini_statement(account_number, date_range, count) do
        {:ok, transactions} -> json(conn, transactions)
        {:error, message} -> json(conn, %{error: message})
      end
    else
      {:error, message} -> json(conn, %{error: message})
    end
  end

  def get_mini_statement(conn, _params) do
    json(conn, %{error: "account_number parameter is required"})
  end

  defp validate_transaction_count(count) when is_binary(count) do
    case Integer.parse(count) do
      {num, ""} when num > 0 -> {:ok, num}
      _ -> {:error, "transaction_count must be a positive integer"}
    end
  end

  defp validate_transaction_count(count) when is_integer(count) and count > 0, do: {:ok, count}
  # Default value if not specified or invalid
  defp validate_transaction_count(_), do: {:ok, 10}

  defp validate_date_params(params) do
    case {Map.get(params, "start_date"), Map.get(params, "end_date")} do
      {nil, nil} ->
        {:ok, nil}

      {start_date, end_date} when is_binary(start_date) and is_binary(end_date) ->
        with {:ok, parsed_start} <- Date.from_iso8601(start_date),
             {:ok, parsed_end} <- Date.from_iso8601(end_date) do
          {:ok, {parsed_start, parsed_end}}
        else
          _ -> {:error, "Invalid date format. Use ISO8601 (YYYY-MM-DD)"}
        end

      {nil, _} ->
        {:error, "Both start_date and end_date must be provided"}

      {_, nil} ->
        {:error, "Both start_date and end_date must be provided"}
    end
  end
end
