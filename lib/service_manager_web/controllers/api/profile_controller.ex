defmodule ServiceManagerWeb.ProfileController do
  use ServiceManagerWeb, :controller

  import ServiceManager.Logging.FunctionTracker

  alias ServiceManagerWeb.Api.Services.ProfileServiceController, as: ProfileService
  alias ServiceManager.Accounts.User, as: User
  alias ServiceManager.Schemas.ActiveDevice, as: Device

  def index(conn, _params) do
    # The home page is often custom made,
    # so skip the default app layout.
    json(conn, %{})
  end

  track do
    def register(conn, params) do
      res = ProfileService.register(%{}, params)
      json(conn, res)
    end
  end

  def update(conn, params) do
    res = ProfileService.update(conn.assigns.user, params)
    json(conn, res)
  end

  track do
    def profile(conn, params) do
      # chain_id = "profile_#{conn.assigns.user.username}"
      res = ProfileService.details(conn.assigns.user, params)
      json(conn, res)
    end
  end

  def approve(conn, params) do
    res = ProfileService.approve(params)
    json(conn, res)
  end

  def approve(params) do
    ProfileService.approve(params)
  end

  # REMOTE CALLS ---------------------------

  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService, as: RemoteProfileService
  alias ServiceManagerWeb.Validators.LinkAccountValidator

  track do
    def link_account(conn, params) do

      if params["device_id"] == nil do
        resp = %{
          "data" => %{
            "errors" => "Device error, could not identify device..."
          },
          "message" => "Device error, could not identify device...",
          "status" => false
        }

        conn
        |> put_status(:bad_request)
        |> json(resp)
      else
        case LinkAccountValidator.validate(params) do
          {:ok, validated_params} ->
            %{"account_number" => account_number} = validated_params
  
            user_by_account = User.find_by(account_number: account_number)
  
            if user_by_account != nil do
              account_exists_message = "An account with this account number already exists."
  
              resp = %{
                "data" => %{
                  "errors" => account_exists_message
                },
                "message" => account_exists_message,
                "status" => false
              }
  
              conn
              |> put_status(:bad_request)
              |> json(resp)
            else
              case RemoteProfileService.get_profile_by_account_number_v4(account_number) do
                {:ok, remote_profile} ->
                  # Validate that request data matches remote profile
                  validation_results = [
                    # validate_field(validated_params, remote_profile, "first_name"),
                    # validate_field(validated_params, remote_profile, "last_name"),
                    # validate_phone(validated_params, remote_profile),
                    # validate_field(validated_params, remote_profile, "id_number")
                  ]
  
                  case Enum.all?(validation_results, & &1) do
                    true ->
                      profile =
                        remote_profile
                        |> Map.merge(validated_params)
                        |> Map.put_new(
                          "password",
                          Enum.map(1..12, fn _ -> Enum.random(0..9) end)
                          |> Enum.join()
                          |> String.replace(~r/(\d{4})(?=\d)/, "\\1-")
                        )
  
                      registration_response = ProfileService.register(%{}, profile)
  
                      case registration_response["status"] do
                        true ->
  
                          case update_device(registration_response["data"]["profile"], params) |> IO.inspect(label: "DEVICE-BINDING:") do
                            {:ok, token} ->
                              resp = %{
                                "data" => %{
                                  "profile" => registration_response["data"]["profile"],
                                  "access_tokenn" => token
                                },
                                "message" => "Account linked successfully",
                                "status" => true
                              }
      
                              json(conn, resp)
  
                            {:error, error} -> 
                              resp = %{
                                "data" => %{
                                  "errors" => error
                                },
                                "message" => error,
                                "status" => false
                              }
      
                              conn
                              |> put_status(:bad_request)
                              |> json(resp)
                          end
  
  
                        false ->
                          resp = %{
                            "data" => %{
                              "errors" => registration_response["data"]["errors"]
                            },
                            "message" => "Account has already been linked.",
                            "status" => false
                          }
  
                          conn
                          |> put_status(:bad_request)
                          |> json(resp)
                      end
  
                    false ->
                      resp = %{
                        "data" => %{
                          "errors" => [
                            "The provided information does not match the account details, contact support to verify your details."
                          ]
                        },
                        "message" =>
                          "The provided information does not match the account details, contact support to verify your details.",
                        "status" => false
                      }
  
                      conn
                      |> put_status(:bad_request)
                      |> json(resp)
                  end
  
                {:error, reason} ->
                  resp = %{
                    "data" => %{},
                    "message" => reason,
                    "status" => false
                  }
  
                  conn
                  |> put_status(:not_found)
                  |> json(resp)
              end
            end
  
          {:error, validation_errors} ->
            resp = %{
              "data" => %{
                "errors" => validation_errors
              },
              "message" => "Invalid request parameters",
              "status" => false
            }
  
            conn
            |> put_status(:bad_request)
            |> json(resp)
        end
      end

      
    end
  end

  track do
    def link_account(params) do
      case LinkAccountValidator.validate(params) do
        {:ok, validated_params} ->
          %{"account_number" => account_number} = validated_params

          case RemoteProfileService.get_profile_by_account_number_v3(account_number) do
            {:ok, remote_profile} ->
              # Validate that request data matches remote profile
              validation_results = [
                # validate_field(validated_params, remote_profile, "first_name"),
                # validate_field(validated_params, remote_profile, "last_name"),
                # validate_phone(validated_params, remote_profile),
                # validate_field(validated_params, remote_profile, "id_number")
              ]

              case Enum.all?(validation_results, & &1) do
                true ->
                  profile =
                    remote_profile
                    |> Map.merge(validated_params)
                    |> Map.put_new(
                      "password",
                      Enum.map_join(1..12, "", fn _ -> Enum.random(0..9) end)
                      |> String.replace(~r/(\d{4})(?=\d)/, "\\1-")
                    )
                    |> Map.put("phone_number", params["phone_number"])

                  registration_response = ProfileService.register(%{}, profile)

                  case registration_response["status"] do
                    true ->
                      %{
                        "data" => %{
                          "profile" => registration_response["data"]["profile"]
                        },
                        "message" => "Account linked successfully",
                        "status" => true
                      }

                    false ->
                      %{
                        "data" => %{
                          "errors" => registration_response["data"]["errors"]
                        },
                        "message" => "Account has already been linked.",
                        "status" => false
                      }
                  end

                false ->
                  %{
                    "data" => %{
                      "errors" => [
                        "The provided information does not match the account details, contact support to verify your details."
                      ]
                    },
                    "message" =>
                      "The provided information does not match the account details, contact support to verify your details.",
                    "status" => false
                  }
              end

            {:error, reason} ->
              %{
                "data" => %{},
                "message" => reason,
                "status" => false
              }
          end

        {:error, validation_errors} ->
          %{
            "data" => %{
              "errors" => validation_errors
            },
            "message" => "Invalid request parameters",
            "status" => false
          }
      end
    end
  end

  # Helper functions for validation
  defp validate_field(params, remote_profile, field) do
    case params[field] do
      nil -> true
      value -> String.downcase(value) == String.downcase(remote_profile[field] || "")
    end
  end

  defp validate_phone(params, remote_profile) do
    case params["mobile_number"] do
      nil ->
        true

      value ->
        normalized_param = String.replace(value, ~r/[^0-9]/, "")
        normalized_remote = String.replace(remote_profile["phone_number"] || "", ~r/[^0-9]/, "")
        
        # Extract last 9 digits from both numbers and compare
        if String.length(normalized_param) >= 9 and String.length(normalized_remote) >= 9 do
          param_last_9 = String.slice(normalized_param, -9, 9)
          remote_last_9 = String.slice(normalized_remote, -9, 9)
          param_last_9 == remote_last_9
        else
          false
        end
    end
  end

  defp check_shifting_pattern(param_digits, remote_digits) do
    param_length = String.length(param_digits)
    remote_length = String.length(remote_digits)
    
    if param_length == 0 or remote_length < param_length do
      false
    else
      # Try each position in the remote number
      0..(remote_length - param_length)
      |> Enum.any?(fn start_pos ->
        String.slice(remote_digits, start_pos, param_length) == param_digits
      end)
    end
  end

  track do
    def get_account_by_number(conn, %{"account_number" => account_number}) do
      case RemoteProfileService.get_profile_by_account_number_v3(account_number) do
        {:ok, profile} ->
          resp = %{
            "data" => %{
              "bank_profile" => profile
            },
            "message" => "success",
            "status" => true
          }

          json(conn, resp)

        {:error, reason} ->
          resp = %{
            "data" => %{},
            "message" => reason,
            "status" => false
          }

          conn
          |> json(resp)
      end
    end
  end

  def get_account_by_number_v2(conn, %{"account_number" => account_number}) do
    case RemoteProfileService.get_profile_by_account_number_v4(account_number) do
      {:ok, profile} ->
        resp = %{
          "data" => %{
            "bank_profile" => profile
          },
          "message" => "success",
          "status" => true
        }

        json(conn, resp)

      {:error, reason} ->
        resp = %{
          "data" => %{},
          "message" => reason,
          "status" => false
        }

        conn
        |> json(resp)
    end
  end

  def update_password(conn, params) do
    res = conn.assigns.user |> ProfileService.update_password(params)
    json(conn, res)
  end

  def update_nickname(conn, %{"nickname" => nickname}) do
    user = conn.assigns.user
    res = ProfileService.update(user, %{"nickname" => nickname})
    json(conn, res)
  end

  def update_multi_session_setting(conn, params) do
    user = conn.assigns.user
    res = ProfileService.update_multi_session_setting(user, params) 
    json(conn, res)
  end

  def disable_user(conn, _params) do
    res = ProfileService.disable_user(conn.assigns.user.id)
    json(conn, res)
  end

  def enable_user(conn, _params) do
    res = ProfileService.enable_user(conn.assigns.user.id)
    json(conn, res)
  end

  defp update_device(user, params) do
    IO.inspect params, label: "DEVICE-PARAMS"
    if params["device_id"] == nil do
      {:error, "An error occurred, device validation failed. could not identify device..."}
    else
      device = Device.find_by(device_id: params["device_id"], user_id: user.id)
      token = ServiceManager.Token.generate_and_sign!(params)
      verfication_message = "Account pending verification"

      if device == nil do
        case Device.create(
                 device_id: params["device_id"],
                 user_id: user.id,
                 last_seen_at: NaiveDateTime.utc_now(),
                 enabled: true,
                 blocked: false,
                 static_access_token: token,
                 device_screen_message: verfication_message,
                 on_hold: true
               ) do
            {:error, _error} ->
              {:error, "An error occurred when registering your device ..."}

            {:ok, device} ->

              

              case ServiceManager.Repo.update(
                User.device_binding_changeset(user, %{
                  primary_device_id: device.device_id
                })
              ) |> IO.inspect(label: "BindError") do
                {:ok, _updated_user} -> {:ok, token}
                {:error, error_message} -> {:error, "User registered but failed to register and bind device"}
              end
          end
        else
          case Device.update(device, [static_access_token: token, device_screen_message: verfication_message, on_hold: true]) do
            {:ok, _device_updated} -> 
              case ServiceManager.Repo.update(
                User.device_binding_changeset(user, %{
                  primary_device_id: device.device_id
                })
              ) do
                {:ok, _updated_user} -> {:ok, token}
                {:error, error_message} -> {:error, "User registered but failed to bind device"}
              end
            {:error, error_message} -> {:error, error_message}
          end
        end

    
    end
  end

end
