defmodule ServiceManagerWeb.ThirdPartyController do
  use ServiceManagerWeb, :controller
  
  import ServiceManager.Logging.FunctionTracker
  
  alias ServiceManagerWeb.Validators.ThirdPartyServiceValidator
  alias ServiceManagerWeb.Services.ThirdPartyMultiplexer

  track do
    def profile_services(conn, params) do
      case ThirdPartyServiceValidator.validate(params) do
        {:ok, validated_params} ->
          case ThirdPartyMultiplexer.route(validated_params) do
            {:ok, result} ->
              json(conn, result)
            
            {:error, error_message} ->
              error_response = %{
                "data" => %{
                  "errors" => error_message
                },
                "message" => error_message,
                "status" => false
              }
              
              conn
              |> put_status(:bad_request)
              |> json(error_response)
          end

        {:error, validation_errors} ->
          error_response = %{
            "data" => %{
              "errors" => validation_errors
            },
            "message" => "Invalid request parameters",
            "status" => false
          }

          conn
          |> put_status(:bad_request)
          |> json(error_response)
      end
    end
  end
end

