defmodule ServiceManagerWeb.TransactionsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Contexts.TransactionsContext
  alias ServiceManager.Contexts.WalletTransactionsContext

  alias ServiceManagerWeb.Api.Services.Remote.TransactionsReportFromRemoteService,
    as: TransactionsService

  alias ServiceManagerWeb.TransactionsJSON

  action_fallback ServiceManagerWeb.FallbackController

  @doc """
  Retrieves a report of regular transactions based on provided filters.

  ## Request Examples

  1. Filter by account number and date range:
  ```
  POST /api/transactions/report
  {
    "account_number": "**********",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }
  ```

  2. Filter by account number, status and type:
  ```
  POST /api/transactions/report
  {
    "account_number": "**********",
    "status": "completed",
    "type": "credit"
  }
  ```

  3. Complex filter with account number, amount range and search:
  ```
  POST /api/transactions/report
  {
    "account_number": "**********",
    "min_amount": 100,
    "max_amount": 1000,
    "search": "payment",
    "status": "completed"
  }
  ```

  4. Filter by account number and specific accounts:
  ```
  POST /api/transactions/report
  {
    "account_number": "**********",
    "from_account_id": 123,
    "to_account_id": 456,
    "status": "completed"
  }
  ```

  ## Request Body Parameters
  - account_number: string (required)
  - status: ["pending", "completed", "failed", "processing"]
  - type: ["credit", "debit", "transfer"]
  - start_date: "YYYY-MM-DD"
  - end_date: "YYYY-MM-DD"
  - min_amount: decimal
  - max_amount: decimal
  - reference: string
  - from_account_id: integer
  - to_account_id: integer
  - search: string (searches across type, reference, status, and description)
  - page: integer (default: 1)
  - page_size: integer (default: 20)
  """
  track do
    def transactions_by_account(conn, %{"account_number" => account_number} = params) do
      search_params =
        Map.merge(params, %{
          "from_account" => account_number,
          "to_account" => account_number
        })

      case TransactionsContext.retrieve_by_account(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def transactions_by_account(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number is required"}))
    end
  end

  track do
    def transactions_by_status(
          conn,
          %{"status" => status, "account_number" => account_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_account" => account_number,
          "to_account" => account_number
        })

      case TransactionsContext.retrieve_by_account(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def transactions_by_status(conn, %{"status" => _status}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number is required"}))
    end
  end

  track do
    def transactions_by_status(conn, %{"account_number" => _account}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "status is required"}))
    end
  end

  track do
    def transactions_by_status(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number and status are required"}))
    end
  end

  track do
    def transactions_by_type(conn, %{"type" => type, "account_number" => account_number} = params) do
      search_params =
        Map.merge(params, %{
          "from_account" => account_number,
          "to_account" => account_number
        })

      case TransactionsContext.retrieve_by_account(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def transactions_by_type(conn, %{"type" => _type}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number is required"}))
    end
  end

  track do
    def transactions_by_type(conn, %{"account_number" => _account}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "type is required"}))
    end
  end

  track do
    def transactions_by_type(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number and type are required"}))
    end
  end

  track do
    def transactions_by_date_range(
          conn,
          %{"start_date" => _start, "end_date" => _end, "account_number" => account_number} =
            params
        ) do
      search_params =
        Map.merge(params, %{
          "from_account" => account_number,
          "to_account" => account_number
        })

      case TransactionsContext.retrieve_by_account(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def transactions_by_date_range(conn, %{"start_date" => _start, "end_date" => _end}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number is required"}))
    end
  end

  track do
    def transactions_by_date_range(conn, %{"account_number" => _account}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "start_date and end_date are required"}))
    end
  end

  track do
    def transactions_by_date_range(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(
        TransactionsJSON.error(%{error: "account_number, start_date and end_date are required"})
      )
    end
  end

  track do
    def search_transactions(
          conn,
          %{"search" => _search_term, "account_number" => account_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_account" => account_number,
          "to_account" => account_number
        })

      case TransactionsContext.retrieve_by_account(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def search_transactions(conn, %{"search" => _search}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number is required"}))
    end
  end

  track do
    def search_transactions(conn, %{"account_number" => _account}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "search term is required"}))
    end
  end

  track do
    def search_transactions(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "account_number and search term are required"}))
    end
  end

  # Wallet Transaction Reports

  track do
    def wallet_transactions_by_mobile(conn, %{"mobile_number" => mobile_number} = params) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def wallet_transactions_by_mobile(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def wallet_transactions_by_status(
          conn,
          %{"status" => status, "mobile_number" => mobile_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def wallet_transactions_by_status(conn, %{"status" => _status}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def wallet_transactions_by_status(conn, %{"mobile_number" => _mobile}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "status is required"}))
    end
  end

  track do
    def wallet_transactions_by_status(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number and status are required"}))
    end
  end

  track do
    def wallet_transactions_by_type(
          conn,
          %{"type" => type, "mobile_number" => mobile_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def wallet_transactions_by_type(conn, %{"type" => _type}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def wallet_transactions_by_type(conn, %{"mobile_number" => _mobile}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "type is required"}))
    end
  end

  track do
    def wallet_transactions_by_type(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number and type are required"}))
    end
  end

  track do
    def wallet_transactions_by_date_range(
          conn,
          %{"start_date" => _start, "end_date" => _end, "mobile_number" => mobile_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def wallet_transactions_by_date_range(conn, %{"start_date" => _start, "end_date" => _end}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def wallet_transactions_by_date_range(conn, %{"mobile_number" => _mobile}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "start_date and end_date are required"}))
    end
  end

  track do
    def wallet_transactions_by_date_range(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(
        TransactionsJSON.error(%{error: "mobile_number, start_date and end_date are required"})
      )
    end
  end

  track do
    def search_wallet_transactions(
          conn,
          %{"search" => _search_term, "mobile_number" => mobile_number} = params
        ) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def search_wallet_transactions(conn, %{"search" => _search}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def search_wallet_transactions(conn, %{"mobile_number" => _mobile}) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "search term is required"}))
    end
  end

  track do
    def search_wallet_transactions(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number and search term are required"}))
    end
  end

  @doc """
  Legacy report endpoint that uses remote service
  """
  @doc """
  Get wallet transactions by mobile number.

  ## Request Example
  ```
  POST /api/transactions/wallet/report/get-by-mobile
  {
    "mobile_number": "+254712345678"
  }
  ```
  """
  track do
    def get_wallet_transactions_by_mobile(conn, %{"mobile_number" => mobile_number} = params) do
      search_params =
        Map.merge(params, %{
          "from_mobile" => mobile_number,
          "to_mobile" => mobile_number
        })

      case WalletTransactionsContext.retrieve_by_mobile(search_params) do
        transactions when is_list(transactions) ->
          conn
          |> put_status(:ok)
          |> json(TransactionsJSON.wallet_transactions_report(%{transactions: transactions}))

        {:error, error} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end

  track do
    def get_wallet_transactions_by_mobile(conn, _params) do
      conn
      |> put_status(:unprocessable_entity)
      |> json(TransactionsJSON.error(%{error: "mobile_number is required"}))
    end
  end

  track do
    def report(conn, params) do
      TransactionsService.retrieve(params)
      |> case do
        {:ok, res} ->
          conn
          |> put_status(:created)
          |> json(TransactionsJSON.transactions_report(%{transactions: res}))

        {:error, error} ->
          conn
          |> put_status(:created)
          |> json(TransactionsJSON.error(%{error: error}))
      end
    end
  end
end
