defmodule ServiceManagerWeb.WalletAuthenticationController do
  use ServiceManagerWeb, :controller

  alias ServiceManagerWeb.Api.Services.WalletAuthenticationService, as: AuthenticationService
  alias ServiceManager.WalletAccounts
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManagerWeb.Api.Services.WalletService
  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService, as: RemoteProfileService

  alias ServiceManagerWeb.Api.Services.Remote.WalletLookupRemoteService,
    as: RemoteAccountslLookupWallet

  # Sign in
  def sign_in(conn, params) do
    res = AuthenticationService.authenticate(params)

    case res do
      %{"status" => true, "data" => %{"user" => %{"profile" => user}}} = response ->
        track_user_presence(conn, user)
        json(conn, response)

      response ->
        json(conn, response)
    end
  end

  defp track_user_presence(conn, user) do
    device_id = get_device_id(conn)
    now = DateTime.utc_now()

    device = %{
      device_id: device_id,
      last_seen_at: now,
      wallet_user_id: user.id,
      user_type: "wallet"
    }

    ServiceManagerWeb.Presence.update(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )

    ServiceManagerWeb.Presence.track(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )

    time =
      DateTime.utc_now()
      |> DateTime.truncate(:second)
      |> to_string
      |> String.replace("Z", "")

    params =
      conn.params
      |> Map.put("last_seen_at", DateTime.utc_now())
      |> Map.put("wallet_user_id", user.id)
  end

  defp get_device_id(conn) do
    # Generate a unique device ID based on user agent and IP
    user_agent = get_req_header(conn, "user-agent") |> List.first() || "unknown"
    remote_ip = conn.remote_ip |> :inet.ntoa() |> to_string()
    :crypto.hash(:sha256, "#{user_agent}:#{remote_ip}") |> Base.encode16()
  end

  def sign_out(conn, %{"access_token" => token} = params) do
    res = AuthenticationService.invalidate(token)
    json(conn, res)
  end

  def register(conn, wallet_user_params) do
    response =
      case WalletAccounts.register_wallet_user(wallet_user_params) do
        {:ok, wallet_user} ->
          %{
            "message" => "success",
            "status" => true,
            "data" => %{
              "wallet" => wallet_user
            }
          }

        {:error, %Ecto.Changeset{} = changeset} ->
          case changeset.errors do
            [{:mobile_number, {"has already been taken", _}} | _] ->
              %{
                "message" => "An account with this mobile number already exists",
                "status" => false,
                "errors" => %{
                  "error" => %{mobile_number: ["already exists"]}
                }
              }

            _ ->
              errors =
                Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
                  Enum.reduce(opts, msg, fn {key, value}, acc ->
                    # Convert the key to a string if it's an atom
                    key_string = if is_atom(key), do: Atom.to_string(key), else: key
                    String.replace(acc, "%{#{key_string}}", inspect(value))
                  end)
                end)

              %{
                "message" => "Wallet registration failed. Please check your input and try again",
                "status" => false,
                "errors" => %{
                  "error" => errors
                }
              }
          end
      end

    json(conn, response)
  end

  def refresh_token(conn, %{"access_token" => token} = params) do
    res = AuthenticationService.refresh_token(token)
    json(conn, res)
  end

  def update_password(conn, params) do
    res = conn.assigns.user |> WalletService.update_password(params)
    json(conn, res)
  end

  def forgot_password(conn, %{"mobile_number" => mobile_number}) do
    if user = WalletAccounts.get_wallet_user_by_mobile_number(mobile_number) do
      # Generate a random password
      new_password = (:rand.uniform(900_000) + 100_000) |> Integer.to_string()

      case WalletService.reset_password(user.mobile_number, new_password) |> IO.inspect() do
        {:ok, updated_user} ->
          # Send SMS with new password
          message_text =
            "Your FDH wallet password has been reset. Your new password is #{new_password}"

          # NotifySms.send_sms(user.mobile_number, message_text)
          ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
            msisdn: user.mobile_number,
            message: message_text
          })

        {:error, _changeset} ->
          # Handle error case but still return same message to prevent enumeration
          nil
      end

      json(conn, %{
        "message" =>
          "If your mobile number is in our system, you will receive instructions to reset your password shortly.",
        "status" => true
      })
    else
      # Return same message even if email not found to prevent email enumeration
      json(conn, %{
        "message" =>
          "If your mobile number is in our system, you will receive instructions to reset your password shortly.",
        "status" => true
      })
    end
  end

  def forgot_password(conn, _params) do
    json(conn, %{
      "message" => "Mobile_number is required",
      "status" => false
    })
  end

  def wallet_remote_details(conn, %{"mobile_number" => mobile_number} = params) do
    case WalletUser.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> json(error: "Wallet not found")

      wallet ->
        conn
        |> put_status(:ok)
        |> json(%{wallet: wallet} |> show_wallet)
    end
  end

  defp show_wallet(%{wallet: wallet}) do
    %{
      data: %{
        mobile_number: wallet.mobile_number,
        currency: wallet.currency,
        status: wallet.status,
        email: wallet.email,
        first_name: wallet.first_name,
        last_name: wallet.last_name,
        id_image: wallet.id_image,
        frozen: wallet.frozen,
        locked: wallet.locked,
        blocked: wallet.blocked,
        nickname: wallet.nickname,
        hidden: wallet.hidden,
        bank_code: wallet.bank_code,
        branch_code: wallet.branch_code,
        bank_name: wallet.bank_name
      },
      message: "Wallet details retrieved successfully",
      status: true
    }
  end
end
