defmodule ServiceManagerWeb.Api.FormV2Schema do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :page_id, :name, :order, :active, :submit_to, :route_id, :app_view]}

  schema "mobile_forms_v2" do
    field :name, :string
    field :order, :integer, default: 0
    field :active, :boolean, default: true
    field :submit_to, :string
    field :route_id, :string
    field :app_view, :boolean, default: true

    belongs_to :page, ServiceManagerWeb.Api.PageSchema, type: Ecto.UUID
    has_many :fields, ServiceManagerWeb.Api.FormFieldV2Schema, foreign_key: :form_id

    timestamps()
  end

  def changeset(form, attrs) do
    form
    |> cast(attrs, [:name, :order, :active, :submit_to, :route_id, :page_id, :app_view])
    |> validate_required([:name, :page_id])
    |> validate_length(:name, min: 1, max: 100)
    |> validate_length(:submit_to, max: 500)
    |> validate_length(:route_id, max: 100)
    |> validate_number(:order, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:page_id)
    |> unique_constraint([:page_id, :name], name: :mobile_forms_v2_unique_page_name)
  end
end

