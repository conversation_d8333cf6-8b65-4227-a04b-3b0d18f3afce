defmodule ServiceManagerWeb.ActiveDevicesController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Contexts.ActiveDevicesContext
  alias ServiceManager.Schemas.ActiveDevice, as: Devices

  action_fallback ServiceManagerWeb.FallbackController

  defp sms(number, message) do
    ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
      msisdn: number,
      message: message
    })
  end

  defp success(data, message \\ "Successfully pulled devices") do
    %{
      data: data,
      message: message,
      status: true
    }
  end

  defp success_with_allow_multi_session(data, user, message \\ "Successfully pulled devices") do
    %{
      allow_multi_session: user.allow_multi_session,
      data: data,
      message: message,
      status: true
    }
  end

  defp failed(message) do
    %{
      data: %{
        error: message
      },
      message: message,
      status: false
    }
  end

  def device(conn, _params) do
    case Devices.find_by(device_id: conn.params["device_id"], user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        response = device |> success("Device details")
        json(conn, response)
    end
  end

  def wallet_device(conn, _params) do
    case Devices.find_by(
           device_id: conn.params["device_id"],
           wallet_user_id: conn.assigns.user.id
         ) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        response = device |> success("Device details")
        json(conn, response)
    end
  end

  def list_devices(conn, _params) do
    case Devices.where(user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("No devices found"))

      devices ->
        device_list =
          devices
          |> success_with_allow_multi_session(conn.assigns.user)

        json(conn, device_list)
    end
  end

  def list_wallet_devices(conn, _params) do
    case Devices.where(wallet_user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("No devices found"))

      devices ->
        device_list =
          devices
          |> success_with_allow_multi_session(conn.assigns.user)

        json(conn, device_list)
    end
  end

  def enable_device(conn, params) do
    case Devices.find_by(device_id: conn.params["device_id"], user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.update(enabled: true)

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device enabled successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to enable device"))
        end
    end
  end

  def enable_wallet_device(conn, params) do
    case Devices.find_by(device_id: conn.params["device_id"], user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.update(enabled: true)

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device enabled successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to enable device"))
        end
    end
  end

  def disable_device(conn, params) do
    case Devices.find_by(device_id: conn.params["device_id"], user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.update(enabled: false)

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device disabled successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to enable device"))
        end
    end
  end

  def disable_wallet_device(conn, params) do
    case Devices.find_by(
           device_id: conn.params["device_id"],
           wallet_user_id: conn.assigns.user.id
         ) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.update(enabled: false)

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device disabled successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to enable device"))
        end
    end
  end

  def discard_device(conn, _params) do
    case Devices.find_by(device_id: conn.params["device_id"], user_id: conn.assigns.user.id) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.delete()

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device removed successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to remove device"))
        end
    end
  end

  def discard_wallet_device(conn, _params) do
    case Devices.find_by(
           device_id: conn.params["device_id"],
           wallet_user_id: conn.assigns.user.id
         ) do
      nil ->
        json(conn, failed("Device not found"))

      device ->
        updated_device = device |> Devices.delete()

        case updated_device do
          {:ok, pulled_device} ->
            response = pulled_device |> success("Device removed successfully")
            json(conn, response)

          {:error, _error} ->
            json(conn, failed("Failed to remove device"))
        end
    end
  end

  # -----------------------------------------------------------------------------------------------------

  def get_data_by_id(conn, %{"id" => id}) do
    result =
      ActiveDevicesContext.get_data!(id)
      |> api_json_format(true, "query completed successfully")

    conn
    |> put_status(:ok)
    |> json(result)
  rescue
    Ecto.NoResultsError ->
      conn
      |> put_status(:not_found)
      |> json(api_json_format(false, "Query Failed (not_found)"))
  end

  def index(conn, params) do
    result =
      ActiveDevicesContext.list(params)
      |> api_json_format(true, "query completed successfully")

    conn
    |> put_status(:ok)
    |> json(result)
  end

  def run_command(conn, %{"command" => command} = params) do
    run(params)
    |> case do
      {:ok, _} ->
        conn
        |> put_status(:ok)
        |> json(api_json_format(true, "#{command} command completed successfully"))

      {:error, :not_found = reason} ->
        conn
        |> put_status(reason)
        |> json(api_json_format(false, "#{command} command failed (#{reason})"))

      {:error, reason} ->
        conn
        |> put_status(:forbidden)
        |> json(api_json_format(false, "#{command} command failed (#{reason})"))
    end
  end

  defp run(%{"command" => command, "id" => id}) when command in ~w(delete DELETE Delete) do
    id
    |> ActiveDevicesContext.get_data!()
    |> case do
      nil -> {:error, :not_found}
      device -> ActiveDevicesContext.delete_active_device(device)
    end
  rescue
    Ecto.NoResultsError -> {:error, :not_found}
  end

  defp run(%{"command" => command, "id" => id}) when command in ~w(block BLOCK Block) do
    id
    |> ActiveDevicesContext.get_data!()
    |> case do
      nil -> {:error, :not_found}
      device -> ActiveDevicesContext.update_data(device)
    end
  rescue
    Ecto.NoResultsError -> {:error, :not_found}
  end

  defp run(_params), do: {:error, :unkown_command}
end
