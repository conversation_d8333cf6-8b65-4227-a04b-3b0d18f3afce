defmodule ServiceManagerWeb.MemorableWordController do
  use ServiceManagerWeb, :controller

  alias ServiceManagerWeb.Services.Local.MemorableWordService
  alias ServiceManager.Accounts.User, as: Account

  # Set memorable word for user account
  def set(conn, %{"memorable_word" => memorable_word}) do
    user_id = conn.assigns.user.id

    case MemorableWordService.set_memorable_word(user_id, memorable_word) do
      {:ok, _user} ->
        conn
        |> put_status(:ok)
        |> json(%{
          message: "Memorable word set successfully",
          status: true,
          data: %{
            memorable_word_hint: memorable_word |> String.slice(-3, 3)
          }
        })

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to set memorable word",
          status: false,
          data: %{
            memorable_word_hint: nil
          }
        })
    end
  end

  def verify(conn, %{"memorable_word" => memorable_word, "email" => email}) do
    user = Account.find_by(email: email)

    case MemorableWordService.verify_memorable_word(user.id, memorable_word) do
      {:ok, :verified} ->
        conn
        |> put_status(:ok)
        |> json(%{
          message: "Memorable word verified successfully",
          status: true,
          data: %{}
        })

      {:error, :invalid_memorable_word} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{
          message: "Invalid memorable word",
          status: false,
          data: %{}
        })

      {:error, :user_not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          message: "User not found",
          status: false,
          data: %{}
        })
    end
  end
end
