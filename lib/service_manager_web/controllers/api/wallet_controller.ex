defmodule ServiceManagerWeb.WalletController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Service.WalletTransactionService, as: AccountTransactionService

  alias ServiceManagerWeb.Api.Services.Remote.WalletLookupRemoteService,
    as: RemoteAccountslLookupWallet

  alias ServiceManagerWeb.Api.Services.Remote.WalletCreateRemoteService,
    as: RemoteAccountsCreateWallet

  alias ServiceManager.WalletAccounts.WalletUser, as: Wallet
  alias ServiceManagerWeb.Api.Services.Local.CrossTransfersService
  alias ServiceManagerWeb.WalletJSON

  action_fallback ServiceManagerWeb.FallbackController

  def index(conn, _params) do
    render(conn, :wallet, wallet: %{})
  end

  def new_wallet_depricated(conn, params) do
    case params
         |> Map.put("email", conn.assigns.user.email)
         |> Map.put("customer_number", conn.assigns.user.customer_no)
         |> Map.put("phone_number", conn.assigns.user.phone_number)
         |> RemoteAccountsCreateWallet.create_wallet() do
      {:ok, res} ->
        conn
        |> put_status(:created)
        |> render(:wallet, wallet: res)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def lookup_wallet(conn, params) do
    case params
         |> Map.put("phone_number", conn.assigns.user.phone_number)
         |> RemoteAccountslLookupWallet.lookup_wallet() do
      {:ok, res} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: res)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def new_wallet(conn, params) do
    render(conn, :wallet, wallet: params)
  end

  def get_wallet(conn, _params) do
    case Wallet.find_by(mobile_number: conn.assigns.user.mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: wallet)
    end
  end

  def get_wallet_third_party(conn, %{"mobile_number" => mobile_number} = params) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: wallet)
    end
  end

  def get_balance(conn, params) do
    render(conn, :wallet, wallet: params)
  end

  def get_transactions(conn, _params) do
    mobile_number = conn.assigns.user.mobile_number

    case AccountTransactionService.get_account_transactions(mobile_number) do
      {:ok, transactions} ->
        conn
        |> put_status(:ok)
        |> render(:transactions, transactions: transactions)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def topup(conn, %{"amount" => amount, "mobile_number" => account_number} = params) do
    description = Map.get(params, "description", "Credit")

    with {updated_account, transaction} <-
           AccountTransactionService.credit_account(account_number, amount, description) do
      conn
      |> put_status(:ok)
      |> render(:account_with_transaction, account: updated_account, transaction: transaction)
    end
  end

  def withdraw(conn, %{"amount" => amount} = params) do
    account_number = conn.assigns.user.mobile_number
    description = Map.get(params, "description", "Debit")

    with {updated_account, transaction} <-
           AccountTransactionService.debit_account(account_number, amount, description) do
      conn
      |> put_status(:ok)
      |> render(:account_with_transaction, account: updated_account, transaction: transaction)
    else
      {:error, :insufficient_funds} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Insufficient funds")

      {:error, %{} = error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: error)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def transfer_to_account(conn, %{"to_account" => to_account, "amount" => amount} = params) do
    from_wallet = conn.assigns.user.mobile_number

    case Wallet.find_by(mobile_number: from_wallet) do
      nil ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Source wallet not found")

      wallet when wallet.status != "ACTIVE" ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Source wallet is not active")

      _wallet ->
        transfer_params = %{
          "from_wallet" => from_wallet,
          "to_account" => to_account,
          "amount" => amount
        }

        case CrossTransfersService.wallet_to_account(transfer_params) do
          {:ok, result} ->
            conn
            |> put_status(:created)
            |> render(:show, transaction: {:ok, result})

          {:error, error} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: error)
        end
    end
  end

  def third_party_transfer_to_account(
        conn,
        %{"from_wallet" => from_wallet, "to_account" => to_account, "amount" => amount} = params
      ) do
    # from_wallet = conn.assigns.user.mobile_number

    case Wallet.find_by(mobile_number: from_wallet) do
      nil ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Source wallet not found")

      wallet when wallet.status != "ACTIVE" ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Source wallet is not active")

      _wallet ->
        transfer_params = %{
          "from_wallet" => from_wallet,
          "to_account" => to_account,
          "amount" => amount
        }

        case CrossTransfersService.wallet_to_account(transfer_params) do
          {:ok, result} ->
            conn
            |> put_status(:created)
            |> render(:show, transaction: {:ok, result})

          {:error, error} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: error)
        end
    end
  end

  def transfer_funds(conn, %{"to_account" => to_account, "amount" => amount} = params) do
    from_account = conn.assigns.user.mobile_number
    description = Map.get(params, "description", "Transfer")

    case AccountTransactionService.transfer_funds(from_account, to_account, amount, description) do
      {:ok, {updated_from_account, updated_to_account, transaction}} ->
        conn
        |> put_status(:ok)
        |> render(:show,
          from: updated_from_account,
          to: updated_to_account,
          transaction: transaction
        )

      {:error, :insufficient_funds} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Insufficient funds")

      {:error, %{} = error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: error)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def third_party_transfer_funds(
        conn,
        %{"from_account" => from_account, "to_account" => to_account, "amount" => amount} = params
      ) do
    # from_account = conn.assigns.user.mobile_number
    description = Map.get(params, "description", "Wallet to Wallet Transfer")

    case AccountTransactionService.transfer_funds(from_account, to_account, amount, description) do
      {:ok, {updated_from_account, updated_to_account, transaction}} ->
        conn
        |> put_status(:ok)
        |> render(:show,
          from: updated_from_account,
          to: updated_to_account,
          transaction: transaction
        )

      {:error, :insufficient_funds} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Insufficient funds")

      {:error, %{} = error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: error)

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def update_nickname(conn, %{"nickname" => nickname}) do
    case conn.assigns.user
         |> Wallet.update(%{nickname: nickname}) do
      {:error, error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: error)

      {:ok, wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: wallet)
    end
  end

  def update_wallet(conn, %{"id_image" => base64_image} = params) do
    user = conn.assigns.user
    file_name = "id_image_#{user.id}_#{:os.system_time(:millisecond)}"

    # Process image first
    case ServiceManager.Services.ImageService.save_base64_image(base64_image, file_name) do
      {:ok, file_uri} ->
        # Remove image from params and add the file URI
        update_params =
          params
          |> Map.delete("image")
          |> Map.put("id_image", file_uri)

        # Update wallet with all params including the image URI
        case Wallet.update(user, update_params) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, error} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: error)
        end

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def update_wallet(conn, params) do
    # Handle updates without image
    case conn.assigns.user
         |> Wallet.update(params) do
      {:error, error} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: error)

      {:ok, wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: wallet)
    end
  end

  def close_wallet(conn, params) do
    render(conn, :wallet, wallet: params)
  end

  def remove_wallet(conn, %{"mobile_number" => account_number} = _params) do
    case Wallet.find_by(mobile_number: account_number) |> Wallet.delete() do
      {:ok, _wallet} ->
        conn
        |> put_status(:ok)
        |> render(:error, error: "Wallet removed successfully")

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def freeze_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, frozen: true) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to freeze wallet")
        end
    end
  end

  def unfreeze_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{frozen: false}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to unfreeze wallet")
        end
    end
  end

  def block_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{blocked: true}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to block wallet")
        end
    end
  end

  def unblock_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{blocked: false}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to unblock wallet")
        end
    end
  end

  def lock_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{locked: true}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to lock wallet")
        end
    end
  end

  def unlock_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{locked: false}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to unlock wallet")
        end
    end
  end

  def update_alerts_status(conn, %{"enable_alerts" => enable_alerts}) do
    case conn.assigns.user
         |> Wallet.update(%{enable_alerts: enable_alerts}) do
      {:ok, updated_wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: updated_wallet)

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to update alerts status")
    end
  end

  def update_large_transaction_alert(conn, %{
        "large_transaction_alert" => enabled,
        "large_transaction_threshold" => threshold
      }) do
    case conn.assigns.user
         |> Wallet.update(%{
           large_transaction_alert: enabled,
           large_transaction_threshold: threshold
         }) do
      {:ok, updated_wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: updated_wallet)

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to update large transaction alert settings")
    end
  end

  def update_low_balance_alert(conn, %{
        "low_balance_alert" => enabled,
        "low_balance_threshold" => threshold
      }) do
    case conn.assigns.user
         |> Wallet.update(%{
           low_balance_alert: enabled,
           low_balance_threshold: threshold
         }) do
      {:ok, updated_wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: updated_wallet)

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to update low balance alert settings")
    end
  end

  def update_suspicious_activity_alert(conn, %{
        "suspicous_activity_alert" => enabled,
        "suspicous_activity_seconds_between_transactions" => seconds
      }) do
    case conn.assigns.user
         |> Wallet.update(%{
           suspicous_activity_alert: enabled,
           suspicous_activity_seconds_between_transactions: seconds
         }) do
      {:ok, updated_wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: updated_wallet)

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to update suspicious activity alert settings")
    end
  end

  def update_bank_details(conn, %{
        "bank_code" => bank_code,
        "branch_code" => branch_code,
        "bank_name" => bank_name
      }) do
    case conn.assigns.user
         |> Wallet.update(%{
           bank_code: bank_code,
           branch_code: branch_code,
           bank_name: bank_name
         }) do
      {:ok, updated_wallet} ->
        conn
        |> put_status(:ok)
        |> render(:wallet, wallet: updated_wallet)

      {:error, _changeset} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Failed to update bank details")
    end
  end

  def hide_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{hidden: true}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to hide wallet")
        end
    end
  end

  def unhide_wallet(conn, %{"mobile_number" => mobile_number}) do
    case Wallet.find_by(mobile_number: mobile_number) do
      nil ->
        conn
        |> put_status(:not_found)
        |> render(:error, error: "Wallet not found")

      wallet ->
        case Wallet.update(wallet, %{hidden: false}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to unhide wallet")
        end
    end
  end

  def update_id_image(conn, %{"id_image" => base64_image}) do
    user = conn.assigns.user
    file_name = "id_image_#{user.id}_#{:os.system_time(:millisecond)}"

    case ServiceManager.Services.ImageService.save_base64_image(base64_image, file_name) do
      {:ok, file_uri} ->
        case Wallet.update(user, %{id_image: file_uri}) do
          {:ok, updated_wallet} ->
            conn
            |> put_status(:ok)
            |> render(:wallet, wallet: updated_wallet)

          {:error, _changeset} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: "Failed to update ID image")
        end

      {:error, reason} ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: reason)
    end
  end

  def reverse_third_party_transfer_to_account(
        conn,
        %{
          "from_wallet" => from_wallet,
          "to_account" => to_account,
          "amount" => amount,
          "original_transaction_id" => original_transaction_id
        } = params
      ) do
    case Wallet.find_by(mobile_number: from_wallet) do
      nil ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Destination wallet not found")

      wallet when wallet.status != "ACTIVE" ->
        conn
        |> put_status(:unprocessable_entity)
        |> render(:error, error: "Destination wallet is not active")

      _wallet ->
        transfer_params = %{
          # Original from_wallet becomes from_account
          "from_account" => from_wallet,
          # Original to_account becomes to_wallet
          "to_wallet" => to_account,
          "amount" => amount,
          "description" => "Reversal for transaction #{original_transaction_id}",
          "original_transaction_id" => original_transaction_id
        }

        case CrossTransfersService.reverse_wallet_to_account_transfer(transfer_params) do
          {:ok, result} ->
            conn
            |> put_status(:created)
            |> render(:show, transaction: {:ok, result})

          {:error, error} ->
            conn
            |> put_status(:unprocessable_entity)
            |> render(:error, error: error)
        end
    end
  end
end
