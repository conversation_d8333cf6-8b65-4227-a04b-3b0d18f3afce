defmodule ServiceManagerWeb.Controllers.Api.Plugs.SyncWalletWithAccount do
  @moduledoc """
  Plug to synchronize wallet users with accounts.

  This plug checks if a wallet user's account_number is empty and if so:
  1. Gets the mobile_number from wallet_user
  2. Finds a matching User by phone_number
  3. If found, creates a new account using the customer_no
  4. Updates the wallet_user's account_number with the new account
  """

  import Plug.Conn
  import ServiceManager.Logging.FunctionTracker
  require Logger

  alias ServiceManager.Accounts.User
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Services.T24.Messages.CreateAccount
  alias ServiceManager.Repo

  def init(opts), do: opts

  def call(conn, _opts) do
    case conn.assigns[:user] do
      %WalletUser{} = wallet_user ->
        sync_account(conn, wallet_user)

      _ ->
        conn
    end
  end

  defp sync_account(conn, %WalletUser{account_number: account_number} = wallet_user)
       when is_nil(account_number) or account_number == "" do
    Logger.info(
      "SyncWalletWithAccount: Checking for existing account for wallet user #{wallet_user.id}"
    )

    case find_matching_user(wallet_user.mobile_number) do
      nil ->
        Logger.info(
          "SyncWalletWithAccount: No matching user found for mobile number #{wallet_user.mobile_number}"
        )

        conn

      user ->
        Logger.info(
          "SyncWalletWithAccount: Found matching user with customer_no #{user.customer_no}"
        )

        create_and_link_account(conn, wallet_user, user)
    end
  end

  defp sync_account(conn, _wallet_user), do: conn

  defp find_matching_user(mobile_number) do
    Repo.get_by(User, phone_number: mobile_number)
  end

  defp create_and_link_account(conn, wallet_user, user) do
    try do
      case CreateAccount.create_account(user.customer_no, wallet_user.currency || "MWK")
           |> IO.inspect(label: "NEW ACCOUNT") do
        {:ok, %{"account_number" => account_number}} when not is_nil(account_number) ->
          Logger.info(
            "SyncWalletWithAccount: Created new account #{account_number} for customer #{user.customer_no}"
          )

          case update_wallet_user_account(wallet_user, account_number) do
            {:ok, updated_wallet_user} ->
              Logger.info(
                "SyncWalletWithAccount: Updated wallet user #{wallet_user.id} with account number #{account_number}"
              )

              assign(conn, :user, updated_wallet_user)

            {:error, changeset} ->
              Logger.error(
                "SyncWalletWithAccount: Failed to update wallet user - #{inspect(changeset.errors)}"
              )

              conn
          end

        {:error, error} ->
          Logger.error("SyncWalletWithAccount: Failed to create account - #{inspect(error)}")
          conn

        _ ->
          Logger.error("SyncWalletWithAccount: Unexpected response from create_account service")
          conn
      end
    rescue
      _ -> conn
    end
  end

  defp update_wallet_user_account(wallet_user, account_number) do
    wallet_user
    |> WalletUser.update_wallet(%{account_number: account_number}, [])
    |> Repo.update()
  end
end
