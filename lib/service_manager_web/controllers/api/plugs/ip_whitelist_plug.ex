defmodule ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug do
  import Plug.Conn
  import Phoenix.Controller
  alias ServiceManager.Cache.IpWhitelistCache
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Settings.GeneralSettings

  def init(opts), do: opts

  def call(conn, _opts) do
    client_ip = get_client_ip(conn)

    # Start a Task to handle IP registration/update asynchronously
    Task.start(fn -> handle_ip_tracking(client_ip) end)

    # Check if IP open channel is enabled
    case get_ip_open_channel_setting() do
      true ->
        # Open channel - check only for explicitly blocked IPs
        case check_ip_whitelist(client_ip) do
          {:error, :blocked} ->
            conn
            |> put_status(:forbidden)
            |> json(%{error: "This IP address has been blocked"})
            |> halt()

          {:error, :expired} ->
            conn
            |> put_status(:forbidden)
            |> json(%{error: "IP whitelist entry has expired"})
            |> halt()

          _ ->
            conn
        end

      false ->
        # Closed channel - only allow whitelisted IPs
        case check_ip_whitelist(client_ip) do
          {:ok, entry} when not is_nil(entry) ->
            conn

          _ ->
            conn
            |> put_status(:forbidden)
            |> json(%{error: "IP address not whitelisted"})
            |> halt()
        end
    end
  end

  defp get_ip_open_channel_setting do
    case Repo.get_by(GeneralSettings, key: "general_settings") do
      # Default to open if setting doesn't exist
      nil ->
        true

      settings ->
        case settings.config do
          # Default to open if config is nil
          nil -> true
          # Default to open if setting is not specified
          config -> Map.get(config, :ip_open_channel, true)
        end
    end
  end

  defp get_client_ip(conn) do
    forwarded_for = get_req_header(conn, "x-forwarded-for")

    cond do
      # Check X-Forwarded-For header (common when behind a proxy)
      forwarded_for != [] ->
        forwarded_for
        |> hd()
        |> String.split(",")
        |> hd()
        |> String.trim()

      # Fall back to direct remote IP
      true ->
        conn.remote_ip
        |> :inet.ntoa()
        |> to_string()
    end
  end

  defp handle_ip_tracking(ip_address) do
    case IpWhitelistCache.get_ip(ip_address) do
      {:error, :not_found} ->
        # New IP - register it and reload cache
        {:ok, _entry} = IpWhitelistCache.register_ip(ip_address)
        IpWhitelistCache.reload_cache()

      {:ok, entry} ->
        # Known IP - update metrics if not blocked
        if entry.status != :inactive do
          update_access_metrics(entry)
        end
    end
  end

  defp check_ip_whitelist(ip_address) do
    case IpWhitelistCache.get_ip(ip_address) do
      {:error, :not_found} ->
        # Allow if IP is not in the list
        {:ok, nil}

      {:ok, entry} ->
        cond do
          entry.status == :inactive ->
            {:error, :blocked}

          has_expired?(entry) ->
            {:error, :expired}

          true ->
            {:ok, entry}
        end
    end
  end

  defp has_expired?(%{expiry_date: nil}), do: false

  defp has_expired?(entry) do
    DateTime.compare(entry.expiry_date, DateTime.utc_now()) == :lt
  end

  defp update_access_metrics(entry) do
    # Update metrics in database
    updated_entry =
      entry
      |> Ecto.Changeset.change(%{
        access_count: entry.access_count + 1,
        last_accessed_at: DateTime.utc_now() |> DateTime.truncate(:second)
      })
      |> ServiceManager.Repo.update!()

    # Update cache with new metrics
    :ets.insert(:ip_whitelist_cache, {entry.ip_address, updated_entry})
  end
end
