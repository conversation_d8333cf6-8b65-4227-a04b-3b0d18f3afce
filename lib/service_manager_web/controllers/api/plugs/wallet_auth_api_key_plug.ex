defmodule ServiceManagerWeb.Plugs.WalletAuthApiKeyPlug do
  import Plug.Conn
  alias ServiceManagerWeb.Api.Services.WalletAuthenticationService, as: AuthenticationService

  def init(default), do: default

  def call(conn, _default) do
    token = Map.get(conn.body_params, "api_key")

    case AuthenticationService.verify_api_key(token) do
      {:ok, user} ->
        # user = ServiceManager.Repo.preload(user, :accounts) plug
        if user.blocked == false do
          conn |> put_status(:ok) |> assign(:user, user)
        else
          conn
          |> put_status(:forbidden)
          |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
          |> Phoenix.Controller.json(%{
            "message" => "Profile not approved",
            "status" => false,
            "data" => %{}
          })
          |> halt()
        end

      {:error, error_info} ->
        conn
        |> put_status(:unauthorized)
        |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
        |> Phoenix.Controller.json(%{
          "message" => error_info,
          "status" => false,
          "data" => %{}
        })
        |> halt()
    end
  end
end
