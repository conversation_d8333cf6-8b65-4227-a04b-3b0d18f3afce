defmodule ServiceManagerWeb.Plugs.CheckUserDisabled do
  import Plug.Conn
  require Logger

  def init(default), do: default

  def call(conn, _default) do
    user = conn.assigns[:user]

    case user do
      nil ->
        Logger.info("CheckUserDisabled: No user found in assigns")
        conn

      %{disabled: true} ->
        Logger.warn("CheckUserDisabled: User #{user.id} is disabled")

        conn
        |> put_status(:forbidden)
        |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
        |> Phoenix.Controller.json(%{
          "message" => "Your account has been disabled. Please contact support for assistance.",
          "status" => false,
          "data" => %{}
        })
        |> halt()

      user ->
        Logger.info("CheckUserDisabled: User #{user.id} is active")
        conn
    end
  end
end
