defmodule ServiceManagerWeb.Plugs.ThirdPartyPlug do
  import Plug.Conn
  require Logger
  alias ServiceManagerWeb.Api.Services.ThirdPartyAuthService, as: AuthenticationService
  alias ServiceManagerWeb.Presence

  def init(default), do: default

  def call(conn, _default) do
    token = Map.get(conn.body_params, "api_key")

    case AuthenticationService.verify_api_key(token) do
      {:ok, user} ->
        if user.approved == true do
          # Track presence first to ensure it's always updated
          # Get the API key record
          api_key =
            ServiceManager.ThirdParty.ThirdPartyApiKey
            |> ServiceManager.Repo.get_by(api_key: token)

          track_user_presence(conn, user, api_key)
          conn |> put_status(:ok) |> assign(:user, user)
        else
          conn
          |> put_status(:forbidden)
          |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
          |> Phoenix.Controller.json(%{
            "message" => "vendor not approved",
            "status" => false,
            "data" => %{}
          })
          |> halt()
        end

      {:error, error_info} ->
        conn
        |> put_status(:unauthorized)
        |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
        |> Phoenix.Controller.json(%{
          "message" => error_info,
          "status" => false,
          "data" => %{}
        })
        |> halt()
    end
  end

  defp track_user_presence(conn, user, api_key) do
    device_id = get_device_id(conn)
    now = DateTime.utc_now()

    # Track only in Presence
    device = %{
      device_id: device_id,
      last_seen_at: now,
      user_id: user.id,
      third_party_api_key_id: user.id,
      user_type: "third_party",
      api_key: api_key.api_key,
      description: api_key.description
    }

    Presence.update(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )

    Presence.track(
      self(),
      "users:presence",
      "user:#{user.id}",
      device
    )
  end

  defp get_device_id(conn) do
    # Generate a unique device ID based on user agent and IP
    user_agent = get_req_header(conn, "user-agent") |> List.first() || "unknown"
    remote_ip = conn.remote_ip |> :inet.ntoa() |> to_string()
    :crypto.hash(:sha256, "#{user_agent}:#{remote_ip}") |> Base.encode16()
  end
end
