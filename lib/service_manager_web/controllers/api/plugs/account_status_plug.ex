defmodule ServiceManagerWeb.Plugs.AccountStatusPlug do
  import Plug.Conn
  require <PERSON><PERSON>
  alias ServiceManager.Accounts.FundAccounts
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Repo
  import Ecto.Query, warn: false

  @account_keys ["from_account", "to_account", "to_wallet", "from_wallet"]

  def init(default), do: default

  def call(conn, _default) do
    params = conn.body_params

    # Extract account numbers from params for specified keys
    account_numbers =
      @account_keys
      |> Enum.map(fn key -> Map.get(params, key) end)
      |> Enum.reject(&is_nil/1)

    case validate_accounts(account_numbers) do
      {:ok, :no_accounts} ->
        # No accounts found in params, continue execution
        conn

      {:ok, :single_account_ok} ->
        # Single account found and not frozen, continue execution
        conn

      {:ok, :multiple_accounts_ok} ->
        # Multiple accounts found and none are frozen, continue execution
        conn

      {:error, message} ->
        # Account(s) frozen or other error, halt execution
        conn
        |> put_status(:forbidden)
        |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
        |> Phoenix.Controller.json(%{
          "message" => message,
          "status" => false,
          "data" => %{}
        })
        |> halt()
    end
  end

  defp validate_accounts([]), do: {:ok, :no_accounts}

  defp validate_accounts(account_numbers) do
    # Get all fund accounts
    fund_accounts =
      account_numbers
      |> Enum.map(fn number ->
        Repo.get_by(FundAccounts, account_number: number)
      end)
      |> Enum.reject(&is_nil/1)

    # Get all wallet accounts
    wallet_accounts =
      account_numbers
      |> Enum.map(fn number ->
        Repo.get_by(WalletUser, mobile_number: number)
      end)
      |> Enum.reject(&is_nil/1)

    # Combine all found accounts
    all_accounts = fund_accounts ++ wallet_accounts

    case all_accounts do
      [] ->
        {:ok, :no_accounts}

      [single_account] ->
        check_single_account_status(single_account)

      multiple_accounts ->
        check_multiple_accounts_status(multiple_accounts)
    end
  end

  defp check_single_account_status(account) do
    cond do
      is_frozen?(account) ->
        {:error, "Account not available"}

      true ->
        {:ok, :single_account_ok}
    end
  end

  defp check_multiple_accounts_status(accounts) do
    frozen_accounts = Enum.filter(accounts, &is_frozen?/1)

    case frozen_accounts do
      [] ->
        {:ok, :multiple_accounts_ok}

      [_ | _] ->
        {:error, "One or more accounts are not available"}
    end
  end

  defp is_frozen?(%FundAccounts{} = account) do
    account.is_frozen || account.frozen
  end

  defp is_frozen?(%WalletUser{} = account) do
    account.frozen
  end
end
