defmodule ServiceManagerWeb.Plugs.AuthPlug do
  import Plug.Conn
  use ServiceManagerWeb, :tracker
  require Logger
  alias ServiceManagerWeb.Api.Services.AuthenticationService
  alias ServiceManager.Processors.UserBalanceSyncProcessor
  alias ServiceManager.Contexts.UserTrackingContext

  # track do
  def init(default), do: default
  # end

  track do
    def call(conn, _default) do
      token = get_req_header(conn, "access_token") |> List.first() || Map.get(conn.body_params, "access_token")
    
      Logger.info("AuthPlug: Verifying token for request")
      # chain_id = get_req_header(conn, "x-chain-id") |> List.first() || "#{Ecto.UUID.generate()}"

      case AuthenticationService.verify_token(token) do
        {:ok, user} ->
          user = ServiceManager.Repo.preload(user, :accounts)

          if user.approved do
            Logger.info("AuthPlug: User #{user.id} successfully authenticated")

            # Try to sync balance with 4-second timeout
            try do
              case UserBalanceSyncProcessor.sync_with_timeout(user.id) do
                {:ok, balance} ->
                  Logger.info("AuthPlug: Successfully synced balance for user #{user.username}")
                  # Update user struct with new balance
                  user = %{user | account_balance: balance}

                {:background, :timeout} ->
                  Logger.info(
                    "AuthPlug: Balance sync timeout for user #{user.id}, continuing with existing balance"
                  )
              end
            rescue
              e ->
                Logger.info(
                  "AuthPlug: Error syncing balance for user #{user.username} Skipping balance update: #{inspect(e)}"
                )
            end

            # Track presence first to ensure it's always updated
            track_user_presence(conn, user.id)
            conn |> put_status(:ok) |> assign(:user, user)
          else
            Logger.warn("AuthPlug: User #{user.id} not approved")

            conn
            |> put_status(:forbidden)
            |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
            |> Phoenix.Controller.json(%{
              "message" => "Pending verification.",
              "status" => false,
              "data" => %{}
            })
            |> halt()
          end

        {:error, error_info} ->
          Logger.warn("AuthPlug: Authentication failed - #{inspect(error_info)}")

          conn
          |> put_status(:unauthorized)
          |> Phoenix.Controller.put_view(ServiceManagerWeb.ErrorView)
          |> Phoenix.Controller.json(%{
            "message" => "Please log in again to continue.",
            "status" => false,
            "data" => %{}
          })
          |> halt()
      end
    end
  end

  defp track_user_presence(conn, user_id) do
    if not is_integer(user_id), do: raise("user_id must be an integer")
    device_id = get_header_or_generate(conn, "device-id")
    now = DateTime.utc_now()

    # Track only in Presence
    device = %{
      device_id: device_id,
      last_seen_at: now,
      user_id: user_id,
      user_type: "mobile_banking"
    }

    ServiceManagerWeb.Presence.update(
      self(),
      "users:presence",
      "user:#{user_id}",
      device
    )

    ServiceManagerWeb.Presence.track(
      self(),
      "users:presence",
      "user:#{user_id}",
      device
    )

    # Track user position in the application
    track_user_position(conn, user_id, device_id)
  end

  # Track user position through the application
  defp track_user_position(conn, user_id, device_id) do
    # Get tracking information from headers
    tracking_id = get_req_header(conn, "tracking-id") |> List.first()
    screen_name = get_req_header(conn, "screen-name") |> List.first() || "unknown"

    # Extract path and action from the request
    path = conn.request_path
    action = conn.private[:phoenix_action] || "unknown"
    section = conn.private[:phoenix_controller] || "unknown"
    section = section |> to_string() |> String.split(".") |> List.last() || "unknown"

    # Get user agent and IP address
    user_agent = get_req_header(conn, "user-agent") |> List.first() || "unknown"
    ip_address = conn.remote_ip |> :inet.ntoa() |> to_string()

    # Create tracking entry
    tracking_params = %{
      "user_id" => user_id,
      "page_path" => path,
      "page_name" => screen_name,
      "section" => section,
      "action" => action,
      "device_id" => device_id,
      "ip_address" => ip_address,
      "user_agent" => user_agent,
      "session_id" => tracking_id || conn.private[:plug_session_id] || "api-#{device_id}",
      "status" => "active"
    }

    # Create tracking record - don't halt the request if tracking fails
    case UserTrackingContext.create_entry_tracking(tracking_params) do
      {:ok, _tracking} ->
        Logger.debug("AuthPlug: Successfully tracked user position for user #{user_id}")

      {:error, changeset} ->
        Logger.warn("AuthPlug: Failed to track user position for user #{user_id}: #{inspect(changeset.errors)}")
    end
  end

  # Get header value or generate a fallback
  defp get_header_or_generate(conn, header_name) do
    get_req_header(conn, header_name) |> List.first() || generate_device_id(conn)
  end

  # Generate a device ID if not provided in headers
  defp generate_device_id(conn) do
    user_agent = get_req_header(conn, "user-agent") |> List.first() || "unknown"
    remote_ip = conn.remote_ip |> :inet.ntoa() |> to_string()
    :crypto.hash(:sha256, "#{user_agent}:#{remote_ip}") |> Base.encode16()
  end


end
