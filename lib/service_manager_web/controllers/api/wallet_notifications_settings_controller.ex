defmodule ServiceManagerWeb.Api.WalletNotificationSettingsController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Repo
  alias ServiceManagerWeb.Endpoint

  action_fallback ServiceManagerWeb.FallbackController

  def show(conn, _params) do
    user = conn.assigns.user
    render(conn, :show, user: user)
  end

  def update(conn, %{"settings" => settings}) do
    user = conn.assigns.user

    changeset =
      WalletUser.notification_settings_changeset(user, %{
        "email_notifications" => settings["email_notifications"],
        "sms_notifications" => settings["sms_notifications"],
        "push_notifications" => settings["push_notifications"]
      })

    case Repo.update(changeset) do
      {:ok, updated_user} ->
        render(conn, :show, user: updated_user)

      {:error, _changeset} ->
        conn
        |> put_status(:bad_request)
        |> render(:error, error: "Failed to update notification settings")
    end
  end

  def update(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> render(:error, error: "Invalid request parameters")
  end

  defp check_notification_limits(user) do
    now = DateTime.utc_now()

    # Reset counters if needed
    {should_reset, updated_user} =
      case user.last_notification_reset do
        nil ->
          {true, user}

        last_reset ->
          cond do
            Date.diff(DateTime.to_date(now), DateTime.to_date(last_reset)) >= 30 ->
              {true, user}

            true ->
              {false, user}
          end
      end

    user =
      if should_reset do
        {:ok, updated_user} =
          Repo.update(
            WalletUser.changeset(user, %{
              notification_count: 0,
              last_notification_reset: now
            })
          )

        updated_user
      else
        user
      end

    # Check limits
    cond do
      user.notification_count >= user.monthly_notification_limit ->
        {:error, "Monthly notification limit reached"}

      user.notification_count >= user.weekly_notification_limit ->
        {:error, "Weekly notification limit reached"}

      user.notification_count >= user.daily_notification_limit ->
        {:error, "Daily notification limit reached"}

      true ->
        {:ok, user}
    end
  end

  defp increment_notification_count(user) do
    {:ok, updated_user} =
      Repo.update(
        WalletUser.changeset(user, %{
          notification_count: user.notification_count + 1
        })
      )

    updated_user
  end

  def send_notification(conn, %{"type" => type, "content" => content, "title" => title} = params) do
    user = conn.assigns.user
    timestamp = DateTime.utc_now()

    # Split type by | and validate each type
    notification_types = String.split(type, "|")
    valid_types = ["sms", "push", "email"]

    with true <- Enum.all?(notification_types, &(&1 in valid_types)),
         {:ok, user} <- check_notification_limits(user) do
      # Filter enabled notification types for the user
      enabled_notifications =
        notification_types
        |> Enum.filter(fn type ->
          case type do
            "email" -> user.email_notifications
            "sms" -> user.sms_notifications
            "push" -> user.push_notifications
          end
        end)

      if Enum.empty?(enabled_notifications) do
        conn
        |> put_status(:bad_request)
        |> render(:error, error: "All specified notification types are disabled for this user")
      else
        # Create and broadcast a notification for each enabled type
        notifications =
          Enum.map(enabled_notifications, fn notification_type ->
            notification = %{
              title: title,
              type: notification_type,
              content: content,
              timestamp: timestamp
            }

            case notification_type do
              "email" ->
                []

              # ServiceManager.Notifications.EmailNotification.deliver(user, content)
              "sms" ->
                ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
                  msisdn: user.mobile_number,
                  message: content
                })

              "push" ->
                # Broadcast to the channel for push notifications
                Endpoint.broadcast(
                  "wallet_notifications:#{user.session_id}",
                  "notification",
                  Map.put(notification, "delivery_type", "push")
                )
            end

            notification
          end)

        # Increment notification count after successful send
        _updated_user = increment_notification_count(user)

        conn
        |> put_status(:ok)
        |> render(:send, notifications: notifications)
      end
    else
      {:error, message} ->
        conn
        |> put_status(:bad_request)
        |> render(:error, error: message)

      false ->
        conn
        |> put_status(:bad_request)
        |> render(:error, error: "Invalid notification type. Must be one of: sms, push, email")
    end
  end

  def send_notification(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> render(:error, error: "Invalid notification parameters")
  end
end
