defmodule ServiceManagerWeb.WalletUserSessionController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.WalletAccounts
  alias ServiceManagerWeb.WalletUserAuth

  def create(conn, %{"_action" => "registered"} = params) do
    create(conn, params, "Account created successfully!")
  end

  def create(conn, %{"_action" => "password_updated"} = params) do
    conn
    |> put_session(:wallet_user_return_to, ~p"/walletusers/settings")
    |> create(params, "Password updated successfully!")
  end

  def create(conn, params) do
    create(conn, params, "Welcome back!")
  end

  defp create(conn, %{"wallet_user" => wallet_user_params}, info) do
    %{"email" => email, "password" => password} = wallet_user_params

    if wallet_user = WalletAccounts.get_wallet_user_by_email_and_password(email, password) do
      conn
      |> put_flash(:info, info)
      |> WalletUserAuth.log_in_wallet_user(wallet_user, wallet_user_params)
    else
      # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
      conn
      |> put_flash(:error, "Invalid email or password")
      |> put_flash(:email, String.slice(email, 0, 160))
      |> redirect(to: ~p"/walletusers/log_in")
    end
  end

  def delete(conn, _params) do
    conn
    |> put_flash(:info, "Logged out successfully.")
    |> WalletUserAuth.log_out_wallet_user()
  end
end
