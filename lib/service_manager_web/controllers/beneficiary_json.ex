defmodule ServiceManagerWeb.BeneficiariesJSON do
  alias ServiceManager.Schemas.Accounts.SchemaBeneficiary

  def index(%{
        beneficiaries: beneficiaries,
        page_number: page_number,
        page_size: page_size,
        total_pages: total_pages,
        total_entries: total_entries
      }) do
    %{
      message: "Beneficiaries fetched successfully",
      status: true,
      data: %{
        beneficiaries: for(beneficiary <- beneficiaries, do: data(beneficiary)),
        pagination: %{
          page_number: page_number,
          page_size: page_size,
          total_pages: total_pages,
          total_entries: total_entries
        }
      }
    }
  end

  def create(%{
        beneficiaries: beneficiaries,
        page_number: page_number,
        page_size: page_size,
        total_pages: total_pages,
        total_entries: total_entries
      }) do
    %{
      message: "Beneficiaries fetched successfully",
      status: true,
      data: %{
        beneficiaries: for(beneficiary <- beneficiaries, do: data(beneficiary)),
        pagination: %{
          page_number: page_number,
          page_size: page_size,
          total_pages: total_pages,
          total_entries: total_entries
        }
      }
    }
  end

  def show(%{beneficiary: beneficiary}) do
    %{
      message: "Beneficiary fetched successfully",
      status: true,
      data: %{
        beneficiary: data(beneficiary)
      }
    }
  end

  def data(%SchemaBeneficiary{} = beneficiary) do
    %{
      id: beneficiary.id,
      name: beneficiary.name,
      account_number: beneficiary.account_number,
      bank_code: beneficiary.bank_code,
      currency: beneficiary.currency,
      description: beneficiary.description,
      status: beneficiary.status,
      is_default: beneficiary.is_default,
      created_at: beneficiary.inserted_at,
      updated_at: beneficiary.updated_at
    }
  end

  def error(%{message: message}) do
    %{
      message: message,
      status: false,
      data: %{
        errors: message
      }
    }
  end
end
