defmodule ServiceManagerWeb.LoansController do
  use ServiceManagerWeb, :controller

  alias ServiceManager.Repo
  alias ServiceManager.Context.LoanMgt
  alias ServiceManager.LoanMgt.Schemas.Loan
  alias ServiceManager.LoanMgt.Helpers.Utils
  alias ServiceManager.Transactions.Transaction
  alias ServiceManager.LoanMgt.Schemas.Customer
  alias ServiceManager.LoanMgt.Helpers.ApiHelpers
  alias ServiceManager.LoanMgt.Schemas.LoanCharges
  alias ServiceManager.LoanMgt.Schemas.Transactions
  alias ServiceManager.LoanMgt.Helpers.ParamsValidator
  alias ServiceManagerWeb.Api.Services.Remote.FundTransferFromRemoteService

  # ========== LOAN REPORT API ===========

  def report(conn, _params) do
    user = conn.assigns.user
    loans = LoanMgt.get_user_loans(user.id)

    data = %{
      loans: loans
    }

    ApiHelpers.success(conn, data, %{success: ["Loan dashboard retrieved successfully"]})
  end

  # ============ PRODUCTS API =============

  def products(conn, _params) do
    user = conn.assigns.user
    products = LoanMgt.get_qualifying_loan_products(user)

    if Enum.empty?(products) do
      ApiHelpers.error(conn, %{error: ["You don't qualify for any loan products at this time"]})
    else
      ApiHelpers.success(conn, %{products: products}, %{
        success: ["Loan products retrieved successfully"]
      })
    end
  end

  # ============ ELIGIBILITY API =============
  def eligible(conn, params) do
    user = conn.assigns.user
    params = Utils.atomify_map(params)

    with {:ok, _msg} <- ParamsValidator.loans_validation(params, :eligible),
         {:ok, product} <- LoanMgt.get_loan_product(params.product_id),
         {:ok, customer_eligibility} <-
           handle_eligibility(user, Map.put(params, :account_number, user.account_number)),
         {:ok, total_borrowed, remaining_balance} <-
           check_loan_balance(product.id, customer_eligibility) do
      ApiHelpers.success(
        conn,
        %{
          customer_eligibility: customer_eligibility,
          product: product,
          total_borrowed: total_borrowed,
          remaining_balance: remaining_balance
        },
        %{
          success: ["You are eligible for this loan product"]
        }
      )
    else
      {:error, message} ->
        ApiHelpers.error(conn, %{error: [message]})

      _error ->
        ApiHelpers.error(conn, %{error: ["An error occurred while retrieving eligible customers"]})
    end
  end

  defp handle_eligibility(customer, params) do
    case LoanMgt.eligible_by_product_and_acc_num(params.product_id, params.account_number) do
      nil -> {:error, "You are not eligible for this loan product"}
      customer_eligibility -> {:ok, customer_eligibility}
    end
  end

  defp check_loan_balance(product_id, eligible) do
    max_loan_amount = eligible.max_loan_amount

    total_borrowed =
      LoanMgt.get_total_borrowed_by_account_number_and_product_id(eligible, product_id)

    remaining_balance = Decimal.sub(max_loan_amount, total_borrowed)

    if Decimal.gt?(remaining_balance, Decimal.new(0)) do
      {:ok, total_borrowed, remaining_balance}
    else
      {:error, "You have reached the maximum loan amount for this product"}
    end
  end

  # ================= APPLY API =================
  def apply(conn, params) do
    user = conn.assigns.user
    params = Utils.atomify_map(params)

    with {:ok, _msg} <- ParamsValidator.loans_validation(params, :apply),
         {:ok, eligible} <- LoanMgt.get_customer_eligibility(params.elegibility_id),
         {:ok, product} <- LoanMgt.get_loan_product(eligible.product_id),
         {:ok, total_borrowed, remaining_balance} <- check_loan_balance(product.id, eligible),
         :ok <- validate_loan_amount(eligible, product, params.amount, remaining_balance),
         {:ok, loan_terms} <-
           calculate_loan_terms(eligible, product, format_amount(params.amount)) do
      ApiHelpers.success(conn, %{loan_terms: loan_terms, remaining_balance: remaining_balance}, %{
        success: ["Please review the loan terms"]
      })
    else
      {:error, reason} ->
        ApiHelpers.error(conn, %{error: [reason]})

      _ ->
        ApiHelpers.error(conn, %{
          error: ["An error occurred while processing your loan application"]
        })
    end
  end

  def validate_loan_amount(eligible, loan_product, amount, remaining_balance) do
    amount = format_amount(amount)

    cond do
      not_within_product_range?(amount, loan_product) ->
        {:error,
         "Amount must be between #{loan_product.min_amount} and #{loan_product.max_amount}"}

      exceeds_eligible_amount?(amount, eligible) ->
        {:error, "Your maximum eligible loan amount is #{eligible.max_loan_amount}"}

      Decimal.gt?(amount, remaining_balance) ->
        {:error, "The requested amount exceeds your remaining balance of #{remaining_balance}"}

      true ->
        :ok
    end
  end

  defp format_amount(amount) do
    case Decimal.cast(amount) do
      {:ok, decimal} -> decimal
      :error -> Decimal.new(amount)
    end
  end

  defp not_within_product_range?(amount, loan_product) do
    Decimal.lt?(amount, loan_product.min_amount) or Decimal.gt?(amount, loan_product.max_amount)
  end

  defp exceeds_eligible_amount?(amount, eligible) do
    Decimal.gt?(amount, eligible.max_loan_amount)
  end

  def calculate_loan_terms(eligible, product, amount) do
    case eligible.eligibility_status do
      "ELIGIBLE" -> LoanMgt.calculate_loan_terms(eligible.customer_id, product, amount)
      "PENDING" -> {:error, "Your loan application is still pending approval"}
      "REJECTED" -> {:error, "Your loan application was rejected"}
      _ -> {:error, "You are not eligible for a loan at this time"}
    end
  end

  # =========== CONFIRM LOAN API =============
  def confirm_loan(conn, params) do
    user = conn.assigns.user

    params = Utils.atomify_map(params)

    with {:ok, _msg} <- ParamsValidator.loans_validation(params, :confirm),
         {:ok, terms} <- LoanMgt.get_loan_terms(params.terms_id),
         {:ok, eligible} <- LoanMgt.get_customer_eligibility(params.elegibility_id),
         {:ok, total_borrowed, remaining_balance} <-
           check_loan_balance(terms.product_id, eligible),
         :ok <-
           validate_loan_amount(
             eligible,
             %{min_amount: Decimal.new(0), max_amount: eligible.max_loan_amount},
             terms.amount,
             remaining_balance
           ),
         {:ok, product} <- product_has_account_details(terms.product_id, :loan_account),
         :ok <- ensure_unique_accounts(product.loan_account, eligible.account_number) do
      transfer_params = %{
        "from_account" => product.loan_account,
        "description" => "Transfer",
        "amount" => to_string(terms.disbursed_amount),
        "to_account" => eligible.account_number
      }

      case transfer_request(transfer_params) do
        {:ok, transaction} ->
          txn = get_transaction_resp(transaction)

          case txn.status do
            "success" ->
              case log_loan_details(user, eligible, terms, txn, total_borrowed, product) do
                {:ok, loan} ->
                  ApiHelpers.success(conn, %{loan: loan}, %{
                    success: ["Loan request was successful"]
                  })

                {:error, reason} ->
                  ApiHelpers.error(conn, %{error: [reason]})
              end

            status ->
              ApiHelpers.error(conn, %{error: "Failed to process transaction, status: #{status}"})
          end

        {:error, reason} ->
          ApiHelpers.error(conn, %{error: [reason]})
      end
    else
      {:error, reason} ->
        ApiHelpers.error(conn, %{error: [reason]})

      _ ->
        ApiHelpers.error(conn, %{
          error: ["An error occurred while processing your loan application"]
        })
    end
  end

  defp ensure_unique_accounts(loan_account, account_number) do
    if loan_account == account_number do
      {:error, "Debit and credit accounts cannot be the same"}
    else
      :ok
    end
  end

  defp calculate_charge_amount(amount, product) do
    charge_amount =
      case product.charge_type do
        "PERCENTAGE" ->
          percentage_rate = Decimal.div(product.charge_rate, Decimal.new(100))
          Decimal.mult(amount, percentage_rate)

        "ACTUAL" ->
          product.charge_rate

        "NONE" ->
          Decimal.new(0)

        _ ->
          {:error, "Invalid charge type, please contact support"}
      end

    case charge_amount do
      {:error, reason} -> {:error, reason}
      amount -> {:ok, amount}
    end
  end

  def product_has_account_details(product_id, type) do
    case LoanMgt.get_loan_product(product_id) do
      {:error, error} ->
        {:error, error}

      {:ok, product} ->
        case type do
          :loan_account ->
            if product.loan_account,
              do: {:ok, product},
              else: {:error, "Loan product does not have loan account details"}

          :collection_account ->
            if product.collection_account,
              do: {:ok, product},
              else: {:error, "Loan product does not have collection account details"}

          _ ->
            {:error, "Invalid account type"}
        end
    end
  end

  defp transfer_request(params) do
    # {:ok, %{}}
    case FundTransferFromRemoteService.transfer(params) do
      {:ok, transaction} -> {:ok, transaction}
      {:error, reason} -> {:error, reason}
      _ -> {:error, "An error occurred while processing the loan application"}
    end
  end

  def log_loan_details(
        %{accounts: accounts} = user,
        eligible,
        terms,
        txn,
        total_borrowed,
        product
      ) do
    account = Enum.find(accounts, &(&1.account_number == eligible.account_number))

    loan_params = %{
      user_id: user.id,
      customer_id: eligible.id,
      account_number: eligible.account_number,
      account_type: eligible.account_type,
      amount: terms.amount,
      remaining_balance: terms.total_repayment,
      charge_amount: terms.charge_amount,
      disbursed_amount: terms.disbursed_amount,
      interest_rate: terms.interest_rate,
      duration: terms.duration,
      total_repayment: terms.total_repayment,
      status: "DISBURSED",
      product_id: terms.product_id,
      bank_account_id: account.id
    }

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:loan, Loan.changeset(%Loan{}, loan_params))
    |> Ecto.Multi.merge(fn %{loan: loan} -> create_transaction(loan, user, txn) end)
    |> Ecto.Multi.merge(fn %{loan: loan} -> log_charge(loan, terms, product) end)
    |> Ecto.Multi.merge(fn %{loan: loan} ->
      update_customer_details(eligible, loan.amount, total_borrowed)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan: loan}} ->
        {:ok, loan}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  def create_transaction(loan, user, txn) do
    params = %{
      loan_id: loan.id,
      type: "DISBURSEMENT",
      amount: loan.disbursed_amount,
      status: "COMPLETED",
      value_date: Date.utc_today(),
      debit_account: txn.debit_account,
      description: "Loan disbursement",
      reference: "LNTXN" <> to_string(loan.id),
      transaction_date: DateTime.utc_now(),
      payment_method: "BANK_TRANSFER",
      external_reference: txn.id,
      credit_account: loan.account_number
    }

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:transaction, Transactions.changeset(%Transactions{}, params))
  end

  # LoanCharges
  def log_charge(loan, terms, product) do
    params = %{
      loan_id: loan.id,
      product_id: product.id,
      amount: terms.charge_amount,
      description: "Loan charge",
      status: "BLOCKED",
      payment_method: "BANK_TRANSFER",
      debit_account: product.loan_account,
      credit_account: product.charge_account,
      reference: "LNCHG-" <> to_string(loan.id) <> "-#{rand_num()}"
    }

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:charge, LoanCharges.changeset(%LoanCharges{}, params))
  end

  def update_customer_details(eligible, loan_amount, total_borrowed) do
    new_total = Decimal.add(total_borrowed, loan_amount)

    new_status =
      if Decimal.lt?(new_total, eligible.max_loan_amount), do: "ELIGIBLE", else: "INELIGIBLE"

    case new_status do
      "ELIGIBLE" ->
        Ecto.Multi.new()

      _ ->
        Ecto.Multi.new()
        |> Ecto.Multi.update(
          :update,
          Customer.changeset(eligible, %{eligibility_status: "INELIGIBLE"})
        )
    end
  end

  # ========== LOAN DETAILS API ===========
  def loan_details(conn, %{"loan_id" => loan_id}) do
    user = conn.assigns.user

    case LoanMgt.get_loan(user.id, loan_id) do
      nil ->
        ApiHelpers.error(conn, %{error: ["Loan not found"]})

      loan ->
        ApiHelpers.success(conn, %{loan: loan}, %{
          success: ["Loan details retrieved successfully"]
        })
    end
  end

  # ============= PENDING LOANS API =============
  def pending_loans(conn, _params) do
    user = conn.assigns.user

    case LoanMgt.get_pending_loans(user.id) do
      [] ->
        ApiHelpers.error(conn, %{error: ["No pending loans found"]})

      loans ->
        data = %{loans: loans}
        ApiHelpers.success(conn, data, %{success: ["Pending loans retrieved successfully"]})
    end
  end

  #  ============ REPAYMENT API =============
  def repay(conn, params) do
    user = conn.assigns.user
    params = Utils.atomify_map(params)

    with {:ok, _msg} <- ParamsValidator.loans_validation(params, :repay),
         {:ok, loan} <- LoanMgt.get_loan_by_id(user.id, params.loan_id),
         {:ok, repayment_amount} <- get_repayment_amount(loan, params),
         :ok <- validate_repayment_amount(loan, repayment_amount),
         {:ok, product} <- product_has_account_details(loan.product_id, :collection_account),
         {:ok, eligible} <- LoanMgt.get_customer_eligibility(loan.customer_id),
         :ok <- ensure_unique_accounts(product.collection_account, params.account_number) do
      transfer_params = %{
        "description" => "Loan Repayment",
        "to_account" => product.collection_account,
        "amount" => to_string(repayment_amount),
        "from_account" => params.account_number
      }

      case transfer_request(transfer_params) do
        {:ok, transaction} ->
          txn = get_transaction_resp(transaction)

          case txn.status do
            "success" ->
              case process_repayment(user, loan, repayment_amount, txn, eligible) do
                {:ok, updated_loan} ->
                  ApiHelpers.success(conn, %{loan: updated_loan, transaction: txn}, %{
                    success: ["Loan repayment was successful"]
                  })

                {:error, reason} ->
                  ApiHelpers.error(conn, %{error: [reason]})
              end

            status ->
              ApiHelpers.error(conn, %{error: "Failed to process transaction, status: #{status}"})
          end

        {:error, reason} ->
          ApiHelpers.error(conn, %{error: [reason]})
      end
    else
      {:error, reason} ->
        ApiHelpers.error(conn, %{error: [reason]})

      _ ->
        ApiHelpers.error(conn, %{error: ["An error occurred while processing the loan repayment"]})
    end
  end

  defp get_repayment_amount(loan, params) do
    if params.pay_full do
      {:ok, Decimal.sub(loan.total_repayment, loan.repaid_amount)}
    else
      case Decimal.cast(params.amount) do
        {:ok, decimal} ->
          amount =
            decimal
            |> Decimal.to_string(:normal)
            |> Decimal.new()

          {:ok, amount}

        error ->
          error
      end
    end
  end

  defp validate_repayment_amount(loan, amount) do
    remaining_balance = Decimal.sub(loan.total_repayment, loan.repaid_amount)

    cond do
      Decimal.lt?(amount, Decimal.new(0)) ->
        {:error, "Invalid repayment amount"}

      Decimal.gt?(amount, remaining_balance) ->
        {:error, "Repayment amount exceeds the remaining balance"}

      true ->
        :ok
    end
  end

  defp process_repayment(user, loan, amount, transaction, eligible) do
    loan_details = prepare_repay_loan_details(loan, amount)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:loan, Loan.changeset(loan, loan_details))
    |> Ecto.Multi.merge(fn %{loan: loan} -> update_customer_eligibility(loan, eligible) end)
    |> Ecto.Multi.merge(fn %{loan: loan} ->
      log_repayment_transaction(loan, amount, transaction)
    end)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan: loan}} ->
        {:ok, loan}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = Utils.traverse_errors(failed_value.errors)
        {:error, reason}
    end
  end

  defp prepare_repay_loan_details(loan, amount) do
    repaid_amount = Decimal.add(loan.repaid_amount, amount)
    remaining_balance = Decimal.sub(loan.total_repayment, repaid_amount)

    status =
      if Decimal.equal?(remaining_balance, Decimal.new(0)), do: "COMPLETED", else: "PARTIAL"

    %{
      repaid_amount: repaid_amount,
      remaining_balance: remaining_balance,
      repayment_status: status
    }
  end

  def update_customer_eligibility(loan, eligible) do
    pending_loans = LoanMgt.customer_loans_pending_repayment(loan.customer_id, loan.id)

    cond do
      loan.repayment_status == "COMPLETED" and Enum.empty?(pending_loans) ->
        Ecto.Multi.new()
        |> Ecto.Multi.update(
          :update,
          Customer.changeset(eligible, %{eligibility_status: "ELIGIBLE"})
        )

      true ->
        Ecto.Multi.new()
    end
  end

  defp log_repayment_transaction(loan, amount, txn) do
    params = %{
      loan_id: loan.id,
      type: "REPAYMENT",
      amount: amount,
      status: "COMPLETED",
      value_date: Date.utc_today(),
      description: "Loan repayment",
      payment_method: "BANK_TRANSFER",
      credit_account: txn.credit_account,
      external_reference: txn.id,
      debit_account: loan.account_number,
      transaction_date: DateTime.utc_now(),
      reference: "LNREP-" <> to_string(loan.id) <> "-#{rand_num()}"
    }

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:transaction, Transactions.changeset(%Transactions{}, params))
  end

  def rand_num() do
    :rand.uniform(100)
  end

  # ============ LOAN TRANSACTIONS API =============
  # ServiceManagerWeb.LoansController.get_map()

  def get_transaction_resp(response) do
    response = response()

    %{
      # From "body"."header"
      id: get_in(response, ["body", "header", "id"]),
      unique_identifier: get_in(response, ["body", "header", "uniqueIdentifier"]),
      status: get_in(response, ["body", "header", "status"]),
      transaction_status: get_in(response, ["body", "header", "transactionStatus"]),

      # From "body"."body"
      credit_account: get_in(response, ["body", "body", "creditAccountNumber"]),
      debit_account: get_in(response, ["body", "body", "debitAccountNumber"]),
      amount: get_in(response, ["body", "body", "debitAmount"]),
      currency: get_in(response, ["body", "body", "debitCurrency"]),
      transaction_type: get_in(response, ["body", "body", "transactionType"])
    }
  end

  def response() do
    %{
      "body" => %{
        "body" => %{
          "creditAccountNumber" => "*************",
          "debitAccountNumber" => "*************",
          "debitAmount" => "30000.00",
          "debitCurrency" => "MWK",
          "transactionType" => "AC"
        },
        "header" => %{
          "audit" => %{
            "T24_time" => 2854,
            "requestParse_time" => 12,
            "responseParse_time" => 6,
            "versionNumber" => "1"
          },
          "id" => "FT242046G3YR",
          "status" => "success",
          "transactionStatus" => "Live",
          "uniqueIdentifier" => "IRFX250073237585863.02"
        }
      },
      "headers" => %{
        "Access-Control-Allow-Headers" =>
          "Origin, Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers",
        "Access-Control-Allow-Methods" =>
          "GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS, CONNECT, PATCH",
        "Access-Control-Allow-Origin" => "*",
        "Access-Control-Expose-Headers" => "",
        "Access-Control-Max-Age" => "3600",
        "Activityid" => "cfe860bb-3116-412b-85f7-53d4007636ad",
        "Cache-Control" => "no-store",
        "Content-Security-Policy" => "default-src 'self'",
        "Content-Type" => "application/json; charset=UTF-8",
        "Date" => "Tue, 07 Jan 2025 21:40:28 GMT",
        "Strict-Transport-Security" => "max-age=31536000; includeSubDomains",
        "Transfer-Encoding" => "chunked",
        "X-Content-Type-Options" => "nosniff",
        "X-Frame-Options" => "SAMEORIGIN",
        "X-Permitted-Cross-Domain-Policies" => "master-only",
        "X-Xss-Protection" => "1; mode=block"
      },
      "status" => 200
    }
  end
end
