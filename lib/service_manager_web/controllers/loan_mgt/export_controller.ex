defmodule ServiceManagerWeb.Backend.LoanMgt.ExportController do
  use ServiceManagerWeb, :controller

  def download_template(conn, %{"id" => type} = _params) do
    csv_content = generate_csv_content(type)
    filename = "loan_#{type}_template.csv"
    send_download(conn, {:binary, csv_content}, filename: filename)
  end

  defp generate_csv_content("customer") do
    data = customer_sample_data()

    headers = [
      "Account Number",
      "Account Type",
      "First Name",
      "Last Name",
      "Phone",
      "Max Loan Amount"
    ]

    rows =
      Enum.map(data, fn row ->
        [
          row.account_number,
          row.account_type,
          row.first_name,
          row.last_name,
          row.phone,
          row.max_loan_amount
        ]
      end)

    csv_data = [headers | rows]

    csv_data
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string()
  end

  defp customer_sample_data do
    [
      %{
        account_number: "ACC001",
        account_type: "BANK_ACCOUNT",
        first_name: "<PERSON>",
        last_name: "Doe",
        phone: "260**********",
        max_loan_amount: "5000.00"
      },
      %{
        account_number: "ACC002",
        account_type: "BANK_ACCOUNT",
        first_name: "Jane",
        last_name: "Smith",
        phone: "260**********",
        max_loan_amount: "7500.00"
      }
    ]
  end

  defp generate_csv_content("product") do
    data = product_sample_data()

    headers = [
      "Product Name",
      "Description",
      "Min Amount",
      "Max Amount",
      "Interest Rate",
      "Default Duration"
    ]

    rows =
      Enum.map(data, fn row ->
        [
          row.name,
          row.description,
          row.min_amount,
          row.max_amount,
          row.interest_rate,
          row.default_duration
        ]
      end)

    csv_data = [headers | rows]

    csv_data
    |> CSV.encode()
    |> Enum.to_list()
    |> to_string()
  end

  defp product_sample_data do
    [
      %{
        name: "Personal Loan",
        description: "Short-term personal loan",
        min_amount: "1000.00",
        max_amount: "10000.00",
        interest_rate: "5.5",
        default_duration: "12"
      },
      %{
        name: "Business Loan",
        description: "Small business loan",
        min_amount: "5000.00",
        max_amount: "50000.00",
        interest_rate: "7.5",
        default_duration: "24"
      }
    ]
  end
end
