defmodule ServiceManagerWeb.Validators.LinkAccountValidator do
  @moduledoc """
  Validator for link account request parameters
  """

  # import ServiceManager.Logging.FunctionTracker

  def schema do
    %{
      "account_number" => [:string, :required],
      # "first_name" => [:string, nil],
      # "id_number" => [:string, nil],
      # "last_name" => [:string, nil],
      "phone_number" => [:string, nil]
    }
  end

  # track do
  def validate(params) do
    case Skooma.valid?(params, schema()) do
      :ok -> {:ok, params}
      {:error, reason} -> {:error, reason}
    end
  end

  # end
end
