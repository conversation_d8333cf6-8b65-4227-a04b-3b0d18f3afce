defmodule ServiceManagerWeb.Validators.ThirdPartyServiceValidator do
  @moduledoc """
  Validator for third-party service request parameters
  """

  def schema do
    %{
      "service" => [:string, :required],
      "service_action" => [:string, :required],
    }
  end

  def validate(params) do
    case Skooma.valid?(params, schema()) do
      :ok -> {:ok, params}
      {:error, reason} -> {:error, reason}
    end
  end

  def validate_mobile_banking_registration(params) do
    mobile_banking_schema = %{
      "phone_number" => [:string, :required],
      "customer_number" => [:string, :required],
      "email_address" => [:string, nil],
      "account_officer" => [:string, nil],
      "profile_type" => [:string, nil],
      "first_name" => [:string, nil],
      "last_name" => [:string, nil],
      "company" => [:string, nil],
      "inputter" => [:string, nil],
      "authoriser" => [:string, nil]
    }

    case Skooma.valid?(params, mobile_banking_schema) |> IO.inspect(label: "Skooma.valid?(params, mobile_banking_schema)") do
      :ok -> {:ok, params}
      {:error, reason} -> {:error, reason}
    end
  end
end
