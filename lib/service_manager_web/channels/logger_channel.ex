defmodule ServiceManagerWeb.LoggerChannel do
  use Phoenix.Channel
  require Logger

  # def join("lobby:lobby", _message, socket) do
  #   {:ok, socket}
  # end

  # def join("lobby:" <> _private_room_id, _params, _socket) do
  #   {:error, %{reason: "unauthorized"}}
  # end

  # def handle_in("new_msg", %{"body" => body}, socket) do
  #   broadcast!(socket, "new_msg", %{body: body})
  #   {:noreply, socket}
  # end

  def join("logger:logs", params, socket) do
    Logger.info("Join attempt for logger:lobby with params: #{inspect(params)}")

    # Handle join with log message
    if Map.has_key?(params, "level") and Map.has_key?(params, "message") do
      broadcast_log(params["level"], params["message"])
      {:ok, socket}
    else
      # Handle normal join
      case socket.assigns do
        %{api_key_record: record} ->
          Logger.info("Join successful with API key record: #{inspect(record)}")
          # Subscribe to PubSub and handle any potential errors
          case Phoenix.PubSub.subscribe(ServiceManager.PubSub, "logger:lobby") do
            :ok ->
              Logger.info("Successfully subscribed to logger:lobby")
              {:ok, %{status: "connected"}, socket}

            {:error, reason} ->
              Logger.error("Failed to subscribe to logger:lobby: #{inspect(reason)}")
              {:error, %{reason: "subscription_failed"}}
          end

        _ ->
          Logger.info("Join failed - unauthorized")
          {:error, %{reason: "unauthorized"}}
      end
    end
  end

  # def join(_topic, params, _socket) do
  #   {:error, %{reason: "unauthorized"}}
  # end

  # def handle_in("log_event", %{"level" => level, "message" => message} = payload, socket) do
  #   Logger.debug("Received log_event with level: #{level}, message: #{message}")
  #   broadcast_log(level, message)
  #   {:reply, {:ok, payload}, socket}
  # end

  # def handle_in("phx_join", payload, socket) do
  #   Logger.debug("Received phx_join with payload: #{inspect(payload)}")
  #   {:reply, {:ok, payload}, socket}
  # end

  # Catch-all for handling any other events
  # def handle_in(event, payload, socket) do
  #   Logger.debug("Received unhandled event #{event} with payload: #{inspect(payload)}")
  #   case payload do
  #     %{"level" => level, "message" => message} ->
  #       broadcast_log(level, message)
  #     _ ->
  #       broadcast_log("info", "Received unhandled event #{event} with payload: #{inspect(payload)}")
  #   end
  #   {:reply, {:ok, payload}, socket}
  # end

  def handle_info({:log, payload}, socket) do
    # Logger.info("Received log message: #{inspect(payload)}")
    IO.inspect(payload)

    try do
      broadcast!(socket, "new_log", payload)
      {:noreply, socket}
    rescue
      _e ->
        # Logger.error("Failed to broadcast log message: #{inspect(e)}")
        {:noreply, socket}
    end
  end

  def handle_info({:custom_event, event, payload}, socket) do
    broadcast!(socket, event, payload)
    {:noreply, socket}
  end

  defp broadcast_log(level, message) do
    # Use the LoggerBroadcaster to handle both logging and broadcasting
    case level do
      "info" -> ServiceManager.Broadcasters.LoggerBroadcaster.info(message)
      "error" -> ServiceManager.Broadcasters.LoggerBroadcaster.error(message)
      "debug" -> ServiceManager.Broadcasters.LoggerBroadcaster.debug(message)
      "warn" -> ServiceManager.Broadcasters.LoggerBroadcaster.warn(message)
      _ -> ServiceManager.Broadcasters.LoggerBroadcaster.info(message)
    end
  end
end
