defmodule ServiceManagerWeb.LoggerBackend do
  @behaviour :gen_event
  require Logger

  @impl true
  def init(_args) do
    {:ok, %{}}
  end

  @impl true
  def handle_event({level, _gl, {Logger, msg, timestamp, metadata}}, state) do
    # Broadcast the log message through the channel
    payload = %{
      level: to_string(level),
      message: to_string(msg),
      timestamp: timestamp,
      metadata: metadata
    }

    try do
      case Registry.lookup(Phoenix.PubSub.Registry, ServiceManager.PubSub) do
        [{_pid, _}] ->
          # PubSub is running, safe to broadcast
          Phoenix.PubSub.broadcast(ServiceManager.PubSub, "logger:lobby", {:log, payload})

        [] ->
          # PubSub not ready yet, skip broadcasting
          :ok
      end
    rescue
      _ ->
        # If any error occurs, just continue without broadcasting
        :ok
    end

    {:ok, state}
  end

  @impl true
  def handle_call(_, state) do
    {:ok, :ok, state}
  end

  @impl true
  def handle_info(_, state) do
    {:ok, state}
  end

  @impl true
  def code_change(_old_vsn, state, _extra) do
    {:ok, state}
  end

  @impl true
  def terminate(_reason, _state) do
    :ok
  end
end
