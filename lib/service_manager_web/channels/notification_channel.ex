defmodule ServiceManagerWeb.NotificationChannel do
  use ServiceManagerWeb, :channel
  alias ServiceManager.Accounts.User
  alias ServiceManager.Repo

  def join("notifications:" <> session_id, _params, socket) do
    case get_user_by_session_id(session_id) do
      %User{} = user ->
        {:ok, assign(socket, :user, user)}

      nil ->
        {:error, %{reason: "unauthorized"}}
    end
  end

  def handle_in("new_notification", %{"type" => type, "content" => content} = payload, socket) do
    user = socket.assigns.user

    cond do
      type == "email" and user.email_notifications ->
        # Handle email notification
        # ServiceManager.Notifications.EmailNotification.deliver(user, content)
        broadcast!(socket, "notification", Map.put(payload, "delivery_type", "email"))

      type == "sms" and user.sms_notifications ->
        # Handle SMS notification

        ServiceManager.Repo.insert(%ServiceManager.Notifications.SMSNotification{
          msisdn: user.mobile_number,
          message: content
        })

        broadcast!(socket, "notification", Map.put(payload, "delivery_type", "sms"))

      type == "push" and user.push_notifications ->
        # Handle push notification
        # ServiceManager.Notifications.PushNotification.deliver(user, content)
        broadcast!(socket, "notification", Map.put(payload, "delivery_type", "push"))

      true ->
        {:reply, {:error, %{reason: "notification type disabled"}}, socket}
    end

    {:reply, :ok, socket}
  end

  defp get_user_by_session_id(session_id) do
    Repo.get_by(User, session_id: session_id)
  end
end
