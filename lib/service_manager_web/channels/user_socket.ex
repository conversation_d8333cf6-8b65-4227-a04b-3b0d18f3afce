defmodule ServiceManagerWeb.UserSocket do
  use Phoenix.Socket,
    # Send a heartbeat every 30 seconds
    ping_interval: 30000

  require Logger

  # Channels
  channel "logger:logs", ServiceManagerWeb.LoggerChannel
  channel "notifications:*", ServiceManagerWeb.NotificationChannel
  channel "wallet_notifications:*", ServiceManagerWeb.WalletNotificationChannel

  alias ServiceManager.Auth.ApiKeyValidator
  alias ServiceManager.Accounts.User
  alias ServiceManager.WalletAccounts.WalletUser
  alias ServiceManager.Repo

  @impl true
  def connect(params, socket, _connect_info) do
    IO.inspect(socket.assigns)

    case params do
      %{"api_key" => api_key} ->
        Logger.info("Validating API key: #{api_key}")

        case ApiKeyValidator.validate_api_key(api_key) do
          {:ok, api_key_record} ->
            Logger.info("API key valid, record: #{inspect(api_key_record)}")
            {:ok, assign(socket, :api_key_record, api_key_record)}

          {:error, reason} ->
            Logger.info("API key validation failed: #{inspect(reason)}")
            :error
        end

      %{"session_id" => session_id, "type" => type} when type in ["wa", "mb"] ->
        Logger.info("Connecting with session_id: #{session_id}, type: #{type}")

        case validate_session(session_id, type) do
          {:ok, user} ->
            {:ok,
             assign(socket, :session_id, session_id)
             |> assign(:auth_type, type)
             |> assign(:user, user)}

          {:error, _reason} ->
            :error
        end

      _ ->
        Logger.info("Missing or invalid required params")
        :error
    end
  end

  defp validate_session(session_id, type) do
    Logger.info("Validating #{type} session: #{session_id}")

    case type do
      "wa" ->
        # Validate wallet session
        case Repo.get_by(WalletUser, session_id: session_id) |> IO.inspect() do
          %WalletUser{} = user ->
            Logger.info("Valid wallet session for user: #{user.id}")
            {:ok, user}

          nil ->
            Logger.info("Invalid wallet session")
            {:error, :invalid_session}
        end

      "mb" ->
        # Validate mobile banking session
        case Repo.get_by(User, session_id: session_id) do
          %User{} = user ->
            Logger.info("Valid mobile banking session for user: #{user.id}")
            {:ok, user}

          nil ->
            Logger.info("Invalid mobile banking session")
            {:error, :invalid_session}
        end
    end
  end

  # Socket id's are topics that allow you to identify all sockets for a given user:
  #
  #     def id(socket), do: "user_socket:#{socket.assigns.user_id}"
  #
  # Would allow you to broadcast a "disconnect" event and terminate
  # all active sockets and channels for a given user:
  #
  #     ServiceManagerWeb.Endpoint.broadcast("user_socket:#{user.id}", "disconnect", %{})
  #
  # Returning `nil` makes this socket anonymous.
  @impl true
  def id(_socket), do: nil
end
