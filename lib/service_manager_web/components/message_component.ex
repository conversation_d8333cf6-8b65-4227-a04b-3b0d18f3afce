defmodule ServiceManagerWeb.MessageComponent do
  use Phoenix.Component

  def message(assigns) do
    ~H"""
    <div class="flex gap-3">
      <div class="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
        <span class="text-sm font-medium text-gray-600">
          <%= String.first(@message.user.first_name) %><%= String.first(@message.user.last_name) %>
        </span>
      </div>
      <div class="flex-1">
        <div class="flex items-center gap-2">
          <span class="font-medium">
            <%= @message.user.first_name %> <%= @message.user.last_name %>
          </span>
          <span class="text-sm text-gray-500">
            <%= Calendar.strftime(@message.inserted_at, "%B %d, %Y at %I:%M %p") %>
          </span>
        </div>
        <div class="mt-1 text-gray-700"><%= @message.content %></div>
        <%= if @message.parent_id == nil and @allow_replies do %>
          <button
            phx-click="show-reply-form"
            phx-value-message-id={@message.id}
            class="mt-1 text-sm text-gray-500 hover:text-gray-700"
          >
            Reply
          </button>
        <% end %>
        <%= if @show_replies and not Enum.empty?(@message.replies) do %>
          <div class="ml-4 mt-2 space-y-2 border-l-2 border-gray-100 pl-4">
            <%= for reply <- @message.replies do %>
              <.message message={reply} allow_replies={false} show_replies={true} />
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
    """
  end
end
