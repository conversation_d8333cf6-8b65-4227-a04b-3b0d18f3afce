defmodule ServiceManagerWeb.Components.StatsComponent do
  use ServiceManagerWeb, :live_component

  @doc """
  Renders a stats dashboard with configurable cards.

  ## Example assigns:
    * title - The title of the stats section
    * stats - List of stat cards, where each card is a map with:
      * title - The title of the stat card
      * value - The main value to display
      * comparison - Optional comparison value (e.g. "from 70,946")
      * change - Optional map with:
        * type - :increase or :decrease
        * value - The percentage change (e.g. "12%")
  """
  def render(assigns) do
    ~H"""
    <div>
      <h3 class="text-base font-semibold text-gray-900"><%= @title %></h3>
      <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <%= for stat <- @stats do %>
          <div class="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
            <dt class="truncate text-sm font-medium text-gray-500"><%= stat.title %></dt>
            <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
              <div class="flex items-baseline text-2xl font-semibold text-fdhBlue">
                <%= stat.value %>
                <%= if stat[:comparison] do %>
                  <span class="ml-2 text-sm font-medium text-gray-500"><%= stat.comparison %></span>
                <% end %>
              </div>

              <%= if stat[:change] do %>
                <div class={"inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0 #{if stat.change.type == :increase, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800"}"}>
                  <svg
                    class={"mr-0.5 -ml-1 size-5 shrink-0 self-center #{if stat.change.type == :increase, do: "text-green-500", else: "text-red-500"}"}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                    data-slot="icon"
                  >
                    <%= if stat.change.type == :increase do %>
                      <path
                        fill-rule="evenodd"
                        d="M10 17a.75.75 0 0 1-.75-.75V5.612L5.29 9.77a.75.75 0 0 1-1.08-1.04l5.25-5.5a.75.75 0 0 1 1.08 0l5.25 5.5a.75.75 0 1 1-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0 1 10 17Z"
                        clip-rule="evenodd"
                      />
                    <% else %>
                      <path
                        fill-rule="evenodd"
                        d="M10 3a.75.75 0 0 1 .75.75v10.638l3.96-4.158a.75.75 0 1 1 1.08 1.04l-5.25 5.5a.75.75 0 0 1-1.08 0l-5.25-5.5a.75.75 0 1 1 1.08-1.04l3.96 4.158V3.75A.75.75 0 0 1 10 3Z"
                        clip-rule="evenodd"
                      />
                    <% end %>
                  </svg>
                  <span class="sr-only">
                    <%= if stat.change.type == :increase, do: "Increased by", else: "Decreased by" %>
                  </span>
                  <%= stat.change.value %>
                </div>
              <% end %>
            </dd>
          </div>
        <% end %>
      </dl>
    </div>
    """
  end
end
