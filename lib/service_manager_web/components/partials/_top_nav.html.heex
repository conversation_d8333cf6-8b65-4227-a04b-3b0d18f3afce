<header class="bg-white p-4 flex justify-between items-center">
  <div class="flex items-center">
    <button id="mobileMenuBtn" class="lg:hidden text-gray-600 hover:text-gray-800 mr-4">
      <i class="fas fa-bars text-2xl"></i>
    </button>
    <h1 class="text-2xl font-semibold text-gray-800"></h1>
  </div>
  <div class="flex items-center space-x-4">
    <%= if @current_user do %>
      <span class="text-gray-600">
        Welcome Back! <%= "#{@current_user.first_name} #{@current_user.last_name}" %>
      </span>
      <%= if version = ServiceManager.Versions.get_latest_version() do %>
        <.link
          href={~p"/mobileBanking/versions"}
          class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
        >
          v<%= version.version_number %>
        </.link>
      <% end %>
    <% end %>
    <div class="relative" x-data="{ open: false }">
      <button @click="open = !open" class="focus:outline-none">
        <%= if @current_user.profile_picture do %>
          <img
            src={@current_user.profile_picture}
            alt="User Avatar"
            class="w-10 h-10 rounded-full object-cover"
          />
        <% else %>
          <img src="/images/avatar/avata.jpeg" alt="User Avatar" class="w-10 h-10 rounded-full" />
        <% end %>
      </button>
      <div
        x-show="open"
        @click.away="open = false"
        class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10"
      >
        <div class="py-1">
          <.link
            href={~p"/mobileBanking/profile"}
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            My Profile
          </.link>
          <.link
            href={~p"/mobileBanking/profile/change-password"}
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            Change Password
          </.link>
          <a
            href={~p"/users/log_out"}
            class="block px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
          >
            Log out
          </a>
        </div>
      </div>
    </div>
    <button class="text-gray-400 hover:text-gray-600">
      <i class="fas fa-bell text-xl"></i>
    </button>
    <button class="text-gray-400 hover:text-gray-600">
      <i class="fas fa-ellipsis-v text-xl"></i>
    </button>
  </div>
</header>
