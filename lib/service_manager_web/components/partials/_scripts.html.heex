<script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          const sidebarNav = document.querySelector('.sidebar nav');
          const ps = new PerfectScrollbar(sidebarNav, {
              wheelSpeed: 2,
              wheelPropagation: true,
              minScrollbarLength: 20
          });

          const mobileMenuBtn = document.getElementById('mobileMenuBtn');
          const sidebar = document.querySelector('.sidebar');
          
          mobileMenuBtn.addEventListener('click', function() {
              sidebar.classList.toggle('show');
          });

          // Close sidebar when clicking outside
          document.addEventListener('click', function(event) {
              if (!sidebar.contains(event.target) && !mobileMenuBtn.contains(event.target)) {
                  sidebar.classList.remove('show');
              }
          });
      });

      function toggleDropdown(event) {
          event.preventDefault();
          const dropdownMenu = event.currentTarget.nextElementSibling;
          dropdownMenu.classList.toggle('hidden');
          const chevron = event.currentTarget.querySelector('.fa-chevron-down');
          chevron.classList.toggle('rotate-180');
      }

      document.addEventListener('DOMContentLoaded', function() {
          const currentPath = window.location.pathname;
          const sidebarItems = document.querySelectorAll('.sidebar-item');
          
          sidebarItems.forEach(item => {
              if (item.getAttribute('href') === currentPath) {
                  item.classList.add('active');
                  let parent = item.closest('.dropdown-menu');
                  if (parent) {
                      parent.classList.remove('hidden');
                      parent.previousElementSibling.classList.add('active');
                  }
              }
          });
      });
</script>

<%= Phoenix.Template.render(ServiceManagerWeb.Partials, "_chart_scripts", "html", assigns) %>
