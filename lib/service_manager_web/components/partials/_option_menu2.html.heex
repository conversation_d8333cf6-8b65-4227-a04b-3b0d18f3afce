<%= if is_nil(@menu) == false and @menu != [] do %>
  <%= cond do %>
    <% @menu.type == :link -> %>
      <li>
        <a
          href={@menu.href}
          class={"ml-1  text-xs block p-3 font-semibold rounded-xl #{if is_active?(@current_path, @menu), do: 'bg-zinc-100 bg-opacity-15' } hover:bg-zinc-100 hover:bg-opacity-30"}
        >
          <span class="sidebar-icon">
            <i class={"fa fa-#{@menu.icon}"} aria-hidden="true"></i>
          </span>
          <span class=""><%= @menu.title %></span>
        </a>
      </li>
    <% @menu.type == :header  -> %>
      <li
        class="sidebar-header"
        x-data={"{ open: #{menu_path_active?(@current_path, @menu.options, :contains)} }"}
      >
        <button
          class="ml-3 my-3 text-xs uppercase font-semibold flex items-center justify-between w-full"
          @click="open = !open"
        >
          <span class=""><%= @menu.title %></span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4 text-fdhOrange"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              x-show="!open"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
            <path
              x-show="open"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M18 12H6"
            />
          </svg>
        </button>
        <hr class="border-t border-gray-300 opacity-5 mt-0 mb-4" />
        <ul
          x-cloak
          x-show="open"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          x-collapse.duration.300ms
        >
          <%= for submenu <- @menu.options do %>
            <%= Phoenix.Template.render(ServiceManagerWeb.Partials, "_option_menu2", "html",
              menu: submenu,
              current_path: @current_path
            ) %>
          <% end %>
        </ul>
      </li>
    <% true -> %>
      <li x-data={"{ open: #{menu_path_active?(@current_path, @menu.options, :contains)} }"}>
        <a href="javascript:;" class="sidebar-item" @click="open = !open">
          <span class="sidebar-icon"><i class={"fas fa-#{@menu.icon}"}></i></span>
          <span class="ml-3 text-xs"><%= @menu.title %></span>
          <span class="ml-auto"><i class="fas fa-chevron-down"></i></span>
        </a>

        <ul
          x-cloak
          x-show="open"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          x-collapse.duration.300ms
          class="p-2 px-3 mt-2 mx-2 shadow-r-2xl space-y-2 overflow-hidden text-xs font-medium text-gray-50 rounded-xl shadow-inner bg-fdh-blue bg-opacity-100 "
        >
          <%= for submenu <- @menu.options do %>
            <div class="block  ">
              <%= Phoenix.Template.render(ServiceManagerWeb.Partials, "_option_menu2", "html",
                menu: submenu,
                current_path: @current_path
              ) %>
            </div>
          <% end %>
        </ul>
      </li>
  <% end %>
<% end %>
