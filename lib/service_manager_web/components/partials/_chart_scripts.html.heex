<script>
  // Sample data for charts
  // const addMoneyData = {
  //     labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  //     datasets: [{
  //         label: 'Add Money',
  //         data: [65, 59, 80, 81, 56, 55, 40],
  //         fill: false,
  //         borderColor: 'rgb(75, 192, 192)',
  //         tension: 0.1
  //     }]
  // };

  // const virtualCardData = {
  //     labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  //     datasets: [{
  //         label: 'Virtual Card',
  //         data: [28, 48, 40, 19, 86, 27, 90],
  //         fill: false,
  //         borderColor: 'rgb(255, 99, 132)',
  //         tension: 0.1
  //     }]
  // };

  // const chartConfig = {
  //     type: 'line',
  //     options: {
  //         responsive: true,
  //         maintainAspectRatio: false,
  //         scales: {
  //             y: {
  //                 beginAtZero: true
  //             }
  //         }
  //     }
  // };

  // Create charts
  // new Chart(document.getElementById('addMoneyChart'), {...chartConfig, data: addMoneyData});
  // new Chart(document.getElementById('virtualCardChart'), {...chartConfig, data: virtualCardData});
  // new Chart(document.getElementById('analyticsAddMoneyChart'), {...chartConfig, data: addMoneyData});
  // new Chart(document.getElementById('analyticsMonthlyChart'), {...chartConfig, data: virtualCardData});
</script>
