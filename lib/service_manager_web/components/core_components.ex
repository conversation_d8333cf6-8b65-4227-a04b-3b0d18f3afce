defmodule ServiceManagerWeb.CoreComponents do
  @moduledoc """
  Provides core UI components.

  At first glance, this module may seem daunting, but its goal is to provide
  core building blocks for your application, such as modals, tables, and
  forms. The components consist mostly of markup and are well-documented
  with doc strings and declarative assigns. You may customize and style
  them in any way you want, based on your application growth and needs.

  The default components use Tailwind CSS, a utility-first CSS framework.
  See the [Tailwind CSS documentation](https://tailwindcss.com) to learn
  how to customize them or feel free to swap in another framework altogether.

  Icons are provided by [heroicons](https://heroicons.com). See `icon/1` for usage.
  """
  use Phoenix.Component

  alias Phoenix.LiveView.JS
  import ServiceManagerWeb.Gettext

  alias ServiceManagerWeb.Utilities.Pagination
  alias ServiceManagerWeb.Utilities.Sorting
  import ServiceManagerWeb.Utilities.Utils
  # import ServiceManagerWeb.Utilities.Extractors
  @query_params Application.compile_env(:service_manager, :query_params)
  @operators Application.compile_env(:service_manager, :operators)

  @doc """
  Renders a modal.

  ## Examples

      <.modal id="confirm-modal">
        This is a modal.
      </.modal>

  JS commands may be passed to the `:on_cancel` to configure
  the closing/cancel event, for example:

      <.modal id="confirm" on_cancel={JS.navigate(~p"/posts")}>
        This is another modal.
      </.modal>

  """
  attr :id, :string, required: true
  attr :show, :boolean, default: false
  attr :on_cancel, JS, default: %JS{}
  slot :inner_block, required: true

  def modal(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && show_modal(@id)}
      phx-remove={hide_modal(@id)}
      data-cancel={JS.exec(@on_cancel, "phx-remove")}
      class="relative z-50 hidden"
    >
      <div
        id={"#{@id}-bg"}
        class="backdrop-filter backdrop-blur-sm bg-opacity-50 fixed inset-0 transition-opacity"
        aria-hidden="true"
      />
      <div
        class="fixed inset-0 overflow-y-auto"
        aria-labelledby={"#{@id}-title"}
        aria-describedby={"#{@id}-description"}
        role="dialog"
        aria-modal="true"
        tabindex="0"
      >
        <div class="flex min-h-full items-center justify-center">
          <div class="w-full max-w-5xl p-4 sm:p-6 lg:py-8">
            <.focus_wrap
              id={"#{@id}-container"}
              phx-window-keydown={JS.exec("data-cancel", to: "##{@id}")}
              phx-key="escape"
              phx-click-away={JS.exec("data-cancel", to: "##{@id}")}
              class="shadow-zinc-700/10 ring-zinc-700/10 relative hidden rounded-2xl bg-white p-14 shadow-lg ring-1 transition"
            >
              <div class="absolute top-6 right-5">
                <button
                  phx-click={JS.exec("data-cancel", to: "##{@id}")}
                  type="button"
                  class="-m-3 flex-none p-3 opacity-20 hover:opacity-40"
                  aria-label={gettext("close")}
                >
                  <.icon name="hero-x-mark-solid" class="h-5 w-5" />
                </button>
              </div>
              <div id={"#{@id}-content"}>
                <%= render_slot(@inner_block) %>
              </div>
            </.focus_wrap>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders flash notices.

  ## Examples

      <.flash kind={:info} flash={@flash} />
      <.flash kind={:info} phx-mounted={show("#flash")}>Welcome Back!</.flash>
  """
  attr :id, :string, doc: "the optional id of flash container"
  attr :flash, :map, default: %{}, doc: "the map of flash messages to display"
  attr :title, :string, default: nil
  attr :kind, :atom, values: [:info, :error], doc: "used for styling and flash lookup"
  attr :rest, :global, doc: "the arbitrary HTML attributes to add to the flash container"

  slot :inner_block, doc: "the optional inner block that renders the flash message"

  def flash(assigns) do
    assigns = assign_new(assigns, :id, fn -> "flash-#{assigns.kind}" end)

    ~H"""
    <div
      :if={msg = render_slot(@inner_block) || Phoenix.Flash.get(@flash, @kind)}
      id={@id}
      phx-click={JS.push("lv:clear-flash", value: %{key: @kind}) |> hide("##{@id}")}
      role="alert"
      class={[
        "fixed top-2 right-2 mr-2 w-80 sm:w-96 z-50 rounded-lg p-3 ring-1",
        @kind == :info && "bg-emerald-50 text-emerald-800 ring-emerald-500 fill-cyan-900",
        @kind == :error && "bg-rose-50 text-rose-900 shadow-md ring-rose-500 fill-rose-900"
      ]}
      {@rest}
    >
      <p :if={@title} class="flex items-center gap-1.5 text-sm font-semibold leading-6">
        <.icon :if={@kind == :info} name="hero-information-circle-mini" class="h-4 w-4" />
        <.icon :if={@kind == :error} name="hero-exclamation-circle-mini" class="h-4 w-4" />
        <%= @title %>
      </p>
      <p class="mt-2 text-sm leading-5"><%= msg %></p>
      <button type="button" class="group absolute top-1 right-1 p-2" aria-label={gettext("close")}>
        <.icon name="hero-x-mark-solid" class="h-5 w-5 opacity-40 group-hover:opacity-70" />
      </button>
    </div>
    """
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"
  attr :id, :string, default: "flash-group", doc: "the optional id of flash container"

  def flash_group(assigns) do
    ~H"""
    <div id={@id}>
      <.flash kind={:info} title={gettext("Success!")} flash={@flash} />
      <.flash kind={:error} title={gettext("Error!")} flash={@flash} />
      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error")}
        phx-connected={hide("#client-error")}
        hidden
      >
        <%= gettext("Attempting to reconnect") %>
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error")}
        phx-connected={hide("#server-error")}
        hidden
      >
        <%= gettext("Hang in there while we get back on track") %>
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>
    </div>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form>
  """
  attr :for, :any, required: true, doc: "the data structure for the form"
  attr :as, :any, default: nil, doc: "the server side parameter to collect all input under"

  attr :rest, :global,
    include: ~w(autocomplete name rel action enctype method novalidate target multipart),
    doc: "the arbitrary HTML attributes to apply to the form tag"

  slot :inner_block, required: true
  slot :actions, doc: "the slot for form actions, such as a submit button"

  def simple_form(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest}>
      <div class="mt-10 space-y-8 bg-white">
        <%= render_slot(@inner_block, f) %>
        <div :for={action <- @actions} class="mt-2 flex items-center justify-between gap-6">
          <%= render_slot(action, f) %>
        </div>
      </div>
    </.form>
    """
  end

  @doc """
  Renders a button.

  ## Examples

      <.button>Send!</.button>
      <.button phx-click="go" class="ml-2">Send!</.button>
  """
  attr :type, :string, default: nil
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value)

  slot :inner_block, required: true

  def button2(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 rounded bg-fdh-blue hover:bg-blue-900 py-2 my-2 px-3",
        "text-sm font-semibold leading-6 text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  @doc """
  Renders a button.

  ## Examples

      <.button>Send!</.button>
      <.button phx-click="go" class="ml-2">Send!</.button>
  """
  attr :type, :string, default: nil
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value)

  slot :inner_block, required: true

  def button1(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 w-full bg-orange-500 bg-fdh-orange text-white rounded-full py-3 px-4 hover:bg-orange-600 transition duration-300 font-semibold text-lg shadow-sm",
        "",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  attr :type, :string, default: nil
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value)

  slot :inner_block, required: true

  def button(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 rounded bg-orange-500 hover:bg-orange-600 py-2 my-2 px-3",
        "text-sm font-semibold leading-6 text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  attr :type, :string, default: nil
  attr :class, :string, default: nil
  attr :rest, :global, include: ~w(disabled form name value)

  slot :inner_block, required: true

  def button3(assigns) do
    ~H"""
    <button
      type={@type}
      class={[
        "phx-submit-loading:opacity-75 rounded bg-rose-500 hover:bg-rose-600 py-2 my-2 px-3",
        "text-sm font-semibold leading-6 text-white active:text-white/80",
        @class
      ]}
      {@rest}
    >
      <%= render_slot(@inner_block) %>
    </button>
    """
  end

  @doc """
  Renders an input with label and error messages.

  A `Phoenix.HTML.FormField` may be passed as argument,
  which is used to retrieve the input name, id, and values.
  Otherwise all attributes may be passed explicitly.

  ## Types

  This function accepts all HTML input types, considering that:

    * You may also set `type="select"` to render a `<select>` tag

    * `type="checkbox"` is used exclusively to render boolean values

    * For live file uploads, see `Phoenix.Component.live_file_input/1`

  See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
  for more information. Unsupported types, such as hidden and radio,
  are best written directly in your templates.

  ## Examples

      <.input field={@form[:email]} type="email" />
      <.input name="my-input" errors={["oh no!"]} />
  """
  attr :id, :any, default: nil
  attr :name, :any
  attr :label, :string, default: nil
  attr :value, :any

  attr :type, :string,
    default: "text",
    values: ~w(checkbox color date datetime-local email file month number password
               range search select tel text textarea time url week phone)

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"

  attr :errors, :list, default: []
  attr :checked, :boolean, doc: "the checked flag for checkbox inputs"
  attr :prompt, :string, default: nil, doc: "the prompt for select inputs"
  attr :options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2"
  attr :multiple, :boolean, default: false, doc: "the multiple flag for select inputs"

  attr :rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)

  def input1(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    errors = if Phoenix.Component.used_input?(field), do: field.errors, else: []

    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input1()
  end

  def input1(%{type: "checkbox"} = assigns) do
    assigns =
      assign_new(assigns, :checked, fn ->
        Phoenix.HTML.Form.normalize_value("checkbox", assigns[:value])
      end)

    ~H"""
    <div>
      <label class="flex items-center gap-4 text-sm leading-6 text-zinc-600">
        <input type="hidden" name={@name} value="false" disabled={@rest[:disabled]} />
        <input
          type="checkbox"
          id={@id}
          name={@name}
          value="true"
          checked={@checked}
          class="rounded border-zinc-300 text-zinc-900 focus:ring-0"
          {@rest}
        />
        <%= @label %>
      </label>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input1(%{type: "select"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <select
        id={@id}
        name={@name}
        class="mt-2 block w-full rounded-md border border-gray-300 bg-white shadow-sm focus:border-zinc-400 focus:ring-0 sm:text-sm"
        multiple={@multiple}
        {@rest}
      >
        <option :if={@prompt} value=""><%= @prompt %></option>
        <%= Phoenix.HTML.Form.options_for_select(@options, @value) %>
      </select>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input1(%{type: "textarea"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <textarea
        id={@id}
        name={@name}
        class={[
          "mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6 min-h-[6rem]",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      ><%= Phoenix.HTML.Form.normalize_value("textarea", @value) %></textarea>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  # All other inputs text, datetime-local, url, password, etc. are handled here...
  def input1(assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <input
        type={@type}
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          "mt-2 block w-full rounded-full text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
          @errors == [] && "border-zinc-300 focus:border-blue-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  @doc """
  Renders an input with label and error messages.

  A `Phoenix.HTML.FormField` may be passed as argument,
  which is used to retrieve the input name, id, and values.
  Otherwise all attributes may be passed explicitly.

  ## Types

  This function accepts all HTML input types, considering that:

    * You may also set `type="select"` to render a `<select>` tag

    * `type="checkbox"` is used exclusively to render boolean values

    * For live file uploads, see `Phoenix.Component.live_file_input/1`

  See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
  for more information. Unsupported types, such as hidden and radio,
  are best written directly in your templates.

  ## Examples

      <.input field={@form[:email]} type="email" />
      <.input name="my-input" errors={["oh no!"]} />
  """
  attr :id, :any, default: nil
  attr :name, :any
  attr :name2, :any
  attr :label, :string, default: nil
  attr :value, :any

  attr :type, :string,
    default: "text",
    values: ~w(checkbox color date datetime-local email file month number password
               range search select tel text textarea time url week radio)

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"

  attr :errors, :list, default: []
  attr :checked, :boolean, doc: "the checked flag for checkbox inputs"
  attr :prompt, :string, default: nil, doc: "the prompt for select inputs"
  attr :options, :list, doc: "the options to pass to Phoenix.HTML.Form.options_for_select/2"
  attr :multiple, :boolean, default: false, doc: "the multiple flag for select inputs"

  attr :rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)

  def input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    errors = if Phoenix.Component.used_input?(field), do: field.errors, else: []

    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:errors, Enum.map(errors, &translate_error(&1)))
    |> assign_new(:name, fn -> if assigns.multiple, do: field.name <> "[]", else: field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> input()
  end

  def input(%{type: "checkbox"} = assigns) do
    assigns =
      assign_new(assigns, :checked, fn ->
        Phoenix.HTML.Form.normalize_value("checkbox", assigns[:value])
      end)

    ~H"""
    <div>
      <label class="flex items-center gap-4 text-sm leading-6 cursor-pointer text-zinc-600">
        <input type="hidden" name={@name} value="false" disabled={@rest[:disabled]} />
        <input
          type="checkbox"
          id={@id}
          name={@name}
          value="true"
          checked={@checked}
          class="rounded border-blue-300 text-fdh-blue focus:ring-0"
          {@rest}
        />
        <%= @label %>
      </label>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "radio"} = assigns) do
    assigns =
      assign_new(assigns, :checked, fn ->
        Phoenix.HTML.Form.normalize_value("radio", assigns[:value])
      end)

    ~H"""
    <div>
      <legend class="text-sm font-semibold leading-6 text-zinc-700"><%= @label %></legend>
      <div :for={{label, value} <- @options} class="mt-2 space-6">
        <div id={"radioGroup-#{@id}-#{value}"} class="flex items-center gap-x-3">
          <input
            id={"#{@id}-#{value}"}
            value={value}
            name={@name}
            type="radio"
            class="h-4 w-4 border-fdh-blue bg-white/5 text-fdh-blue focus:ring-fdh-blue focus:ring-offset-gray-900"
          />
          <.label for={"#{@id}-#{value}"}><%= label %></.label>
        </div>
      </div>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "select"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <select
        id={@id}
        name={@name}
        class="mt-2 block w-full rounded-md border border-gray-300 bg-white shadow-sm focus:border-zinc-400 focus:ring-0 sm:text-sm"
        multiple={@multiple}
        {@rest}
      >
        <option :if={@prompt} value=""><%= @prompt %></option>
        <%= Phoenix.HTML.Form.options_for_select(@options, @value) %>
      </select>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "textarea"} = assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <textarea
        id={@id}
        name={@name}
        class={[
          "mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6 min-h-[6rem]",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      ><%= Phoenix.HTML.Form.normalize_value("textarea", @value) %></textarea>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  def input(%{type: "phone"} = assigns) do
    # Load country phone codes from JSON file
    country_codes =
      case File.read(Path.join(:code.priv_dir(:service_manager), "static/assets/country_phone_codes.json")) do
        {:ok, json} ->
          Jason.decode!(json)
        _ ->
          # Fallback to default codes if file can't be read
          [
            %{"name" => "United States", "code" => "+1"},
            %{"name" => "United Kingdom", "code" => "+44"},
            %{"name" => "Nigeria", "code" => "+234"},
            %{"name" => "Kenya", "code" => "+254"},
            %{"name" => "Tanzania", "code" => "+255"},
            %{"name" => "Uganda", "code" => "+256"},
            %{"name" => "Zambia", "code" => "+260"},
            %{"name" => "Zimbabwe", "code" => "+263"},
            %{"name" => "Malawi", "code" => "+265"},
            %{"name" => "South Africa", "code" => "+27"},
            %{"name" => "India", "code" => "+91"},
            %{"name" => "China", "code" => "+86"},
            %{"name" => "Japan", "code" => "+81"},
            %{"name" => "Australia", "code" => "+61"}
          ]
      end

    # Extract country code and phone number from value if present
    {country_code, phone_number} =
      if assigns[:value] && is_binary(assigns[:value]) do
        # Ensure country codes are available, use default if not.
        codes_list = Enum.map(country_codes, &%{code: &1["code"], name: &1["name"]})

        # Find the longest country code prefix that matches the beginning of the value
        matched_code = Enum.find(Enum.sort_by(codes_list, &String.length(&1.code), :desc), fn country ->
          String.starts_with?(assigns[:value], country.code)
        end)

        if matched_code do
          code = matched_code.code
          # Remove the matched country code and any leading whitespace to get the phone number part
          number = assigns[:value]
          |> String.replace_prefix(code, "")
          |> String.trim_leading()
          {code, number}
        else
          # Fallback if no country code prefix is found. Assume the whole value is the number with default code.
          {"+265", assigns[:value]}  # Default code and the entire value as number
        end
      else
        # Handle nil or non-binary values
        {"+265", ""}  # Default code and empty number
      end

    assigns = assign(assigns, :country_codes, country_codes)
    assigns = assign(assigns, :country_code, country_code)
    assigns = assign(assigns, :phone_number, phone_number)

    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <div class="flex mt-2 relative">
        <div class="w-2/5 inline-flex relative">
          <div class="relative w-full">
            <button
              type="button"
              id={"#{@id}_dropdown_button"}
              class="flex justify-between items-center w-full rounded-l-lg border border-r-0 border-zinc-300 focus:border-zinc-400 focus:ring-0 sm:text-sm sm:leading-6 px-2 py-2 bg-white"
              onclick={"togglePhoneDropdown('#{@id}_dropdown')"}
            >
              <span id={"#{@id}_selected_code"}><%= @country_code %></span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div
              id={"#{@id}_dropdown"}
              class="absolute z-10 w-64 mt-1 bg-white border border-zinc-300 rounded-md shadow-lg hidden max-h-80 overflow-auto"
            >
              <div class="py-2" id={"#{@id}_dropdown_items"}>
                <%= for country <- @country_codes do %>
                  <a
                    href="#"
                    class="block px-4 py-3 text-base hover:bg-gray-100"
                    data-code={country["code"]}
                    data-name={country["name"]}
                    phx-no-format
                  >
                    <span class="font-medium"><%= country["code"] %></span> (<%= country["name"] %>)
                  </a>
                <% end %>
              </div>
            </div>
          </div>
          <!-- Phone input functions are now in app.js -->
        </div>
        <div class="w-3/5 inline-flex">
          <input
            type="tel"
            id={"#{@id}_number"}
            placeholder="Phone number"
            value={@phone_number}
            class={[
              "rounded-r-lg border border-l-0 w-full text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
              @errors == [] && "border-zinc-300 focus:border-zinc-400",
              @errors != [] && "border-rose-400 focus:border-rose-400"
            ]}
            onkeyup={"document.getElementById('#{@id}').value = document.getElementById('#{@id}_selected_code').textContent + ' ' + this.value;"}
            {@rest}
          />
        </div>
        <input
          type="hidden"
          name={@name}
          id={@id}
          value={@value || "#{@country_code} "}
        />
      </div>
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  # All other inputs text, datetime-local, url, password, etc. are handled here...
  def input(assigns) do
    ~H"""
    <div>
      <.label for={@id}><%= @label %></.label>
      <input
        type={@type}
        name={@name}
        id={@id}
        value={Phoenix.HTML.Form.normalize_value(@type, @value)}
        class={[
          "mt-2 block w-full rounded-lg text-zinc-900 focus:ring-0 sm:text-sm sm:leading-6",
          @errors == [] && "border-zinc-300 focus:border-zinc-400",
          @errors != [] && "border-rose-400 focus:border-rose-400"
        ]}
        {@rest}
      />
      <.error :for={msg <- @errors}><%= msg %></.error>
    </div>
    """
  end

  @doc """
  Renders a label.
  """
  attr :for, :string, default: nil
  slot :inner_block, required: true

  def label(assigns) do
    ~H"""
    <label for={@for} class="block text-sm font-semibold leading-6 text-zinc-800">
      <%= render_slot(@inner_block) %>
    </label>
    """
  end

  @doc """
  Generates a generic error message.
  """
  slot :inner_block, required: true

  def error(assigns) do
    ~H"""
    <p class="mt-3 flex gap-3 text-sm leading-6 text-rose-600">
      <.icon name="hero-exclamation-circle-mini" class="mt-0.5 h-5 w-5 flex-none" />
      <%= render_slot(@inner_block) %>
    </p>
    """
  end

  @doc """
  Renders a header with title.
  """
  attr :class, :string, default: nil

  slot :inner_block, required: true
  slot :subtitle
  slot :actions

  def header(assigns) do
    ~H"""
    <header class={[@actions != [] && "flex items-center justify-between gap-6", @class]}>
      <div>
        <h1 class="text-lg font-semibold leading-8 text-zinc-800">
          <%= render_slot(@inner_block) %>
        </h1>
        <p :if={@subtitle != []} class="mt-2 text-sm leading-6 text-zinc-600">
          <%= render_slot(@subtitle) %>
        </p>
      </div>
      <div class="flex-none"><%= render_slot(@actions) %></div>
    </header>
    """
  end

  @doc ~S"""
  Renders a table with generic styling.

  ## Examples

      <.table id="users" rows={@users}>
        <:col :let={user} label="id"><%= user.id %></:col>
        <:col :let={user} label="username"><%= user.username %></:col>
      </.table>
  """
  attr :id, :string, required: true
  attr :filter_params, :map, default: @query_params
  attr :pagination, :map
  attr :selected_column, :string, default: "inserted_at"
  attr :list_of_operators, :list, default: @operators
  attr :operator, :string, default: ""
  attr :query_fields_list, :list, default: []
  attr :rows, :list, required: true
  attr :row_id, :any, default: nil, doc: "the function for generating the row id"
  attr :row_click, :any, default: nil, doc: "the function for handling phx-click on each row"
  attr :filter_url, :string, default: "#"
  attr :export_url, :string, default: "#"
  attr :show_filter, :boolean, default: false
  attr :show_export, :boolean, default: false

  attr :row_item, :any,
    default: &Function.identity/1,
    doc: "the function for mapping each row before calling the :col and :action slots"

  slot :col, required: true do
    attr :label, :string
    attr :filter_item, :string
  end

  slot :action, doc: "the slot for showing user actions in the last table column"

  def table(assigns) do
    assigns =
      with %{rows: %Phoenix.LiveView.LiveStream{}} <- assigns do
        assign(assigns, row_id: assigns.row_id || fn {id, _item} -> id end)
      end

    ~H"""
    <div class="relative">
      <div class="w-full bg-white rounded-lg shadow-sm">
        <div class="p-4 border-b border-gray-200">
          <%= search_field(assigns) %>
        </div>
        <div class="overflow-x-auto" style="position: relative; isolation: isolate;">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  :for={col <- @col}
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                >
                  <div class="flex items-center gap-2">
                    <span><%= col[:label] %></span>
                    <a
                      href={Sorting.table_link_encode_url(@filter_params, col[:filter_item])}
                      data-phx-link="redirect"
                      data-phx-link-state="push"
                      class="inline-flex items-center hover:text-gray-700 transition-colors duration-200"
                    >
                      <%= Phoenix.HTML.raw(
                        icon_def(@filter_params, col[:filter_item], @selected_column)
                      ) %>
                    </a>
                  </div>
                </th>
                <th :if={@action != []} class="relative px-6 py-3">
                  <span class="sr-only"><%= gettext("Actions") %></span>
                </th>
              </tr>
            </thead>
            <tbody
              id={@id}
              phx-update={match?(%Phoenix.LiveView.LiveStream{}, @rows) && "stream"}
              class="bg-white divide-y divide-gray-200"
            >
              <tr
                :for={row <- @rows}
                id={@row_id && @row_id.(row)}
                class="hover:bg-gray-50 transition duration-150"
              >
                <td
                  :for={{col, i} <- Enum.with_index(@col)}
                  phx-click={@row_click && @row_click.(row)}
                  class={[
                    "px-6 py-4 whitespace-nowrap text-sm text-gray-900",
                    i == 0 && "font-medium",
                    @row_click && "cursor-pointer"
                  ]}
                >
                  <%= render_slot(col, @row_item.(row)) %>
                </td>
                <td
                  :if={@action != []}
                  class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right"
                >
                  <span
                    :for={action <- @action}
                    class="text-blue-600 hover:text-blue-900 font-medium cursor-pointer ml-4 first:ml-0"
                  >
                    <%= render_slot(action, @row_item.(row)) %>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <%= render_pagination(assigns) %>
      </div>
    </div>
    """
  end

  @doc ~S"""
  Renders a top_table with generic styling.

  """
  # Add these attributes
  attr :id, :string, required: true
  attr :filter_params, :map, default: %{}
  attr :selected_column, :string, default: "inserted_at"
  attr :filter_url, :string, default: "#"
  attr :export_url, :string, default: "#"
  attr :show_filter, :boolean, default: false
  attr :show_export, :boolean, default: false

  def search_field(assigns) do
    # Calculate if any options are available to show
    assigns = assign(assigns, :show_options, assigns.show_filter || assigns.show_export)

    ~H"""
    <div class="row mb-1">
      <div class="flex flex-col sm:flex-row justify-between items-center w-full gap-4 sm:gap-2">
        <form class="dataTables_filter flex items-center w-full sm:w-auto" phx-submit="search">
          <div class="relative w-full sm:w-auto">
            <input
              type="search"
              value={"#{@filter_params["search"]}"}
              class="w-full sm:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              name="isearch"
              placeholder="Search"
              aria-describedby="basic-addon3"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-gray-400 absolute left-3 top-3"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </form>

        <%= if @show_options do %>
          <div class="relative w-full sm:w-auto">
            <button
              type="button"
              class="w-full sm:w-auto inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              id="options-menu"
              aria-haspopup="true"
              aria-expanded="true"
              phx-click={JS.toggle(to: "#dropdown-menu")}
            >
              Options
              <svg
                class="-mr-1 ml-2 h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>

            <div
              id="dropdown-menu"
              class="hidden origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-50"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="options-menu"
            >
              <div class="py-1" role="none">
                <%= if @show_filter and @filter_url != "#" do %>
                  <.link
                    navigate={@filter_url}
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    role="menuitem"
                  >
                    <svg
                      class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Filter
                  </.link>
                <% end %>

                <%= if @show_export and @export_url != "#" do %>
                  <.link
                    navigate={@export_url}
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    role="menuitem"
                  >
                    <svg
                      class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                      <path
                        fill-rule="evenodd"
                        d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Export to Excel
                  </.link>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  defp render_pagination(assigns) do
    ~H"""
    <!-- BEGIN: Pagination -->
    <div class="intro-y mb-12 mt-5 flex flex-wrap items-center sm:flex-row sm:flex-nowrap">
      <nav class="w-full ml-2 sm:mr-auto sm:w-auto">
        <ul class="flex w-full mr-0 sm:mr-auto sm:w-auto">
          <%= if @pagination[:page_number] > 1 do %>
            <li class="flex-1 sm:flex-initial">
              <a
                href={Pagination.get_priv_pagination_link(@pagination, @filter_params)}
                data-phx-link="redirect"
                data-phx-link-state="push"
                data-tw-merge=""
                class="transition duration-200 border items-center justify-center pt-4 py-3 rounded-md cursor-pointer
                 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700
                  dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:bg-opacity-90 [&:hover:not(:disabled)]:border-opacity-90
                   [&:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px] shadow-none
                    font-normal flex border-transparent text-slate-800 sm:mr-2 px-1 sm:px-3"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5"
                  />
                </svg>
              </a>
            </li>
          <% else %>
            <li class="flex-1 sm:flex-initial">
              <a
                href="#"
                data-tw-merge=""
                class="transition duration-200 border items-center justify-center pt-4 py-3 rounded-md cursor-pointer
                    focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700
                      dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:bg-opacity-90 [&:hover:not(:disabled)]:border-opacity-90
                      [&:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px] shadow-none
                        font-normal flex border-transparent text-slate-400 sm:mr-2 px-1 sm:px-3"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5"
                  />
                </svg>
              </a>
            </li>
          <% end %>

          <%= for number <- gen_page_numbers(@pagination.total_pages, @pagination.page_number) do %>
            <%= if @pagination[:page_number] == number do %>
              <li class="flex-1 sm:flex-initial">
                <a
                  data-tw-merge=""
                  class="transition duration-200 border shadow-lg items-center justify-center py-2
                                             rounded-md cursor-pointer focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus-visible:outline-none
                                              dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:bg-opacity-90
                                               [&:hover:not(:disabled)]:border-opacity-90 [&:not(button)]:text-center
                                               disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px] shadow-none font-normal flex
                                               border-transparent text-fdhBlue sm:mr-2 px-1 sm:px-3 !box dark:bg-darkmode-400"
                >
                  <%= number %>
                </a>
              </li>
            <% else %>
              <li class="flex-1 sm:flex-initial">
                <a
                  href={Pagination.get_number_pagination_link(number, @filter_params)}
                  data-phx-link="redirect"
                  data-phx-link-state="push"
                  data-tw-merge=""
                  class="transition duration-200 border items-center justify-center py-2 rounded-md cursor-pointer focus:ring-4 focus:ring-primary
                   focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50
                    [&:hover:not(:disabled)]:bg-opacity-90 [&:hover:not(:disabled)]:border-opacity-90 [&:not(button)]:text-center
                    disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px] shadow-none font-normal flex border-transparent
                    text-slate-800 sm:mr-2 px-1 sm:px-3"
                >
                  <%= number %>
                </a>
              </li>
            <% end %>
          <% end %>

          <%= if @pagination[:page_number] < @pagination.total_pages do %>
            <li class="flex-1 sm:flex-initial">
              <a
                href={Pagination.get_next_pagination_link(@pagination, @filter_params)}
                data-phx-link="redirect"
                data-phx-link-state="push"
                data-tw-merge=""
                class="transition duration-200 border items-center justify-center pt-4 py-3 rounded-md cursor-pointer focus:ring-4 focus:ring-primary
                focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:bg-opacity-90
                [&:hover:not(:disabled)]:border-opacity-90 [&:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px]
                 shadow-none font-normal flex border-transparent text-slate-800 sm:mr-2 px-1 sm:px-3"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </a>
            </li>
          <% else %>
            <li class="flex-1 sm:flex-initial">
              <a
                href="#"
                data-tw-merge=""
                class="transition duration-200 border items-center justify-center pt-4 py-3 rounded-md cursor-pointer focus:ring-4 focus:ring-primary
                focus:ring-opacity-20 focus-visible:outline-none dark:focus:ring-slate-700 dark:focus:ring-opacity-50 [&:hover:not(:disabled)]:bg-opacity-90
                [&:hover:not(:disabled)]:border-opacity-90 [&:not(button)]:text-center disabled:opacity-70 disabled:cursor-not-allowed min-w-0 sm:min-w-[40px]
                 shadow-none font-normal flex border-transparent text-slate-400 sm:mr-2 px-1 sm:px-3"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="size-3"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"
                  />
                </svg>
              </a>
            </li>
          <% end %>
        </ul>
      </nav>

      <form>
        <select
          name="page_size"
          phx-change="page_size"
          data-tw-merge=""
          class="disabled:bg-slate-100 disabled:cursor-not-allowed disabled:dark:bg-darkmode-800/50 [&[readonly]]:bg-slate-100 [&[readonly]]:cursor-not-allowed [&[readonly]]:dark:bg-darkmode-800/50 transition duration-200 ease-in-out text-sm border-slate-200 shadow-sm rounded-md py-2 px-3 pr-8 focus:ring-4 focus:ring-primary focus:ring-opacity-20 focus:border-primary focus:border-opacity-40 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50 group-[.form-inline]:flex-1 !box mt-3 w-20 sm:mt-0"
        >
          <%= for size <- show_options() do %>
            <%= if size == @filter_params["page_size"] do %>
              <option selected><%= size %></option>
            <% else %>
              <option><%= size %></option>
            <% end %>
          <% end %>
        </select>
      </form>
    </div>
    <!-- END: Pagination -->
    """
  end

  defp icon_def(filter_params, filter_item, selected_column) do
    if filter_item == selected_column do
      if filter_params["sort_order"] == "asc" do
        ~s(
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-4 p-1 text-black">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 15.75 7.5-7.5 7.5 7.5" />
                </svg>
                )
      else
        ~s(<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-4 p-1 text-black">
                <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
              </svg>
              )
      end
    else
      ~s(
          <div class="text-zinc-200">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
            </svg>
          </div>)
    end
  end

  @doc """
  Renders a data list.

  ## Examples

      <.list>
        <:item title="Title"><%= @post.title %></:item>
        <:item title="Views"><%= @post.views %></:item>
      </.list>
  """
  slot :item, required: true do
    attr :title, :string, required: true
  end

  def list(assigns) do
    ~H"""
    <div class="mt-14 p-4 bg-white rounded-lg shadow-xl border">
      <dl class="-my-4 divide-y divide-zinc-100">
        <div :for={item <- @item} class="flex gap-4 py-4 text-sm leading-6 sm:gap-8">
          <dt class="w-1/4 flex-none text-zinc-500"><%= item.title %></dt>
          <dd class="text-zinc-700"><%= render_slot(item) %></dd>
        </div>
      </dl>
    </div>
    """
  end

  @doc """
  Renders a back navigation link.

  ## Examples

      <.back navigate={~p"/posts"}>Back to posts</.back>
  """
  attr :navigate, :any, required: true
  slot :inner_block, required: true

  def back(assigns) do
    ~H"""
    <div class="mt-16">
      <.link
        navigate={@navigate}
        class="text-sm font-semibold leading-6 text-zinc-900 hover:text-zinc-700"
      >
        <.icon name="hero-arrow-left-solid" class="h-3 w-3" />
        <%= render_slot(@inner_block) %>
      </.link>
    </div>
    """
  end

  @doc """
  Renders a [Heroicon](https://heroicons.com).

  Heroicons come in three styles – outline, solid, and mini.
  By default, the outline style is used, but solid and mini may
  be applied by using the `-solid` and `-mini` suffix.

  You can customize the size and colors of the icons by setting
  width, height, and background color classes.

  Icons are extracted from the `deps/heroicons` directory and bundled within
  your compiled app.css by the plugin in your `assets/tailwind.config.js`.

  ## Examples

      <.icon name="hero-x-mark-solid" />
      <.icon name="hero-arrow-path" class="ml-1 w-3 h-3 animate-spin" />
  """
  attr :name, :string, required: true
  attr :class, :string, default: nil

  def icon(%{name: "hero-" <> _} = assigns) do
    ~H"""
    <span class={[@name, @class]} />
    """
  end

  # Dropdown option

  attr :id, :string, required: true
  attr :class, :string, default: ""
  attr :label, :string, required: true
  slot :inner_block, required: true

  def dropdown(assigns) do
    ~H"""
    <div id={@id} x-data="dropdown" class={["relative", @class]}>
      <button
        x-ref="button"
        @click="toggle"
        type="button"
        class="inline-flex items-center mb-2 text-left rounded-lg bg-fdh-blue px-3 py-2 text-md font-medium text-white"
      >
        <span><%= @label %></span>
        <svg
          class="w-4 h-4 ml-2 -mr-1 transition-transform duration-200"
          x-bind:class="open ? 'rotate-180' : ''"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
      <template x-teleport="body">
        <div
          x-ref="dropdown"
          x-show="open"
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="fixed w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-[9999]"
          style="max-height: 80vh; overflow-y: auto; transform: translateX(-16px);"
          x-init="$watch('open', value => value && $nextTick(() => updatePosition()))"
        >
          <div class="py-1 divide-y divide-gray-100">
            <%= render_slot(@inner_block) %>
          </div>
        </div>
      </template>
    </div>
    """
  end

  def collapsible_section(assigns) do
    assigns = assign_new(assigns, :title_class, fn -> "" end)

    ~H"""
    <div x-data="{ open: false }">
      <a
        class={"mt-4 flex justify-between items-center w-full text-sm uppercase font-semibold cursor-pointer #{@title_class}"}
        @click="open = !open"
      >
        <span><%= @title %></span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            x-show="!open"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
          <path
            x-show="open"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M18 12H6"
          />
        </svg>
      </a>
      <hr />
      <div
        x-cloak
        x-show="open"
        x-transition:enter="transition ease-out duration-400"
        x-transition:enter-start="transform opacity-0 scale-95"
        x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75"
        x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        x-collapse.duration.300ms
        class="pl-4 mt-2 border-l-2 border-gray-100"
      >
        <%= render_slot(@inner_block) %>
      </div>
    </div>
    """
  end

  def rights_input_group(assigns) do
    ~H"""
    <div>
      <div class="flex items-center space-x-4 mb-2 cursor-pointer">
        <span class="font-semibold"><%= @title %></span>
        <.input
          field={@all_field}
          type="checkbox"
          label=""
          class="inline-flex items-center justify-end"
          phx-value-group={@title}
        />
      </div>
      <.inputs_for :let={b} field={@field}>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 border p-2 rounded">
          <%= for {right, label} <- @rights do %>
            <.input
              field={b[right]}
              type="checkbox"
              label={label}
              class="child-checkbox"
              data-parent={@title}
              phx-value-group={@title}
            />
          <% end %>
        </div>
      </.inputs_for>
    </div>
    """
  end

  # Updated status_pill component with unknown status
  def status_pill(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
      @status in [true, "true", "active", "sent", "SENT"] &&
        "bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20",
      @status in [false, "failed", "FAILED", "blocked"] &&
        "bg-pink-50 text-pink-700 ring-1 ring-inset ring-pink-700/10",
      @status in ["complete", "COMPLETE", "completed", "COMPLETED", "approved"] &&
        "bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20",
      @status in ["processing", "PROCESSING", "pending", "inactive"] &&
        "bg-orange-50 text-orange-700 ring-1 ring-inset ring-orange-600/20",
      @status in ["active", "ACTIVE"] &&
        "bg-blue-50 text-blue-700 ring-1 ring-inset ring-blue-700/10",
      @status in ["rejected"] && "bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20",
      @status not in [
        true,
        false,
        "ACTIVE",
        "active",
        "complete",
        "COMPLETE",
        "completed",
        "COMPLETED",
        "processing",
        "PROCESSING",
        "pending",
        "approved",
        "rejected"
      ] && "bg-gray-100 text-gray-700 ring-1 ring-inset ring-gray-700/10"
    ]}>
      <%= @text %>
    </span>
    """
  end

  def custom_list(assigns) do
    assigns = assign_new(assigns, :class, fn -> "" end)
    assigns = assign_new(assigns, :style, fn -> :default end)
    assigns = assign_new(assigns, :container_class, fn -> "" end)
    assigns = assign_new(assigns, :items, fn -> [] end)

    ~H"""
    <div class={["p-4 rounded-lg", @container_class]}>
      <%= case @style do %>
        <% :grid -> %>
          <div class={["grid grid-cols-1 md:grid-cols-2 gap-4", @class]}>
            <div
              :for={{title, value} <- @items}
              class="bg-white shadow-sm rounded-lg p-4 border border-gray-100"
            >
              <div class="flex justify-between items-start">
                <dt class="text-sm font-medium text-gray-500"><%= title %></dt>
                <dd class="text-sm text-gray-900"><%= value %></dd>
              </div>
            </div>
          </div>
        <% :table -> %>
          <div class={["overflow-hidden", @class]}>
            <table class="min-w-full divide-y divide-gray-200">
              <tbody class="divide-y divide-gray-200">
                <tr :for={{title, value} <- @items} class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-500">
                    <%= title %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><%= value %></td>
                </tr>
              </tbody>
            </table>
          </div>
        <% :cards -> %>
          <div class={["space-y-4", @class]}>
            <div
              :for={{title, value} <- @items}
              class="bg-white shadow-sm rounded-lg p-4 border border-gray-100"
            >
              <dt class="text-sm font-medium text-gray-500 mb-1"><%= title %></dt>
              <dd class="text-sm text-gray-900"><%= value %></dd>
            </div>
          </div>
        <% _ -> %>
          <dl class={["divide-y divide-gray-100", @class]}>
            <div :for={{title, value} <- @items} class="flex gap-4 py-4 text-sm leading-6 sm:gap-8">
              <dt class="w-1/4 flex-none text-gray-500"><%= title %></dt>
              <dd class="text-gray-900"><%= value %></dd>
            </div>
          </dl>
      <% end %>
    </div>
    """
  end

  ## JS Commands

  def show(js \\ %JS{}, selector) do
    JS.show(js,
      to: selector,
      time: 300,
      transition:
        {"transition-all transform ease-out duration-300",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
         "opacity-100 translate-y-0 sm:scale-100"}
    )
  end

  def hide(js \\ %JS{}, selector) do
    JS.hide(js,
      to: selector,
      time: 200,
      transition:
        {"transition-all transform ease-in duration-200",
         "opacity-100 translate-y-0 sm:scale-100",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"}
    )
  end

  def show_modal(js \\ %JS{}, id) when is_binary(id) do
    js
    |> JS.show(to: "##{id}")
    |> JS.show(
      to: "##{id}-bg",
      time: 300,
      transition: {"transition-all transform ease-out duration-300", "opacity-0", "opacity-100"}
    )
    |> show("##{id}-container")
    |> JS.add_class("overflow-hidden", to: "body")
    |> JS.focus_first(to: "##{id}-content")
  end

  def hide_modal(js \\ %JS{}, id) do
    js
    |> JS.hide(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-in duration-200", "opacity-100", "opacity-0"}
    )
    |> hide("##{id}-container")
    |> JS.hide(to: "##{id}", transition: {"block", "block", "hidden"})
    |> JS.remove_class("overflow-hidden", to: "body")
    |> JS.pop_focus()
  end

  @doc """
  Translates an error message using gettext.
  """
  def translate_error({msg, opts}) do
    # When using gettext, we typically pass the strings we want
    # to translate as a static argument:
    #
    #     # Translate the number of files with plural rules
    #     dngettext("errors", "1 file", "%{count} files", count)
    #
    # However the error messages in our forms and APIs are generated
    # dynamically, so we need to translate them by calling Gettext
    # with our gettext backend as first argument. Translations are
    # available in the errors.po file (as we use the "errors" domain).
    if count = opts[:count] do
      Gettext.dngettext(ServiceManagerWeb.Gettext, "errors", msg, msg, count, opts)
    else
      Gettext.dgettext(ServiceManagerWeb.Gettext, "errors", msg, opts)
    end
  end

  @doc """
  Translates the errors for a field from a keyword list of errors.
  """
  def translate_errors(errors, field) when is_list(errors) do
    for {^field, {msg, opts}} <- errors, do: translate_error({msg, opts})
  end
end
