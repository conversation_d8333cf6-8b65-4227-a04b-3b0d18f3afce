defmodule ServiceManagerWeb.Components.Utilities.RecordAge do
  @moduledoc """
  Provides utility functions for calculating and comparing record ages.
  """

  @doc """
  Compares a given naive datetime with the current time and a specified duration.

  ## Parameters
    - datetime: A NaiveDateTime representing the time to compare
    - duration: A Timex.Duration representing the time period to add

  ## Returns
    - "old" if the current time is past the datetime plus duration ServiceManagerWeb.Components.Utilities.RecordAgeCalculator.test_compare_time_with_duration/0
    - "new" otherwise

  ## Examples
      iex> compare_time_with_duration(~N[2023-01-01 00:00:00], Timex.Duration.from_days(30))
      "old"

      iex> compare_time_with_duration(NaiveDateTime.utc_now(), Timex.Duration.from_hours(1))  result = ServiceManagerWeb.Components.Utilities.RecordAgeCalculator.compare_time_with_duration(datetime, duration)

      "new"
  """
  def age(datetime_string, duration_in_seconds) do
    with {:ok, naive_datetime} <- NaiveDateTime.from_iso8601(datetime_string),
         {:ok, utc_datetime} <- DateTime.from_naive(naive_datetime, "Etc/UTC"),
         compared_time <- DateTime.add(utc_datetime, duration_in_seconds, :second),
         current_time <- DateTime.utc_now() do
      if DateTime.compare(current_time, compared_time) == :gt, do: "old", else: "new"
    else
      _ -> {:error, "Invalid datetime string or duration"}
    end
  end

  @doc """
  ## Examples
      iex> compare_time_with_duration("2023-01-01T00:00:00", 3600)
      "old"

      iex> compare_time_with_duration(DateTime.to_iso8601(NaiveDateTime.utc_now()), 3600)
      "new"
  """
  @doc """
  Tests the `compare_time_with_duration/2` function.
  """
  def test_compare_time_with_duration() do
    # Test with a datetime in the past and a duration of 1 hour
    "2024-10-05 13:15:59.567495" |> age(10)
  end
end
