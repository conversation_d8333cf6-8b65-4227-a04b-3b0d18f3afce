defmodule ServiceManagerWeb.Utilities.PermissionHelpers do
  @moduledoc """
  Helper functions for checking user permissions in LiveView templates.
  This module provides a consistent way to check permissions across different modules.
  """

  alias ServiceManager.Services.Security.Authorization

  @doc """
  Checks if a user can perform a specific action on a resource.
  
  ## Examples
  
      iex> can?(user, :create, :users)
      true
      
      iex> can?(user, :delete, :roles_and_permissions)
      false
  """
  def can?(user, action, resource) do
    Authorization.can?(user, action, resource)
  end

  @doc """
  Checks if a user has any of the specified permissions for a resource.
  
  ## Examples
  
      iex> has_any_permission?(user, [:update, :delete], :users)
      true
      
      iex> has_any_permission?(user, [:create, :update], :reports)
      false
  """
  def has_any_permission?(user, actions, resource) when is_list(actions) do
    Enum.any?(actions, fn action -> can?(user, action, resource) end)
  end

  @doc """
  Checks if a user has all of the specified permissions for a resource.
  
  ## Examples
  
      iex> has_all_permissions?(user, [:view, :update], :users)
      true
      
      iex> has_all_permissions?(user, [:create, :delete], :reports)
      false
  """
  def has_all_permissions?(user, actions, resource) when is_list(actions) do
    Enum.all?(actions, fn action -> can?(user, action, resource) end)
  end

  @doc """
  Common permission check functions for standard CRUD operations.
  """
  def can_create?(user, resource), do: can?(user, :create, resource)
  def can_view?(user, resource), do: can?(user, :view, resource)
  def can_update?(user, resource), do: can?(user, :update, resource)
  def can_delete?(user, resource), do: can?(user, :delete, resource)
  def can_approve?(user, resource), do: can?(user, :approve, resource)
  def can_activate?(user, resource), do: can?(user, :activate, resource)
  def can_review?(user, resource), do: can?(user, :review, resource)

  @doc """
  Checks if a user has any action permissions (useful for showing/hiding action columns).
  """
  def has_any_action_permission?(user, resource) do
    has_any_permission?(user, [:view, :update, :delete, :approve, :activate], resource)
  end

  @doc """
  Macro to generate permission helper functions for a specific resource.
  
  ## Usage
  
      defmodule MyLiveView do
        import ServiceManagerWeb.Utilities.PermissionHelpers
        
        generate_permission_helpers(:users)
        
        # This generates:
        # defp can_create_users?(user), do: can_create?(user, :users)
        # defp can_update_users?(user), do: can_update?(user, :users)
        # etc.
      end
  """
  defmacro generate_permission_helpers(resource) do
    quote do
      defp unquote(:"can_create_#{resource}?")(user), do: unquote(__MODULE__).can_create?(user, unquote(resource))
      defp unquote(:"can_view_#{resource}?")(user), do: unquote(__MODULE__).can_view?(user, unquote(resource))
      defp unquote(:"can_update_#{resource}?")(user), do: unquote(__MODULE__).can_update?(user, unquote(resource))
      defp unquote(:"can_delete_#{resource}?")(user), do: unquote(__MODULE__).can_delete?(user, unquote(resource))
      defp unquote(:"can_approve_#{resource}?")(user), do: unquote(__MODULE__).can_approve?(user, unquote(resource))
      defp unquote(:"can_activate_#{resource}?")(user), do: unquote(__MODULE__).can_activate?(user, unquote(resource))
      defp unquote(:"has_any_#{resource}_permission?")(user), do: unquote(__MODULE__).has_any_action_permission?(user, unquote(resource))
    end
  end
end
