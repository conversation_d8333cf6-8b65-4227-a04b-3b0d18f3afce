defmodule ServiceManagerWeb.Components.Utilities.PathComp do
  @moduledoc """
  Utility module for working with current path in LiveView components.
  Provides functions to compare paths and check menu item states.
  """

  @doc """
  Compares if the current path matches or contains the given path.

  ## Parameters
    - current_path: The current path to check
    - path: The path to compare against
    - match_type: Type of matching to perform (:exact, :starts_with, or :contains)
    
  ## Examples
      iex> compare_current_path("/users/1/edit", "/users", :starts_with)
      true
      iex> compare_current_path("/users/1", "/posts", :exact)
      false
  """
  def compare_current_path(current_path, path, match_type \\ :exact)

  def compare_current_path(current_path, path, :exact) do
    if not is_nil(current_path) and current_path == path do
      true
    else
      false
    end
  end

  def compare_current_path(current_path, path, :starts_with) do
    if not is_nil(current_path) and String.starts_with?(current_path, path) do
      true
    else
      false
    end
  end

  def compare_current_path(current_path, path, :contains) do
    if not is_nil(current_path) and String.contains?(current_path, path) do
      true
    else
      false
    end
  end

  @doc """
  Checks if a menu item should be open based on its href.

  ## Parameters
    - current_path: The current path
    - menu_item: Map containing href key
  """
  def is_active?(current_path, %{href: href}) when not is_nil(href) do
    compare_current_path(current_path, href)
  end

  @doc """
  Checks if any submenu options match the current path.

  ## Parameters
    - current_path: The current path
    - options: List of menu options
  """
  def is_active?(current_path, options) when is_list(options) do
    Enum.any?(options, fn
      %{href: href} when not is_nil(href) ->
        compare_current_path(current_path, href)

      %{options: suboptions} when is_list(suboptions) ->
        is_active?(current_path, suboptions)

      _ ->
        false
    end)
  end

  @doc """
  Checks if any submenu options match or start with the current path.

  ## Parameters
    - current_path: The current path
    - menu_options: List of menu option maps, each containing :href
    - match_type: Type of matching to perform (:exact, :starts_with, or :contains)
    
  ## Returns
    - true if current_path matches any submenu href according to match_type
    - false if no matches found or current_path is nil
  """
  def menu_path_active?(current_path, menu_options, match_type \\ :starts_with) do
    if is_nil(current_path) do
      false
    else
      Enum.any?(menu_options, fn
        %{options: suboptions} when is_list(suboptions) and suboptions != [] ->
          menu_path_active?(current_path, suboptions, match_type)

        %{href: href} when not is_nil(href) ->
          compare_current_path(current_path, href, match_type)

        _ ->
          false
      end)
    end
  end

  @doc """
  Checks if a single menu item is active based on its href.

  ## Parameters
    - current_path: The current path
    - href: The href to check against
    - match_type: Type of matching to perform (:exact, :starts_with, or :contains)
  """
  def menu_item_active?(current_path, href, match_type \\ :starts_with) do
    compare_current_path(current_path, href, match_type)
  end
end
