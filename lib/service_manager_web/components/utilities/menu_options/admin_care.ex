defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.AdminCare do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    [
      user(user_rights),
      wallets(user_rights),
      cards(user_rights),
      admin_user(user_rights),
      beneficiaries(user_rights)
    ]
  end

  def beneficiaries(_user_rights) do
    %{
      id: 0,
      title: "Beneficiaries",
      icon: "users",
      href: "/mobileBanking/beficiaries",
      user_access: {:backend, :beneficiaries, :index},
      type: :link,
      options: []
    }
  end

  defp user(_user_rights) do
    %{
      id: 0,
      title: "Mobile App Accounts",
      icon: "users",
      href: "/mobileBanking/user_managements",
      user_access: {:backend, :users, :index},
      type: :link,
      options: []
    }
  end

  defp admin_user(_user_rights) do
    %{
      id: 0,
      title: "Admin Accounts",
      icon: "user",
      href: "/mobileBanking/SystemUsers",
      user_access: {:backend, :users, :index},
      type: :link,
      options: []
    }
  end

  defp wallets(_user_rights) do
    %{
      id: 0,
      title: "Wallet Accounts",
      icon: "users",
      href: "/mobileBanking/WalletsOverview",
      user_access: {:backend, :wallets_reports, :index},
      type: :link,
      options: []
    }
  end

  defp cards(_user_rights) do
    %{
      id: 0,
      title: "Card Management",
      icon: "credit-card",
      href: "/mobileBanking/cards",
      user_access: {:backend, :wallets_reports, :index},
      type: :link,
      options: []
    }
  end
end
