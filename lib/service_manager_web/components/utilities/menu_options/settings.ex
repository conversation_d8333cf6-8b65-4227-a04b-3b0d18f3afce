defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Settings do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "SETTINGS",
      icon: "cog",
      user_access: [
        {:backend, :web_settings, :index},
        {:backend, :application_settings, :index},
        {:backend, :apis_settings, :index}
      ],
      type: :link,
      href: "/mobileBanking/systemSettings"
    }
  end
end
