defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Logs do
  def render_menu(user) do
    options(user.user_permissions.rights)
  end

  defp options(user_rights) do
    %{
      id: 20,
      title: "Logs",
      icon: "users",
      user_access: [
        {:backend, :currency, :index},
        {:backend, :exchange_rate, :index}
      ],
      type: :header,
      options: [
        finch_logs(user_rights),
        oban_logs(user_rights),
        activity_logs(user_rights),
        sms_logs(user_rights)
      ]
    }
  end

  defp activity_logs(_user_rights) do
    %{
      id: 20,
      title: "Activity Logs",
      icon: "server",
      href: "/mobileBanking/monitor/backend/logs",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp sms_logs(_user_rights) do
    %{
      id: 20,
      title: "SMS Logs",
      icon: "server",
      href: "/mobileBanking/sms_logs",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp finch_logs(_user_rights) do
    %{
      id: 0,
      title: "T24 Logs",
      icon: "server",
      href: "/mobileBanking/SystemLogs/t24",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp oban_logs(_user_rights) do
    %{
      id: 0,
      title: "Orban Job Logs",
      icon: "archive",
      href: "/mobileBanking/SystemLogs/oban",
      user_access: {:backend, :exchange_rate, :index},
      type: :link,
      options: []
    }
  end

  defp fees(_user_rights) do
    %{
      id: 0,
      title: "Fees & Charges",
      icon: "file-invoice-dollar",
      href: "/mobileBanking/fees&Chargers",
      user_access: {:backend, :fees, :index},
      type: :link,
      options: []
    }
  end

  defp virtual_cards(_user_rights) do
    %{
      id: 0,
      title: "Virtual Card APIs",
      icon: "credit-card",
      href: "/mobileBanking/VirtualCardsAPIConfigs",
      user_access: {:backend, :virtual_cards, :index},
      type: :link,
      options: []
    }
  end

  defp paylink_api(_user_rights) do
    %{
      id: 0,
      title: "Paylink Api",
      icon: "link",
      href: "/",
      user_access: {:backend, :paylink, :index},
      type: :link,
      options: []
    }
  end

  defp gift_card(_user_rights) do
    %{
      id: 0,
      title: "Gift Card APIs",
      icon: "gift",
      href: "/",
      user_access: {:backend, :gift_cards, :index},
      type: :link,
      options: []
    }
  end

  defp pay_bill_methods(_user_rights) do
    %{
      id: 20,
      title: "Payment Methods",
      icon: "file-invoice",
      href: "/mobileBanking/paymentMethods",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end
end
