defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Loanmanagement do
  def render_menu(user) do
    options(user.user_permissions.rights)
  end

  defp options(user_rights) do
    %{
      id: 20,
      title: "Loan Management",
      icon: "money-bill-wave",
      user_access: [
        {:backend, :loan_products, :index},
        {:backend, :loan_partnership, :index},
        {:backend, :loan_upload_products, :index},
        {:backend, :loan_customers, :index},
        {:backend, :loan_upload_customers, :index},
        {:backend, :loan_customers_batches, :index},
        {:backend, :loan_reports, :index},
        {:backend, :loan_transactions, :index}
      ],
      type: :header,
      options: [
        loan_config(user_rights),
        loan_customers(user_rights),
        loan_reports(user_rights)
      ]
    }
  end

  defp loan_config(user_rights) do
    %{
      id: 0,
      title: "Loan Configuration",
      icon: "cogs",
      user_access: [
        {:backend, :loan_products, :index},
        {:backend, :loan_partnership, :index},
        {:backend, :loan_upload_products, :index}
      ],
      type: :dropdown,
      options: [
        %{
          id: 1,
          icon: "handshake",
          title: "Loan Partnerships",
          href: "/mobileBanking/loan_mgt/partnerships",
          user_access: {:backend, :loan_partnership, :index},
          type: :link
        },
        %{
          id: 2,
          icon: "file-invoice-dollar",
          title: "Loan Products",
          href: "/mobileBanking/loan_mgt/products",
          user_access: {:backend, :loan_products, :index},
          type: :link
        },
        %{
          id: 3,
          icon: "upload",
          title: "Upload Products",
          href: "/mobileBanking/loan_mgt/upload_products",
          user_access: {:backend, :loan_upload_products, :index},
          type: :link
        }
      ]
    }
  end

  defp loan_customers(user_rights) do
    %{
      id: 0,
      title: "Loan Customers",
      icon: "users",
      user_access: [
        {:backend, :loan_upload_customers, :index},
        {:backend, :loan_customers_batches, :index},
        {:backend, :loan_customers, :index}
      ],
      type: :dropdown,
      options: [
        %{
          id: 1,
          icon: "user-plus",
          title: "Upload Customers",
          href: "/mobileBanking/loan_mgt/customers/upload_customers",
          user_access: {:backend, :loan_upload_customers, :index},
          type: :link
        },
        %{
          id: 2,
          icon: "user-friends",
          title: "Customer Batches",
          href: "/mobileBanking/loan_mgt/customer_batches",
          user_access: {:backend, :loan_customers_batches, :index},
          type: :link
        },
        %{
          id: 3,
          icon: "address-book",
          title: "All Customers",
          href: "/mobileBanking/loan_mgt/customers",
          user_access: {:backend, :loan_customers, :index},
          type: :link
        }
      ]
    }
  end

  defp loan_reports(user_rights) do
    %{
      id: 0,
      title: "Reports",
      icon: "chart-line",
      user_access: [
        {:backend, :loan_reports, :index},
        {:backend, :loan_transactions, :index}
      ],
      type: :dropdown,
      options: [
        %{
          id: 1,
          icon: "file-alt",
          title: "Loan Reports",
          href: "/mobileBanking/loan_mgt/loan_reports",
          user_access: {:backend, :loan_reports, :index},
          type: :link
        },
        %{
          id: 2,
          icon: "exchange-alt",
          title: "Loan Transactions",
          href: "/mobileBanking/loan_mgt/loan_transactions",
          user_access: {:backend, :loan_transactions, :index},
          type: :link
        },
        %{
          id: 3,
          icon: "money-check-alt",
          title: "Loan Charges",
          href: "/mobileBanking/loan_mgt/charges",
          user_access: {:backend, :loan_transactions, :index},
          type: :link
        }
      ]
    }
  end
end
