defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.VerificationCenter do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "VERIFICATION CENTER",
      icon: "envelope",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        email(user_rights)
      ]
    }
  end

  defp email(_user_rights) do
    %{
      id: 0,
      title: "Setuo Email",
      icon: "envelope",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end
end
