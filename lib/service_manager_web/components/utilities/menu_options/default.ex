defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Default do
  def render_menu(user) do
    options(user.user_permissions.rights)
  end

  defp options(user_rights) do
    %{
      id: 20,
      title: "DEFAULT",
      icon: "users",
      user_access: [
        {:backend, :currency, :index},
        {:backend, :exchange_rate, :index},
        {:backend, :fees, :index},
        {:backend, :virtual_cards, :index},
        {:backend, :paylink, :index},
        {:backend, :gift_cards, :index}
      ],
      type: :header,
      options: [
        # currency(user_rights),
        # exchange_rate(user_rights),
        fees(user_rights),
        # virtual_cards(user_rights),
        # pay_bill_methods(user_rights),
        check_requests(user_rights),
        check_canclation_request(user_rights),
        wallet_tiers(user_rights)
        #            paylink_api(user_rights),
        #            gift_card(user_rights),
        #            pay_bill_methods(user_rights),
      ]
    }
  end

  defp currency(_user_rights) do
    %{
      id: 0,
      title: "Setup Currency",
      icon: "cog",
      href: "/mobileBanking/currencies",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp exchange_rate(_user_rights) do
    %{
      id: 0,
      title: "Exchange Rate",
      icon: "exchange-alt",
      href: "/mobileBanking/exchange_rates",
      user_access: {:backend, :exchange_rate, :index},
      type: :link,
      options: []
    }
  end

  defp fees(_user_rights) do
    %{
      id: 0,
      title: "Fees & Charges",
      icon: "file-invoice-dollar",
      href: "/mobileBanking/fees&Chargers",
      user_access: {:backend, :fees, :index},
      type: :link,
      options: []
    }
  end

  defp virtual_cards(_user_rights) do
    %{
      id: 0,
      title: "Virtual Card APIs",
      icon: "credit-card",
      href: "/mobileBanking/VirtualCardsAPIConfigs",
      user_access: {:backend, :virtual_cards, :index},
      type: :link,
      options: []
    }
  end

  defp paylink_api(_user_rights) do
    %{
      id: 0,
      title: "Paylink Api",
      icon: "link",
      href: "/",
      user_access: {:backend, :paylink, :index},
      type: :link,
      options: []
    }
  end

  defp gift_card(_user_rights) do
    %{
      id: 0,
      title: "Gift Card APIs",
      icon: "gift",
      href: "/",
      user_access: {:backend, :gift_cards, :index},
      type: :link,
      options: []
    }
  end

  defp check_requests(_user_rights) do
    %{
      id: 0,
      title: "Cheque Book Requests",
      icon: "book",
      href: "/mobileBanking/cheque-book-requests",
      user_access: {:backend, :gift_cards, :index},
      type: :link,
      options: []
    }
  end

  defp pay_bill_methods(_user_rights) do
    %{
      id: 20,
      title: "Payment Methods",
      icon: "file-invoice",
      href: "/mobileBanking/paymentMethods",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp check_canclation_request(_user_rights) do
    %{
      id: 21,
      title: "Cheque Cancelation Requests",
      icon: "cancel",
      href: "/mobileBanking/cheque-requests",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end

  defp wallet_tiers(_user_rights) do
    %{
      id: 25,
      title: "Wallet Tiers",
      icon: "wallet",
      href: "/mobileBanking/WalletTiers",
      user_access: {:backend, :currency, :index},
      type: :link,
      options: []
    }
  end
end
