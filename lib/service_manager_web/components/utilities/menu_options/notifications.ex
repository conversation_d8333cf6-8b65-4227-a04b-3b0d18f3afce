defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Notifications do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "NOTIFICATIONS",
      icon: "bell",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        push(user_rights),
        notification(user_rights)
      ]
    }
  end

  defp push(_user_rights) do
    %{
      id: 0,
      title: "Push Notification",
      icon: "bell",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end

  defp notification(_user_rights) do
    %{
      id: 0,
      title: "Notification",
      icon: "comment",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end
end
