defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.PaymentMethods do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "PAYMENT METHODS",
      icon: "plus-circle",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        add(user_rights),
        withdraw(user_rights)
      ]
    }
  end

  defp add(_user_rights) do
    %{
      id: 0,
      title: "Add Money",
      icon: "plus-circle",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end

  defp withdraw(_user_rights) do
    %{
      id: 0,
      title: "Withdraw",
      icon: "minus-circle",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end
end
