defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.Bonus do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "BONUS",
      icon: "newspaper",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        cache(user_rights)
      ]
    }
  end

  defp cache(_user_rights) do
    %{
      id: 0,
      title: "Clear Cache",
      icon: "broom",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end
end
