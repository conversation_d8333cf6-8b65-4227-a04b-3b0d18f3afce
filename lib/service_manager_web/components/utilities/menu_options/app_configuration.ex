defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.AppConfiguration do
  @moduledoc """
  Menu configuration for App Configuration section including Mobile Forms and Dynamic Forms
  """
  def render_menu(_user_rights) do
    %{
      id: 31,
      title: "App Configuration",
      icon: "adjustments-horizontal",
      user_access: [
        {:backend, :mobile_forms, :index},
        {:backend, :dynamic_forms, :index}
      ],
      type: :dropdown,
      options: [
        mobile_forms(),
        dynamic_forms()
      ]
    }
  end

  defp mobile_forms() do
    %{
      id: 1,
      title: "Mobile Forms",
      icon: "device-phone-mobile",
      href: "/mobileBanking/mobile-forms",
      user_access: {:backend, :mobile_forms, :index},
      type: :link
    }
  end

  defp dynamic_forms() do
    %{
      id: 2,
      title: "Dynamic Forms",
      icon: "code-bracket",
      href: "/mobileBanking/dynamic-forms",
      user_access: {:backend, :dynamic_forms, :index},
      type: :link
    }
  end
end