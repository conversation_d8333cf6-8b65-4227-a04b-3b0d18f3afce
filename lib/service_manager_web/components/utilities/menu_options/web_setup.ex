defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.WebSetup do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "SETUP WEB CONTENT",
      icon: "file-alt",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        pages(user_rights),
        useful_links(user_rights),
        ext(user_rights)
      ]
    }
  end

  defp pages(_user_rights) do
    %{
      id: 0,
      title: "Setup Pages",
      icon: "file-alt",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end

  defp useful_links(_user_rights) do
    %{
      id: 0,
      title: "Useful Links",
      icon: "link",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end

  defp ext(_user_rights) do
    %{
      id: 0,
      title: "Extensions",
      icon: "puzzle-piece",
      href: "/",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end
end
