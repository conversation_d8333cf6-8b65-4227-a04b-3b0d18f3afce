defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.UserManagement do
  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "User Management",
      icon: "users",
      user_access: {:user_management, :view},
      type: :header,
      options: [
        system_users(user_rights),
        agents(user_rights),
        user_rights(user_rights)
      ]
    }
  end

  defp system_users(_user_rights) do
    %{
      id: 0,
      title: "System Users",
      icon: "users",
      href: "/userManagement",
      user_access: {:users, :view},
      type: :dropdown,
      options: []
    }
  end

  defp agents(_user_rights) do
    %{
      id: 1,
      title: "Agents",
      icon: "user",
      href: "/agentsManagement",
      user_access: {:agents, :view},
      type: :dropdown,
      options: []
    }
  end

  defp user_rights(_user_rights) do
    %{
      id: 2,
      title: "User Roles & Rights",
      icon: "unlock",
      href: "/user_roles",
      user_access: {:user_roles, :view},
      type: :dropdown,
      options: []
    }
  end
end
