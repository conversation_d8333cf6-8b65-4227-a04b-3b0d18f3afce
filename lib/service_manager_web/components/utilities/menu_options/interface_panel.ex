defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.InterfacePanel do
  alias ServiceManagerWeb.Components.Utilities.MenuOptions.{
    AdminCare
  }

  def render_menu(user_rights), do: options(user_rights)

  defp options(user_rights) do
    %{
      id: 20,
      title: "Interface Panel",
      icon: "user-shield",
      user_access: [
        {:backend, :beneficiaries, :index},
        {:backend, :users, :index}
      ],
      type: :header,
      options: [
        admin_care(user_rights),
        logs(user_rights)
      ]
    }
  end

  defp admin_care(user_rights) do
    %{
      id: 0,
      title: "Admin Care",
      icon: "user-shield",
      href: "#",
      user_access: [
        {:backend, :beneficiaries, :index},
        {:backend, :users, :index}
      ],
      type: :dropdown,
      options: AdminCare.render_menu(user_rights)
    }
  end

  defp logs(_user_rights) do
    %{
      id: 2,
      title: "Roles & Permissions",
      icon: "unlock",
      href: "/mobileBanking/ruserRoles&permissions",
      user_access: {:backend, :beneficiaries, :index},
      type: :link,
      options: []
    }
  end
end
