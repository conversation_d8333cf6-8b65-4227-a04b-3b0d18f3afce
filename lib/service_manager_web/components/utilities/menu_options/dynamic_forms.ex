defmodule ServiceManagerWeb.Components.Utilities.MenuOptions.DynamicForms do
  def render_menu(_user_rights) do
    %{
      id: 30,
      title: "Dynamic Forms",
      icon: "code",
      user_access: [
        {:backend, :dynamic_forms, :index},
        {:backend, :dynamic_processes, :index}
      ],
      type: :dropdown,
      options: [
        dashboard(),
        forms(),
        processes(),
        code_steps(),
        routes(),
        api_docs()
      ]
    }
  end


  defp dashboard() do
    %{
      id: 1,
      title: "Dashboard",
      icon: "chart-bar",
      href: "/mobileBanking/dynamic-forms",
      user_access: {:backend, :dynamic_forms, :index},
      type: :link
    }
  end

  defp forms() do
    %{
      id: 2,
      title: "Forms",
      icon: "document-text",
      href: "/mobileBanking/dynamic-forms/forms",
      user_access: {:backend, :dynamic_forms, :index},
      type: :link
    }
  end

  defp processes() do
    %{
      id: 3,
      title: "Processes & Plugins",
      icon: "cog",
      href: "/mobileBanking/dynamic-forms/processes",
      user_access: {:backend, :dynamic_processes, :index},
      type: :link
    }
  end

  defp code_steps() do
    %{
      id: 4,
      title: "Code Steps Library",
      icon: "squares-plus",
      href: "/mobileBanking/dynamic-forms/code-steps",
      user_access: {:backend, :dynamic_processes, :index},
      type: :link
    }
  end

  defp routes() do
    %{
      id: 5,
      title: "API Routes",
      icon: "globe-alt",
      href: "/mobileBanking/dynamic-forms/routes",
      user_access: {:backend, :dynamic_forms, :index},
      type: :link
    }
  end

  defp api_docs() do
    %{
      id: 6,
      title: "API Documentation",
      icon: "book-open",
      href: "/mobileBanking/dynamic-forms/api-docs",
      user_access: {:backend, :dynamic_forms, :index},
      type: :link
    }
  end
end
