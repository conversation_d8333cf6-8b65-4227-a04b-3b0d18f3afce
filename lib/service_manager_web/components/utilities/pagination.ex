defmodule ServiceManagerWeb.Utilities.Pagination do
  import ServiceManagerWeb.Utilities.Extractors
  # import Phoenix.LiveView.Helpers

  def filter_pagination_link(params) do
    filter_params =
      %{
        "first_name" => params["first_name"] || params[:first_name],
        "last_name" => params["last_name"] || params[:last_name],
        "email" => params["email"] || params[:email],
        "phone_number" => params["phone_number"] || params[:phone_number],
        "approved" => params["approved"] || params[:approved],
        "name" => params["name"] || params[:name],
        "nickname" => params["nickname"] || params[:nickname],
        "page" => params["page"] || params[:page] || "1",
        "sort_field" => params["sort_field"] || params[:sort_field] || "inserted_at",
        "sort_order" => params["sort_order"] || params[:sort_order] || "desc",
        "search" => params["search"] || params[:search],
        "page_size" => params["page_size"] || params[:page_size] || "10",
        "from_date" => params["from_date"] || params[:from_date],
        "to_date" => params["to_date"] || params[:to_date],
        "operator" => params["operator"] || params[:operator],
        "query_field" => params["query_field"] || params[:query_field],
        "query_search" => params["query_search"] || params[:query_search],
        "start_date" => params["start_date"] || params[:start_date],
        "end_date" => params["end_date"] || params[:end_date],
        "status" => params["status"] || params[:status],
        "type" => params["type"] || params[:type],
        "amount_from" => params["amount_from"] || params[:amount_from],
        "amount_to" => params["amount_to"] || params[:amount_to],
        "reference" => params["reference"] || params[:reference],
        "from_account" => params["from_account"] || params[:from_account],
        "to_account" => params["to_account"] || params[:to_account],
        "user" => params["user"] || params[:user],
        "account" => params["account"] || params[:account],
        "tag" => params["tag"] || params[:tag],
        "account_name" => params["account_name"] || params[:account_name],
        "account_type" => params["account_type"] || params[:account_type],
        "currency" => params["currency"] || params[:currency],
        "balance_from" => params["balance_from"] || params[:balance_from],
        "balance_to" => params["balance_to"] || params[:balance_to],
        "mobile_number" => params["mobile_number"] || params[:mobile_number]
      }
      |> Enum.filter(fn {_, v} -> v != nil and v != "" end)
      |> Enum.into(%{})
      |> URI.encode_query()

    "?" <> filter_params
  end

  def get_priv_pagination_link(entries, params) do
    opts = generate_options(params, reduce_page(entries.page_number))
    "?" <> querystring(params, opts)
  end

  def get_next_pagination_link(entries, params) do
    opts = generate_options(params, add_page(entries.page_number))

    "?" <> querystring(params, opts)
  end

  def get_number_pagination_link(page, params) do
    opts = generate_options(params, page)

    "?" <> querystring(params, opts)
  end

  defp querystring(request, opts) do
    params =
      request
      # |> Map.delete("page")
      # |> Map.delete(:page)
      |> Plug.Conn.Query.encode()
      |> URI.decode_query()

    opts = %{
      # For the pagination
      "page" => get_page(opts),
      "page_size" => get_page_size(opts),
      "sort_field" => get_sort_field(opts),
      "sort_order" => get_sort_order(opts)
    }

    params
    |> Map.merge(opts)
    |> Enum.filter(fn {_, v} -> v != nil end)
    |> Enum.into(%{})
    |> URI.encode_query()
  end

  def generate_options(params, page) do
    %{
      sort_field: get_sort_field(params),
      sort_order: get_sort_order(params),
      search: get_search(params),
      page_size: get_page_size(params),
      page: page
    }
  end

  def reduce_page(page_number) do
    page_number - 1
  end

  def add_page(page_number) do
    page_number + 1
  end
end
