defmodule ServiceManagerWeb.Components.Utilities.AdminMenu do
  alias ServiceManagerWeb.Components.Utilities.MenuOptions.{
    InterfacePanel,
    Default,
    Transactions,
    Loanmanagement,
    Settings,
    VerificationCenter,
    WebSetup,
    PaymentMethods,
    Notifications,
    Bonus,
    Logs,
    DynamicForms
  }

  alias ServiceManager.Services.Security.SecurityEnforcement

  def render_menu(user_rights \\ %{}) do
    if is_nil(user_rights) == false do
      [
        dashboard(user_rights),
        Default.render_menu(user_rights),
        Transactions.render_menu(user_rights),
        Loanmanagement.render_menu(user_rights),
        InterfacePanel.render_menu(user_rights),
        Settings.render_menu(user_rights),
        VerificationCenter.render_menu(user_rights),
        WebSetup.render_menu(user_rights),
        PaymentMethods.render_menu(user_rights),
        Notifications.render_menu(user_rights),
        Bonus.render_menu(user_rights),
        Logs.render_menu(user_rights)
      ]
    else
      []
    end
    |> SecurityEnforcement.filter_menu_options(user_rights)
  end

  defp dashboard(_user_rights) do
    %{
      id: 0,
      title: "Dashboard",
      icon: "th-large",
      href: "/mobileBanking",
      user_access: {:backend, :miscellaneous, :index},
      type: :link
    }
  end
end
