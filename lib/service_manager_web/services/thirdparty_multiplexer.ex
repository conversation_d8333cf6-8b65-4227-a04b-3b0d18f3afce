defmodule ServiceManagerWeb.Services.ThirdPartyMultiplexer do
  @moduledoc """
  Multiplexer for routing third-party service requests to appropriate handlers
  """

  alias ServiceManagerWeb.Api.Services.Remote.ProfileFromRemoteService, as: RemoteProfileService

  # Define service routes as a list of tuples
  @service_routes [
    # Registration Services
    {"REGISTRATION", "MOBILE_BANKING_REGISTRATION", "ServiceManagerWeb.Services.ThirdParty.RegistrationService.handle_mobile_banking_registration(params)"},
    
    # Profile Management Services
    {"PROFILE", "DISABLE_USER", "ServiceManagerWeb.Api.Services.ProfileServiceController.disable_user(params[\"user_id\"])"},
    {"PROFILE", "ENABLE_USER", "ServiceManagerWeb.Api.Services.ProfileServiceController.enable_user(params[\"user_id\"])"},
    {"PROFILE", "APPROVE", "ServiceManagerWeb.Api.Services.ProfileServiceController.approve(params)"},
    {"PROFILE", "DISABLE", "ServiceManagerWeb.Api.Services.ProfileServiceController.disable(params)"},
    {"PROFILE", "REGISTER", "ServiceManagerWeb.Api.Services.ProfileServiceController.register(%{}, params)"},
    {"PROFILE", "UPDATE", "ServiceManagerWeb.Api.Services.ProfileServiceController.update(params[\"user\"], params)"},
    {"PROFILE", "UPDATE_PASSWORD", "ServiceManagerWeb.Api.Services.ProfileServiceController.update_password(params[\"user\"], params)"},
    {"PROFILE", "UPDATE_MULTI_SESSION", "ServiceManagerWeb.Api.Services.ProfileServiceController.update_multi_session_setting(params[\"user\"], params)"},
    {"PROFILE", "GET_DETAILS", "ServiceManagerWeb.Api.Services.ProfileServiceController.details(params[\"user\"], params)"},
    {"PROFILE", "GET_USER_BY_EMAIL", "ServiceManagerWeb.Api.Services.ProfileServiceController.get_user_by_email(params[\"email\"])"},
    {"PROFILE", "UPDATE_NICKNAME", "ServiceManagerWeb.Services.ThirdPartyMultiplexer.update_nickname(params)"},
    
    # Account Management Services
    {"ACCOUNT", "LINK_BANK_ACCOUNT", "ServiceManagerWeb.Api.Services.ProfileServiceController.link_bank_account(params)"},
    {"ACCOUNT", "LINK_ACCOUNT", "ServiceManagerWeb.ProfileController.link_account(params)"},
    {"ACCOUNT", "GET_BALANCE", "ServiceManagerWeb.Api.Services.ProfileServiceController.get_balance(params[\"account_number\"])"},
    {"ACCOUNT", "GET_BY_NUMBER", "ServiceManagerWeb.Services.ThirdPartyMultiplexer.get_account_by_number(params)"},
    {"ACCOUNT", "GET_BY_NUMBER_V2", "ServiceManagerWeb.Services.ThirdPartyMultiplexer.get_account_by_number_v2(params)"}
  ]

  @doc """
  Routes requests based on service and service_action parameters
  """
  def route(%{"service" => service, "service_action" => action} = params) do
    case find_route(service, action) do
      {:ok, handler_code} ->
        execute_handler(handler_code, params)

      {:error, _} ->
        {:error, "Unsupported service: #{service} with action: #{action}"}
    end
  end

  def route(_params) do
    {:error, "Missing required service or service_action parameters"}
  end

  defp find_route(service, action) do
    case Enum.find(@service_routes, fn {s, a, _} -> s == service && a == action end) do
      {_, _, handler_code} -> {:ok, handler_code}
      nil -> {:error, :not_found}
    end
  end

  defp execute_handler(handler_code, params) do
    try do
      {result, _} = Code.eval_string(handler_code, [params: params], __ENV__)
      result
    rescue
      error ->
        {:error, "Handler execution failed: #{inspect(error)}"}
    end
  end

  # Wrapper functions for operations that require conn
  def update_nickname(%{"user" => user, "nickname" => nickname} = params) do
    ServiceManagerWeb.Api.Services.ProfileServiceController.update(user, %{"nickname" => nickname})
  end

  def get_account_by_number(%{"account_number" => account_number} = params) do
    case RemoteProfileService.get_profile_by_account_number_v3(account_number) do
      {:ok, profile} ->
        %{
          "data" => %{
            "bank_profile" => profile
          },
          "message" => "success",
          "status" => true
        }

      {:error, reason} ->
        %{
          "data" => %{},
          "message" => reason,
          "status" => false
        }
    end
  end

  def get_account_by_number_v2(%{"account_number" => account_number} = params) do
    case RemoteProfileService.get_profile_by_account_number_v4(account_number) do
      {:ok, profile} ->
        %{
          "data" => %{
            "bank_profile" => profile
          },
          "message" => "success",
          "status" => true
        }

      {:error, reason} ->
        %{
          "data" => %{},
          "message" => reason,
          "status" => false
        }
    end
  end
end 