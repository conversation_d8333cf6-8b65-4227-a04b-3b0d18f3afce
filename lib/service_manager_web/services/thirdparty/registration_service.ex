defmodule ServiceManagerWeb.Services.ThirdParty.RegistrationService do
  @moduledoc """
  Handles registration-related third-party service requests
  """

  alias ServiceManagerWeb.Validators.ThirdPartyServiceValidator
  alias ServiceManagerWeb.Api.Services.ProfileServiceController, as: ProfileService
  alias ServiceManagerWeb.ProfileController
  alias ServiceManager.Accounts.User, as: User
  alias ServiceManager.Contexts.UserManagementContext
  alias ServiceManager.Accounts.User, as: UserManagement
  alias ServiceManager.Repo
  alias ServiceManager.Notifications.SMSNotification

  @doc """
  Handles mobile banking registration requests
  """
  def handle_mobile_banking_registration(params) do
    with {:ok, validated_params} <- validate_registration_params(params),
         {:ok, transformed_params} <- transform_registration_params(validated_params),
         {:ok, result} <- process_registration(transformed_params) do
      {:ok, result}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp validate_registration_params(params) do
    ThirdPartyServiceValidator.validate_mobile_banking_registration(params)
  end

  defp transform_registration_params(params) do
    transformed = %{
      "phone_number" => params["phone_number"],
      "username" => params["user_id"],
      "password" => params["password"],
      "customer_number" => params["customer_number"],
      "email" => params["email_address"],
      "account_officer" => params["account_officer"],
      "profile_type" => params["profile_type"],
      "profile_name" => params["profile_name"],
      "company" => params["company"],
      "inputter" => params["inputter"],
      "authoriser" => params["authoriser"],
      "first_name" => params["first_name"],
      "last_name" => params["last_name"]
    }

    {:ok, transformed}
  end

  # TODO: Add logic to handle registration of mobile banking account

  defp process_registration(registration_params) do
    customer_number = registration_params["customer_number"]

    # Store in ETS table with TTL cleanup
    :ets.insert(:registration_cache, {to_string(customer_number), registration_params, :os.system_time(:second) + 300})

    # Optional: Keep Cachex as backup
    Cachex.put(:registration, customer_number, registration_params, ttl: :timer.minutes(5))

    with {:ok, _} <- validate_customer_not_exists(customer_number),
         {:ok, account_ids} <- fetch_customer_accounts(customer_number),
         {:ok, primary_account} <- validate_primary_account(account_ids),
         {:ok, link_params} <- prepare_link_params(registration_params, primary_account),
         {:ok, registration_result} <- register_and_link_account(link_params) do

      {:ok, %{
        "data" => %{
          "profile" => registration_result["data"]["profile"]
        },
        "message" => "Mobile banking registration successful",
        "status" => true
      }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp validate_customer_not_exists(customer_number) do
    case User.find_by(customer_no: customer_number) do
      nil ->
        {:ok, :not_exists}

      _existing_user ->
        {:error, "A user with customer number #{customer_number} already exists"}
    end
  end

  defp fetch_customer_accounts(customer_number) do
    # try do
      account_ids = ServiceManager.Services.T24.Messages.PullProfileAccounts.get_account_ids_direct!(customer_number) |> IO.inspect(label: "REMOTE-RESPONSE")

      case account_ids do
        [] -> {:error, "No accounts found for customer #{customer_number}"}
        accounts -> {:ok, accounts}
      end
    # rescue
    #   e ->
    #     IO.inspect(e, label: "Error fetching account IDs for customer #{customer_number}")
    #     {:error, "Failed to fetch customer accounts from T24"}
    # end
  end

  defp validate_primary_account([first_account | _rest]) do
    {:ok, first_account}
  end

  defp validate_primary_account([]) do
    {:error, "No primary account available"}
  end

  defp prepare_link_params(registration_params, primary_account_number) do

    username_generator = fn first_name, last_name ->
      first_name_clean = String.downcase(first_name)
      last_name_initial = String.first(String.downcase(last_name))
      random_number = :rand.uniform(9000) + 1000
      "#{first_name_clean}.#{last_name_initial}.#{random_number}"
    end

    link_params = %{
      "email" => registration_params["email"] || "",
      "account_officer" => registration_params["account_officer"] || "",
      "profile_type" => registration_params["profile_type"] || "",
      "profile_name" => registration_params["profile_name"] || "",
      "company" => registration_params["company"] || "",
      "inputter" => registration_params["inputter"] || "",
      "authoriser" => registration_params["authoriser"] || "",
      "customer_number" => registration_params["customer_number"] || "",
      "account_number" => primary_account_number,
      "phone_number" => registration_params["phone_number"],
      "first_name" => registration_params["first_name"],
      "last_name" => registration_params["last_name"],
      "username" => username_generator.(registration_params["first_name"], registration_params["last_name"]),
      "mobile_number" => registration_params["phone_number"] # For validation compatibility
    }

    {:ok, link_params}
  end

  defp register_and_link_account(link_params) do
    case ProfileController.link_account(link_params) do
      %{"status" => true} = response ->

        user = response["data"]["profile"]
        case approve_user(user.id) do
          {:ok, message} ->
            {:ok, Map.put(response, "activation_message", message)}

          {:error, reason} ->
            {:error, reason}
        end

      %{"status" => false} = response ->
        {:error, response["data"]["errors"] || response["message"] || "Registration and linking failed"}

      _ ->
        {:error, "Unexpected response from registration service"}
    end
  end


  # ServiceManagerWeb.Services.ThirdParty.RegistrationService.approve_user(186)
  def approve_user(id) do
    case UserManagementContext.get_data!(id) do
      nil ->
        {:error, "User not found"}

      user ->
        # Use update_changeset to update the approved field
        changeset =
          UserManagement.update_changeset(user, %{
            "approved" => true
          })

        # Generate a random password (12 characters to meet minimum requirement)
        new_password =
          Enum.map(1..12, fn _ -> Enum.random(0..9) end)
          |> Enum.join()
          |> String.replace(~r/(\d{4})(?=\d)/, "\\1-")

        changeset =
          changeset
          |> UserManagement.update_password_changeset(%{
            password: new_password,
            first_time_login: true
          })

        case Repo.update(changeset) do
          {:ok, updated_user} ->
            message =
              "Your profile has been registered. Your username is #{updated_user.username} and temporary password is: #{new_password}. Please change it upon your first login."

            ServiceManager.Repo.insert(%SMSNotification{
              msisdn: updated_user.phone_number,
              message: message
            })

            {:ok, "Profile has been approved"}

          {:error, _changeset} ->
            {:error, "Failed to approve profile"}
        end
    end
  end

end
