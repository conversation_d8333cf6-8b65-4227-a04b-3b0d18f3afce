defmodule ServiceManagerWeb.Services.ThirdParty.BaseService do
  @moduledoc """
  Base module for third-party services with common functionality
  """

  @doc """
  Standard success response format
  """
  def success_response(data, message \\ "Operation successful") do
    {:ok, %{
      "data" => data,
      "message" => message,
      "status" => true
    }}
  end

  @doc """
  Standard error response format
  """
  def error_response(errors, message \\ "Operation failed") do
    {:error, %{
      "data" => %{"errors" => errors},
      "message" => message,
      "status" => false
    }}
  end

  @doc """
  Validates required fields are present and not empty
  """
  def validate_required_fields(params, required_fields) do
    missing_fields = 
      required_fields
      |> Enum.filter(fn field -> 
        is_nil(params[field]) || params[field] == ""
      end)

    case missing_fields do
      [] -> {:ok, params}
      fields -> {:error, "Missing required fields: #{Enum.join(fields, ", ")}"}
    end
  end
end 