defmodule ServiceManagerWeb do
  @moduledoc """
  The entrypoint for defining your web interface, such
  as controllers, components, channels, and so on.

  This can be used in your application as:

      use ServiceManagerWeb, :controller
      use ServiceManagerWeb, :html

  The definitions below will be executed for every controller,
  component, etc, so keep them short and clean, focused
  on imports, uses and aliases.

  Do NOT define functions inside the quoted expressions
  below. Instead, define additional modules and import
  those modules here.
  """

  def static_paths, do: ~w(assets fonts images favicon.ico robots.txt webfonts)

  def router do
    quote do
      use Phoenix.Router, helpers: false

      # Import common connection and controller functions to use in pipelines
      import Plug.Conn
      import Phoenix.Controller
      import Phoenix.LiveView.Router
    end
  end

  def tracker do
    quote do
      import ServiceManager.Logging.FunctionTracker
    end
  end

  def channel do
    quote do
      use Phoenix.Channel
    end
  end

  def controller do
    quote do
      use Phoenix.Controller,
        formats: [:html, :json],
        layouts: [html: ServiceManagerWeb.Layouts]

      import Plug.Conn
      import ServiceManagerWeb.Gettext
      import ServiceManager.Logging.FunctionTracker
      import ServiceManager.Utilities.ApiJsonParser

      unquote(verified_routes())
    end
  end

  def live_view do
    quote do
      use Phoenix.LiveView,
        layout: {ServiceManagerWeb.Layouts, :app}

      unquote(html_helpers())
    end
  end

  def live_component do
    quote do
      use Phoenix.LiveComponent

      unquote(html_helpers())
    end
  end

  def html do
    quote do
      use Phoenix.Component

      # Import convenience functions from controllers
      import Phoenix.Controller,
        only: [get_csrf_token: 0, view_module: 1, view_template: 1]

      # Include general helpers for rendering HTML
      unquote(html_helpers())
    end
  end

  defp html_helpers do
    quote do
      # HTML escaping functionality
      import Phoenix.HTML
      # Core UI components and translation
      import ServiceManagerWeb.CoreComponents
      import ServiceManagerWeb.Gettext
      import ServiceManagerWeb.CoreComponents.Utilities.CustomTableComponents
      import ServiceManagerWeb.Components.Utilities.MainMenu
      import ServiceManagerWeb.Utilities.{Utils, Permitted, Pagination, Sorting}
      import ServiceManagerWeb.Components.Utilities.RecordAge
      import ServiceManagerWeb.Components.Utilities.PathComp

      # Shortcut for generating JS commands
      alias Phoenix.LiveView.JS
      alias ServiceManager.Contexts.LogsContext

      # Routes generation with the ~p sigil
      unquote(verified_routes())
    end
  end

  def verified_routes do
    quote do
      use Phoenix.VerifiedRoutes,
        endpoint: ServiceManagerWeb.Endpoint,
        router: ServiceManagerWeb.Router,
        statics: ServiceManagerWeb.static_paths()
    end
  end

  @doc """
  When used, dispatch to the appropriate controller/live_view/etc.
  """
  defmacro __using__(which) when is_atom(which) do
    apply(__MODULE__, which, [])
  end
end
