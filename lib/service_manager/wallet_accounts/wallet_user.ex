defmodule ServiceManager.WalletAccounts.WalletUser do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @derive {Jason.Encoder,
           only: [
             :email,
             :otp,
             :pin,
             :first_name,
             :last_name,
             :surname,
             :other_names,
             :gender,
             :date_of_birth_string,
             :date_of_issue_string,
             :date_of_expiry_string,
             :place_of_birth_district_name,
             :id_number,
             :mobile_number,
             :currency,
             :balance,
             :status,
             :first_time_login,
             :frozen,
             :locked,
             :blocked,
             :confirmed_at,
             :account_number,
             :account_type,
             :last_transaction_date,
             :email_notifications,
             :sms_notifications,
             :push_notifications,
             :wallet_tier_id,
             :date_of_birth,
             :address,
             :city,
             :occupation,
             :employer_name,
             :source_of_funds,
             :kyc_complete,
             :kyc_verified_at,
             :daily_notification_limit,
             :weekly_notification_limit,
             :monthly_notification_limit,
             :notification_count,
             :last_notification_reset,
             :session_id,
             :nickname,
             :hidden,
             :large_transaction_alert,
             :large_transaction_threshold,
             :low_balance_alert,
             :low_balance_threshold,
             :suspicous_activity_alert,
             :suspicous_activity_seconds_between_transactions,
             :bank_code,
             :branch_code,
             :bank_name,
             :enable_alerts,
             :encrypted_balance
           ]}
  schema "walletusers" do
    field :email, :string
    field :first_name, :string
    field :last_name, :string
    field :id_number, :string
    field :id_image, :string
    field :mobile_number, :string
    field :memorable_word, :string
    field :otp, :string
    field :pin, :string
    field :currency, :string, default: "MWK"
    field :balance, :decimal, default: 0
    field :encrypted_balance, ServiceManager.Utilities.Encrypted.Decimal
    field :status, :string, default: "ACTIVE"
    field :first_time_login, :boolean, default: true
    field :frozen, :boolean, default: false
    field :locked, :boolean, default: false
    field :blocked, :boolean, default: false
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :current_password, :string, virtual: true, redact: true
    field :confirmed_at, :utc_datetime
    field :account_number, :string
    field :account_type, :string
    field :type, :string
    field :last_transaction_date, :naive_datetime
    field :email_notifications, :boolean, default: true
    field :sms_notifications, :boolean, default: true
    field :push_notifications, :boolean, default: true

    # Notification limits
    field :daily_notification_limit, :integer, default: 100
    field :weekly_notification_limit, :integer, default: 500
    field :monthly_notification_limit, :integer, default: 2000
    field :notification_count, :integer, default: 0
    field :last_notification_reset, :utc_datetime
    field :session_id, Ecto.UUID
    field :nickname, :string

    # Alert and bank fields
    field :hidden, :boolean, default: false
    field :large_transaction_alert, :boolean, default: false
    field :large_transaction_threshold, :decimal, default: Decimal.new(0)
    field :low_balance_alert, :boolean, default: false
    field :low_balance_threshold, :decimal, default: Decimal.new(0)
    field :suspicous_activity_alert, :boolean, default: false
    field :suspicous_activity_seconds_between_transactions, :integer, default: 0
    field :bank_code, :string
    field :branch_code, :string
    field :bank_name, :string
    field :enable_alerts, :boolean, default: false

    # Wallet tier
    belongs_to :wallet_tier, ServiceManager.WalletAccounts.WalletTier

    # KYC fields
    field :date_of_birth, :date
    field :address, :string
    field :city, :string
    field :occupation, :string
    field :employer_name, :string
    field :source_of_funds, :string
    field :kyc_complete, :boolean, default: false
    field :kyc_verified_at, :utc_datetime

    # NRB validation fields
    field :surname, :string
    field :other_names, :string
    field :gender, :string
    field :date_of_birth_string, :string
    field :date_of_issue_string, :string
    field :date_of_expiry_string, :string
    field :place_of_birth_district_name, :string
    field :nrb_validation, :boolean
    field :nrb_response_code, :integer
    field :nrb_response_message, :string
    field :nrb_status, :string
    field :nrb_status_reason, :string
    field :nrb_status_code, :string

    field :primary_device_id, :string

    timestamps(type: :utc_datetime)
  end

  @doc """
  A wallet_user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """
  def registration_changeset(wallet_user, attrs, opts \\ []) do
    wallet_user
    |> cast(attrs, [
      :email,
      :password,
      :first_name,
      :last_name,
      :id_number,
      :id_image,
      :mobile_number,
      :memorable_word
    ])
    |> validate_mobile_number(opts)
    |> validate_password(opts)
    |> put_encrypted_balance_default()
  end

  def changeset(wallet_user, attrs, opts \\ []) do
    wallet_user
    |> cast(attrs, [
      :email,
      :first_name,
      :last_name,
      :id_number,
      :id_image,
      :type,
      :frozen,
      :locked,
      :blocked,
      :notification_count,
      :last_notification_reset,
      :session_id,
      :nickname,
      :hidden,
      :large_transaction_alert,
      :large_transaction_threshold,
      :low_balance_alert,
      :low_balance_threshold,
      :suspicous_activity_alert,
      :suspicous_activity_seconds_between_transactions,
      :bank_code,
      :branch_code,
      :bank_name,
      :enable_alerts,
      :primary_device_id,
      :balance,
      :encrypted_balance
    ])
    |> put_encrypted_balance_default()
  end

  def update_wallet(wallet_user, attrs, opts \\ []) do
    wallet_user
    |> cast(attrs, [
      :email,
      :password,
      :first_name,
      :last_name,
      :id_number,
      :id_image,
      :memorable_word,
      :otp,
      :pin,
      :session_id,
      :account_number
    ])
    |> maybe_hash_field(:memorable_word)
    |> maybe_hash_field(:pin)
  end

  def update_balance(wallet_user, attrs, _opts \\ []) do
    wallet_user
    |> cast(attrs, [:balance, :encrypted_balance, :last_transaction_date])
    |> put_encrypted_balance_default()
  end

  def notification_settings_changeset(wallet_user, attrs) do
    wallet_user
    |> cast(attrs, [
      :email_notifications,
      :sms_notifications,
      :push_notifications,
      :daily_notification_limit,
      :weekly_notification_limit,
      :monthly_notification_limit
    ])
    |> validate_required([
      :email_notifications,
      :sms_notifications,
      :push_notifications
    ])
    |> validate_number(:daily_notification_limit,
      greater_than: 0,
      message: "must be greater than 0"
    )
    |> validate_number(:weekly_notification_limit,
      greater_than: 0,
      message: "must be greater than 0"
    )
    |> validate_number(:monthly_notification_limit,
      greater_than: 0,
      message: "must be greater than 0"
    )
    |> validate_notification_limits()
  end

  defp validate_notification_limits(changeset) do
    daily = get_field(changeset, :daily_notification_limit)
    weekly = get_field(changeset, :weekly_notification_limit)
    monthly = get_field(changeset, :monthly_notification_limit)

    cond do
      daily && weekly && daily > weekly ->
        add_error(changeset, :daily_notification_limit, "cannot be greater than weekly limit")

      weekly && monthly && weekly > monthly ->
        add_error(changeset, :weekly_notification_limit, "cannot be greater than monthly limit")

      true ->
        changeset
    end
  end

  # defp validate_email(changeset, opts) do
  #   changeset
  #   |> validate_required([:email])
  #   # |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
  #   # |> validate_length(:email, max: 160)
  #   |> maybe_validate_unique_email(opts)
  # end

  defp validate_mobile_number(changeset, opts) do
    changeset
    |> validate_required([:mobile_number])
    |> validate_format(:mobile_number, ~r/^(\+265|265|0)(88|99|98|97|96|95|94|93|92|91|90|89|87|86|85|84|83|82|81|80)\d{7}$/,
      message: "must be a valid Malawian mobile number with format +265 88XXXXXXX or +265 99XXXXXXX"
    )
    |> maybe_validate_unique_phone_number(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 6, max: 72)
    # Examples of additional password validation:
    # |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    # |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    # |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/, message: "at least one digit or punctuation character")
    |> maybe_hash_password(opts)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # If using Bcrypt, then further validate it is at most 72 bytes long
      |> validate_length(:password, max: 72, count: :bytes)
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Bcrypt.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_hash_field(changeset, field_name) do
    field_value = get_field(changeset, field_name)

    if field_value do
      changeset
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(field_name, Bcrypt.hash_pwd_salt(field_value))
    else
      changeset
    end
  end

  @doc """
  Verifies the hashed field.

  If there is no wallet_user or the wallet_user doesn't have the field, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def verify_hashed_field(user, field_name, input_value) do
    hashed_value = Map.get(user, field_name)
    Bcrypt.verify_pass(input_value, hashed_value)
  end

  defp maybe_validate_unique_phone_number(changeset, opts) do
    if Keyword.get(opts, :validate_mobile_number, true) do
      changeset
      |> unsafe_validate_unique(:mobile_number, ServiceManager.Repo)
      |> unique_constraint(:mobile_number)
    else
      changeset
    end
  end

  @doc """
  A wallet_user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """

  # def email_changeset(wallet_user, attrs, opts \\ []) do
  #   wallet_user
  #   |> cast(attrs, [:email])
  #   |> validate_email(opts)
  #   |> case do
  #     %{changes: %{email: _}} = changeset -> changeset
  #     %{} = changeset -> add_error(changeset, :email, "did not change")
  #   end
  # end

  @doc """
  A wallet_user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(wallet_user, attrs, opts \\ []) do
    wallet_user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(wallet_user) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    change(wallet_user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no wallet_user or the wallet_user doesn't have a password, we call
  `Bcrypt.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(
        %ServiceManager.WalletAccounts.WalletUser{hashed_password: hashed_password},
        password
      )
      when is_binary(hashed_password) and byte_size(password) > 0 do
    Bcrypt.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Bcrypt.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset. update
  """
  def validate_current_password(changeset, password) do
    changeset = cast(changeset, %{current_password: password}, [:current_password])

    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end

  def update_password_changeset(user, attrs) do
    user
    |> cast(attrs, [:password, :first_time_login])
    |> validate_required([:password])
    |> validate_length(:password, min: 6, max: 72)
    |> maybe_hash_password([])
  end

  @doc """
  A changeset for upgrading a wallet account with KYC information.
  Validates required KYC fields and updates wallet tier and limits.
  """
  def upgrade_changeset(wallet_user, attrs) do
    changeset =
      wallet_user
      |> cast(attrs, [
        :date_of_birth,
        :address,
        :city,
        :occupation,
        :employer_name,
        :source_of_funds,
        :wallet_tier_id,
        :type,
        :kyc_complete,
        :kyc_verified_at,
        :email,
        :id_number,
        :id_image,
        # NRB validation fields
        :surname,
        :other_names,
        :gender,
        :date_of_birth_string,
        :date_of_issue_string,
        :date_of_expiry_string,
        :place_of_birth_district_name,
        :nrb_validation,
        :nrb_response_code,
        :nrb_response_message,
        :nrb_status,
        :nrb_status_reason,
        :nrb_status_code
      ])
      |> validate_required([:wallet_tier_id])
      |> validate_length(:address, min: 5, message: "must be at least 5 characters")
      |> validate_length(:city, min: 2, message: "must be at least 2 characters")
      |> validate_age()
      |> validate_tier_requirements()
  end

  defp validate_tier_requirements(changeset) do
    case get_field(changeset, :wallet_tier_id) do
      nil ->
        changeset

      tier_id ->
        case ServiceManager.Repo.get(ServiceManager.WalletAccounts.WalletTier, tier_id) do
          nil ->
            changeset

          tier ->
            required_fields = tier.required_kyc_fields || []

            changeset
            |> validate_required(Enum.map(required_fields, &String.to_existing_atom/1))
        end
    end
  end

  defp validate_age(changeset) do
    case get_field(changeset, :date_of_birth) do
      nil ->
        changeset

      dob ->
        age = Date.diff(Date.utc_today(), dob) |> div(365)

        if age >= 18 do
          changeset
        else
          add_error(changeset, :date_of_birth, "must be at least 18 years old")
        end
    end
  end

  defp put_encrypted_balance_default(changeset) do
    balance = get_field(changeset, :balance) || Decimal.new(0)
    put_change(changeset, :encrypted_balance, convert_to_decimal(balance))
  end

  defp convert_to_decimal(value) when is_integer(value), do: Decimal.new(value)
  defp convert_to_decimal(value) when is_float(value), do: Decimal.from_float(value)
  defp convert_to_decimal(value) when is_binary(value), do: Decimal.new(value)
  defp convert_to_decimal(%Decimal{} = value), do: value
  defp convert_to_decimal(_), do: Decimal.new(0)
end
