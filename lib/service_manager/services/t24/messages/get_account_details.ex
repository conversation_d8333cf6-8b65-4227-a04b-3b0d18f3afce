defmodule ServiceManager.Services.T24.Messages.GetAccountDetails do
  @moduledoc """
  Service module for retrieving customer profile information from T24.
  """
  import ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error
  alias ServiceManager.Services.T24.Messages.GetAccountBalance
  require Logger

  @doc """
  Retrieves customer profile information from T24 for the given an account number.

  ## Parameters
    - account_number: String representing the customer ID

  ## Returns
    - {:ok, response} on success where response contains customer profile data
    - {:error, reason} on failure
  """
  @spec get_account_details(String.t()) :: {:ok, map()} | {:error, String.t()}
  # track do
  def get_account_details(account_number)
      when is_binary(account_number) and account_number != "" do
    Logger.info("GetAccountDetails: Retrieving account details for account #{account_number}")
    url = "https://fdh-esb.ngrok.dev/api/esb/accounts/1.0/account/#{account_number}"
    username = Application.get_env(:service_manager, :t24_username)
    password = Application.get_env(:service_manager, :t24_password)
    credentials = Base.encode64("#{username}:#{password}")

    headers = [
      {"Accept", "*/*"},
      {"Content-Type", "application/json"},
      {"Authorization", "Basic #{credentials}"}
    ]

    HTTPService.get(url, headers)
    |> case do
      {:ok, profile} ->
        Logger.info(
          "GetAccountDetails: Successfully retrieved details for account #{account_number}"
        )

        {:ok, profile}

      {:error, reason} when is_binary(reason) ->
        Logger.warn(
          "GetAccountDetails: Failed to retrieve details for account #{account_number} - #{reason}"
        )

        {:error, Error.new(:unexpected, reason)}

      {:error, %Error{} = error} ->
        Logger.warn(
          "GetAccountDetails: Failed to retrieve details for account #{account_number} - #{inspect(error)}"
        )

        {:error, error}

      error ->
        Logger.warn(
          "GetAccountDetails: Unexpected error for account #{account_number} - #{inspect(error)}"
        )

        error
    end
  end

  # end

  def get_account_details(_) do
    Logger.warn("GetAccountDetails: Invalid account number format provided")
    {:error, Error.new(:validation, "Invalid customer ID format")}
  end

  @spec test() :: any()
  def test() do
    Logger.info("GetAccountDetails: Running test with account *************")

    "*************"
    |> get_account_details()
    |> case do
      {:error, reason} ->
        Logger.warn("GetAccountDetails: Test failed - #{inspect(reason)}")
        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        parsed_body = Jason.decode!(body)

        case parsed_body do
          %{"body" => [account_info | _]} ->
            Logger.info("GetAccountDetails: Test successful, processing account info")

            account_info
            |> add_balance_info("*************")

          _ ->
            Logger.warn("GetAccountDetails: Test failed - Invalid response format")
            {:error, Error.new(:unexpected, "Invalid response format")}
        end
    end
  end

  @doc """
  Adds balance information to the account details response.
  """
  # track do
  def add_balance_info(account_details, account_number) do
    Logger.info("GetAccountDetails: Adding balance info for account #{account_number}")
    balance_info = GetAccountBalance.get_account_balance_parsed(account_number)

    # Extract relevant fields from account details
    formatted_account = %{
      "customer_id" => account_details["CUSTOMER"],
      "account_id" =>
        get_in(account_details, ["accountDetails", Access.at(0), "accountID", Access.at(0)]),
      "account_currency" =>
        get_in(account_details, ["accountDetails", Access.at(0), "accountCurrency", Access.at(0)]),
      "account_status" => account_details["accountStatus"],
      "category_id" => account_details["categoryId"],
      "category_name" => account_details["categoryName"],
      "holder_name" => account_details["holderName"],
      "nick_name" => account_details["nickName"],
      "online_limit" => account_details["onlineLimit"],
      "opening_date" => account_details["openingDate"],
      "position_type" => account_details["positionType"],
      "hvt" => account_details["HVT"]
    }

    # Extract customer details if present
    customer_details =
      case get_in(account_details, ["customerDetails", Access.at(0)]) do
        nil ->
          Logger.warn(
            "GetAccountDetails: No customer details found for account #{account_number}"
          )

          %{}

        details ->

          lookup_key = to_string(formatted_account["customer_id"])


          registration_data =
            case :ets.lookup(:registration_cache, lookup_key) do
              [{key, data, expires_at}] ->
                if expires_at > :os.system_time(:second) do
                  data
                else
                  :ets.delete(:registration_cache, formatted_account["customer_id"])
                  %{}
                end
              [] ->
                # Fallback to Cachex
                case Cachex.get(:registration, lookup_key) do
                  {:ok, data} when is_map(data) ->
                    data
                  _ ->
                    %{}
                end
            end


          first_name = registration_data["first_name"]
          last_name = registration_data["last_name"]


          %{
            "first_name" => first_name,
            "last_name" => last_name,
            "other_names" => registration_data["other_name"] || "",
            "email" => registration_data["email_address"] || "",
            "date_of_birth" => get_in(details, ["dateOfBirth", Access.at(0)]) || "",
            "employment_status" => get_in(details, ["employmentStatus", Access.at(0)]) || "",
            "marital_status" => get_in(details, ["maritalStatus", Access.at(0)]) || "",
            "phone_number" => registration_data["phone_number"] || "",
            "post_code" => get_in(details, ["postCode", Access.at(0)]) || "",
            "street" => get_in(details, ["street", Access.at(0)]) || "",
            "town_country" => get_in(details, ["townCountry", Access.at(0)]) || ""
          }
      end

    # Merge account details, customer details, and balance info
    formatted_account =
      formatted_account
      |> Map.merge(customer_details)
      |> Map.merge(balance_info)

    IO.inspect(formatted_account, label: "FORMATTED ACCOUNT")

    formatted_account
  end

  # end

  # track do
  def get_account_details_parsed(account_number) do
    Logger.debug(
      "GetAccountDetails: Retrieving and parsing details for account #{account_number}"
    )

    account_number
    |> get_account_details()
    |> case do
      {:error, reason} ->
        Logger.debug(
          "GetAccountDetails: Failed to parse details for account #{account_number} - #{inspect(reason)}"
        )

        {:error, Error.new(:validation, reason)}

      {:ok, %{status: status, body: body, headers: headers}} ->
        parsed_body = Jason.decode!(body)

        case parsed_body do
          %{"body" => [account_info | _]} ->
            Logger.debug(
              "GetAccountDetails: Successfully parsed details for account #{account_number}"
            )

            account_info =
              account_info
              |> add_balance_info(account_number)

            {:ok, account_info}

          _ ->
            Logger.debug(
              "GetAccountDetails: Invalid response format for account #{account_number}"
            )

            {:error, Error.new(:unexpected, "Invalid response format")}
        end
    end
  end

  # end
end

# ServiceManager.Services.T24.Messages.GetAccountDetails.get_account_details_parsed("*************")
