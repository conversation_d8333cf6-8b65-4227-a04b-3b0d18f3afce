defmodule ServiceManager.Services.T24.Messages.CreateAccount do
  @moduledoc """
  Service module for creating new accounts in T24.
  """
  import ServiceManager.Logging.FunctionTracker

  alias ServiceManager.Services.T24.HTTPService
  alias ServiceManager.Services.T24.Error
  require Logger

  # ServiceManager.Services.T24.Messages.CreateAccount.create_account("2000585", "MWK")

  @spec create_account(String.t(), String.t()) :: {:ok, map()} | {:error, String.t()}
  def create_account(customer_id, currency) when is_binary(customer_id) and is_binary(currency) do
    Logger.info("CreateAccount: Creating account for customer #{customer_id}")

    url = "https://fdh-esb.ngrok.dev/api/esb/virtual/1.0/account/create/fdh/acc"
    username = Application.get_env(:service_manager, :t24_username)
    password = Application.get_env(:service_manager, :t24_password)
    credentials = Base.encode64("#{username}:#{password}")

    headers = [
      {"Content-Type", "application/json"},
      {"Authorization", "Basic #{credentials}"}
    ]

    body =
      Jason.encode!(%{
        header: %{},
        body: %{
          customerId: customer_id,
          currency: currency
        }
      })

    HTTPService.post(url, body, headers)
    |> handle_response(customer_id)
  end

  defp handle_response({:ok, {:error, reason}}, customer_id) when is_binary(reason) do
    err_message =
      "CreateAccount: Failed to create account for customer #{customer_id} - #{reason}"

    Logger.warn(err_message)
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)
    Telegex.send_message(telegram_channel_id, err_message)
    {:error, Error.new(:unexpected, reason)}
  end

  defp handle_response({:error, reason}, customer_id) when is_binary(reason) do
    err_message =
      "CreateAccount: Failed to create account for customer #{customer_id} - #{reason}"

    Logger.warn(err_message)
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)
    Telegex.send_message(telegram_channel_id, err_message)
    {:error, Error.new(:unexpected, reason)}
  end

  defp handle_response({:error, %Error{} = error}, customer_id) do
    err_message =
      "CreateAccount: Failed to create account for customer #{customer_id} - #{inspect(error)}"

    Logger.warn(err_message)
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)
    Telegex.send_message(telegram_channel_id, err_message)
    {:error, error}
  end

  defp handle_response(error, customer_id) do
    err_message =
      "CreateAccount: Unexpected error for customer #{customer_id} - #{inspect(error)}"

    Logger.warn(err_message)
    telegram_channel_id = Application.get_env(:service_manager, :telegram_channel_id)
    Telegex.send_message(telegram_channel_id, err_message)
    error
  end

  defp handle_response(response, customer_id) do
    Logger.info("CreateAccount: Successfully created account for customer #{customer_id}")
    {:ok, parse_response(response)}
  end

  @doc """
  Parses and flattens the response from create_account/2.

  ## Parameters
    - response: The response tuple from create_account/2

  ## Returns
    - {:ok, map} containing flattened response data
    - error tuple if parsing fails or for non-200 responses
  """
  @spec parse_response({:ok, map()}) :: {:ok, map()} | {:error, any()}
  def parse_response({:ok, %{status: 200, body: body} = response}) do
    case Jason.decode(body) do
      {:ok, decoded} ->
        # Extract and flatten relevant fields from both header and body
        flattened = %{
          "account_number" => get_in(decoded, ["header", "id"]),
          "transaction_status" => get_in(decoded, ["header", "transactionStatus"]),
          "status" => get_in(decoded, ["header", "status"]),
          "unique_identifier" => get_in(decoded, ["header", "uniqueIdentifier"]),
          "account_name" => get_in(decoded, ["body", "accountName"]),
          "customer_id" => get_in(decoded, ["body", "customerId"]),
          "currency" => get_in(decoded, ["body", "currency"])
        }

        flattened

      {:error, _} = error ->
        error
    end
  end

  def parse_response(error), do: error
end
