defmodule ServiceManager.Utilities.StructConverter do
  @moduledoc """
  Enhanced struct converter that handles Ecto schemas properly.

  Efficiently converts Elixir structs to maps while handling special Ecto types
  and relationships, including:
    - Ecto schema metadata
    - Associations (loaded and unloaded)
    - Nested structs and embedded schemas
    - Date, Time, DateTime, and other special types
  """

  @doc """
  Converts a struct to a complete map while handling Ecto-specific concerns.

  ## Examples

      iex> user = %User{id: 1, name: "<PERSON>"}
      iex> StructConverter.to_total_map(user)
      %{id: 1, name: "Alice"}
  """
  @spec to_total_map(struct | map | list | any) :: map | list | any
  def to_total_map(value) do
    convert(value)
  end

  # Handle nil values
  defp convert(nil), do: nil

  # Handle Ecto.Schema.Metadata structs - skip them entirely
  defp convert(%{__struct__: Ecto.Schema.Metadata}), do: nil

  # Skip unloaded associations
  defp convert(%{__struct__: Ecto.Association.NotLoaded}), do: nil

  # Handle Date, Time, DateTime, NaiveDateTime - keep as is
  defp convert(%{__struct__: module} = value)
       when module in [Date, Time, DateTime, NaiveDateTime],
       do: value

  # Handle regular structs including Ecto schemas
  defp convert(%{__struct__: _} = struct) do
    struct
    |> Map.from_struct()
    # Always remove __meta__
    |> Map.drop([:__meta__])
    |> Enum.reduce(%{}, fn {k, v}, acc ->
      case convert(v) do
        nil -> acc
        converted -> Map.put(acc, k, converted)
      end
    end)
  end

  # Handle regular maps
  defp convert(%{} = map) do
    Enum.reduce(map, %{}, fn {k, v}, acc ->
      case convert(v) do
        nil -> acc
        converted -> Map.put(acc, k, converted)
      end
    end)
  end

  # Handle lists - efficiently with list comprehension
  defp convert(items) when is_list(items) do
    for item <- items,
        converted = convert(item),
        not is_nil(converted),
        do: converted
  end

  # Handle any other data types - atoms, strings, numbers, etc.
  defp convert(value), do: value
end
