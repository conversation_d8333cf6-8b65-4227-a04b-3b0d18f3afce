defmodule ServiceManager.Validators.CallbackSchemaValidator do
  @moduledoc """
  Validates and maps callback payloads according to defined schemas.
  Provides functionality to list available transaction fields and validate mapping schemas.
  """

  @transaction_fields [
    # Core transaction fields
    "transaction_id",
    "reference",
    "status",
    "amount",
    "currency",
    "narration",
    "transaction_type",
    "transaction_date",
    "value_date",
    "posting_date",
    "core_reference",
    "channel",
    # Account fields
    "credit_account",
    "debit_account",
    "account_number",
    "account_name",
    "account_type",
    # Customer fields
    "customer_id",
    "customer_name",
    "customer_type",
    # Additional fields
    "merchant_id",
    "payment_method",
    "payment_reference",
    "batch_number",
    "reversal_reference"
  ]

  @wallet_fields [
    # Core wallet fields
    "wallet_id",
    "transaction_id",
    "reference",
    "status",
    "amount",
    "currency",
    "description",
    "transaction_type",
    "transaction_date",
    # Wallet specific
    "wallet_number",
    "wallet_type",
    "source_wallet",
    "destination_wallet",
    "balance_before",
    "balance_after",
    # Customer fields
    "customer_id",
    "customer_name",
    "customer_phone",
    # Additional fields
    "merchant_id",
    "payment_method",
    "payment_reference",
    "metadata"
  ]

  @doc """
  Lists all available fields for a given transaction type.
  """
  def list_available_fields(type) do
    case type do
      "transaction" -> @transaction_fields
      "wallet" -> @wallet_fields
      _ -> {:error, "Invalid transaction type. Must be 'transaction' or 'wallet'"}
    end
  end

  @doc """
  Validates a mapping schema for a given transaction type.
  Returns {:ok, validated_schema} or {:error, reason}
  """
  def validate_schema(schema, type) when is_map(schema) do
    available_fields = list_available_fields(type)

    case available_fields do
      {:error, reason} ->
        {:error, reason}

      fields ->
        # Extract target fields from schema
        target_fields =
          schema
          |> Map.values()
          |> Enum.reject(&is_nil/1)

        # Validate all target fields exist in available fields
        invalid_fields = Enum.reject(target_fields, &(&1 in fields))

        if invalid_fields == [] do
          {:ok, schema}
        else
          {:error, "Invalid target fields: #{Enum.join(invalid_fields, ", ")}"}
        end
    end
  end

  def validate_schema(_, _), do: {:error, "Schema must be a map"}

  @doc """
  Maps a payload according to the provided schema.
  The schema defines how to build the request structure, where:
  - Keys are paths in the target structure (e.g., "transaction.credit_account")
  - Values are fields to get from the source payload

  Example:
  ```
  schema = %{
    "transaction.credit_account" => "credit_account",
    "transaction.debit_account" => "debit_account",
    "transaction.type" => "transaction_type"
  }
  ```

  This will build:
  ```
  %{
    "transaction" => %{
      "credit_account" => value_from_credit_account,
      "debit_account" => value_from_debit_account,
      "type" => value_from_transaction_type
    }
  }
  ```
  """
  def map_payload(payload, schema) when is_map(payload) and is_map(schema) do
    schema
    |> Enum.reduce(%{}, fn {target_path, source_field}, acc ->
      case Map.get(payload, source_field) do
        nil -> acc
        value -> put_in_path(acc, String.split(target_path, "."), value)
      end
    end)
  end

  def map_payload(_, _), do: {:error, "Payload and schema must be maps"}

  @doc """
  Validates and maps a payload using the provided schema.
  Returns {:ok, mapped_payload} or {:error, reason}
  """
  def validate_and_map(payload, schema, type) do
    with {:ok, validated_schema} <- validate_schema(schema, type),
         mapped_payload <- map_payload(payload, validated_schema) do
      {:ok, mapped_payload}
    end
  end

  # Private Functions

  defp put_in_path(map, [key], value) do
    Map.put(map, key, value)
  end

  defp put_in_path(map, [head | tail], value) do
    Map.update(map, head, put_in_path(%{}, tail, value), fn existing ->
      put_in_path(existing, tail, value)
    end)
  end
end
