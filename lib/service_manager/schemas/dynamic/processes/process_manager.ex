defmodule ServiceManager.Schemas.Dynamic.Processes.ProcessManager do
  @moduledoc """
  Manager module for dynamic processes.
  Handles CRUD operations, compilation, and execution of dynamic processes.
  """

  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Schemas.Dynamic.Processes.DynamicProcess
  alias ServiceManager.Schemas.Dynamic.Processes.RouteProcessLink
  alias ServiceManager.Schemas.Dynamic.Processes.ProcessChainLink
  alias ServiceManager.Routing.DynamicRoute

  use ServiceManager.Logging.FunctionTracker

  @doc """
  Create a new dynamic process.
  """
  def create_process(attrs) do
    %DynamicProcess{}
    |> DynamicProcess.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, process} ->
        # Compile the process module
        case compile_process(process) do
          {:ok, _module} -> {:ok, process}
          {:error, reason} -> {:error, reason}
        end
      error -> error
    end
  end

  @doc """
  Update an existing dynamic process.
  """
  def update_process(%DynamicProcess{} = process, attrs) do
    process
    |> DynamicProcess.changeset(attrs)
    |> Repo.update()
    |> case do
      {:ok, updated_process} ->
        # Recompile the process module
        case compile_process(updated_process) do
          {:ok, _module} -> {:ok, updated_process}
          {:error, reason} -> {:error, reason}
        end
      error -> error
    end
  end

  @doc """
  Delete a dynamic process.
  """
  def delete_process(%DynamicProcess{} = process) do
    # First delete any chain links
    from(pcl in ProcessChainLink,
         where: pcl.source_process_id == ^process.id or pcl.target_process_id == ^process.id)
    |> Repo.delete_all()

    # Delete any route links
    from(rpl in RouteProcessLink, where: rpl.initial_process_id == ^process.id)
    |> Repo.delete_all()

    # Delete the process
    Repo.delete(process)
  end

  @doc """
  Get a dynamic process by ID.
  """
  def get_process(id) do
    case Repo.get(DynamicProcess, id) do
      nil -> {:error, :not_found}
      process -> {:ok, process}
    end
  end

  @doc """
  List all dynamic processes.
  """
  def list_processes do
    Repo.all(DynamicProcess)
  end

  @doc """
  Link a process to a route as the initial process.
  """
  def link_process_to_route(route_id, process_id, user_id \\ nil) do
    # Check if the route already has a process linked
    case Repo.get_by(RouteProcessLink, route_id: route_id) do
      nil ->
        %RouteProcessLink{}
        |> RouteProcessLink.changeset(%{
          route_id: route_id,
          initial_process_id: process_id,
          created_by_id: user_id
        })
        |> Repo.insert()
      existing ->
        existing
        |> RouteProcessLink.changeset(%{
          initial_process_id: process_id,
          created_by_id: user_id
        })
        |> Repo.update()
    end
  end

  @doc """
  Unlink a process from a route.
  """
  def unlink_process_from_route(route_id) do
    case Repo.get_by(RouteProcessLink, route_id: route_id) do
      nil -> {:error, :not_found}
      link -> Repo.delete(link)
    end
  end

  @doc """
  Initialize a process as the root of its own chain.
  """
  def initialize_as_root_process(process_id, user_id \\ nil) do
    # Check if already has a root entry
    existing_chain = ProcessChainLink.find_chain_for_process(process_id)

    case existing_chain do
      [] ->
        # Not in any chain, create new root
        chain_id = Ecto.UUID.generate()

        %ProcessChainLink{}
        |> ProcessChainLink.changeset(%{
          source_process_id: process_id,
          target_process_id: process_id,
          is_root: true,
          chain_id: chain_id,
          order_position: 0,
          position: 0,
          created_by_id: user_id
        })
        |> Repo.insert()

      [%ProcessChainLink{is_root: true} = root_link | _] ->
        # Already has root entry
        {:ok, root_link}

      _ ->
        # In a chain but no root exists, this shouldn't happen but let's handle it
        {:error, :invalid_chain_state}
    end
  end

  @doc """
  Link two processes in a chain.
  """
  def link_processes(source_id, target_id, position \\ 0, user_id \\ nil) do
    # First ensure the source process is initialized as root if not already in a chain
    chain_id = case ProcessChainLink.find_chain_for_process(source_id) do
      [] ->
        # Not in any chain, initialize as root
        {:ok, root_link} = initialize_as_root_process(source_id, user_id)
        root_link.chain_id

      [%ProcessChainLink{chain_id: existing_chain_id} | _] ->
        existing_chain_id
    end

    # Get the next order position
    next_position = get_next_order_position(chain_id)

    # Check if the link already exists
    case Repo.get_by(ProcessChainLink, source_process_id: source_id, target_process_id: target_id) do
      nil ->
        %ProcessChainLink{}
        |> ProcessChainLink.changeset(%{
          source_process_id: source_id,
          target_process_id: target_id,
          chain_id: chain_id,
          order_position: next_position,
          position: position,
          created_by_id: user_id
        })
        |> Repo.insert()
      existing ->
        existing
        |> ProcessChainLink.changeset(%{
          position: position,
          order_position: next_position,
          created_by_id: user_id
        })
        |> Repo.update()
    end
  end

  @doc """
  Unlink two processes in a chain.
  """
  def unlink_processes(source_id, target_id) do
    case Repo.get_by(ProcessChainLink, source_process_id: source_id, target_process_id: target_id) do
      nil -> {:error, :not_found}
      link -> Repo.delete(link)
    end
  end

  @doc """
  Get the initial process for a route.
  """
  def get_initial_process(route_id) do
    query = from rpl in RouteProcessLink,
            where: rpl.route_id == ^route_id,
            join: p in DynamicProcess, on: p.id == rpl.initial_process_id,
            select: p

    case Repo.one(query) do
      nil -> {:error, :not_found}
      process -> {:ok, process}
    end
  end

  @doc """
  Get the complete process chain for a process (including the root).
  """
  def get_complete_chain(process_id) do
    case ProcessChainLink.find_chain_for_process(process_id) do
      [] -> {:ok, %{root: nil, chain: []}}

      chain_links ->
        # Find the root link
        root_link = Enum.find(chain_links, & &1.is_root)

        # Get non-root links sorted by order_position
        chain_links_sorted =
          chain_links
          |> Enum.reject(& &1.is_root)
          |> Enum.sort_by(& &1.order_position)

        root_process = if root_link, do: root_link.source_process, else: nil
        chain_processes = Enum.map(chain_links_sorted, & &1.target_process)

        chain_id = if root_link, do: root_link.chain_id, else: nil
        {:ok, %{root: root_process, chain: chain_processes, chain_id: chain_id}}
    end
  end

  @doc """
  Get the process chain for a source process (legacy function for compatibility).
  """
  def get_process_chain(source_id) do
    case get_complete_chain(source_id) do
      {:ok, %{chain: chain}} -> {:ok, chain}
      error -> error
    end
  end

  @doc """
  Reorder a process in the chain (move up or down).
  """
  def reorder_chain_process(process_id, direction, user_id \\ nil) when direction in [:up, :down] do
    case ProcessChainLink.find_chain_for_process(process_id) do
      [] -> {:error, :not_in_chain}

      chain_links ->
        # Find the current process link (non-root)
        current_link = Enum.find(chain_links, fn link ->
          !link.is_root && link.target_process_id == process_id
        end)

        if current_link do
          # Get all non-root links sorted by order_position
          sorted_links =
            chain_links
            |> Enum.reject(& &1.is_root)
            |> Enum.sort_by(& &1.order_position)

          # Find the current index
          current_index = Enum.find_index(sorted_links, & &1.id == current_link.id)

          cond do
            direction == :up && current_index > 0 ->
              swap_positions(Enum.at(sorted_links, current_index), Enum.at(sorted_links, current_index - 1), user_id)

            direction == :down && current_index < length(sorted_links) - 1 ->
              swap_positions(Enum.at(sorted_links, current_index), Enum.at(sorted_links, current_index + 1), user_id)

            true ->
              {:error, :cannot_move_further}
          end
        else
          {:error, :process_not_found_in_chain}
        end
    end
  end

  @doc """
  Generate a module name for a process.
  """
  def module_name_for_process(process_id) do
    module_name = "Elixir.ServiceManager.DynamicProcesses.Process_#{process_id}"
    String.to_atom(module_name)
  end

  @doc """
  Check if a process's code has changed since it was last compiled.
  """
  def process_code_changed?(%DynamicProcess{} = process) do
    module_name = module_name_for_process(process.id)

    if Code.ensure_loaded?(module_name) do
      # Get the module's attributes to check the code hash
      case module_name.__info__(:attributes)[:code_hash] do
        [hash] -> hash != :erlang.phash2(process.code)
        _ -> true # No hash found, assume changed
      end
    else
      true # Module not loaded, assume changed
    end
  end

  @doc """
  Compile a dynamic process into a module.
  """
  def compile_process(%DynamicProcess{} = process) do
    module_name = module_name_for_process(process.id)
    code_hash = :erlang.phash2(process.code)

    # Create the module code
    module_code = """
    defmodule #{module_name} do
      @moduledoc \"\"\"
      Dynamically generated process module for process #{process.id}: #{process.name}
      \"\"\"

      import Map, only: [get: 2, put: 3] # Import specific functions

      # Store the code hash for change detection
      @code_hash #{code_hash}

      # Make the code hash available as a module attribute
      # def __info__(:attributes), do: [code_hash: [@code_hash]]

      #{process.code}
    end
    """

    # Compile the module
    case Code.compile_string(module_code) do
      [{^module_name, _bytecode}] ->
        {:ok, module_name}
      _ ->
        {:error, "Failed to compile process module"}
    end
  rescue
    e -> {:error, "Compilation error: #{inspect(e)}"}
  end

  @doc """
  Execute a process with the given input.
  """
  def execute_process(process_id, input) do
    with {:ok, process} <- get_process(process_id) do
      case process.execution_mode do
        "steps" ->
          # Execute from code steps
          execute_from_steps(process_id, input)

        "plugin_code" ->
          # Execute from compiled plugin code (default behavior)
          with {:ok, module} <- ensure_process_compiled(process) do
            # Call the process function
            try do
              apply(module, :process, [input])
            rescue
              e -> {:error, "Execution error: #{inspect(e)}"}
            catch
              kind, reason -> {:error, "Execution error (#{kind}): #{inspect(reason)}"}
            end
          end

        _ ->
          {:error, "Unknown execution mode: #{process.execution_mode}"}
      end
    end
  end

  @doc """
  Execute a process from its code steps.
  """
  defp execute_from_steps(process_id, input) do
    alias ServiceManager.Schemas.Dynamic.Processes.CodeStepManager

    with {:ok, generated_code} <- CodeStepManager.generate_code_from_steps(process_id) do
      # Create a temporary process struct with the generated code
      temp_process = %DynamicProcess{
        id: process_id,
        code: generated_code
      }

      # Compile and execute the generated code
      with {:ok, module} <- compile_process(temp_process) do
        try do
          apply(module, :process, [input])
        rescue
          e -> {:error, "Step execution error: #{inspect(e)}"}
        catch
          kind, reason -> {:error, "Step execution error (#{kind}): #{inspect(reason)}"}
        end
      end
    else
      {:error, reason} -> {:error, "Failed to generate code from steps: #{inspect(reason)}"}
    end
  end

  @doc """
  Ensure a process module is compiled and up to date.
  """
  def ensure_process_compiled(%DynamicProcess{} = process) do
    module_name = module_name_for_process(process.id)

    if Code.ensure_loaded?(module_name) do
      if process_code_changed?(process) do
        compile_process(process)
      else
        {:ok, module_name}
      end
    else
      compile_process(process)
    end
  end

  @doc """
  Force sync a process by purging from memory and recompiling from current DB state.
  This ensures that any code changes are immediately reflected in execution.
  """
  def sync_process(process_id) do
    with {:ok, process} <- get_process(process_id) do
      module_name = module_name_for_process(process.id)

      # Force purge the module from memory to ensure fresh compilation
      if Code.ensure_loaded?(module_name) do
        :code.purge(module_name)
        :code.delete(module_name)
      end

      # Compile fresh from current DB state
      case compile_process(process) do
        {:ok, _module} ->
          # Update sync timestamp
          update_process(process, %{
            last_synced_at: DateTime.utc_now(),
            sync_status: "synced"
          })

        {:error, reason} ->
          # Update sync status with error
          update_process(process, %{
            sync_status: "error"
          })
          {:error, "Sync failed: #{reason}"}
      end
    end
  end

  @doc """
  Execute a process chain starting with the initial process.
  """
  track do
    def execute_chain(route_id, initial_params) do
      with {:ok, initial_process} <- get_initial_process(route_id),
          {:ok, %{root: root_process, chain: chain}} <- get_complete_chain(initial_process.id) do

        # Start with the root process if it exists, otherwise use the initial process
        starting_process = root_process || initial_process

        # Debug logging
        IO.puts("=== CHAIN EXECUTION START ===")
        IO.inspect(initial_params, label: "Initial params")
        IO.inspect(starting_process.name, label: "Starting process")
        IO.inspect(Enum.map(chain, &(&1.name)), label: "Chain processes")

        # Execute the starting process
        case execute_process(starting_process.id, initial_params) do
          {:ok, result} ->
            IO.inspect(result, label: "Result from #{starting_process.name}")

            # Fold through the chain, passing each result to the next process
            final_result = Enum.reduce_while(chain, {:ok, result}, fn process, {:ok, acc_result} ->
              IO.puts("Executing process: #{process.name}")
              IO.inspect(acc_result, label: "Input to #{process.name}")

              case execute_process(process.id, acc_result) do
                {:ok, new_result} ->
                  IO.inspect(new_result, label: "Output from #{process.name}")
                  {:cont, {:ok, new_result}}
                {:error, reason} ->
                  IO.inspect(reason, label: "Error in #{process.name}")
                  {:halt, {:error, reason}}
              end
            end)

            IO.puts("=== CHAIN EXECUTION END ===")
            final_result

          {:error, reason} ->
            IO.inspect(reason, label: "Error in starting process #{starting_process.name}")
            {:error, reason}
        end
      end
    end
  end

  # Private helper functions

  defp get_next_order_position(chain_id) do
    query = from pcl in ProcessChainLink,
            where: pcl.chain_id == ^chain_id and not pcl.is_root,
            select: max(pcl.order_position)

    case Repo.one(query) do
      nil -> 1  # First non-root process
      max_pos -> max_pos + 1
    end
  end

  defp swap_positions(link1, link2, user_id) do
    Repo.transaction(fn ->
      # Swap the order_position values
      temp_position = link1.order_position

      # Update first link
      link1
      |> ProcessChainLink.changeset(%{
        order_position: link2.order_position,
        created_by_id: user_id
      })
      |> Repo.update!()

      # Update second link
      link2
      |> ProcessChainLink.changeset(%{
        order_position: temp_position,
        created_by_id: user_id
      })
      |> Repo.update!()
    end)
  end
end
