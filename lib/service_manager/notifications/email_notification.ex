defmodule ServiceManager.Notifications.EmailNotification do
  use Ecto.Schema
  import Ecto.Changeset

  schema "email_notifications" do
    field :user_id, :id
    field :subject, :string
    field :body, :string
    field :sent_at, :utc_datetime
    field :status, :string

    timestamps()
  end

  def changeset(email_notification, attrs) do
    email_notification
    |> cast(attrs, [:user_id, :subject, :body, :sent_at, :status])
    |> validate_required([:user_id, :subject, :body])
  end
end
