defmodule ServiceManager.Logging.CustomResponseDemo do
  @moduledoc """
  Demonstration module showing how to use custom response logging
  with the enhanced function tracker.
  """
  
  use ServiceManager.Logging.FunctionTracker

  @doc """
  Example function demonstrating custom response logging.
  The function returns a complex result but logs only a simplified version.
  """
  track do
    def process_payment(payment_data) do
      # Simulate complex processing
      full_result = %{
        id: Ecto.UUID.generate(),
        status: "completed",
        amount: payment_data.amount,
        currency: payment_data.currency,
        timestamp: DateTime.utc_now(),
        internal_data: %{
          processor_id: "internal_123",
          risk_score: 0.15,
          fees: %{processing: 2.50, network: 1.25},
          sensitive_token: "sk_live_abc123xyz789"  # This shouldn't be logged
        },
        customer: %{
          id: payment_data.customer_id,
          email: payment_data.customer_email,
          internal_notes: "High value customer - VIP treatment"
        }
      }

      # Set custom response log - only log what's safe and relevant
      response_log = %{
        transaction_id: full_result.id,
        status: full_result.status,
        amount: full_result.amount,
        currency: full_result.currency,
        customer_id: full_result.customer.id,
        processing_time: "#{System.monotonic_time() - System.monotonic_time()}ms"
      }

      # Return the full result (but only response_log will be logged)
      full_result
    end
  end

  @doc """
  Example function that logs intermediate processing results
  rather than the final return value.
  """
  track do
    def sync_user_data(user_id) do
      # Step 1: Fetch user data
      user_data = fetch_user_from_database(user_id)
      
      # Step 2: Sync with external service
      sync_result = sync_with_external_service(user_data)
      
      # Log the sync operation details instead of the final result
      response_log = %{
        user_id: user_id,
        sync_status: sync_result.status,
        records_updated: sync_result.updated_count,
        sync_duration: sync_result.duration_ms,
        external_service_version: sync_result.service_version
      }
      
      # Step 3: Update local database
      update_result = update_local_database(user_data, sync_result)
      
      # Return success/failure but log the sync details
      case update_result do
        {:ok, _} -> {:ok, "User data synchronized successfully"}
        {:error, reason} -> {:error, "Sync failed: #{reason}"}
      end
    end
  end

  @doc """
  Example function showing conditional custom logging.
  """
  track do
    def fetch_account_balance(account_id, include_details \\ false) do
      account = get_account(account_id)
      
      if include_details do
        # For detailed requests, log more information
        response_log = %{
          account_id: account_id,
          balance: account.balance,
          currency: account.currency,
          last_transaction_date: account.last_transaction_date,
          account_type: account.type,
          request_type: "detailed"
        }
      else
        # For simple requests, log minimal information
        response_log = %{
          account_id: account_id,
          balance: account.balance,
          request_type: "simple"
        }
      end
      
      # Return the full account object
      account
    end
  end

  @doc """
  Example function that doesn't use custom logging - will use default behavior.
  """
  track do
    def get_user_profile(user_id) do
      # No response_log variable set, so the return value will be logged normally
      %{
        id: user_id,
        name: "John Doe",
        email: "<EMAIL>",
        created_at: DateTime.utc_now()
      }
    end
  end

  # Helper functions (not tracked)
  defp fetch_user_from_database(user_id) do
    %{id: user_id, name: "User #{user_id}", email: "user#{user_id}@example.com"}
  end

  defp sync_with_external_service(user_data) do
    # Simulate external API call
    Process.sleep(100)
    %{
      status: "success",
      updated_count: 3,
      duration_ms: 150,
      service_version: "v2.1.0"
    }
  end

  defp update_local_database(_user_data, _sync_result) do
    {:ok, "updated"}
  end

  defp get_account(account_id) do
    %{
      id: account_id,
      balance: 1500.00,
      currency: "USD",
      type: "checking",
      last_transaction_date: DateTime.utc_now(),
      internal_metadata: %{
        risk_level: "low",
        compliance_flags: []
      }
    }
  end
end
