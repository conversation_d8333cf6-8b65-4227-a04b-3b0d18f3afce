defmodule ServiceManager.Logging.FunctionTracker do
  alias ServiceManager.{Repo, Logging.LogEntry}

  # Keys for process dictionary
  @chain_id_key :function_tracker_chain_id
  @process_chain_key :function_tracker_process_chain
  @root_process_key :function_tracker_root_process

  # Configuration defaults
  @default_response_size_limit 10_000  # 10KB
  @default_enable_response_redaction true
  @default_enable_response_parsing true

  defmacro __using__(_opts) do
    quote do
      import ServiceManager.Logging.FunctionTracker, only: [track: 1, round_to_five_minutes: 1]
    end
  end

  # Public helper functions
  def format_currency(amount, currency \\ "ZMW")

  def format_currency(amount, currency) when is_number(amount) do
    "#{format_number(amount)} #{currency}"
  end

  def format_currency(_, _), do: "N/A"

  def format_number(num) when is_integer(num), do: num |> Integer.to_string() |> reverse_format()
  def format_number(num) when is_float(num), do: :erlang.float_to_binary(num, decimals: 2)
  def format_number(other), do: inspect(other)

  def reverse_format(str) do
    str
    |> String.reverse()
    |> String.graphemes()
    |> Enum.chunk_every(3)
    |> Enum.join(",")
    |> String.reverse()
  end

  def redact_sensitive("Authorization", "Basic " <> _), do: "Basic [REDACTED]"
  def redact_sensitive(_, value), do: inspect(value)

  # Response data redaction patterns
  @sensitive_response_fields [
    "password", "token", "secret", "key", "authorization", "auth",
    "pin", "otp", "ssn", "social_security", "credit_card", "card_number",
    "cvv", "cvc", "account_number", "routing_number", "iban",
    "private_key", "api_key", "access_token", "refresh_token",
    "session_id", "csrf_token", "x-csrf-token"
  ]

  def redact_response_data(data) when is_map(data) do
    Enum.reduce(data, %{}, fn {key, value}, acc ->
      redacted_value =
        if should_redact_field?(key) do
          "[REDACTED]"
        else
          redact_response_data(value)
        end
      Map.put(acc, key, redacted_value)
    end)
  end

  def redact_response_data(data) when is_list(data) do
    Enum.map(data, &redact_response_data/1)
  end

  def redact_response_data(data), do: data

  defp should_redact_field?(field) when is_binary(field) do
    field_lower = String.downcase(field)
    Enum.any?(@sensitive_response_fields, fn pattern ->
      String.contains?(field_lower, pattern)
    end)
  end

  defp should_redact_field?(field) when is_atom(field) do
    should_redact_field?(Atom.to_string(field))
  end

  defp should_redact_field?(_), do: false

  # Configuration helpers
  defp get_response_size_limit do
    Application.get_env(:service_manager, :function_tracker, [])
    |> Keyword.get(:response_size_limit, @default_response_size_limit)
  end

  defp response_redaction_enabled? do
    Application.get_env(:service_manager, :function_tracker, [])
    |> Keyword.get(:enable_response_redaction, @default_enable_response_redaction)
  end

  defp response_parsing_enabled? do
    Application.get_env(:service_manager, :function_tracker, [])
    |> Keyword.get(:enable_response_parsing, @default_enable_response_parsing)
  end

  defp get_additional_sensitive_fields do
    Application.get_env(:service_manager, :function_tracker, [])
    |> Keyword.get(:additional_sensitive_fields, [])
  end

  def try_parse_json(body) when is_binary(body) do
    case Jason.decode(body) do
      {:ok, parsed} -> parsed
      _ -> %{"error" => "Failed to parse JSON", "raw_body" => String.slice(body, 0, 500)}
    end
  end

  def try_parse_json(_), do: %{}

  def parse_response_body(body, content_type \\ nil)

  def parse_response_body(body, content_type) when is_binary(body) do
    cond do
      # Handle JSON content
      is_json_content?(content_type) or looks_like_json?(body) ->
        case Jason.decode(body) do
          {:ok, parsed} ->
            %{
              type: "json",
              parsed: parsed,
              size: byte_size(body)
            }
          {:error, reason} ->
            %{
              type: "json",
              error: "Failed to parse JSON: #{inspect(reason)}",
              raw_preview: String.slice(body, 0, 500),
              size: byte_size(body)
            }
        end

      # Handle XML content
      is_xml_content?(content_type) or looks_like_xml?(body) ->
        %{
          type: "xml",
          raw_preview: String.slice(body, 0, 500),
          size: byte_size(body)
        }

      # Handle plain text
      is_text_content?(content_type) ->
        %{
          type: "text",
          content: String.slice(body, 0, 1000),
          size: byte_size(body)
        }

      # Handle binary/unknown content
      true ->
        %{
          type: "binary",
          size: byte_size(body),
          preview: body |> :binary.part(0, min(100, byte_size(body))) |> Base.encode64()
        }
    end
  end

  def parse_response_body(body, _content_type) do
    %{
      type: "unknown",
      content: inspect(body, limit: 500),
      size: if(is_binary(body), do: byte_size(body), else: nil)
    }
  end

  defp is_json_content?(content_type) when is_binary(content_type) do
    String.contains?(String.downcase(content_type), ["application/json", "text/json", "+json"])
  end
  defp is_json_content?(_), do: false

  defp is_xml_content?(content_type) when is_binary(content_type) do
    String.contains?(String.downcase(content_type), ["application/xml", "text/xml", "+xml"])
  end
  defp is_xml_content?(_), do: false

  defp is_text_content?(content_type) when is_binary(content_type) do
    String.contains?(String.downcase(content_type), ["text/", "application/x-www-form-urlencoded"])
  end
  defp is_text_content?(_), do: false

  defp looks_like_json?(body) when is_binary(body) do
    trimmed = String.trim(body)
    (String.starts_with?(trimmed, "{") and String.ends_with?(trimmed, "}")) or
    (String.starts_with?(trimmed, "[") and String.ends_with?(trimmed, "]"))
  end
  defp looks_like_json?(_), do: false

  defp looks_like_xml?(body) when is_binary(body) do
    trimmed = String.trim(body)
    String.starts_with?(trimmed, "<") and String.contains?(trimmed, ">")
  end
  defp looks_like_xml?(_), do: false

  def extract_security_headers(headers) do
    headers
    |> Enum.filter(fn {k, _} ->
      String.downcase(k) in [
        "strict-transport-security",
        "content-security-policy",
        "x-xss-protection"
      ]
    end)
    |> Enum.map(fn {k, v} -> {String.downcase(k), v} end)
  end

  # Helper functions to find chain_id in AST
  defp find_chain_id_in_ast({:=, _, [{:chain_id, _, _}, {:__block__, _, _} = block]}) do
    find_chain_id_in_ast(block)
  end

  defp find_chain_id_in_ast({:=, _, [{:chain_id, _, _}, value]}) do
    case value do
      {:__block__, _, _} -> find_chain_id_in_ast(value)
      _ -> Macro.expand(value, __ENV__)
    end
  end

  defp find_chain_id_in_ast({:__block__, _, exprs}) do
    Enum.find_value(exprs, &find_chain_id_in_ast/1)
  end

  defp find_chain_id_in_ast({:def, _, [_, [do: body]]}) do
    find_chain_id_in_ast(body)
  end

  defp find_chain_id_in_ast({var, _, _} = ast) when is_atom(var) do
    if Atom.to_string(var) == "chain_id", do: ast, else: nil
  end

  defp find_chain_id_in_ast(list) when is_list(list) do
    Enum.find_value(list, &find_chain_id_in_ast/1)
  end

  defp find_chain_id_in_ast(value) when is_binary(value), do: value
  defp find_chain_id_in_ast(_), do: nil

  # Helper function to round timestamp to 5-minute intervals
  def round_to_five_minutes(datetime) do
    minute = datetime.minute
    rounded_minute = div(minute, 5) * 5
    
    datetime
    |> DateTime.truncate(:second)
    |> Map.put(:minute, rounded_minute)
    |> Map.put(:second, 0)
  end

  defmacro track(do: {:def, meta, [{name, _, args} = fun_head, [do: body]]}) do
    args = args || []
    chain_id_value = find_chain_id_in_ast(body)

    quote do
      # Module attributes for process chain tracking
      @chain_id_key :function_tracker_chain_id
      @process_chain_key :function_tracker_process_chain
      @root_process_key :function_tracker_root_process

      # Helper functions for process chain management
      defp get_process_chain do
        Process.get(@process_chain_key) ||
          %{
            root_pid: nil,
            chain_id: nil,
            children: MapSet.new()
          }
      end

      defp set_process_chain(chain) do
        Process.put(@process_chain_key, chain)
      end

      defp is_root_process? do
        case Process.get(@root_process_key) do
          true -> true
          _ -> false
        end
      end

      defp mark_as_root_process do
        Process.put(@root_process_key, true)
      end

      defp add_child_process(pid) do
        chain = get_process_chain()
        updated_chain = Map.update!(chain, :children, &MapSet.put(&1, pid))
        set_process_chain(updated_chain)
      end

      defp remove_child_process(pid) do
        chain = get_process_chain()
        updated_chain = Map.update!(chain, :children, &MapSet.delete(&1, pid))
        set_process_chain(updated_chain)
      end

      defp monitor_process(pid) do
        Process.monitor(pid)
      end

      defp handle_process_down(pid) do
        remove_child_process(pid)
      end

      defp cleanup_chain do
        chain = get_process_chain()

        if chain.children |> MapSet.size() == 0 do
          Process.delete(@chain_id_key)
          Process.delete(@process_chain_key)
          Process.delete(@root_process_key)
        end
      end

      defp propagate_chain_id(chain_id) do
        current_pid = self()
        parent = Process.info(current_pid, :links)

        case parent do
          {:links, [parent_pid | _]} when is_pid(parent_pid) ->
            # We're a child process, inherit chain from parent
            chain = %{
              root_pid: parent_pid,
              chain_id: chain_id,
              children: MapSet.new()
            }

            set_process_chain(chain)
            Process.put(@chain_id_key, chain_id)
            monitor_process(parent_pid)

          _ ->
            # We're the root process
            chain = %{
              root_pid: current_pid,
              chain_id: chain_id,
              children: MapSet.new()
            }

            set_process_chain(chain)
            Process.put(@chain_id_key, chain_id)
            mark_as_root_process()
        end
      end

      # Helper functions to find chain_id in parameters
      defp find_chain_id_in_params(args, binding) do
        if not is_list(args), do: nil

        arg_values =
          binding
          |> Enum.take(length(args))
          |> Enum.map(fn {_name, value} -> value end)

        Enum.zip(args, arg_values)
        |> Enum.find_value(&find_chain_id_in_param/1)
      end

      defp find_chain_id_in_param({_arg_name, value}) do
        cond do
          is_map(value) -> find_chain_id_in_map(value)
          is_list(value) -> find_chain_id_in_list(value)
          true -> nil
        end
      end

      # List of struct types to skip traversing
      @skip_structs [
        DateTime,
        Time,
        Date,
        NaiveDateTime,
        URI,
        Version,
        Plug.Conn,
        Phoenix.LiveView.Socket,
        Phoenix.Socket,
        Phoenix.LiveView.Rendered
      ]

      defp find_chain_id_in_map(value) do
        cond do
          # Skip if not a map or struct
          not (is_map(value) or is_struct(value)) ->
            nil

          # Skip certain struct types
          is_struct(value) and value.__struct__ in @skip_structs ->
            nil

          # Handle structs by converting to map
          is_struct(value) ->
            try do
              map = Map.from_struct(value)
              find_chain_id_in_regular_map(map)
            rescue
              _ -> nil
            end

          # Handle regular maps
          true ->
            find_chain_id_in_regular_map(value)
        end
      end

      defp find_chain_id_in_regular_map(map) do
        try do
          case Map.get(map, :chain_id) || Map.get(map, "chain_id") do
            nil ->
              # Recursively check nested maps and lists
              Enum.reduce_while(map, nil, fn {_k, v}, _acc ->
                result =
                  cond do
                    is_map(v) or is_struct(v) -> find_chain_id_in_map(v)
                    is_list(v) -> find_chain_id_in_list(v)
                    true -> nil
                  end

                case result do
                  nil -> {:cont, nil}
                  value -> {:halt, value}
                end
              end)

            value ->
              value
          end
        rescue
          Protocol.UndefinedError -> nil
          _ -> nil
        end
      end

      defp find_chain_id_in_list(list) do
        if not is_list(list), do: nil

        Enum.find_value(list, fn value ->
          cond do
            is_map(value) -> find_chain_id_in_map(value)
            is_list(value) -> find_chain_id_in_list(value)
            true -> nil
          end
        end)
      end

      defp find_chain_id_in_list(_), do: nil

      def unquote(fun_head) do
        # Get or create chain_id
        chain_id =
          cond do
            # First try to find in function parameters
            param_chain_id = find_chain_id_in_params(unquote(args), binding()) -> param_chain_id
            # Then try AST
            is_binary(unquote(chain_id_value)) -> unquote(chain_id_value)
            # Then try existing process chain
            existing = Process.get(@chain_id_key) -> existing
            # Finally generate new chain_id
            true -> "#{Ecto.UUID.generate()}"
          end

        # Propagate chain_id to process chain
        propagate_chain_id(chain_id)

        # Handle process monitoring
        receive do
          {:DOWN, _ref, :process, pid, _reason} ->
            handle_process_down(pid)
        after
          0 -> :ok
        end

        # Capture start time for duration calculation
        start_time = System.monotonic_time()

        # Get process info for file naming
        process_info = Process.info(self())
        pid = inspect(self()) |> String.replace(~r/[#<>]/, "")

        timestamp =
          DateTime.utc_now()
          # Add 2 hours for Africa/Lusaka timezone
          |> DateTime.add(2 * 3600, :second)

        formatted_timestamp = Calendar.strftime(timestamp, "%Y-%m-%d %H:%M:%S")
        
        # Round timestamp to 5-minute intervals for file naming
        rounded_timestamp = ServiceManager.Logging.FunctionTracker.round_to_five_minutes(timestamp)

        # Execute the function body
        result = unquote(body)

        # Calculate duration
        duration = System.monotonic_time() - start_time
        duration_ms = System.convert_time_unit(duration, :native, :millisecond)

        # Get function arguments
        arg_values =
          binding()
          |> Enum.take(length(unquote(args)))
          |> Enum.map(fn {name, value} ->
            {name, inspect(value, pretty: true, limit: :infinity)}
          end)

        # Build structured log entry
        log_entry =
          [
            header_line(
              formatted_timestamp,
              unquote(name),
              length(unquote(args)),
              __MODULE__,
              pid,
              chain_id
            ),
            process_state_section(process_info),
            function_params_section(unquote(args), arg_values),
            function_output_section(result),
            request_section(binding()),
            response_section(result, duration_ms),
            separator_line()
          ]
          |> Enum.join("\n")

        # Helper function to safely encode data
        safe_encode = fn data ->
          data
          |> inspect(pretty: true, limit: :infinity)
          |> Jason.encode!()
        end

        # Create log file path with 5-minute rounded filename
        log_dir = "logs/processes/tracking/#{Calendar.strftime(rounded_timestamp, "%Y%m%d")}"
        File.mkdir_p!(log_dir)
        
        # Generate filename with date-time rounded to 5 minutes
        file_timestamp = Calendar.strftime(rounded_timestamp, "%Y%m%d_%H%M")
        log_file = "#{log_dir}/#{file_timestamp}.log"

        # Create database log entry attributes with safely encoded data
        log_attrs = %{
          chain_id: chain_id,
          function_name: "#{unquote(name)}",
          module_name: "#{__MODULE__}",
          process_id: pid,
          duration_ms: duration_ms,
          params: safe_encode.(extract_params(binding())),
          result: safe_encode.(result),
          process_state: safe_encode.(process_info),
          request_info: safe_encode.(extract_request_info(binding())),
          response_info: safe_encode.(extract_response_info(result)),
          timestamp: timestamp,
          file_path: log_file,
          raw_log: log_entry
        }

        # Wrap database insert and file write in a transaction
        case Repo.transaction(fn ->
               case %LogEntry{}
                    |> LogEntry.changeset(log_attrs)
                    |> Repo.insert() do
                 {:ok, _entry} ->
                   # On successful database insert, write to file
                   File.write!(log_file, log_entry <> "\n", [:append])
                   :ok

                 {:error, changeset} ->
                   Repo.rollback(changeset)
               end
             end) do
          {:ok, :ok} ->
            # Write to console for immediate feedback
            # IO.puts(log_entry)
            :ok

          {:error, error} ->
            # Log error but don't fail the function
            IO.puts("Error logging function call: #{inspect(error)}")
            :ok
        end

        # Only cleanup if we're the root process and all children are done
        if is_root_process?() do
          cleanup_chain()
        end

        result
      end

      defp header_line(timestamp, name, arity, module, pid, chain_id) do
        base = "[CHAIN_ID: #{chain_id}] [#{timestamp}] #{name}/#{arity} - #{module} (#{pid})"
        if chain_id, do: "#{base} [Chain: #{chain_id}]", else: base
      end

      defp process_state_section(process_info) do
        memory_stats =
          [
            "heap=#{ServiceManager.Logging.FunctionTracker.format_number(process_info[:heap_size])}/#{ServiceManager.Logging.FunctionTracker.format_number(process_info[:total_heap_size])}",
            "stack=#{process_info[:stack_size]}"
          ]
          |> Enum.join(", ")

        gc_info = process_info[:garbage_collection] || []

        [
          "├─ Location: line #{unquote(meta[:line])}",
          "├─ Process State:",
          "│  ├─ registered_name: #{process_info[:registered_name] || "nil"}",
          "│  ├─ status: #{inspect(process_info[:status])}",
          "│  ├─ message_queue_len: #{process_info[:message_queue_len] || 0}",
          "│  ├─ links: #{inspect(process_info[:links] || [])}",
          "│  ├─ dictionary: #{inspect(process_info[:dictionary] || [])}",
          "│  ├─ trap_exit: #{process_info[:trap_exit] || false}",
          "│  ├─ error_handler: #{inspect(process_info[:error_handler])}",
          "│  ├─ priority: #{inspect(process_info[:priority])}",
          "│  ├─ group_leader: #{inspect(process_info[:group_leader])}",
          "│  ├─ memory: #{memory_stats}",
          "│  ├─ reductions: #{ServiceManager.Logging.FunctionTracker.format_number(process_info[:reductions])}",
          "│  ├─ gc: #{format_gc_info(gc_info)}",
          "│  └─ suspending: #{inspect(process_info[:suspending] || [])}"
        ]
        |> Enum.join("\n")
      end

      defp format_gc_info(gc_info) do
        [
          "max_heap_size=#{inspect(gc_info[:max_heap_size])}",
          "min_bin_vheap_size=#{gc_info[:min_bin_vheap_size]}",
          "min_heap_size=#{gc_info[:min_heap_size]}",
          "fullsweep_after=#{gc_info[:fullsweep_after]}",
          "minor_gcs=#{gc_info[:minor_gcs]}"
        ]
        |> Enum.join(", ")
      end

      defp request_section(binding) do
        {headers, url} = extract_request_params(binding)
        params = extract_params(binding)

        [
          "├─ Request:",
          "│  ├─ URL: #{url || "N/A"}",
          "│  └─ Headers:",
          format_headers(headers)
        ]
        |> List.flatten()
        |> Enum.join("\n")
      end

      defp format_headers(headers) do
        cond do
          headers == [] ->
            ["│     └─ ... (0 headers total)"]

          true ->
            formatted =
              headers
              |> Enum.map(fn {k, v} ->
                "│     ├─ #{k}: #{ServiceManager.Logging.FunctionTracker.redact_sensitive(k, v)}"
              end)

            case formatted do
              [] ->
                ["│     └─ ... (0 headers total)"]

              [_ | _] = list ->
                {init, [last]} = Enum.split(list, -1)

                init ++
                  [String.replace(last, "├─", "└─")] ++
                  ["│     └─ ... (#{length(headers)} headers total)"]
            end
        end
      end

      defp extract_params(binding) do
        Enum.reduce(binding, %{}, fn {key, value}, acc ->
          cond do
            key in [:headers, :url] -> acc
            true -> Map.put(acc, key, value)
          end
        end)
      end

      defp response_section(result, duration) do
        cond do
          match?({:ok, %{status: _, body: _, headers: _}}, result) ->
            {:ok, %{status: status, body: body, headers: resp_headers}} = result

            # Get enhanced response info
            response_info = extract_response_info(result)
            content_type = get_content_type(resp_headers)
            body_size = if is_binary(body), do: byte_size(body), else: 0

            # Parse body with enhanced parsing
            parsed_body =
              if response_parsing_enabled?() do
                ServiceManager.Logging.FunctionTracker.parse_response_body(body, content_type)
              else
                ServiceManager.Logging.FunctionTracker.try_parse_json(body)
              end

            audit_times =
              case parsed_body do
                %{parsed: parsed} when is_map(parsed) ->
                  get_in(parsed, ["header", "audit"]) || %{}
                parsed when is_map(parsed) ->
                  get_in(parsed, ["header", "audit"]) || %{}
                _ -> %{}
              end

            security_headers =
              ServiceManager.Logging.FunctionTracker.extract_security_headers(resp_headers)

            sections = [
              "└─ Response (#{status} in #{duration}ms):",
              response_metadata_section(content_type, body_size, response_info),
              if(map_size(audit_times) > 0, do: response_timing_section(audit_times)),
              enhanced_response_data_section(parsed_body),
              if(Enum.any?(security_headers), do: security_headers_section(security_headers))
            ]

            sections
            |> Enum.reject(&is_nil/1)
            |> Enum.join("\n")

          match?({:error, _}, result) ->
            ["└─ Response: #{inspect(result)}"] |> Enum.join("\n")

          is_map(result) and Map.has_key?(result, :__struct__) ->
            ["└─ Response: #{inspect(result, pretty: true, limit: :infinity)}"] |> Enum.join("\n")

          true ->
            ["└─ Response: #{inspect(result, pretty: true, limit: :infinity)}"] |> Enum.join("\n")
        end
      end

      defp response_metadata_section(content_type, body_size, response_info) do
        metadata_items = [
          if(content_type, do: "content-type: #{content_type}"),
          "size: #{format_bytes(body_size)}",
          if(Map.get(response_info, :size_limit_exceeded), do: "⚠️  SIZE LIMIT EXCEEDED"),
          if(Map.get(response_info, :metadata, %{}) |> Map.get(:redaction_enabled), do: "🔒 redacted"),
          if(Map.get(response_info, :metadata, %{}) |> Map.get(:parsing_enabled), do: "📝 parsed")
        ]
        |> Enum.reject(&is_nil/1)
        |> Enum.join(", ")

        "   ├─ Metadata: #{metadata_items}"
      end

      defp format_bytes(bytes) when is_integer(bytes) do
        cond do
          bytes >= 1_048_576 -> "#{Float.round(bytes / 1_048_576, 2)} MB"
          bytes >= 1_024 -> "#{Float.round(bytes / 1_024, 2)} KB"
          true -> "#{bytes} bytes"
        end
      end
      defp format_bytes(_), do: "unknown"

      defp enhanced_response_data_section(parsed_body) do
        case parsed_body do
          %{type: "json", parsed: data} ->
            format_json_response_data(data)

          %{type: "json", error: error, raw_preview: preview} ->
            [
              "   ├─ Response Data (JSON - Parse Error):",
              "   │  ├─ Error: #{error}",
              "   │  └─ Preview: #{String.slice(preview, 0, 200)}..."
            ] |> Enum.join("\n")

          %{type: "xml", raw_preview: preview, size: size} ->
            [
              "   ├─ Response Data (XML - #{format_bytes(size)}):",
              "   │  └─ Preview: #{String.slice(preview, 0, 200)}..."
            ] |> Enum.join("\n")

          %{type: "text", content: content, size: size} ->
            [
              "   ├─ Response Data (Text - #{format_bytes(size)}):",
              "   │  └─ Content: #{String.slice(content, 0, 300)}..."
            ] |> Enum.join("\n")

          %{type: "binary", size: size, preview: preview} ->
            [
              "   ├─ Response Data (Binary - #{format_bytes(size)}):",
              "   │  └─ Base64 Preview: #{String.slice(preview, 0, 100)}..."
            ] |> Enum.join("\n")

          %{type: "truncated", message: message, preview: preview} ->
            [
              "   ├─ Response Data (Truncated):",
              "   │  ├─ Reason: #{message}",
              "   │  └─ Preview: #{String.slice(preview, 0, 200)}..."
            ] |> Enum.join("\n")

          # Fallback to original response_data_section for backward compatibility
          data when is_map(data) ->
            response_data_section(data)

          _ ->
            nil
        end
      end

      defp format_json_response_data(data) when is_map(data) do
        # Use existing logic for account data and customer details
        response_data_section(data)
      end

      defp format_json_response_data(_), do: nil

      defp response_timing_section(audit) do
        times =
          [
            {"T24_time", audit["T24_time"]},
            {"request_parse", audit["requestParse_time"]},
            {"response_parse", audit["responseParse_time"]}
          ]
          |> Enum.reject(fn {_, v} -> is_nil(v) end)

        case times do
          [] ->
            nil

          times ->
            [
              "   ├─ Processing Times:",
              Enum.map(Enum.drop(times, -1), fn {k, v} -> "   │  ├─ #{k}: #{v}ms" end),
              "   │  └─ #{elem(List.last(times), 0)}: #{elem(List.last(times), 1)}ms"
            ]
            |> List.flatten()
            |> Enum.join("\n")
        end
      end

      defp response_data_section(parsed) do
        cond do
          is_map(parsed) and match?(%{"body" => [%{} | _]}, parsed) ->
            [body | _] = parsed["body"]
            sections = []

            # Account data section
            sections =
              if has_account_data?(body) do
                account_data =
                  %{
                    "cleared_balance" => body["clearedBalance"],
                    "available_balance" => body["availableBalance"],
                    "working_balance" => body["workingBalance"],
                    "online_balance" => body["onlineActualBalance"],
                    "holder" => body["displayName"] || body["holderName"],
                    "customer_id" => get_customer_id(body),
                    "currency" => body["currencyId"],
                    "category" => body["categoryId"]
                  }
                  |> Enum.reject(fn {_, v} -> is_nil(v) end)
                  |> Enum.map(fn {k, v} ->
                    if is_number(v) do
                      {k,
                       ServiceManager.Logging.FunctionTracker.format_currency(
                         v,
                         body["currencyId"]
                       )}
                    else
                      {k, v}
                    end
                  end)

                case account_data do
                  [] -> sections
                  data -> [format_section("Account Data", data) | sections]
                end
              else
                sections
              end

            # Customer details section
            sections =
              if has_customer_details?(body) do
                customer_data =
                  case get_in(body, ["customerDetails", Access.at(0)]) do
                    nil ->
                      []

                    details ->
                      [
                        {"first_name", get_first(details["firstName"])},
                        {"last_name", get_first(details["lastName"])},
                        {"date_of_birth", get_first(details["dateOfBirth"])},
                        {"phone_number", get_first(details["phoneNumber"])},
                        {"email", get_first(details["email"])},
                        {"street", get_first(details["street"])},
                        {"town_country", get_first(details["townCountry"])},
                        {"post_code", get_first(details["postCode"])},
                        {"employment_status", get_first(details["employmentStatus"])},
                        {"marital_status", get_first(details["maritalStatus"])}
                      ]
                      |> Enum.reject(fn {_, v} -> is_nil(v) end)
                  end

                case customer_data do
                  [] -> sections
                  data -> [format_section("Customer Details", data) | sections]
                end
              else
                sections
              end

            case sections do
              [] -> nil
              sections -> Enum.reverse(sections) |> Enum.join("\n")
            end

          true ->
            nil
        end
      end

      defp has_account_data?(body) do
        Enum.any?(
          [
            body["clearedBalance"],
            body["availableBalance"],
            body["workingBalance"],
            body["onlineActualBalance"],
            body["displayName"],
            body["holderName"]
          ],
          &(!is_nil(&1))
        )
      end

      defp has_customer_details?(body) do
        case get_in(body, ["customerDetails", Access.at(0)]) do
          nil -> false
          _ -> true
        end
      end

      defp get_first(value) do
        cond do
          is_nil(value) -> nil
          is_list(value) and value == [] -> nil
          is_list(value) -> hd(value)
          true -> value
        end
      end

      defp format_section(title, data) do
        formatted_data =
          data
          |> Enum.map(fn {k, v} -> {k, format_value(v)} end)

        [
          "   ├─ #{title}:",
          Enum.map(Enum.drop(formatted_data, -1), fn {k, v} -> "   │  ├─ #{k}: #{v}" end),
          "   │  └─ #{elem(List.last(formatted_data), 0)}: #{elem(List.last(formatted_data), 1)}"
        ]
        |> List.flatten()
        |> Enum.join("\n")
      end

      defp format_value(value) do
        cond do
          is_list(value) ->
            case value do
              [] -> "[]"
              [first | _] -> if is_binary(first), do: first, else: inspect(value)
              _ -> inspect(value)
            end

          is_map(value) ->
            inspect(value)

          true ->
            to_string(value)
        end
      end

      defp security_headers_section(headers) do
        formatted_headers =
          headers
          |> Enum.map(fn {k, v} -> {String.replace(k, "-", " "), v} end)
          |> Enum.sort_by(fn {k, _} -> k end)

        case formatted_headers do
          [] ->
            nil

          headers ->
            [
              "   └─ Security Headers:",
              Enum.map(Enum.drop(headers, -1), fn {k, v} -> "      ├─ #{k}: #{v}" end),
              "      └─ #{elem(List.last(headers), 0)}: #{elem(List.last(headers), 1)}"
            ]
            |> List.flatten()
            |> Enum.join("\n")
        end
      end

      defp separator_line, do: String.duplicate("=", 60)

      # Helper functions
      defp extract_request_info(binding) do
        {headers, url} = extract_request_params(binding)

        %{
          url: url,
          headers: headers
        }
      end

      defp extract_response_info(result) do
        case result do
          {:ok, %{status: status, body: body, headers: headers}} ->
            content_type = get_content_type(headers)

            # Parse response body with enhanced parsing
            parsed_body =
              if response_parsing_enabled?() do
                ServiceManager.Logging.FunctionTracker.parse_response_body(body, content_type)
              else
                ServiceManager.Logging.FunctionTracker.try_parse_json(body)
              end

            # Apply redaction if enabled
            final_body =
              if response_redaction_enabled?() do
                ServiceManager.Logging.FunctionTracker.redact_response_data(parsed_body)
              else
                parsed_body
              end

            # Check size limits
            body_size = if is_binary(body), do: byte_size(body), else: nil
            size_limit = get_response_size_limit()

            response_info = %{
              status: status,
              content_type: content_type,
              body_size: body_size,
              size_limit_exceeded: body_size && body_size > size_limit,
              headers: extract_response_headers(headers),
              body: final_body,
              metadata: %{
                parsing_enabled: response_parsing_enabled?(),
                redaction_enabled: response_redaction_enabled?(),
                timestamp: DateTime.utc_now()
              }
            }

            # Truncate if size limit exceeded
            if body_size && body_size > size_limit do
              Map.put(response_info, :body, %{
                type: "truncated",
                message: "Response body truncated due to size limit (#{body_size} > #{size_limit} bytes)",
                preview: String.slice(to_string(body), 0, min(500, size_limit))
              })
            else
              response_info
            end

          _ ->
            %{
              result: inspect(result, pretty: true, limit: :infinity),
              metadata: %{
                parsing_enabled: response_parsing_enabled?(),
                redaction_enabled: response_redaction_enabled?(),
                timestamp: DateTime.utc_now()
              }
            }
        end
      end

      defp get_content_type(headers) when is_list(headers) do
        headers
        |> Enum.find(fn {key, _value} ->
          String.downcase(key) == "content-type"
        end)
        |> case do
          {_key, value} -> value
          nil -> nil
        end
      end

      defp get_content_type(_), do: nil

      defp extract_response_headers(headers) when is_list(headers) do
        headers
        |> Enum.take(10)  # Limit number of headers logged
        |> Enum.map(fn {key, value} ->
          {key, ServiceManager.Logging.FunctionTracker.redact_sensitive(key, value)}
        end)
      end

      defp extract_response_headers(_), do: []

      defp extract_request_params(binding) do
        Enum.reduce(binding, {[], nil}, fn {key, value}, {headers, url} ->
          cond do
            key == :headers -> {value, url}
            key == :url -> {headers, value}
            true -> {headers, url}
          end
        end)
      end

      defp function_params_section(arg_names, arg_values) do
        [
          "├─ Function Parameters:",
          case arg_values do
            [] ->
              "│  └─ No parameters"

            values ->
              values
              |> Enum.map(fn {name, value} -> "│  ├─ #{name}: #{value}" end)
              |> Enum.reverse()
              |> (fn lines ->
                    cond do
                      lines == [] ->
                        []

                      true ->
                        {init, [last]} = Enum.split(lines, -1)
                        init ++ [String.replace(last, "├─", "└─")]
                    end
                  end).()
          end
        ]
        |> Enum.join("\n")
      end

      defp function_output_section(result) do
        [
          "├─ Function Output:",
          "│  └─ #{inspect(result, pretty: true, limit: :infinity, width: 80)}"
        ]
        |> Enum.join("\n")
      end

      defp get_customer_id(body) do
        body["customerDetails"] || body["CUSTOMER"] || "Unknown"
      end
    end
  end
end
