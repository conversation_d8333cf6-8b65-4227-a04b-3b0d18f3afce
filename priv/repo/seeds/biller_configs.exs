alias ServiceManager.Repo
alias ServiceManager.Schemas.Billers.BillerConfig

# Seed biller configurations for all supported services

# Register General Configuration
register_general_config = %{
  biller_type: "register_general",
  biller_name: "Register General",
  display_name: "Register General",
  description: "General registration and invoice services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "get_invoice" => "/api/billers/register-general/v1/accounts/{account_number}",
    "confirm_invoice" => "/api/billers/register-general/v1/transactions"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_invoice" => true,
    "supports_payment" => true,
    "requires_confirmation" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "min_length" => 3,
      "max_length" => 20
    }
  }
}

# BWB Postpaid Configuration
bwb_postpaid_config = %{
  biller_type: "bwb_postpaid",
  biller_name: "BWB Postpaid",
  display_name: "Blantyre Water Board - Postpaid",
  description: "Blantyre Water Board postpaid services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "account_details" => "/esb/api/bwb-postpaid-test/v1/accounts/{account_number}",
    "post_transaction" => "/esb/api/bwb-postpaid-test/v1/transactions"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_account_lookup" => true,
    "supports_payment" => true,
    "postpaid" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{8,12}$"
    }
  }
}

# LWB Postpaid Configuration
lwb_postpaid_config = %{
  biller_type: "lwb_postpaid",
  biller_name: "LWB Postpaid",
  display_name: "Lilongwe Water Board - Postpaid",
  description: "Lilongwe Water Board postpaid services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}",
    "post_transaction" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_account_lookup" => true,
    "supports_payment" => true,
    "postpaid" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{6,10}$"
    }
  }
}

# SRWB Postpaid Configuration
srwb_postpaid_config = %{
  biller_type: "srwb_postpaid",
  biller_name: "SRWB Postpaid",
  display_name: "Southern Region Water Board - Postpaid",
  description: "Southern Region Water Board postpaid services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "account_details" => "/esb/api/lwb-postpaid-test/v1/accounts/{account_number}",
    "post_transaction" => "/esb/api/srwb-postpaid-test/v1/transactions"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_account_lookup" => true,
    "supports_payment" => true,
    "postpaid" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[A-Z0-9]{6,12}$"
    }
  }
}

# SRWB Prepaid Configuration
srwb_prepaid_config = %{
  biller_type: "srwb_prepaid",
  biller_name: "SRWB Prepaid",
  display_name: "Southern Region Water Board - Prepaid",
  description: "Southern Region Water Board prepaid services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "get_invoice" => "/esb/api/srwb-prepaid-test/v1/accounts/{account_number}",
    "confirm_invoice" => "/esb/api/srwb-prepaid-test/v1/transactions"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_invoice" => true,
    "supports_payment" => true,
    "prepaid" => true,
    "requires_confirmation" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{10,15}$"
    }
  }
}

# MASM Configuration
masm_config = %{
  biller_type: "masm",
  biller_name: "MASM",
  display_name: "Malawi Savings Bank",
  description: "Malawi Savings Bank services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "account_details" => "/esb/api/masm-test/v1/accounts/{account_number}",
    "confirm_invoice" => "/esb/api/masm-test/v1/transactions"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_account_lookup" => true,
    "supports_payment" => true,
    "requires_type_parameter" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{8,12}$"
    },
    "type" => %{
      "required" => true,
      "allowed_values" => ["M", "S", "C"]
    }
  }
}

# Airtel Validation Configuration
airtel_validation_config = %{
  biller_type: "airtel_validation",
  biller_name: "Airtel Validation",
  display_name: "Airtel Money Validation",
  description: "Airtel Money account validation services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "validation" => "/esb/api/airtel-validation/v1/accounts/{account_number}"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_validation" => true,
    "validation_only" => true
  },
  validation_rules: %{
    "account_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{9,10}$"
    }
  }
}

# TNM Bundles Configuration
tnm_bundles_config = %{
  biller_type: "tnm_bundles",
  biller_name: "TNM Bundles",
  display_name: "TNM Internet Bundles",
  description: "TNM Internet bundle services",
  is_active: true,
  base_url: "http://localhost:8080",
  endpoints: %{
    "bundle_details" => "/esb/api/internetbundles/v1/{bundle_id}",
    "confirm_bundle" => "/esb/api/internetbundles/v1/{transaction_id}/{phone_number}/{bundle_id}"
  },
  authentication: %{
    "type" => "basic",
    "username" => "admin",
    "password" => "admin"
  },
  default_currency: "MWK",
  supported_currencies: ["MWK"],
  timeout_ms: 30000,
  retry_attempts: 3,
  features: %{
    "supports_bundles" => true,
    "requires_phone_number" => true,
    "requires_transaction_id" => true
  },
  validation_rules: %{
    "bundle_id" => %{
      "required" => true,
      "pattern" => "^[0-9]{3,10}$"
    },
    "phone_number" => %{
      "required" => true,
      "pattern" => "^[0-9]{9,10}$"
    }
  }
}

# List of all configurations
configs = [
  register_general_config,
  bwb_postpaid_config,
  lwb_postpaid_config,
  srwb_postpaid_config,
  srwb_prepaid_config,
  masm_config,
  airtel_validation_config,
  tnm_bundles_config
]

# Insert configurations
Enum.each(configs, fn config ->
  case Repo.get_by(BillerConfig, biller_type: config.biller_type) do
    nil ->
      %BillerConfig{}
      |> BillerConfig.changeset(config)
      |> Repo.insert!()
      |> then(fn inserted_config ->
        IO.puts("✓ Created biller config: #{inserted_config.display_name}")
      end)
    
    existing_config ->
      existing_config
      |> BillerConfig.changeset(config)
      |> Repo.update!()
      |> then(fn updated_config ->
        IO.puts("✓ Updated biller config: #{updated_config.display_name}")
      end)
  end
end)

IO.puts("\n🎉 Biller configurations seeded successfully!")
IO.puts("Total configurations: #{length(configs)}")