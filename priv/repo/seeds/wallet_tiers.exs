# Script for creating default wallet tiers
alias ServiceManager.Repo
alias ServiceManager.WalletAccounts.WalletTier

# Helper function to convert string to Decimal
defp to_decimal(value) when is_binary(value), do: Decimal.new(value)
defp to_decimal(value) when is_integer(value), do: Decimal.new(value)

# Define available KYC fields for reference
@available_kyc_fields [
  "first_name",
  "last_name",
  "mobile_number",
  "email",
  "date_of_birth",
  "address",
  "city",
  "occupation",
  "employer_name",
  "source_of_funds",
  "id_number",
  "id_image"
]

# Basic Tier
Repo.insert!(%WalletTier{
  name: "Basic",
  description: "Basic wallet tier with essential features",
  position: 1,
  is_default: true,

  # Balance Limits
  minimum_balance: to_decimal("0"),
  maximum_balance: to_decimal("100000"),
  maximum_credit_limit: to_decimal("400000"),
  maximum_debt_limit: to_decimal("400000"),

  # Transaction Limits
  min_transaction_amount: to_decimal("100"),
  max_transaction_amount: to_decimal("10000"),
  daily_transaction_limit: to_decimal("50000"),
  monthly_transaction_limit: to_decimal("500000"),

  # Transaction Count Limits
  daily_transaction_count: 10,
  monthly_transaction_count: 100,

  # Required KYC Fields
  required_kyc_fields: [
    "first_name",
    "last_name",
    "mobile_number",
    "email",
    "id_number"
  ],
  kyc_rules: %{
    "id_required" => true
  }
})

# Silver Tier
Repo.insert!(%WalletTier{
  name: "Silver",
  description: "Enhanced wallet tier with higher limits",
  position: 2,
  is_default: false,

  # Balance Limits
  minimum_balance: to_decimal("0"),
  maximum_balance: to_decimal("500000"),
  maximum_credit_limit: to_decimal("1500000"),
  maximum_debt_limit: to_decimal("1500000"),

  # Transaction Limits
  min_transaction_amount: to_decimal("100"),
  max_transaction_amount: to_decimal("50000"),
  daily_transaction_limit: to_decimal("200000"),
  monthly_transaction_limit: to_decimal("2000000"),

  # Transaction Count Limits
  daily_transaction_count: 20,
  monthly_transaction_count: 300,

  # Required KYC Fields
  required_kyc_fields: [
    "first_name",
    "last_name",
    "mobile_number",
    "email",
    "date_of_birth",
    "address",
    "city",
    "id_number",
    "id_image"
  ],
  kyc_rules: %{
    "minimum_age" => 18,
    "id_required" => true,
    "address_required" => true
  }
})

# Gold Tier
Repo.insert!(%WalletTier{
  name: "Gold",
  description: "Premium wallet tier with maximum limits",
  position: 3,
  is_default: false,

  # Balance Limits
  minimum_balance: to_decimal("0"),
  maximum_balance: to_decimal("2000000"),
  maximum_credit_limit: to_decimal("8000000"),
  maximum_debt_limit: to_decimal("8000000"),

  # Transaction Limits
  min_transaction_amount: to_decimal("100"),
  max_transaction_amount: to_decimal("200000"),
  daily_transaction_limit: to_decimal("1000000"),
  monthly_transaction_limit: to_decimal("10000000"),

  # Transaction Count Limits
  daily_transaction_count: 50,
  monthly_transaction_count: 1000,

  # Required KYC Fields
  required_kyc_fields: [
    "first_name",
    "last_name",
    "mobile_number",
    "email",
    "date_of_birth",
    "address",
    "city",
    "occupation",
    "employer_name",
    "source_of_funds",
    "id_number",
    "id_image"
  ],
  kyc_rules: %{
    "minimum_age" => 18,
    "id_required" => true,
    "address_required" => true,
    "employment_verification" => true,
    "source_of_funds_required" => true
  }
})
