# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
alias ServiceManager.Repo
alias ServiceManager.Schemas.Route

ServiceManager.Schemas.Seeds.SetUser.run()

alias ServiceManager.Schemas.AdminUsers

# Create admin user
admin_attrs = %{
  email: "<EMAIL>",
  password: "Qwerty123456",
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  phone_number: "+************",
  customer_no: "ADM001",
  nickname: "SysAdmin",
  address: "123 Admin Street",
  city: "Lilongwe",
  state: "Central",
  zip: "00000",
  country: "Malawi",
  approved: true,
  account_balance: 0.0,
  account_number: "*************",
  memorable_word: "AdminSecureWord123",
  roles_and_permission_id: 1
}

{:ok, _admin} =
  %AdminUsers{}
  |> AdminUsers.registration_changeset(admin_attrs)
  |> AdminUsers.confirm_changeset()
  |> Repo.insert()

# Helper function to parse URL into host and path
parse_url = fn url ->
  uri = URI.parse(url)
  {uri.host || uri.path, uri.path || "/"}
end

# Define all routes
routes = [
  # Account Related
  %{
    name: "get_account_balance",
    url:
      "http://**************:9090/getAccountBalance2/api/v1.0.0/party/ws/account/balances/*************",
    method: "GET"
  },
  %{
    name: "get_account_by_phone",
    url: "https://fdh-esb.ngrok.dev/api/esb/account_by_phone/1.0/phone/**********",
    method: "GET"
  },
  %{
    name: "get_account_transactions",
    url: "https://fdh-esb.ngrok.dev/api/esb/reports/v1/account/transactions/*************",
    method: "GET"
  },
  %{
    name: "get_book_balance",
    url: "https://fdh-esb.ngrok.dev/api/esb/cb/bookbalance/book/balances/*************",
    method: "GET"
  },
  %{
    name: "create_account",
    url: "https://fdh-esb.ngrok.dev/api/esb/cb/accounts/v1/create/account/*************",
    method: "POST"
  },
  %{
    name: "create_iris_account",
    url: "https://fdh-esb.ngrok.dev/api/esb/create_account/1.0/account/irisapi",
    method: "POST"
  },
  %{
    name: "create_virtual_account",
    url: "https://fdh-esb.ngrok.dev/api/esb/virtual/1.0/account/create/fdh/acc",
    method: "POST"
  },

  # Customer Related
  %{
    name: "get_customer_by_phone",
    url: "https://fdh-esb.ngrok.dev/api/esb/customer/1.0/phone/**********",
    method: "GET"
  },
  %{
    name: "get_customer_number",
    url: "https://fdh-esb.ngrok.dev/api/esb/customer/number/1.0/details/144",
    method: "GET"
  },
  %{
    name: "get_customer_profile",
    url: "https://fdh-esb.ngrok.dev/api/esb/customers/v1/customer/profile/1617214",
    method: "GET"
  },

  # Transaction Related
  %{
    name: "bank_transfer",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/other/1.0/bank",
    method: "POST"
  },
  %{
    name: "own_transfer",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/own/1.0/payments/mobile/own",
    method: "POST"
  },
  %{
    name: "wallet_transfer",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/wallet/1.0/mobile/ofs/fdh",
    method: "POST"
  },
  %{
    name: "mobile_money_transfer",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/mobilemoney/wallet/1.0/mobile/ofs",
    method: "POST"
  },
  %{
    name: "get_transaction_report",
    url: "https://fdh-esb.ngrok.dev/api/esb/report/transaction/FT24204740T1",
    method: "GET"
  },
  %{
    name: "initiate_transaction",
    url: "https://fdh-esb.ngrok.dev/api/esb/transaction/1.0/initiate/transaction",
    method: "POST"
  },

  # Payment Related
  %{
    name: "bill_payment",
    url: "https://fdh-esb.ngrok.dev/api/esb/crwd/1.0/bill/payment/app",
    method: "POST"
  },
  %{
    name: "fdh_payment",
    url: "https://fdh-esb.ngrok.dev/api/esb/transfers/fdh/1.0/payments/mobile/ofs",
    method: "POST"
  },
  %{
    name: "hello_paisa_payment",
    url: "https://fdh-esb.ngrok.dev/api/esb/paisa/1.0/payments/hellopaisa",
    method: "POST"
  },
  %{
    name: "premier_bet_payment",
    url: "https://fdh-esb.ngrok.dev/api/esb/premier/1.0/bet",
    method: "POST"
  },
  %{
    name: "azam_payment",
    url: "https://fdh-esb.ngrok.dev/api/esb/azam/1.0/azamtv/prepaid/mob",
    method: "POST"
  },

  # Other Services
  %{
    name: "get_currency_exchange",
    url:
      "http://**************:9090/currencyExchanges/api/v1.0.0/party/currency/exchrate/MWK/USD/100.00/BUY",
    method: "GET"
  },
  %{
    name: "get_mini_statement",
    url: "https://fdh-esb.ngrok.dev/api/esb/statement/1.0/mini/stmt/1440000057995",
    method: "GET"
  },
  %{
    name: "get_standing_orders",
    url: "https://fdh-esb.ngrok.dev/api/esb/standing/1.0/orders/1010000003987.902",
    method: "GET"
  },
  %{
    name: "reserve_funds",
    url: "https://fdh-esb.ngrok.dev/api/esb/cb/aclockedevents/reserve/funds/1",
    method: "POST"
  },
  %{
    name: "release_funds",
    url:
      "http://**************:9090/releaseReservedFunds/api/v1.0.0/party/aclockedevents/release/reserved/funds/1040000638647",
    method: "POST"
  },
  %{
    name: "thunes_sms",
    url: "https://fdh-esb.ngrok.dev/api/esb/thunes/1.0/ebthunes/sms/commit",
    method: "POST"
  }
]

# Insert all routes
Enum.each(routes, fn route ->
  {host, path} = parse_url.(route.url)

  attrs = %{
    name: route.name,
    host: host,
    path: path,
    method: route.method
  }

  # Insert or update based on name
  case Repo.get_by(Route, name: route.name) do
    nil ->
      %Route{}
      |> Route.changeset(attrs)
      |> Repo.insert()

    existing_route ->
      existing_route
      |> Route.changeset(attrs)
      |> Repo.update()
  end
end)

IO.puts("Routes seeded successfully!")
