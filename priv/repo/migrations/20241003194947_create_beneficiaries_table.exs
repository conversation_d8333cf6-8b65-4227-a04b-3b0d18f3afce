defmodule ServiceManager.Repo.Migrations.CreateBeneficiariesTable do
  use Ecto.Migration

  def change do
    create table(:beneficiaries) do
      add :name, :string, null: false
      add :account_number, :string, null: false
      add :bank_code, :string, null: false
      add :currency, :string
      add :description, :text
      add :status, :string, default: "active"
      add :is_default, :boolean, default: false

      timestamps()
    end

    create unique_index(:beneficiaries, [:account_number, :bank_code])
  end
end
