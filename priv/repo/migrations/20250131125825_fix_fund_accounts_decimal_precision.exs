defmodule ServiceManager.Repo.Migrations.FixFundAccountsDecimalPrecision do
  use Ecto.Migration

  def up do
    # execute """
    # ALTER TABLE fund_accounts
    # ALTER COLUMN balance TYPE numeric(20,2),
    # ALTER COLUMN large_transaction_threshold TYPE numeric(20,2),
    # ALTER COLUMN low_balance_threshold TYPE numeric(20,2)
    # """
  end

  def down do
    # execute """
    # ALTER TABLE fund_accounts
    # ALTER COLUMN balance TYPE numeric(10,2),
    # ALTER COLUMN large_transaction_threshold TYPE numeric(10,2),
    # ALTER COLUMN low_balance_threshold TYPE numeric(10,2)
    # """
  end
end
