defmodule ServiceManager.Repo.Migrations.CreateFundRequests do
  use Ecto.Migration

  def change do
    create table(:fund_requests) do
      add :amount, :decimal, null: false
      add :status, :string, null: false, default: "pending"
      add :transaction_reference, :string
      add :description, :string
      add :receiver_account, :string, null: false
      add :sender_account, :string, null: false

      add :sender_id, references(:accounts_users, on_delete: :nothing)
      add :receiver_id, references(:accounts_users, on_delete: :nothing)

      timestamps()
    end

    create index(:fund_requests, [:sender_id])
    create index(:fund_requests, [:receiver_id])
    create unique_index(:fund_requests, [:transaction_reference])
  end
end
