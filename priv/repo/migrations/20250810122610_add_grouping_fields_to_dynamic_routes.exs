defmodule ServiceManager.Repo.Migrations.AddGroupingFieldsToDynamicRoutes do
  use Ecto.Migration

  def change do
    alter table(:dynamic_routes) do
      add :category, :string
      add :group_name, :string
      add :priority, :integer, default: 0
      add :tags, {:array, :string}, default: []
      add :description, :text
    end

    create index(:dynamic_routes, [:category])
    create index(:dynamic_routes, [:group_name])
    create index(:dynamic_routes, [:priority])
  end
end
