defmodule ServiceManager.Repo.Migrations.CreateWalletTiers do
  use Ecto.Migration

  def change do
    create table(:wallet_tiers) do
      add :name, :string, null: false
      add :description, :string
      add :position, :integer, null: false
      add :is_default, :boolean, default: false

      # Balance Limits
      add :minimum_balance, :decimal, null: false, default: 0
      add :maximum_balance, :decimal, null: false

      # Transaction Limits
      add :min_transaction_amount, :decimal, null: false
      add :max_transaction_amount, :decimal, null: false
      add :daily_transaction_limit, :decimal, null: false
      add :monthly_transaction_limit, :decimal, null: false

      # Transaction Count Limits
      add :daily_transaction_count, :integer, null: false
      add :monthly_transaction_count, :integer, null: false

      # Dynamic KYC Requirements
      add :required_kyc_fields, {:array, :string}, null: false
      add :kyc_rules, :map, null: false, default: fragment("'{}'::jsonb")

      timestamps()
    end

    # Add indexes
    # create index(:wallet_tiers, [:is_default])
    # create index(:wallet_tiers, [:position])
    # create unique_index(:wallet_tiers, [:position])

    # Create a constraint to ensure only one default tier
    # create constraint(:wallet_tiers, :only_one_default_tier,
    #   check: """
    #   CASE WHEN is_default = true THEN
    #     (SELECT COUNT(*) FROM wallet_tiers WHERE is_default = true) <= 1
    #   ELSE true
    #   END
    #   """
    # )
  end
end
