defmodule ServiceManager.Repo.Migrations.CreateLoanTransactions do
  use Ecto.Migration

  def change do
    create table(:loan_transactions) do
      add :type, :string, null: false
      add :amount, :decimal, precision: 10, scale: 2, null: false
      add :opening_balance, :decimal, precision: 10, scale: 2
      add :closing_balance, :decimal, precision: 10, scale: 2
      add :description, :string
      add :status, :string, null: false
      add :status_description, :string
      add :reference, :string
      add :value_date, :date, null: false
      add :transaction_date, :utc_datetime, null: false
      add :payment_method, :string
      add :external_reference, :string
      add :debit_account, :string, null: false
      add :credit_account, :string, null: false
      add :from_account_id, references(:fund_accounts, on_delete: :restrict)
      add :to_account_id, references(:fund_accounts, on_delete: :restrict)
      add :loan_id, references(:loans, on_delete: :restrict), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:loan_transactions, [:from_account_id])
    create index(:loan_transactions, [:to_account_id])
    create index(:loan_transactions, [:loan_id])
  end
end
