defmodule ServiceManager.Repo.Migrations.CreateFormWizards do
  use Ecto.Migration

  def change do
    create table(:form_wizards) do
      add :name, :string, null: false
      add :description, :text
      add :active, :boolean, default: true
      add :created_by_id, references(:tbl_system_users, on_delete: :nilify_all)

      timestamps()
    end

    create index(:form_wizards, [:name])
    create index(:form_wizards, [:active])
    create index(:form_wizards, [:created_by_id])
  end
end
