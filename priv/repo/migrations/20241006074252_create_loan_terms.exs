defmodule ServiceManager.Repo.Migrations.CreateLoanTerms do
  use Ecto.Migration

  def change do
    create table(:loan_terms) do
      add :amount, :decimal, precision: 18, scale: 2, null: false
      add :interest_rate, :decimal, precision: 18, scale: 2, null: false
      add :duration, :integer, null: false
      add :total_repayment, :decimal, precision: 18, scale: 2, null: false
      add :customer_id, references(:accounts_users, on_delete: :restrict), null: false
      add :maker_id, references(:accounts_users, on_delete: :restrict)
      add :checker_id, references(:accounts_users, on_delete: :restrict)
      add :product_id, references(:loan_products, on_delete: :restrict), null: false

      timestamps()
    end

    create index(:loan_terms, [:customer_id])
    create index(:loan_terms, [:maker_id])
    create index(:loan_terms, [:checker_id])
    create index(:loan_terms, [:product_id])
  end
end
