defmodule ServiceManager.Repo.Migrations.AddStatusToAppUsers do
  use Ecto.Migration

  def up do
    alter table(:accounts_users) do
      add :deletion_status, :boolean, default: false
    end

    alter table(:tbl_system_users) do
      add :status, :string, default: "pending"
    end

    create index(:tbl_system_users, [:status])

    create index(:accounts_users, [:deletion_status])
  end

  def down do
    drop index(:accounts_users, [:deletion_status])

    drop index(:tbl_system_users, [:status])

    alter table(:tbl_system_users) do
      remove :status
    end

    alter table(:accounts_users) do
      remove :deletion_status
    end
  end
end
