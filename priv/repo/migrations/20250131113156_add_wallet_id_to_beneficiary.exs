defmodule ServiceManager.Repo.Migrations.AddWalletIdToBeneficiary do
  use Ecto.Migration

  def change do
    alter table(:beneficiaries) do
      add_if_not_exists :beneficiary_type, :string
      add_if_not_exists :wallet_id, references(:walletusers, on_delete: :nothing)
    end

    drop unique_index(:beneficiaries, [:account_number, :user_id, :beneficiary_type])

    # Create the unique index with a WHERE clause to exclude NULL values
    execute("""
    CREATE UNIQUE INDEX beneficiaries_account_user_type_index
    ON beneficiaries (account_number, user_id, beneficiary_type)
    WHERE account_number IS NOT NULL AND user_id IS NOT NULL AND beneficiary_type IS NOT NULL
    """)
  end
end
