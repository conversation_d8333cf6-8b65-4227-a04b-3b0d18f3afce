defmodule ServiceManager.Repo.Migrations.CreateDynamicForms do
  use Ecto.Migration

  def change do
    create table(:dynamic_forms) do
      add :name, :string, null: false
      add :description, :text
      add :http_method, :string, null: false
      add :form, :map, null: false  # The actual form definition
      add :validation_schema, :map, null: false  # JSON Schema for validation
      add :required, :boolean, default: true  # Is this form required for the route?

      timestamps()
    end

    create index(:dynamic_forms, [:name])
    create index(:dynamic_forms, [:http_method])
    
    # Junction table to link routes and forms
    create table(:dynamic_route_forms, primary_key: false) do
      add :route_id, references(:dynamic_routes, on_delete: :delete_all), null: false
      add :form_id, references(:dynamic_forms, on_delete: :delete_all), null: false
      
      timestamps()
    end
    
    create unique_index(:dynamic_route_forms, [:route_id, :form_id])
  end
end
