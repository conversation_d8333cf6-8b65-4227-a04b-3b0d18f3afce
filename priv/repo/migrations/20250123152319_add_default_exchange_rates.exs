defmodule ServiceManager.Repo.Migrations.AddDefaultExchangeRates do
  use Ecto.Migration

  def change do
    # Add default exchange rates
    execute """
            INSERT INTO exchange_rates (from_currency_code, to_currency_code, rate, inserted_at, updated_at)
            SELECT t.from_currency, t.to_currency, t.rate, NOW(), NOW()
            FROM (VALUES 
              ('USD', 'MWK', 1680.00),
              ('MWK', 'USD', 0.000595),
              ('EUR', 'MWK', 1830.00),
              ('MWK', 'EUR', 0.000546),
              ('GBP', 'MWK', 2140.00),
              ('MWK', 'GBP', 0.000467),
              ('ZMW', 'MWK', 80.00),
              ('MWK', 'ZMW', 0.0125),
              ('ZAR', 'MWK', 90.00),
              ('MWK', 'ZAR', 0.0111)
            ) AS t(from_currency, to_currency, rate)
            WHERE NOT EXISTS (
              SELECT 1 FROM exchange_rates 
              WHERE from_currency_code = t.from_currency 
              AND to_currency_code = t.to_currency
            )
            """,
            """
            DELETE FROM exchange_rates 
            WHERE (from_currency_code, to_currency_code) IN (
              ('USD', 'MWK'), ('MWK', 'USD'),
              ('EUR', 'MWK'), ('MWK', 'EUR'),
              ('GBP', 'MWK'), ('MWK', 'GBP'),
              ('ZMW', 'MWK'), ('MWK', 'ZMW'),
              ('ZAR', 'MWK'), ('MWK', 'ZAR')
            )
            """
  end
end
