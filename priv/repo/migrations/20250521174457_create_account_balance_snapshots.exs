defmodule ServiceManager.Repo.Migrations.CreateAccountBalanceSnapshots do
  use Ecto.Migration

  def change do
    create_if_not_exists table(:balance_snapshots) do
      add :account_id, :integer, null: false
      add :transaction_id, :integer, null: false
      add :balance_type, :string, null: false  # "pre" or "post"
      add :balance, :decimal, precision: 20, scale: 2, null: false
      add :working_balance, :decimal, precision: 20, scale: 2
      add :cleared_balance, :decimal, precision: 20, scale: 2
      add :currency, :string
      add :snapshot_reason, :string  # "transaction", "reversal", "adjustment", etc.

      timestamps()
    end

    create_if_not_exists index(:balance_snapshots, [:account_id])
    create_if_not_exists index(:balance_snapshots, [:transaction_id])
    create_if_not_exists index(:balance_snapshots, [:balance_type])
    create_if_not_exists index(:balance_snapshots, [:inserted_at])
  end
end
