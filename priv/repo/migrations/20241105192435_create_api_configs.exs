defmodule ServiceManager.Repo.Migrations.CreateApiConfigs do
  use Ecto.Migration

  def change do
    create table(:virtual_cards_api_configs) do
      add :provider_name, :string
      add :base_url, :string
      add :auth_token, :string
      add :api_key, :string
      add :api_secret, :string
      add :version, :string
      add :timeout, :integer
      add :status, :string
      add :notes, :text
      add :created_by, :integer
      add :updated_by, :integer
      add :is_default, :boolean, default: false

      timestamps(type: :utc_datetime)
    end
  end
end
