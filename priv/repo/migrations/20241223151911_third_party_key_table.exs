defmodule ServiceManager.Repo.Migrations.ThirdPartyKeyTable do
  use Ecto.Migration

  def change do
    create table(:third_party_api_key) do
      add :api_key, :string, null: false
      add :description, :string, null: true
      add :user_id, references(:tbl_system_users, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:third_party_api_key, [:api_key])
  end
end
