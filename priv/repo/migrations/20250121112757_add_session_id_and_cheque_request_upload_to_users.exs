defmodule ServiceManager.Repo.Migrations.AddSessionIdAndChequeRequestUploadToUsers do
  use Ecto.Migration

  def change do
    #   unless column_exists?(:accounts_users, :session_id) do
    #     alter table(:accounts_users) do
    #       add :session_id, :string
    #     end
    #   end

    #   unless column_exists?(:accounts_users, :allow_cheque_request_upload) do
    #     alter table(:accounts_users) do
    #       add :allow_cheque_request_upload, :boolean, default: false
    #     end
    #   end
    # end

    # defp column_exists?(table, column) do
    #   query = """
    #   SELECT column_name
    #   FROM information_schema.columns
    #   WHERE table_name = '#{table}'
    #   AND column_name = '#{column}'
    #   """

    #   case Ecto.Adapters.SQL.query(ServiceManager.Repo, query, []) do
    #     {:ok, %{rows: [[_]]}} -> true
    #     _ -> false
    #   end
  end
end
