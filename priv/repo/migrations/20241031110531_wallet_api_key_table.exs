defmodule ServiceManager.Repo.Migrations.WalletApiKeyTable do
  use Ecto.Migration

  def change do
    create table(:wallet_api_key) do
      add :api_key, :string, null: false
      add :description, :string, null: true
      add :wallet_user_id, references(:walletusers, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:wallet_api_key, [:api_key])
  end
end
