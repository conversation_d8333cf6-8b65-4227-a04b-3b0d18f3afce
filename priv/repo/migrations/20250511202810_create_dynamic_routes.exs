defmodule ServiceManager.Repo.Migrations.CreateDynamicRoutes do
  use Ecto.Migration

  def change do
    create table(:dynamic_routes) do
      add :name, :string, null: false
      add :method, :string, null: false
      add :path, :string, null: false
      add :enabled, :boolean, default: true, null: false
      add :parts, {:array, :string}, null: false
      add :flat_parts, {:array, :string}, null: false

      timestamps()
    end

    create unique_index(:dynamic_routes, [:method, :path])
  end
end
