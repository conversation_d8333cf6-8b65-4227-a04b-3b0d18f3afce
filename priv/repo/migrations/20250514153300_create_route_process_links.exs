defmodule ServiceManager.Repo.Migrations.CreateRouteProcessLinks do
  use Ecto.Migration

  def change do
    # Create the table if it doesn't exist
    create_if_not_exists table(:route_process_links) do
      add :route_id, references(:dynamic_routes, on_delete: :delete_all), null: false
      add :initial_process_id, references(:dynamic_processes, on_delete: :delete_all), null: false
      add :created_by_id, references(:tbl_system_users, on_delete: :nilify_all)

      timestamps()
    end

    # Create indexes if they don't exist
    create_if_not_exists index(:route_process_links, [:route_id])
    create_if_not_exists index(:route_process_links, [:initial_process_id])
    create_if_not_exists unique_index(:route_process_links, [:route_id])
  end
end
