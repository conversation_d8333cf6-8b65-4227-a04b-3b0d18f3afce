defmodule ServiceManager.Repo.Migrations.AddOriginalTransactionIdToTransactions do
  use Ecto.Migration

  def change do
    alter table(:transactions) do
      add :original_transaction_id, references(:transactions, on_delete: :nothing)
      add :is_reversal, :boolean, default: false
    end

    alter table(:wallet_transactions) do
      add :original_transaction_id, references(:wallet_transactions, on_delete: :nothing)
      add :is_reversal, :boolean, default: false
    end

    create index(:transactions, [:original_transaction_id])
    create index(:transactions, [:is_reversal])
    create index(:wallet_transactions, [:original_transaction_id])
    create index(:wallet_transactions, [:is_reversal])
  end
end
