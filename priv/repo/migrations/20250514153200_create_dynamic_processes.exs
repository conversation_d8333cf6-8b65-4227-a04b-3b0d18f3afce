defmodule ServiceManager.Repo.Migrations.CreateDynamicProcesses do
  use Ecto.Migration

  def change do
    # Create the table if it doesn't exist
    create_if_not_exists table(:dynamic_processes) do
      add :name, :string, null: false
      add :description, :text
      add :code, :text, null: false
      add :expected_params, :map, default: %{}
      add :created_by_id, references(:tbl_system_users, on_delete: :nilify_all)

      timestamps()
    end

    # Create indexes if they don't exist
    create_if_not_exists index(:dynamic_processes, [:name])
    create_if_not_exists unique_index(:dynamic_processes, [:name])
  end
end
