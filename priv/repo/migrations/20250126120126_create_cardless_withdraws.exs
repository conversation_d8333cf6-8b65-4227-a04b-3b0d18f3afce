defmodule ServiceManager.Repo.Migrations.CreateCardlessWithdraws do
  use Ecto.Migration

  def change do
    create table(:cardless_withdraws) do
      add :amount, :decimal, null: false
      add :reference_number, :string, null: false
      add :status, :string, default: "pending"
      add :otp, :string
      add :otp_verified, :boolean, default: false
      add :suspense_account, :string
      add :narration, :string
      add :source_account, :string, null: false
      add :beneficiary_phone, :string, null: false
      add :beneficiary_name, :string, null: false

      add :user_id, references(:accounts_users, on_delete: :nothing), null: false

      timestamps()
    end

    create index(:cardless_withdraws, [:user_id])
    create unique_index(:cardless_withdraws, [:reference_number])

    create table(:cardless_wallet_withdraws) do
      add :amount, :decimal, null: false
      add :reference_number, :string, null: false
      add :status, :string, default: "pending"
      add :otp, :string
      add :otp_verified, :boolean, default: false
      add :suspense_account, :string
      add :narration, :string
      add :source_wallet, :string, null: false
      add :beneficiary_phone, :string, null: false
      add :beneficiary_name, :string, null: false

      add :wallet_user_id, references(:walletusers, on_delete: :nothing), null: false

      timestamps()
    end

    create index(:cardless_wallet_withdraws, [:wallet_user_id])
    create unique_index(:cardless_wallet_withdraws, [:reference_number])
  end
end
