defmodule ServiceManager.Repo.Migrations.AddProfileFieldsToAccountsUsers do
    use Ecto.Migration
  
    def change do
      alter table(:accounts_users) do
        add :profile_name, :string, default: ""
        add :company, :string, default: ""
        add :inputter, :string, default: ""
        add :authoriser, :string, default: ""
        add :customer_number, :string, default: ""
        add :account_officer, :string, default: ""
        add :profile_type, :string, default: ""
      end
    end
  end