defmodule ServiceManager.Repo.Migrations.CreateCurrencies do
  use Ecto.Migration

  def change do
    create table(:currencies) do
      add :code, :string, size: 5, null: false
      add :name, :string, null: false
      add :symbol, :string, size: 5
      add :country, :string
      add :exchange_rate, :decimal, precision: 15, scale: 6, default: 0.00
      add :minor_unit, :integer, default: 2
      add :status, :string, default: "inactive"
      add :notes, :text
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end
  end
end
