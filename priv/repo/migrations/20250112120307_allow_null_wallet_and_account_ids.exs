defmodule ServiceManager.Repo.Migrations.AllowNullWalletAndAccountIds do
  use Ecto.Migration

  def change do
    # Drop existing constraints for transactions table
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_from_account_id_fkey"
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_to_account_id_fkey"
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_from_wallet_id_fkey"
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_to_wallet_id_fkey"

    # Drop existing constraints for wallet_transactions table
    execute "ALTER TABLE wallet_transactions DROP CONSTRAINT IF EXISTS wallet_transactions_from_account_id_fkey"

    execute "ALTER TABLE wallet_transactions DROP CONSTRAINT IF EXISTS wallet_transactions_to_account_id_fkey"

    execute "ALTER TABLE wallet_transactions DROP CONSTRAINT IF EXISTS wallet_transactions_from_wallet_id_fkey"

    execute "ALTER TABLE wallet_transactions DROP CONSTRAINT IF EXISTS wallet_transactions_to_wallet_id_fkey"

    # Modify columns in transactions table
    alter table(:transactions) do
      modify :from_account_id, references(:fund_accounts, on_delete: :nothing), null: true
      modify :to_account_id, references(:fund_accounts, on_delete: :nothing), null: true
      modify :from_wallet_id, references(:walletusers, on_delete: :nothing), null: true
      modify :to_wallet_id, references(:walletusers, on_delete: :nothing), null: true
    end

    # Modify columns in wallet_transactions table
    alter table(:wallet_transactions) do
      modify :from_account_id, references(:fund_accounts, on_delete: :nothing), null: true
      modify :to_account_id, references(:fund_accounts, on_delete: :nothing), null: true
      modify :from_wallet_id, references(:walletusers, on_delete: :nothing), null: true
      modify :to_wallet_id, references(:walletusers, on_delete: :nothing), null: true
    end
  end
end
