defmodule ServiceManager.Repo.Migrations.WalletTransactionsSchema do
  use Ecto.Migration

  def change do
    create table(:wallet_transactions) do
      add :type, :string, null: false
      add :amount, :decimal, null: true, precision: 10, scale: 2
      add :description, :string
      add :status, :string, null: false
      add :from_account_id, references(:walletusers, on_delete: :restrict)
      add :to_account_id, references(:walletusers, on_delete: :restrict)
      add :credit_amount, :decimal, null: false, precision: 10, scale: 2, default: 0
      add :debit_amount, :decimal, null: false, precision: 10, scale: 2, default: 0
      add :reference, :string, null: false
      add :value_date, :date, null: false
      add :opening_balance, :decimal, null: false, precision: 10, scale: 2
      add :closing_balance, :decimal, null: false, precision: 10, scale: 2

      timestamps()
    end

    create index(:wallet_transactions, [:from_account_id])
    create index(:wallet_transactions, [:to_account_id])
    create index(:wallet_transactions, [:type])
    create index(:wallet_transactions, [:status])
  end
end
