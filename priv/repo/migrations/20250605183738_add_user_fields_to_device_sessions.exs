defmodule ServiceManager.Repo.Migrations.AddUserFieldsToDeviceSessions do
  use Ecto.Migration

  def up do
    create table(:device_sessions, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :device_id, :string
      add :user_id, :string
      add :wallet_user_id, :string
      add :third_party_id, :string
      add :session_token, :string
      add :access_token, :string
      add :access_code, :string
      add :salt, :string
      add :secret, :string
      add :refresh_token, :string
      add :token_hash, :string
      add :expires_at, :utc_datetime
      add :last_activity_at, :utc_datetime
      add :ip_address, :string
      add :user_agent, :string
      add :is_active, :boolean, default: true
      add :revoked_at, :utc_datetime
      add :revocation_reason, :string

      timestamps()
    end

    create unique_index(:device_sessions, [:session_token])
    create unique_index(:device_sessions, [:access_token])
  end

  def down do
    drop table(:device_sessions)
  end
end
