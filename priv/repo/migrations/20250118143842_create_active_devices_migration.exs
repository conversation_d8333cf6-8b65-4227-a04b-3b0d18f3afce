defmodule ServiceManager.Repo.Migrations.CreateActiveDevicesMigration do
  use Ecto.Migration

  def change do
    create table(:active_devices) do
      add :device_id, :string, null: false
      add :last_seen_at, :utc_datetime, null: false
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: true
      add :wallet_user_id, references(:walletusers, on_delete: :delete_all), null: true

      timestamps()
    end

    create unique_index(:active_devices, [:device_id, :user_id], where: "user_id IS NOT NULL")

    create unique_index(:active_devices, [:device_id, :wallet_user_id],
             where: "wallet_user_id IS NOT NULL"
           )

    create index(:active_devices, [:user_id])
    create index(:active_devices, [:last_seen_at])
  end
end
