defmodule ServiceManager.Repo.Migrations.CreateProcessChainLinks do
  use Ecto.Migration

  def change do
    # Create the table if it doesn't exist
    create_if_not_exists table(:process_chain_links) do
      add :source_process_id, references(:dynamic_processes, on_delete: :delete_all), null: false
      add :target_process_id, references(:dynamic_processes, on_delete: :delete_all), null: false
      add :position, :integer, null: false, default: 0
      add :created_by_id, references(:tbl_system_users, on_delete: :nilify_all)

      timestamps()
    end

    # Create indexes if they don't exist
    create_if_not_exists index(:process_chain_links, [:source_process_id])
    create_if_not_exists index(:process_chain_links, [:target_process_id])
    create_if_not_exists unique_index(:process_chain_links, [:source_process_id, :target_process_id])
    create_if_not_exists index(:process_chain_links, [:position])
  end
end
