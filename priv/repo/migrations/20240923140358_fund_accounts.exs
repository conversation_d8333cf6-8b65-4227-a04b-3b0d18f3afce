defmodule ServiceManager.Repo.Migrations.FundAccount do
  use Ecto.Migration

  def change do
    create table(:cards) do
      add :token, :string
      add :last4, :string
      add :exp_month, :integer
      add :exp_year, :integer
      add :brand, :string

      timestamps()
    end

    create unique_index(:cards, [:token])

    create table(:fund_accounts) do
      add :account_number, :string, null: false
      add :balance, :decimal, null: false, precision: 10, scale: 2
      add :currency, :string, null: false
      add :account_type, :string, null: false
      add :status, :string, null: false
      add :last_transaction_date, :naive_datetime
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false
      add :cards_id, references(:cards, on_delete: :delete_all)

      timestamps()
    end

    create unique_index(:fund_accounts, [:account_number])
    create index(:fund_accounts, [:user_id])

    alter table(:cards) do
      add :account_id, references(:fund_accounts, on_delete: :nothing)
    end

    create table(:transactions) do
      add :type, :string, null: false
      add :amount, :decimal, null: false, precision: 10, scale: 2
      add :description, :string
      add :status, :string, null: true
      add :from_account_id, references(:fund_accounts, on_delete: :restrict)
      add :to_account_id, references(:fund_accounts, on_delete: :restrict)
      add :credit_amount, :decimal, null: true, precision: 10, scale: 2, default: 0
      add :debit_amount, :decimal, null: true, precision: 10, scale: 2, default: 0
      add :reference, :string, null: true
      add :value_date, :date, null: true
      add :opening_balance, :decimal, null: true, precision: 10, scale: 2
      add :closing_balance, :decimal, null: true, precision: 10, scale: 2

      timestamps()
    end

    create index(:transactions, [:from_account_id])
    create index(:transactions, [:to_account_id])
    create index(:transactions, [:type])
    create index(:transactions, [:status])
  end
end
