defmodule ServiceManager.Repo.Migrations.CreateDynamicRouteWizards do
  use Ecto.Migration

  def change do
    create table(:dynamic_route_wizards) do
      add :route_id, references(:dynamic_routes, on_delete: :delete_all), null: false
      add :wizard_id, references(:form_wizards, on_delete: :delete_all), null: false

      timestamps()
    end

    create index(:dynamic_route_wizards, [:route_id])
    create index(:dynamic_route_wizards, [:wizard_id])
    create unique_index(:dynamic_route_wizards, [:route_id, :wizard_id])
  end
end
