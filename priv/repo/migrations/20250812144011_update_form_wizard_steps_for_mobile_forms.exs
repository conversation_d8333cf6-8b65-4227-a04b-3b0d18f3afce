defmodule ServiceManager.Repo.Migrations.UpdateFormWizardStepsForMobileForms do
  use Ecto.Migration

  def change do
    alter table(:form_wizard_steps) do
      add :mobile_form_id, references(:mobile_forms_v2, on_delete: :delete_all, type: :binary_id), null: true
      add :step_type, :string, default: "form", null: false
      add :dynamic_process_id, :integer, null: true
    end

    create index(:form_wizard_steps, [:mobile_form_id])
    create index(:form_wizard_steps, [:step_type])
    create index(:form_wizard_steps, [:dynamic_process_id])

    # Make form_id nullable since we can now use mobile_form_id instead
    alter table(:form_wizard_steps) do
      modify :form_id, :integer, null: true
    end

    # Add constraint to ensure either form_id or mobile_form_id is set
    create constraint(:form_wizard_steps, :must_have_form_reference, 
      check: "form_id IS NOT NULL OR mobile_form_id IS NOT NULL")
  end
end
