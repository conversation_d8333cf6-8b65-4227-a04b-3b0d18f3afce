defmodule ServiceManager.Repo.Migrations.AddWalletFieldsToTransactions do
  use Ecto.Migration

  def change do
    alter table(:transactions) do
      add :from_wallet_id, references(:walletusers, on_delete: :nothing)
      add :to_wallet_id, references(:walletusers, on_delete: :nothing)
    end

    alter table(:wallet_transactions) do
      add :from_wallet_id, references(:walletusers, on_delete: :nothing)
      add :to_wallet_id, references(:walletusers, on_delete: :nothing)
    end

    create index(:transactions, [:from_wallet_id])
    create index(:transactions, [:to_wallet_id])
    create index(:wallet_transactions, [:from_wallet_id])
    create index(:wallet_transactions, [:to_wallet_id])
  end
end
