defmodule ServiceManager.Repo.Migrations.AddExternalTransferTypeToTransactions do
  use Ecto.Migration

  def up do
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_type_check"

    execute "ALTER TABLE transactions ADD CONSTRAINT transactions_type_check CHECK (type IN ('credit', 'debit', 'transfer', 'direct-transfer', 'external-transfer'))"
  end

  def down do
    execute "ALTER TABLE transactions DROP CONSTRAINT transactions_type_check"

    execute "ALTER TABLE transactions ADD CONSTRAINT transactions_type_check CHECK (type IN ('credit', 'debit', 'transfer', 'direct-transfer'))"
  end
end
