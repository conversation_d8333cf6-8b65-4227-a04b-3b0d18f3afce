defmodule ServiceManager.Repo.Migrations.AddNotificationLimitsToWalletUsers do
  use Ecto.Migration

  def change do
    alter table(:walletusers) do
      add :daily_notification_limit, :integer, default: 100
      add :weekly_notification_limit, :integer, default: 500
      add :monthly_notification_limit, :integer, default: 2000
      add :notification_count, :integer, default: 0
      add :last_notification_reset, :utc_datetime
    end
  end
end
