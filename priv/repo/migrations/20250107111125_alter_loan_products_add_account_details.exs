defmodule ServiceManager.Repo.Migrations.AlterLoanProductsAddAccountDetails do
  use Ecto.Migration

  def up do
    alter table(:loan_products) do
      add :loan_account, :string
      add :collection_account, :string
    end

    create index(:loan_products, [:loan_account])
    create index(:loan_products, [:collection_account])
  end

  def down do
    execute "DROP INDEX IF EXISTS loan_products_loan_account_index"
    execute "DROP INDEX IF EXISTS loan_products_collection_account_index"

    alter table(:loan_products) do
      remove :loan_account
      remove :collection_account
    end
  end
end
