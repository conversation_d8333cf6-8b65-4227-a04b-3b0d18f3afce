defmodule ServiceManager.Repo.Migrations.AddMessageableTypeCheckConstraint do
  use Ecto.Migration

  def change do
    # Drop existing constraint
    execute "ALTER TABLE messages DROP CONSTRAINT valid_messageable_type",
            "ALTER TABLE messages ADD CONSTRAINT valid_messageable_type CHECK (messageable_type IN ('Issue', 'QAndA'))"

    # Add new constraint with ChangelogItem
    create constraint("messages", :valid_messageable_type,
             check: "messageable_type IN ('ChangelogItem', 'Issue', 'QAndA')"
           )
  end
end
