defmodule ServiceManager.Repo.Migrations.CreateLogEntries do
  use Ecto.Migration

  def change do
    create table(:log_entries) do
      add :chain_id, :string, null: false
      add :function_name, :string, null: false
      add :module_name, :string, null: false
      add :process_id, :string, null: false
      add :duration_ms, :integer
      add :params, :text
      add :result, :text
      add :process_state, :text
      add :request_info, :text
      add :response_info, :text
      add :timestamp, :utc_datetime, null: false

      timestamps()
    end

    create unique_index(:log_entries, [:chain_id])
  end
end
