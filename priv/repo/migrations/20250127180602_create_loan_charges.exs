defmodule ServiceManager.Repo.Migrations.CreateLoanCharges do
  use Ecto.Migration

  def change do
    create table(:loan_charges) do
      add :amount, :decimal, precision: 10, scale: 2, null: false
      add :description, :string
      add :status, :string, null: false
      add :status_description, :string
      add :reference, :string
      add :value_date, :date
      add :transaction_date, :utc_datetime
      add :payment_method, :string
      add :external_reference, :string
      add :debit_account, :string, null: false
      add :credit_account, :string, null: false
      add :loan_id, references(:loans, on_delete: :restrict), null: false
      add :product_id, references(:loan_products, on_delete: :restrict), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:loan_charges, [:debit_account])
    create index(:loan_charges, [:credit_account])
    create index(:loan_charges, [:loan_id])
    create index(:loan_charges, [:product_id])
  end
end
