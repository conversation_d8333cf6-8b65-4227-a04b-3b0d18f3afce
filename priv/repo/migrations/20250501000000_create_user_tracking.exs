defmodule ServiceManager.Repo.Migrations.CreateUserTracking do
  use Ecto.Migration

  def change do
    create table(:user_tracking) do
      # User identification
      add :user_id, references(:accounts_users, on_delete: :nilify_all)
      add :wallet_user_id, references(:walletusers, on_delete: :nilify_all)
      add :session_id, :string

      # Page/location information
      add :page_path, :string, null: false
      add :page_name, :string
      add :section, :string
      add :action, :string
      add :previous_page_path, :string
      add :next_page_path, :string

      # Device and connection information
      add :device_id, :string
      add :device_name, :string
      add :ip_address, :string
      add :user_agent, :text

      # Timing information
      add :entry_timestamp, :utc_datetime, null: false
      add :exit_timestamp, :utc_datetime
      add :duration_seconds, :integer

      # Status information
      add :status, :string, default: "active"

      # Additional metadata
      add :metadata, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    # Create indexes for efficient querying
    create index(:user_tracking, [:user_id])
    create index(:user_tracking, [:wallet_user_id])
    create index(:user_tracking, [:session_id])
    create index(:user_tracking, [:page_path])
    create index(:user_tracking, [:entry_timestamp])
    create index(:user_tracking, [:device_id])
    create index(:user_tracking, [:status])
  end
end
