defmodule ServiceManager.Repo.Migrations.CreateCallbacks do
  use Ecto.Migration

  def change do
    create table(:callbacks) do
      add :callback_type, :string, null: false
      add :callback_url, :string, null: false
      add :request_headers, :map, null: false
      add :request_body, :map, null: false
      add :response_status, :integer
      add :response_body, :map
      add :response_headers, :map
      add :duration_ms, :integer
      add :status, :string, null: false, default: "pending"
      add :error_message, :text
      add :retry_count, :integer, default: 0
      add :next_retry_at, :utc_datetime
      add :metadata, :map, default: %{}
      add :callback_registry_id, references(:callback_registry, on_delete: :nilify_all)

      timestamps()
    end

    create index(:callbacks, [:callback_type])
    create index(:callbacks, [:status])
    create index(:callbacks, [:callback_registry_id])
    create index(:callbacks, [:next_retry_at])
  end
end
