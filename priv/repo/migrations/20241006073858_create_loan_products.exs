defmodule ServiceManager.Repo.Migrations.CreateLoanProducts do
  use Ecto.Migration

  def change do
    create table(:loan_products) do
      add :name, :string, null: false
      add :description, :string
      add :min_amount, :decimal, precision: 18, scale: 2, null: false
      add :max_amount, :decimal, precision: 18, scale: 2, null: false
      add :interest_rate, :decimal, precision: 5, scale: 2, null: false
      add :default_duration, :integer, null: false
      add :is_active, :boolean, default: true, null: false
      add :maker_id, references(:accounts_users, on_delete: :restrict)
      add :checker_id, references(:accounts_users, on_delete: :restrict)

      timestamps()
    end

    create index(:loan_products, [:maker_id])
    create index(:loan_products, [:checker_id])
    create index(:loan_products, [:is_active])
  end
end
