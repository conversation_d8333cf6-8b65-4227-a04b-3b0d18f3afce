defmodule ServiceManager.Repo.Migrations.AlterWalletUserTable do
  use Ecto.Migration

  # Add memorable_word to walletusers table
  def change do
    alter table(:walletusers) do
      add :first_name, :string
      add :last_name, :string
      add :id_number, :string
      add :id_image, :string
      add :mobile_number, :string
      add :currency, :string, defualt: "MWK"
      add :balance, :decimal, defualt: 0.00, precision: 10, scale: 2
      add :status, :string, defualt: "ACTIVE"
      add :first_time_login, :boolean, defualt: true
      add :frozen, :boolean, defualt: false
      add :locked, :boolean, defualt: false
      add :blocked, :boolean, defualt: false
    end

    create unique_index(:walletusers, [:mobile_number])
  end
end
