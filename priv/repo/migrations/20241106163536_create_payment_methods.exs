defmodule ServiceManager.Repo.Migrations.CreatePaymentMethods do
  use Ecto.Migration

  def change do
    create table(:payment_methods) do
      add :name, :string
      add :type, :string
      add :description, :text
      add :status, :string, default: "inactive", null: false
      add :fees_id, :integer
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end
  end
end
