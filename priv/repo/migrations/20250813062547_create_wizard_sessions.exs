defmodule ServiceManager.Repo.Migrations.CreateWizardSessions do
  use Ecto.Migration

  def change do
    create table(:wizard_sessions) do
      add :session_token, :string, null: false
      add :wizard_id, references(:form_wizards, on_delete: :delete_all), null: false
      add :current_step_number, :integer, null: false
      add :current_step_id, references(:form_wizard_steps, on_delete: :nilify_all)
      add :form_data, :map, default: %{}
      add :completed_steps, {:array, :integer}, default: []
      add :status, :string, default: "active"
      add :client_ip, :string
      add :user_agent, :string
      add :expires_at, :utc_datetime, null: false

      timestamps()
    end

    create unique_index(:wizard_sessions, [:session_token])
    create index(:wizard_sessions, [:wizard_id])
    create index(:wizard_sessions, [:status])
    create index(:wizard_sessions, [:expires_at])
  end
end
