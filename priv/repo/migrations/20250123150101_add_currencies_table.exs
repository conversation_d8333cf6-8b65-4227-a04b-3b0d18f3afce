defmodule ServiceManager.Repo.Migrations.AddCurrenciesTable do
  use Ecto.Migration

  def change do
    alter table(:currencies) do
      add_if_not_exists :code, :string, null: false
      add_if_not_exists :name, :string, null: false
      add_if_not_exists :symbol, :string
      add_if_not_exists :is_active, :boolean, default: true
      add_if_not_exists :created_by, :integer
      add_if_not_exists :updated_by, :integer
    end

    create_if_not_exists unique_index(:currencies, [:code])

    # Add default currencies
    default_currencies = [
      {"USD", "US Dollar", "$"},
      {"EUR", "Euro", "€"},
      {"GBP", "British Pound", "£"},
      {"JPY", "Japanese Yen", "¥"},
      {"CNY", "Chinese Yuan", "¥"},
      {"MWK", "Malawian Kwacha", "K"},
      {"ZMW", "Zambian Kwacha", "K"},
      {"ZAR", "South African Rand", "R"}
    ]

    flush()

    for {code, name, symbol} <- default_currencies do
      execute """
      INSERT INTO currencies (code, name, symbol, inserted_at, updated_at)
      SELECT '#{code}', '#{name}', '#{symbol}', NOW(), NOW()
      WHERE NOT EXISTS (
        SELECT 1 FROM currencies WHERE code = '#{code}'
      )
      """
    end
  end
end
