defmodule ServiceManager.Repo.Migrations.CreateBillerConfigs do
  use Ecto.Migration

  def change do
    create table(:biller_configs) do
      # Biller Identity
      add :biller_type, :string, null: false
      add :biller_name, :string, null: false
      add :display_name, :string, null: false
      add :description, :text
      add :is_active, :boolean, default: true, null: false

      # API Configuration
      add :base_url, :string, null: false
      add :endpoints, :map, null: false, default: %{}
      add :authentication, :map, null: false, default: %{}
      
      # Transaction Settings
      add :default_currency, :string, default: "MWK", null: false
      add :supported_currencies, {:array, :string}, default: ["MWK"], null: false
      add :timeout_ms, :integer, default: 30000, null: false
      add :retry_attempts, :integer, default: 3, null: false
      
      # Features and Capabilities
      add :features, :map, null: false, default: %{}
      add :validation_rules, :map, null: false, default: %{}

      timestamps()
    end

    # Create unique indexes
    create unique_index(:biller_configs, [:biller_type])
    create unique_index(:biller_configs, [:biller_name])
    
    # Create indexes for common queries
    create index(:biller_configs, [:is_active])
    create index(:biller_configs, [:default_currency])
    
    # Add check constraints
    execute """
    ALTER TABLE biller_configs ADD CONSTRAINT biller_type_valid 
    CHECK (biller_type IN (
      'register_general',
      'bwb_postpaid',
      'lwb_postpaid',
      'srwb_postpaid',
      'srwb_prepaid',
      'masm',
      'airtel_validation',
      'tnm_bundles'
    ))
    """, """
    ALTER TABLE biller_configs DROP CONSTRAINT biller_type_valid
    """

    execute """
    ALTER TABLE biller_configs ADD CONSTRAINT default_currency_valid 
    CHECK (default_currency IN ('MWK', 'USD', 'EUR', 'GBP', 'ZAR'))
    """, """
    ALTER TABLE biller_configs DROP CONSTRAINT default_currency_valid
    """

    execute """
    ALTER TABLE biller_configs ADD CONSTRAINT timeout_ms_valid 
    CHECK (timeout_ms > 0 AND timeout_ms < 300000)
    """, """
    ALTER TABLE biller_configs DROP CONSTRAINT timeout_ms_valid
    """

    execute """
    ALTER TABLE biller_configs ADD CONSTRAINT retry_attempts_valid 
    CHECK (retry_attempts >= 0 AND retry_attempts < 10)
    """, """
    ALTER TABLE biller_configs DROP CONSTRAINT retry_attempts_valid
    """
  end
end
