defmodule ServiceManager.Repo.Migrations.CreateTransactionsStatusHistory do
  use Ecto.Migration

  def change do
    create_if_not_exists table(:transaction_status_history) do
      add :transaction_id, :integer, null: false
      add :from_status, :string
      add :to_status, :string, null: false
      add :changed_by, :integer
      add :notes, :string
      add :metadata, :map, default: %{}

      timestamps()
    end

    create_if_not_exists index(:transaction_status_history, [:transaction_id])
    create_if_not_exists index(:transaction_status_history, [:to_status])
    create_if_not_exists index(:transaction_status_history, [:inserted_at])
  end
end
