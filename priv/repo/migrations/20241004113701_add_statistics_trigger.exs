defmodule ServiceManager.Repo.Migrations.AddStatisticsTrigger do
  use Ecto.Migration

  def up do
    # Create statistics table
    create table(:statistics) do
      add :total_users, :integer, default: 0
      add :total_accounts, :integer, default: 0
      add :total_cards, :integer, default: 0
      add :total_transactions, :integer, default: 0
      add :total_bank_accounts, :integer, default: 0
      add :total_beneficiaries, :integer, default: 0
      add :total_fund_accounts, :integer, default: 0
      add :total_transfers, :integer, default: 0
      add :total_tokens, :integer, default: 0
      add :updated_at, :utc_datetime
    end

    # Create the trigger function
    execute """
    CREATE OR REPLACE FUNCTION update_statistics()
    RETURNS TRIGGER AS $$
    DECLARE
      users_count INTEGER := 0;
      accounts_count INTEGER := 0;
      cards_count INTEGER := 0;
      transactions_count INTEGER := 0;
      bank_accounts_count INTEGER := 0;
      beneficiaries_count INTEGER := 0;
      fund_accounts_count INTEGER := 0;
      transfers_count INTEGER := 0;
      tokens_count INTEGER := 0;
    BEGIN
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'accounts_users') THEN
        SELECT COUNT(*) INTO users_count FROM accounts_users;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fund_accounts') THEN
        SELECT COUNT(*) INTO accounts_count FROM fund_accounts;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'cards') THEN
        SELECT COUNT(*) INTO cards_count FROM cards;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        SELECT COUNT(*) INTO transactions_count FROM transactions;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'bank_accounts') THEN
        SELECT COUNT(*) INTO bank_accounts_count FROM bank_accounts;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'beneficiaries') THEN
        SELECT COUNT(*) INTO beneficiaries_count FROM beneficiaries;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'fund_accounts') THEN
        SELECT COUNT(*) INTO fund_accounts_count FROM fund_accounts;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transfers') THEN
        SELECT COUNT(*) INTO transfers_count FROM transfers;
      END IF;
      IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tokens') THEN
        SELECT COUNT(*) INTO tokens_count FROM tokens;
      END IF;

      INSERT INTO statistics (
        total_users,
        total_accounts,
        total_cards,
        total_transactions,
        total_bank_accounts,
        total_beneficiaries,
        total_fund_accounts,
        total_transfers,
        total_tokens,
        updated_at
      ) VALUES (
        users_count,
        accounts_count,
        cards_count,
        transactions_count,
        bank_accounts_count,
        beneficiaries_count,
        fund_accounts_count,
        transfers_count,
        tokens_count,
        NOW()
      )
      ON CONFLICT (id) DO UPDATE
      SET
        total_users = EXCLUDED.total_users,
        total_accounts = EXCLUDED.total_accounts,
        total_cards = EXCLUDED.total_cards,
        total_transactions = EXCLUDED.total_transactions,
        total_bank_accounts = EXCLUDED.total_bank_accounts,
        total_beneficiaries = EXCLUDED.total_beneficiaries,
        total_fund_accounts = EXCLUDED.total_fund_accounts,
        total_transfers = EXCLUDED.total_transfers,
        total_tokens = EXCLUDED.total_tokens,
        updated_at = EXCLUDED.updated_at;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create triggers for each table
    tables =
      ~w(users accounts cards transactions bank_accounts beneficiaries fund_accounts transfers tokens)

    for table <- tables do
      execute """
      DO $$
      BEGIN
        -- Drop the trigger if it exists
        DROP TRIGGER IF EXISTS update_statistics_#{table}_trigger ON #{table};

        -- Create the trigger if the table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = '#{table}') THEN
          EXECUTE 'CREATE TRIGGER update_statistics_#{table}_trigger
                   AFTER INSERT OR UPDATE OR DELETE ON #{table}
                   FOR EACH STATEMENT
                   EXECUTE FUNCTION update_statistics()';
        END IF;
      END $$;
      """
    end
  end

  def down do
    # Drop triggers
    tables =
      ~w(users accounts cards transactions bank_accounts beneficiaries fund_accounts transfers tokens)

    for table <- tables do
      execute """
      DROP TRIGGER IF EXISTS update_statistics_#{table}_trigger ON #{table};
      """
    end

    # Drop function
    execute "DROP FUNCTION IF EXISTS update_statistics();"

    # Drop statistics table
    drop table(:statistics)
  end
end
