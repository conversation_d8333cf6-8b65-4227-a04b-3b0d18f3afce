defmodule ServiceManager.Repo.Migrations.CreateLoanApplications do
  use Ecto.Migration

  def change do
    create table(:loan_applications) do
      add :amount, :decimal, precision: 18, scale: 2, null: false
      add :duration, :integer, null: false
      add :status, :string, null: false
      add :user_id, references(:accounts_users, on_delete: :restrict), null: false
      add :product_id, references(:loan_products, on_delete: :restrict), null: false

      timestamps()
    end

    create index(:loan_applications, [:user_id])
    create index(:loan_applications, [:product_id])
  end
end
