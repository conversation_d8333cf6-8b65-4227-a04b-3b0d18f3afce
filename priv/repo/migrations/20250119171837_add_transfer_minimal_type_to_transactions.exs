defmodule ServiceManager.Repo.Migrations.AddTransferMinimalTypeToTransactions do
  use Ecto.Migration

  def change do
    # Drop existing check constraint if it exists
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_type_check"

    # Add new check constraint with transfer-minimal type
    create constraint(:transactions, :transactions_type_check,
             check: "type IN ('credit', 'debit', 'transfer', 'direct-transfer')"
           )
  end
end
