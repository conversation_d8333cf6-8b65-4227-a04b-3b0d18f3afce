defmodule ServiceManager.Repo.Migrations.AddFieldsToFees do
  use Ecto.Migration

  def change do
    alter table(:fees_and_charges) do
      add :category, :string
      add :calculation_method, :string
      add :percentage_rate, :decimal
      add :account_type, :string
      add :notification_enabled, :boolean, default: false
      add :notification_days_before, :integer
      add :comparison_data, :map
      add :min_amount, :decimal
      add :max_amount, :decimal
      add :frequency, :string
      add :application_time, :string
      add :transaction_type, :string
      add :conditions, :map
      add :exchange_rate_source, :string
    end

    create index(:fees_and_charges, [:category])
    create index(:fees_and_charges, [:account_type])
    create index(:fees_and_charges, [:transaction_type])
  end
end
