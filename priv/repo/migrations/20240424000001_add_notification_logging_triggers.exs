defmodule ServiceManager.Repo.Migrations.AddNotificationLoggingTriggers do
  use Ecto.Migration

  def up do
    # Create email_notifications table
    create table(:email_notifications) do
      add :user_id, :integer, null: false
      add :status, :string, null: false
      add :sent_at, :utc_datetime
      timestamps()
    end

    # Create sms_notifications table
    create table(:sms_notifications) do
      add :user_id, :integer, null: false
      add :status, :string, null: false
      add :sent_at, :utc_datetime
      timestamps()
    end

    # Create push_notifications table
    create table(:push_notifications) do
      add :user_id, :integer, null: false
      add :status, :string, null: false
      add :sent_at, :utc_datetime
      timestamps()
    end

    # Create notification_logs table
    create table(:notification_logs) do
      add :notification_type, :string, null: false
      add :notification_id, :integer, null: false
      add :user_id, :integer, null: false
      add :status, :string, null: false
      add :logged_at, :utc_datetime, null: false

      timestamps()
    end

    # Create function for logging notifications
    execute """
    CREATE OR REPLACE FUNCTION log_notification()
    RETURNS TRIGGER AS $$
    BEGIN
      INSERT INTO notification_logs (notification_type, notification_id, user_id, status, logged_at, inserted_at, updated_at)
      VALUES (
        TG_TABLE_NAME,
        NEW.id,
        NEW.user_id,
        NEW.status,
        NEW.sent_at,
        NOW(),
        NOW()
      );
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create triggers for each notification table
    execute "CREATE TRIGGER log_email_notification AFTER INSERT OR UPDATE ON email_notifications FOR EACH ROW EXECUTE FUNCTION log_notification();"

    execute "CREATE TRIGGER log_sms_notification AFTER INSERT OR UPDATE ON sms_notifications FOR EACH ROW EXECUTE FUNCTION log_notification();"

    execute "CREATE TRIGGER log_push_notification AFTER INSERT OR UPDATE ON push_notifications FOR EACH ROW EXECUTE FUNCTION log_notification();"
  end

  def down do
    # Drop triggers
    execute "DROP TRIGGER IF EXISTS log_email_notification ON email_notifications;"
    execute "DROP TRIGGER IF EXISTS log_sms_notification ON sms_notifications;"
    execute "DROP TRIGGER IF EXISTS log_push_notification ON push_notifications;"

    # Drop function
    execute "DROP FUNCTION IF EXISTS log_notification();"

    # Drop notification tables
    drop table(:email_notifications)
    drop table(:sms_notifications)
    drop table(:push_notifications)

    # Drop notification_logs table
    drop table(:notification_logs)
  end
end
