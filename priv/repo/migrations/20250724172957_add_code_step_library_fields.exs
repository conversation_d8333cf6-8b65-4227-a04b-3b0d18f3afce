defmodule ServiceManager.Repo.Migrations.AddCodeStepLibraryFields do
  use Ecto.Migration

  def change do
    alter table(:code_steps) do
      # Library-specific fields
      add :is_template, :boolean, default: false, null: false
      add :step_group, :string
      add :step_class, :string
      add :usage_count, :integer, default: 0, null: false
      add :template_params, :map
      add :tags, {:array, :string}, default: []
      add :description, :text
      add :popularity_score, :float, default: 0.0
      add :is_featured, :boolean, default: false
      add :author_id, references(:tbl_system_users, on_delete: :nilify_all)
    end

    # Add indexes for better query performance
    create index(:code_steps, [:is_template])
    create index(:code_steps, [:step_group])
    create index(:code_steps, [:step_class])
    create index(:code_steps, [:step_category])
    create index(:code_steps, [:usage_count])
    create index(:code_steps, [:popularity_score])
    create index(:code_steps, [:is_featured])
    create index(:code_steps, [:tags], using: :gin)
  end
end
