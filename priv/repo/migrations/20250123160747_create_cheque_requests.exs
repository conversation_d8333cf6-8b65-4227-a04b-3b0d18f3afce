defmodule ServiceManager.Repo.Migrations.CreateChequeRequests do
  use Ecto.Migration

  def change do
    create table(:cheque_requests) do
      # "cancel" or "stop"
      add :action, :string, null: false
      add :cheque_number, :string, null: false
      add :account_number, :string, null: false
      # pending, approved, rejected
      add :status, :string, default: "pending"
      add :processed_at, :utc_datetime
      add :processed_by, :string
      add :remarks, :text
      add :user_id, references(:accounts_users, on_delete: :restrict), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:cheque_requests, [:user_id])
    create index(:cheque_requests, [:cheque_number])
    create index(:cheque_requests, [:account_number])
    create index(:cheque_requests, [:status])
  end
end
