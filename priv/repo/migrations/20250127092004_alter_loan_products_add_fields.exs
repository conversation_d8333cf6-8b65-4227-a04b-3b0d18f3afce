defmodule ServiceManager.Repo.Migrations.AlterLoanProductsAddFields do
  use Ecto.Migration

  def up do
    alter table(:loan_products) do
      add :charge_type, :string
      add :charge_rate, :decimal, precision: 18, scale: 2
      add :charge_account, :string
    end

    create index(:loan_products, [:charge_account])
  end

  def down do
    drop index(:loan_products, [:charge_account])

    alter table(:loan_products) do
      remove :charge_type
      remove :charge_rate
      remove :charge_account
    end
  end
end
