defmodule ServiceManager.Repo.Migrations.CreateCallbackRegistry do
  use Ecto.Migration

  def change do
    create table(:callback_registry) do
      add :callback_url, :string, null: false
      add :api_key, :string
      add :secret_key, :string
      add :status, :string, default: "active"
      add :callback_type, :string, null: false
      add :retry_count, :integer, default: 0
      add :last_called_at, :utc_datetime
      add :metadata, :map, default: %{}
      add :headers, :map, default: %{}

      timestamps()
    end

    create index(:callback_registry, [:callback_type])
    create index(:callback_registry, [:status])
  end
end
