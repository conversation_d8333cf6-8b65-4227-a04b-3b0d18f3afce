defmodule ServiceManager.Repo.Migrations.AddKycAndWalletTierFields do
  use Ecto.Migration

  def change do
    create_if_not_exists table(:wallet_tiers) do
      add :name, :string, null: false
      add :description, :string
      add :position, :integer, null: false
      add :is_default, :boolean, default: false

      # Balance Limits
      add :minimum_balance, :decimal, null: false, default: 0
      add :maximum_balance, :decimal, null: false

      # Transaction Limits
      add :min_transaction_amount, :decimal, null: false
      add :max_transaction_amount, :decimal, null: false
      add :daily_transaction_limit, :decimal, null: false
      add :monthly_transaction_limit, :decimal, null: false

      # Transaction Count Limits
      add :daily_transaction_count, :integer, null: false
      add :monthly_transaction_count, :integer, null: false

      # Dynamic KYC Requirements
      add :required_kyc_fields, {:array, :string}, null: false
      add :kyc_rules, :map, null: false, default: fragment("'{}'::jsonb")

      timestamps()
    end

    alter table(:walletusers) do
      # KYC fields
      add_if_not_exists :date_of_birth, :date
      add_if_not_exists :address, :string
      add_if_not_exists :city, :string
      add_if_not_exists :occupation, :string
      add_if_not_exists :employer_name, :string
      add_if_not_exists :source_of_funds, :string
      add_if_not_exists :kyc_complete, :boolean, default: false
      add_if_not_exists :kyc_verified_at, :utc_datetime
      add_if_not_exists :wallet_tier_id, references(:wallet_tiers)

      # Remove old fields if they exist
      remove_if_exists :transaction_limit, :decimal
      remove_if_exists :daily_limit, :decimal
      remove_if_exists :monthly_limit, :decimal
    end

    # Add indexes
    create_if_not_exists index(:wallet_tiers, [:is_default])
    # Ensure unique positions
    create_if_not_exists unique_index(:wallet_tiers, [:position])
    create_if_not_exists index(:walletusers, [:wallet_tier_id])
    create_if_not_exists index(:walletusers, [:kyc_complete])
  end
end
