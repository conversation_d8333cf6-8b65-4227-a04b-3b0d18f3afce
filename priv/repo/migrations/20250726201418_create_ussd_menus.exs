defmodule ServiceManager.Repo.Migrations.CreateUssdMenus do
  use Ecto.Migration

  def change do
    create table(:ussd_menus, primary_key: false) do
      add :menu_id, :uuid, primary_key: true
      add :title, :string, null: false, size: 200
      add :menu_order, :integer, default: 0, null: false
      add :version, :string, null: false, size: 20
      add :active, :boolean, default: true, null: false

      timestamps()
    end

    # Create indexes for performance
    create index(:ussd_menus, [:active])
    create index(:ussd_menus, [:version])
    create index(:ussd_menus, [:menu_order])
    
    # Unique constraint for title and version combination
    create unique_index(:ussd_menus, [:title, :version], name: :ussd_menus_unique_title_version_constraint)
  end
end
