defmodule ServiceManager.Repo.Migrations.AlterProcessChainLinksAddRootSupport do
  use Ecto.Migration

  def change do
    alter table(:process_chain_links) do
      # Add boolean flag to identify if a process is the root of its own chain
      add :is_root, :boolean, default: false, null: false
      
      # Add UUID to group related process links in a chain
      add :chain_id, :uuid, null: true
      
      # Add more precise ordering field for chain processes
      add :order_position, :integer, default: 0, null: false
    end

    # Create indexes for performance
    create_if_not_exists index(:process_chain_links, [:chain_id, :order_position])
    create_if_not_exists index(:process_chain_links, [:is_root, :chain_id])
    create_if_not_exists index(:process_chain_links, [:chain_id])
  end
end
