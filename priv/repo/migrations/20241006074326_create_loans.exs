defmodule ServiceManager.Repo.Migrations.CreateLoans do
  use Ecto.Migration

  def change do
    create table(:loans) do
      add :amount, :decimal, precision: 18, scale: 2, null: false
      add :interest_rate, :decimal, precision: 18, scale: 2, null: false
      add :duration, :integer, null: false
      add :total_repayment, :decimal, precision: 18, scale: 2, null: false
      add :repaid_amount, :decimal, precision: 18, scale: 2, null: false, default: 0
      add :remaining_balance, :decimal, precision: 18, scale: 2, null: false, default: 0
      add :status, :string, null: false
      add :repayment_status, :string, null: false
      add :account_type, :string, null: false
      add :account_number, :string, null: false
      add :user_id, references(:accounts_users, on_delete: :restrict), null: false
      add :bank_account_id, references(:bank_accounts, on_delete: :restrict), null: false
      add :product_id, references(:loan_products, on_delete: :restrict), null: false

      timestamps()
    end

    create index(:loans, [:user_id])
    create index(:loans, [:bank_account_id])
    create index(:loans, [:product_id])
    create index(:loans, [:status])
    create index(:loans, [:account_number])
  end
end
