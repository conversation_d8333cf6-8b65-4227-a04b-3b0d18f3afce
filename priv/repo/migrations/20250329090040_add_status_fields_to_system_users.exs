defmodule ServiceManager.Repo.Migrations.AddStatusFieldsTOSystemUsers do
  use Ecto.Migration

  def up do
    alter table(:tbl_system_users) do
      add :activation_status, :string, default: "pending"
      add :reason, :text
    end

    create index(:tbl_system_users, [:activation_status])
  end

  def down do
    drop index(:tbl_system_users, [:activation_status])

    alter table(:tbl_system_users) do
      remove :activation_status
      remove :reason
    end
  end
end
