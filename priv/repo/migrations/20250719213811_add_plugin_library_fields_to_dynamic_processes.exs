defmodule ServiceManager.Repo.Migrations.AddPluginLibraryFieldsToDynamicProcesses do
  use Ecto.Migration

  def change do
    alter table(:dynamic_processes) do
      add :category, :string, default: "General"
      add :group, :string, default: "User Plugins" 
      add :plugin_type, :string, default: "public"
      add :rating, :decimal, precision: 3, scale: 2, default: 0.0
      add :downloads, :integer, default: 0
      add :tags, {:array, :string}, default: []
      add :version, :string, default: "1.0.0"
      add :author, :string
      add :license, :string, default: "MIT"
      add :repository_url, :string
      add :documentation_url, :string
      add :featured, :boolean, default: false
      add :verified, :boolean, default: false
    end

    create index(:dynamic_processes, [:category])
    create index(:dynamic_processes, [:group])
    create index(:dynamic_processes, [:plugin_type])
    create index(:dynamic_processes, [:rating])
    create index(:dynamic_processes, [:featured])
    create index(:dynamic_processes, [:verified])
  end
end
