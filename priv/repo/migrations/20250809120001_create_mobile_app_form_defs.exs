defmodule ServiceManager.Repo.Migrations.CreateMobileAppFormDefs do
  use Ecto.Migration

  def change do
    create table(:mobile_app_form_defs, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :form, :string, null: false
      add :screen, :string
      add :page, :string
      add :version, :string, null: false
      add :submit_to, :string
      add :active, :boolean, default: true, null: false

      timestamps()
    end

    create unique_index(:mobile_app_form_defs, [:form, :screen, :page, :version],
             name: :mobile_app_form_defs_unique_form_scope)

    create index(:mobile_app_form_defs, [:form])
    create index(:mobile_app_form_defs, [:screen])
    create index(:mobile_app_form_defs, [:page])
    create index(:mobile_app_form_defs, [:version])
    create index(:mobile_app_form_defs, [:active])
  end
end

