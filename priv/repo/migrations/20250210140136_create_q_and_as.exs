defmodule ServiceManager.Repo.Migrations.CreateQAndAs do
  use Ecto.Migration

  def change do
    create table(:q_and_as) do
      add :title, :string, null: false
      add :question, :text, null: false
      add :status, :string, null: false
      add :votes_count, :integer, default: 0, null: false
      add :version_id, references(:versions, on_delete: :delete_all), null: false
      add :created_by, references(:tbl_system_users, on_delete: :restrict), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:q_and_as, [:version_id])
    create index(:q_and_as, [:created_by])
    create index(:q_and_as, [:status])
    create index(:q_and_as, [:votes_count])
  end
end
