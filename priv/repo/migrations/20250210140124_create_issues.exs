defmodule ServiceManager.Repo.Migrations.CreateIssues do
  use Ecto.Migration

  def change do
    create table(:issues) do
      add :title, :string, null: false
      add :description, :text, null: false
      add :status, :string, null: false
      add :priority, :string, null: false
      add :assignee_id, references(:tbl_system_users, on_delete: :nilify_all)
      add :version_id, references(:versions, on_delete: :delete_all), null: false
      add :created_by, references(:tbl_system_users, on_delete: :restrict), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:issues, [:assignee_id])
    create index(:issues, [:version_id])
    create index(:issues, [:created_by])
    create index(:issues, [:status])
    create index(:issues, [:priority])
  end
end
