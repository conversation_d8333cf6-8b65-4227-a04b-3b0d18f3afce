defmodule ServiceManager.Repo.Migrations.CreateAccountingEntries do
  use Ecto.Migration

  def change do
    create table(:accounting_entries) do
      add :entry_type, :string, null: false
      add :amount, :decimal, precision: 20, scale: 2, null: false
      add :description, :string
      add :transaction_date, :utc_datetime, null: false
      add :value_date, :date, null: false
      add :reference, :string, null: false
      add :external_reference, :string
      add :status, :string, default: "posted", null: false
      add :currency, :string, default: "MWK", null: false
      add :metadata, :map, default: %{}
      
      # Foreign keys
      add :ledger_id, references(:accounting_ledgers, on_delete: :restrict), null: false
      add :transaction_id, references(:transactions, on_delete: :restrict), null: true
      add :wallet_transaction_id, references(:wallet_transactions, on_delete: :restrict), null: true
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end

    create index(:accounting_entries, [:entry_type])
    create index(:accounting_entries, [:reference])
    create index(:accounting_entries, [:external_reference])
    create index(:accounting_entries, [:status])
    create index(:accounting_entries, [:ledger_id])
    create index(:accounting_entries, [:transaction_id])
    create index(:accounting_entries, [:wallet_transaction_id])
    create index(:accounting_entries, [:transaction_date])
    create index(:accounting_entries, [:value_date])
  end
end
