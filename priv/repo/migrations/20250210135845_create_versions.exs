defmodule ServiceManager.Repo.Migrations.CreateVersions do
  use Ecto.Migration

  def change do
    create table(:versions) do
      add :version_number, :string, null: false
      add :release_date, :utc_datetime, null: false
      add :title, :string, null: false
      add :description, :text, null: false
      add :version_type, :string, null: false
      add :status, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:versions, [:version_number])
    create index(:versions, [:status])
    create index(:versions, [:release_date])
  end
end
