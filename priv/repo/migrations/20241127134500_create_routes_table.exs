defmodule ServiceManager.Repo.Migrations.CreateRoutesTable do
  use Ecto.Migration

  def change do
    create table(:routes) do
      add :name, :string, null: false
      add :host, :string, null: false
      add :path, :string, null: false
      add :method, :string, null: false

      timestamps()
    end

    create unique_index(:routes, [:name])
    create index(:routes, [:host])
    create index(:routes, [:path])
  end
end
