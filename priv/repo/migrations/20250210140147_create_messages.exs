defmodule ServiceManager.Repo.Migrations.CreateMessages do
  use Ecto.Migration

  def change do
    create table(:messages) do
      add :content, :text, null: false
      add :messageable_type, :string, null: false
      # Using bigint for messageable_id to support both issues and q_and_as
      add :messageable_id, :bigint, null: false
      add :user_id, references(:tbl_system_users, on_delete: :restrict), null: false
      add :parent_id, references(:messages, on_delete: :delete_all)

      timestamps(type: :utc_datetime)
    end

    create index(:messages, [:messageable_type, :messageable_id])
    create index(:messages, [:user_id])
    create index(:messages, [:parent_id])

    # Add check constraint for messageable_type
    create constraint(:messages, :valid_messageable_type,
             check: "messageable_type IN ('Issue', 'QAndA')"
           )

    # Add trigger to ensure parent_id belongs to same thread
    execute """
            CREATE FUNCTION check_message_parent_thread()
            RETURNS TRIGGER AS $$
            BEGIN
              IF NEW.parent_id IS NOT NULL THEN
                IF NOT EXISTS (
                  SELECT 1 FROM messages
                  WHERE id = NEW.parent_id
                  AND messageable_type = NEW.messageable_type
                  AND messageable_id = NEW.messageable_id
                ) THEN
                  RAISE EXCEPTION 'parent message must belong to the same thread';
                END IF;
              END IF;
              RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """,
            "DROP FUNCTION IF EXISTS check_message_parent_thread()"

    execute """
            CREATE TRIGGER enforce_message_parent_thread
            BEFORE INSERT OR UPDATE ON messages
            FOR EACH ROW
            EXECUTE FUNCTION check_message_parent_thread();
            """,
            "DROP TRIGGER IF EXISTS enforce_message_parent_thread ON messages"
  end
end
