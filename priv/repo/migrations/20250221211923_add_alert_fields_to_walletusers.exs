defmodule ServiceManager.Repo.Migrations.AddAlertFieldsToWalletusers do
  use Ecto.Migration

  def change do
    alter table(:walletusers) do
      add :hidden, :boolean, default: false
      add :large_transaction_alert, :boolean, default: false
      add :large_transaction_threshold, :decimal, default: 0
      add :low_balance_alert, :boolean, default: false
      add :low_balance_threshold, :decimal, default: 0
      add :suspicous_activity_alert, :boolean, default: false
      add :suspicous_activity_seconds_between_transactions, :integer, default: 0
      add :bank_code, :string
      add :branch_code, :string
      add :bank_name, :string
      add :enable_alerts, :boolean, default: false
    end
  end
end
