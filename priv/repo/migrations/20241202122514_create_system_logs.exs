defmodule ServiceManager.Repo.Migrations.CreateSystemLogs do
  use Ecto.Migration

  def change do
    create table(:system_logs) do
      add :timestamp, :utc_datetime_usec, default: fragment("CURRENT_TIMESTAMP")
      add :level, :string, null: false
      add :category, :string, null: false
      add :message, :text, null: false
      add :metadata, :map
      add :source, :string
      add :trace_id, :string
      add :user_id, :integer
      add :ip_address, :inet

      timestamps()
    end

    # Add indexes
    create index(:system_logs, [:timestamp])
    create index(:system_logs, [:level])
    create index(:system_logs, [:category])
    create index(:system_logs, [:trace_id])
    create index(:system_logs, [:metadata], using: :gin)
  end
end
