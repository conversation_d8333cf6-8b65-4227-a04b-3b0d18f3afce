defmodule ServiceManager.Repo.Migrations.AddCallbackFieldsToTransactions do
  use Ecto.Migration

  def change do
    # alter table(:transactions) do
    #   add :callback_status, :string, default: "pending"
    #   add :cbs_transaction_reference, :string
    #   add :external_reference, :string
    # end

    # alter table(:wallet_transactions) do
    #   add :callback_status, :string, default: "pending"
    #   add :cbs_transaction_reference, :string
    #   add :external_reference, :string
    # end
  end
end
