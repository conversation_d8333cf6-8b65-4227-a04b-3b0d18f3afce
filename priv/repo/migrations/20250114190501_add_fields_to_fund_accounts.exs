defmodule ServiceManager.Repo.Migrations.AddFieldsToFundAccounts do
  use Ecto.Migration

  def change do
    alter table(:fund_accounts) do
      add :bank_name, :string, default: ""
      add :bank_code, :string, default: ""
      add :branch_code, :string, default: ""
      add :enable_alerts, :boolean, default: false
      add :frozen, :boolean, default: false
      add :hidden, :boolean, default: false
      add :large_transaction_alert, :boolean, default: false
      add :large_transaction_threshold, :decimal, default: 0
      add :low_balance_alert, :boolean, default: false
      add :low_balance_threshold, :decimal, default: 0
      add :suspicous_activity_alert, :boolean, default: false
      add :suspicous_activity_seconds_between_transactions, :integer, default: 0
    end
  end
end
