defmodule ServiceManager.Repo.Migrations.CreateWalletusersAuthTables do
  use Ecto.Migration

  def change do
    execute "CREATE EXTENSION IF NOT EXISTS citext", ""

    create table(:walletusers) do
      add :email, :citext, null: true
      add :hashed_password, :string, null: false
      add :confirmed_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create table(:walletusers_tokens) do
      add :wallet_user_id, references(:walletusers, on_delete: :delete_all), null: false
      add :token, :binary, null: false
      add :context, :string, null: false
      add :sent_to, :string

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create index(:walletusers_tokens, [:wallet_user_id])
    create unique_index(:walletusers_tokens, [:context, :token])
  end
end
