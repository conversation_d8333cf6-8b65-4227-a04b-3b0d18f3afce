defmodule ServiceManager.Repo.Migrations.WalletToken do
  use Ecto.Migration

  def change do
    create table(:wallet_tokens) do
      add :token, :string, null: false
      add :user_id, references(:walletusers, on_delete: :delete_all), null: false
      add :expires_at, :utc_datetime, null: false
      add :revoked_at, :utc_datetime

      timestamps()
    end

    create unique_index(:wallet_tokens, [:token])
    create index(:wallet_tokens, [:user_id])
  end
end
