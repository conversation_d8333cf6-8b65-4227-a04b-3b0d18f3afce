defmodule ServiceManager.Repo.Migrations.CreateIpWhitelists do
  use Ecto.Migration

  def change do
    create table(:ip_whitelists) do
      add :ip_address, :string, null: false
      add :description, :string
      add :status, :string, null: false, default: "active"
      add :last_accessed_at, :utc_datetime
      add :expiry_date, :utc_datetime
      add :access_count, :integer, default: 0, null: false
      add :risk_level, :string, null: false, default: "medium"
      add :environment, :string, null: false, default: "production"

      add :user_id, references(:accounts_users, on_delete: :restrict)
      add :wallet_user_id, references(:walletusers, on_delete: :restrict)
      add :third_party_api_key_id, references(:third_party_api_key, on_delete: :restrict)

      timestamps(type: :utc_datetime)
    end

    create unique_index(:ip_whitelists, [:ip_address])
    create index(:ip_whitelists, [:status])
    create index(:ip_whitelists, [:user_id])
    create index(:ip_whitelists, [:wallet_user_id])
    create index(:ip_whitelists, [:third_party_api_key_id])
    create index(:ip_whitelists, [:environment])
  end
end
