defmodule ServiceManager.Repo.Migrations.MigrateExistingProcessChains do
  use Ecto.Migration

  def up do
    # Get all existing process chain links that don't have chain_id set
    execute """
    UPDATE process_chain_links 
    SET chain_id = gen_random_uuid(), order_position = position
    WHERE chain_id IS NULL;
    """
    
    # Create root entries for processes that are sources but not targets
    # These are likely the root processes in existing chains
    execute """
    WITH root_processes AS (
      SELECT DISTINCT source_process_id 
      FROM process_chain_links pcl1
      WHERE NOT EXISTS (
        SELECT 1 FROM process_chain_links pcl2 
        WHERE pcl2.target_process_id = pcl1.source_process_id
      ) AND pcl1.chain_id IS NOT NULL
    )
    INSERT INTO process_chain_links (
      source_process_id, 
      target_process_id, 
      is_root, 
      chain_id, 
      order_position, 
      position, 
      inserted_at, 
      updated_at
    )
    SELECT 
      rp.source_process_id,
      rp.source_process_id,
      true,
      gen_random_uuid(),
      0,
      0,
      NOW(),
      NOW()
    FROM root_processes rp;
    """
    
    # Update existing chain links to have the same chain_id as their root
    execute """
    WITH RECURSIVE chain_walker AS (
      -- Start with root processes
      SELECT 
        source_process_id, 
        target_process_id, 
        chain_id, 
        is_root,
        0 as depth
      FROM process_chain_links 
      WHERE is_root = true
      
      UNION ALL
      
      -- Walk the chain
      SELECT 
        pcl.source_process_id,
        pcl.target_process_id,
        cw.chain_id,
        pcl.is_root,
        cw.depth + 1
      FROM process_chain_links pcl
      JOIN chain_walker cw ON pcl.source_process_id = cw.target_process_id
      WHERE pcl.is_root = false AND cw.depth < 10 -- Prevent infinite loops
    )
    UPDATE process_chain_links 
    SET chain_id = cw.chain_id
    FROM chain_walker cw
    WHERE process_chain_links.source_process_id = cw.source_process_id 
      AND process_chain_links.target_process_id = cw.target_process_id
      AND process_chain_links.is_root = false;
    """
  end

  def down do
    # Reset the new fields to their default values
    execute """
    UPDATE process_chain_links 
    SET is_root = false, chain_id = NULL, order_position = 0;
    """
    
    # Remove any root entries that were created
    execute """
    DELETE FROM process_chain_links 
    WHERE is_root = true AND source_process_id = target_process_id;
    """
  end
end
