defmodule ServiceManager.Repo.Migrations.CreateFinchRequests do
  use Ecto.Migration

  def change do
    create table(:finch_requests) do
      add :method, :string, null: false
      add :url, :string, null: false
      add :headers, :map
      add :body, :text
      add :options, :map
      add :status, :string, default: "pending"

      timestamps()
    end

    create index(:finch_requests, [:status])
  end
end
