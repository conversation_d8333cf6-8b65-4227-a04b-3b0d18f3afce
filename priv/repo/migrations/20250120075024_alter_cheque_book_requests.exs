defmodule ServiceManager.Repo.Migrations.AlterChequeBookRequests do
  use Ecto.Migration

  def change do
    alter table(:cheque_book_requests) do
      add_if_not_exists :uid, :uuid
      add_if_not_exists :request_status, :string, default: "pending"
      add_if_not_exists :number_of_leaves, :integer, default: 25
      add_if_not_exists :request_reason, :string
      add_if_not_exists :rejection_reason, :string
      add_if_not_exists :issued_at, :utc_datetime
      add_if_not_exists :fulfilled_at, :utc_datetime
      add_if_not_exists :request_reference, :string
      add_if_not_exists :bank_code, :string
      add_if_not_exists :user_id, references(:accounts_users, on_delete: :nothing)

      # timestamps(type: :utc_datetime)
    end

    create_if_not_exists index(:cheque_book_requests, [:user_id])
    create_if_not_exists unique_index(:cheque_book_requests, [:uid])
    create_if_not_exists unique_index(:cheque_book_requests, [:request_reference])
  end
end
