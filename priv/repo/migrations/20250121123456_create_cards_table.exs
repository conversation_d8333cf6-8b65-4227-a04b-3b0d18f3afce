defmodule ServiceManager.Repo.Migrations.CreateCardsTable do
  use Ecto.Migration

  def up do
    # execute "DROP TABLE IF EXISTS cards CASCADE"
    # create table(:cards, primary_key: false) do
    #   add :id, :binary_id, primary_key: true
    #   add :card_number, :string, null: false
    #   add :card_type, :string, null: false
    #   add :expiry_date, :date, null: false
    #   add :cvv, :string, null: false
    #   add :pin_hash, :string
    #   add :status, :string, default: "active"
    #   add :currency, :string, null: false
    #   add :daily_limit, :decimal, null: false
    #   add :monthly_limit, :decimal, null: false
    #   add :last_used_at, :utc_datetime
    #   add :activation_status, :string, default: "pending"
    #   add :pin_tries, :integer, default: 0
    #   add :is_virtual, :boolean, default: false
    #   add :metadata, :map, default: %{}

    #   # Relationships
    #   add :user_id, references(:accounts_users, type: :id, on_delete: :nilify_all)
    #   add :wallet_user_id, references(:walletusers, type: :id, on_delete: :nilify_all)
    #   add :beneficiary_id, references(:beneficiaries, type: :id, on_delete: :nilify_all)
    #   add :third_party_api_key_id, references(:third_party_api_key, type: :id, on_delete: :nilify_all)

    #   timestamps(type: :utc_datetime)
    # end

    # # Indexes
    # create unique_index(:cards, [:card_number])
    # create index(:cards, [:user_id])
    # create index(:cards, [:wallet_user_id])
    # create index(:cards, [:beneficiary_id])
    # create index(:cards, [:third_party_api_key_id])
    # create index(:cards, [:status])
    # create index(:cards, [:activation_status])
  end

  def down do
    # drop table(:cards)
  end
end
