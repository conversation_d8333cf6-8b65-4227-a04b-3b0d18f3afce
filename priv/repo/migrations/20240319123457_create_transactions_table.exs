defmodule ServiceManager.Repo.Migrations.CreateTransactionsTable do
  use Ecto.Migration

  def change do
    # create table(:transactions) do
    #   add :type, :string
    #   add :amount, :decimal, null: false
    #   add :credit_amount, :decimal
    #   add :debit_amount, :decimal
    #   add :description, :string
    #   add :status, :string
    #   add :reference, :string
    #   add :value_date, :date
    #   add :opening_balance, :decimal
    #   add :closing_balance, :decimal
    #   add :transaction_details, :map, default: %{}
    #   add :callback_status, :string, default: "pending"
    #   add :cbs_transaction_reference, :string
    #   add :external_reference, :string
    #   add :sender_account, :string
    #   add :receiver_account, :string

    #   # Relationships
    #   add :from_account_id, references(:fund_accounts)
    #   add :to_account_id, references(:fund_accounts)
    #   add :owner_id, references(:accounts_users)
    #   add :from_wallet_id, references(:walletusers)
    #   add :to_wallet_id, references(:walletusers)

    #   timestamps()
    # end

    # # Indexes
    # create index(:transactions, [:type])
    # create index(:transactions, [:status])
    # create index(:transactions, [:reference])
    # create index(:transactions, [:callback_status])
    # create index(:transactions, [:from_account_id])
    # create index(:transactions, [:to_account_id])
    # create index(:transactions, [:owner_id])
    # create index(:transactions, [:from_wallet_id])
    # create index(:transactions, [:to_wallet_id])
  end
end
