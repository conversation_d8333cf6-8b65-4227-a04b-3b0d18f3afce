defmodule ServiceManager.Repo.Migrations.AlterLoanTermsRemoveMakerAndChecker do
  use Ecto.Migration

  @tables_and_columns %{
    loan_terms: [:maker_id, :checker_id]
  }

  def up do
    Enum.each(@tables_and_columns, fn {table, columns} ->
      Enum.each(columns, fn column ->
        # Drop existing foreign key constraints and indexes
        execute "ALTER TABLE #{table} DROP CONSTRAINT IF EXISTS #{table}_#{column}_fkey"
        execute "DROP INDEX IF EXISTS #{table}_#{column}_index"

        # Drop the columns
        alter table(table) do
          remove column
        end

        # Recreate the columns with new foreign key constraints
        alter table(table) do
          add column, references(:tbl_system_users, on_delete: :restrict)
        end

        # Recreate indexes
        create index(table, [column])
      end)
    end)
  end

  def down do
    Enum.each(@tables_and_columns, fn {table, columns} ->
      Enum.each(columns, fn column ->
        # Drop existing foreign key constraints and indexes
        execute "ALTER TABLE #{table} DROP CONSTRAINT IF EXISTS #{table}_#{column}_fkey"
        execute "DROP INDEX IF EXISTS #{table}_#{column}_index"

        # Drop the columns
        alter table(table) do
          remove column
        end

        # Recreate the columns with original foreign key constraints
        alter table(table) do
          add column, references(:tbl_system_users, on_delete: :restrict)
        end
      end)
    end)
  end
end
