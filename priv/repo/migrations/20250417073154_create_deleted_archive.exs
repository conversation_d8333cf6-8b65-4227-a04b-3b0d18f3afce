defmodule ServiceManager.Repo.Migrations.CreateDeletedArchive do
  use Ecto.Migration

  def change do
    create table(:deleted_archive) do
      add :item, :string, null: false
      add :item_id, :integer, null: false
      add :description, :text
      add :data, :map
      add :created_by, references(:tbl_system_users, on_delete: :restrict)
      add :updated_by, references(:tbl_system_users, on_delete: :restrict)

      timestamps(type: :utc_datetime)
    end
  end
end
