defmodule ServiceManager.Repo.Migrations.CreateChangelogItems do
  use Ecto.Migration

  def change do
    create table(:changelog_items) do
      add :item_type, :string, null: false
      add :title, :string, null: false
      add :description, :text, null: false
      add :version_id, references(:versions, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:changelog_items, [:version_id])
    create index(:changelog_items, [:item_type])
  end
end
