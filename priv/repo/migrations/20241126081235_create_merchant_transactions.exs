defmodule ServiceManager.Repo.Migrations.CreateMerchantTransactions do
  use Ecto.Migration

  def change do
    create table(:merchant_transactions) do
      add :merchant_id, references(:merchants, on_delete: :restrict), null: false
      add :transaction_id, :string, null: false
      add :amount, :decimal, precision: 20, scale: 2, null: false
      add :currency, :string, null: false
      add :qr_code_data, :string
      add :description, :string
      add :callback_url, :string
      add :status, :string, default: "pending", null: false
      add :callback_status, :string
      add :callback_response, :text

      timestamps()
    end

    create unique_index(:merchant_transactions, [:transaction_id])
    create index(:merchant_transactions, [:merchant_id])
    create index(:merchant_transactions, [:status])
  end
end
