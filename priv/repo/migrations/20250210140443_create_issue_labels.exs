defmodule ServiceManager.Repo.Migrations.CreateIssueLabels do
  use Ecto.Migration

  def change do
    create table(:issue_labels) do
      add :issue_id, references(:issues, on_delete: :delete_all), null: false
      add :label_id, references(:labels, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:issue_labels, [:issue_id])
    create index(:issue_labels, [:label_id])
    # Ensure an issue can't have the same label multiple times
    create unique_index(:issue_labels, [:issue_id, :label_id])
  end
end
