defmodule ServiceManager.Repo.Migrations.CreateFees do
  use Ecto.Migration

  def change do
    create table(:fees_and_charges) do
      add :code, :string
      add :name, :string
      add :description, :text
      add :amount, :decimal, default: 0.00
      add :currency_code, :string
      add :charge_type, :string
      add :effective_date, :date
      add :expiration_date, :date
      add :is_feature, :boolean, default: false, null: false
      add :status, :string
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end
  end
end
