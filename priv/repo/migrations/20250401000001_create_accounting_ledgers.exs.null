defmodule ServiceManager.Repo.Migrations.CreateAccountingLedgers do
  use Ecto.Migration

  def change do
    create table(:accounting_ledgers) do
      add :code, :string, null: false
      add :name, :string, null: false
      add :description, :string
      add :account_type, :string, null: false
      add :account_category, :string, null: false
      add :parent_id, references(:accounting_ledgers, on_delete: :restrict), null: true
      add :is_active, :boolean, default: true, null: false
      add :is_system, :boolean, default: false, null: false
      add :balance, :decimal, precision: 20, scale: 2, default: 0, null: false
      add :currency, :string, default: "MWK", null: false
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end

    create unique_index(:accounting_ledgers, [:code])
    create index(:accounting_ledgers, [:account_type])
    create index(:accounting_ledgers, [:account_category])
    create index(:accounting_ledgers, [:parent_id])
    create index(:accounting_ledgers, [:is_active])
  end
end
