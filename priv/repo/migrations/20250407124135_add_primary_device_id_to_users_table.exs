defmodule ServiceManager.Repo.Migrations.AddPrimaryDeviceIdToUsersTable do
  use Ecto.Migration

  def change do
    alter table(:accounts_users) do
      add :primary_device_id, :string
    end

    alter table(:walletusers) do
      add :primary_device_id, :string
    end

    alter table(:active_devices) do
      add :device_name, :string
      add :enabled, :boolean, default: true
      add :blocked, :boolean, default: false
    end
  end
end
