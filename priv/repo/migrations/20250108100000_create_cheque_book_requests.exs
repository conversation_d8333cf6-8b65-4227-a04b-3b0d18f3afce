defmodule ServiceManager.Repo.Migrations.CreateChequeBookRequests do
  use Ecto.Migration

  def change do
    create table(:cheque_book_requests) do
      add :account_number, :string, null: false
      add :branch_code, :string, null: false
      add :cheque_leaves, :integer, default: 25
      add :request_status, :string, default: "pending"
      add :issued_at, :utc_datetime
      add :fulfilled_at, :utc_datetime

      timestamps()
    end

    create index(:cheque_book_requests, [:account_number])
    create index(:cheque_book_requests, [:request_status])
  end
end
