defmodule ServiceManager.Repo.Migrations.CreateFormWizardSteps do
  use Ecto.Migration

  def change do
    create table(:form_wizard_steps) do
      add :step_number, :integer, null: false
      add :wizard_id, references(:form_wizards, on_delete: :delete_all), null: false
      add :form_id, references(:dynamic_forms, on_delete: :delete_all), null: false
      add :conditions, :map  # JSON for conditional logic
      add :branching_rules, :map  # JSON for branching configuration
      add :next_step_id, references(:form_wizard_steps, on_delete: :nilify_all)

      timestamps()
    end

    create index(:form_wizard_steps, [:wizard_id])
    create index(:form_wizard_steps, [:form_id])
    create index(:form_wizard_steps, [:step_number])
    create unique_index(:form_wizard_steps, [:wizard_id, :step_number])
  end
end
