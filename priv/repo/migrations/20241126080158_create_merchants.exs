defmodule ServiceManager.Repo.Migrations.CreateMerchants do
  use Ecto.Migration

  def change do
    create table(:merchants) do
      add :name, :string, null: false
      add :email, :string, null: true
      add :address, :string
      add :phone_number, :string
      add :is_active, :boolean, default: true, null: false
      add :merchant_code, :string, null: false

      timestamps()
    end

    create unique_index(:merchants, [:email])
    create unique_index(:merchants, [:merchant_code])
  end
end
