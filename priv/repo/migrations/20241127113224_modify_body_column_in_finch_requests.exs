defmodule ServiceManager.Repo.Migrations.ModifyBodyColumnInFinchRequests do
  use Ecto.Migration

  def up do
    # Add new column
    alter table(:finch_requests) do
      add :body_json, :map, default: %{}
    end

    # Copy data with conversion
    execute """
    UPDATE finch_requests
    SET body_json = CASE
      WHEN body IS NULL THEN '{}'::jsonb
      WHEN body = '' THEN '{}'::jsonb
      WHEN body ~ '^\\{.*\\}$' THEN body::jsonb
      ELSE jsonb_build_object('raw', body)
    END;
    """

    # Remove old column and rename new one
    alter table(:finch_requests) do
      remove :body
    end

    rename table(:finch_requests), :body_json, to: :body
  end

  def down do
    # Add old column back
    alter table(:finch_requests) do
      add :body_text, :text
    end

    # Copy data back
    execute """
    UPDATE finch_requests
    SET body_text = CASE
      WHEN body->>'raw' IS NOT NULL THEN body->>'raw'
      ELSE body::text
    END;
    """

    # Remove new column and rename old one back
    alter table(:finch_requests) do
      remove :body
    end

    rename table(:finch_requests), :body_text, to: :body
  end
end
