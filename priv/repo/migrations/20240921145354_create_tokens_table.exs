defmodule ServiceManager.Repo.Migrations.CreateTokensTable do
  use Ecto.Migration

  def change do
    create table(:tokens) do
      add :token, :string, null: false
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false
      add :expires_at, :utc_datetime, null: false
      add :revoked_at, :utc_datetime

      timestamps()
    end

    create unique_index(:tokens, [:token])
    create index(:tokens, [:user_id])
  end
end
