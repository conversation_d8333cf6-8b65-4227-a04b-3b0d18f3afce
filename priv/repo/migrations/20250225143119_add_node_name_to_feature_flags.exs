defmodule ServiceManager.Repo.Migrations.AddNodeNameToFeatureFlags do
  use Ecto.Migration

  def change do
    alter table(:feature_flags) do
      add :node_name, :string, null: false, default: Node.self() |> to_string()
      modify :name, :string, null: false
    end

    # Drop old unique index
    drop_if_exists index(:feature_flags, [:name])

    # Create new composite unique index
    create unique_index(:feature_flags, [:name, :node_name], name: :feature_flags_name_node_index)
  end
end
