defmodule ServiceManager.Repo.Migrations.CreateLoanCustomerBatch do
  use Ecto.Migration

  def change do
    create table(:loan_customer_batch) do
      add :batch_reference, :string, null: false
      add :filename, :string, null: false
      add :status, :string, null: false, default: "PENDING"
      add :valid_count, :integer, null: false, default: 0
      add :invalid_count, :integer, null: false, default: 0
      add :item_count, :integer, null: false, default: 0
      add :maker_id, references(:accounts_users, on_delete: :restrict)
      add :checker_id, references(:accounts_users, on_delete: :restrict)
      add :partner_id, references(:loan_partnerships, on_delete: :restrict), null: false

      timestamps()
    end

    create unique_index(:loan_customer_batch, [:batch_reference])
    create index(:loan_customer_batch, [:maker_id])
    create index(:loan_customer_batch, [:checker_id])
    create index(:loan_customer_batch, [:partner_id])
  end
end
