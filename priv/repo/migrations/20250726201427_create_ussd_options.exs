defmodule ServiceManager.Repo.Migrations.CreateUssdOptions do
  use Ecto.Migration

  def change do
    create table(:ussd_options, primary_key: false) do
      add :option_id, :uuid, primary_key: true
      add :menu_id, references(:ussd_menus, column: :menu_id, type: :uuid, on_delete: :delete_all), null: false
      add :text, :string, null: false, size: 200
      add :action, :string, null: false, size: 20
      add :target_menu_id, references(:ussd_menus, column: :menu_id, type: :uuid, on_delete: :nilify_all), null: true
      add :option_order, :integer, default: 0, null: false
      add :active, :boolean, default: true, null: false

      timestamps()
    end

    # Create indexes for performance
    create index(:ussd_options, [:menu_id])
    create index(:ussd_options, [:target_menu_id])
    create index(:ussd_options, [:active])
    create index(:ussd_options, [:action])
    create index(:ussd_options, [:option_order])
    
    # Unique constraint for menu_id and option_order combination
    create unique_index(:ussd_options, [:menu_id, :option_order], name: :ussd_options_unique_menu_order_constraint)
  end
end
