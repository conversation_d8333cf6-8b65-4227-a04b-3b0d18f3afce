defmodule ServiceManager.Repo.Migrations.CreateLoanCustomers do
  use Ecto.Migration

  def change do
    create table(:loan_customers) do
      add :first_name, :string
      add :last_name, :string
      add :email, :string
      add :phone, :string
      add :status, :string
      add :account_number, :string
      add :account_type, :string
      add :ref_id, :string, null: false
      add :eligibility_status, :string, null: false
      add :max_loan_amount, :decimal, precision: 18, scale: 2, null: false
      add :last_eligibility_check, :utc_datetime, null: false
      add :maker_id, references(:accounts_users, on_delete: :restrict)
      add :checker_id, references(:accounts_users, on_delete: :restrict)
      add :customer_id, references(:accounts_users, on_delete: :restrict), null: false
      add :product_id, references(:loan_products, on_delete: :restrict), null: false
      add :partner_id, references(:loan_partnerships, on_delete: :restrict), null: false

      timestamps()
    end

    create index(:loan_customers, [:ref_id])
    create index(:loan_customers, [:customer_id])
    create index(:loan_customers, [:product_id])
    create index(:loan_customers, [:partner_id])
    create index(:loan_customers, [:maker_id])
    create index(:loan_customers, [:checker_id])
  end
end
