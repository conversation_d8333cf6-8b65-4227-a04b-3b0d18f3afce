defmodule ServiceManager.Repo.Migrations.AddReversalToTransactionTypeCheck do
  use Ecto.Migration

  def up do
    # Drop the existing check constraint
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_type_check"
    
    # Add a new check constraint that includes "reversal"
    execute "ALTER TABLE transactions ADD CONSTRAINT transactions_type_check CHECK (type IN ('credit', 'debit', 'transfer', 'direct-transfer', 'external-transfer', 'reversal'))"
  end

  def down do
    # Drop the new check constraint
    execute "ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_type_check"
    
    # Add back the original check constraint without "reversal"
    execute "ALTER TABLE transactions ADD CONSTRAINT transactions_type_check CHECK (type IN ('credit', 'debit', 'transfer', 'direct-transfer', 'external-transfer'))"
  end
end
