defmodule ServiceManager.Repo.Migrations.CreateCodeSteps do
  use Ecto.Migration

  def change do
    create table(:code_steps) do
      add :process_id, references(:dynamic_processes, on_delete: :delete_all), null: false
      add :step_type, :string, null: false
      add :content, :text, null: false
      add :parent_step_id, references(:code_steps, on_delete: :delete_all)
      add :order_position, :integer, null: false, default: 0
      add :indentation_level, :integer, null: false, default: 0
      add :expected_inputs, :map, default: %{}
      add :input_count, :integer, default: 1
      add :is_root, :boolean, default: false
      add :step_category, :string
      add :created_by_id, references(:tbl_system_users, on_delete: :nilify_all)

      timestamps()
    end

    create index(:code_steps, [:process_id])
    create index(:code_steps, [:parent_step_id])
    create index(:code_steps, [:process_id, :order_position])
    create index(:code_steps, [:process_id, :is_root])
    create index(:code_steps, [:step_type])
    create index(:code_steps, [:step_category])
  end
end
