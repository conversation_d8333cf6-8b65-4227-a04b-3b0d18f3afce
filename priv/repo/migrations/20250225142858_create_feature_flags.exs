defmodule ServiceManager.Repo.Migrations.CreateFeatureFlags do
  use Ecto.Migration

  def up do
    create table(:feature_flags) do
      add :name, :string, null: false
      add :description, :string
      add :category, :string, null: false
      add :enabled, :boolean, default: false, null: false
      # Store all values as strings
      add :default_value, :string
      # Store all values as strings
      add :value, :string
      add :registered, :boolean, default: false, null: false
      add :value_type, :string, default: "boolean", null: false

      timestamps()
    end

    create unique_index(:feature_flags, [:name])
    create index(:feature_flags, [:category])
    create index(:feature_flags, [:value_type])
  end

  def down do
    drop index(:feature_flags, [:value_type])
    drop index(:feature_flags, [:category])
    drop index(:feature_flags, [:name])
    drop table(:feature_flags)
  end
end
