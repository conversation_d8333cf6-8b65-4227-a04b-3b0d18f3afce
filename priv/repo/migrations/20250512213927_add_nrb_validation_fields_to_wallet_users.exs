defmodule ServiceManager.Repo.Migrations.AddNrbValidationFieldsToWalletUsers do
  use Ecto.Migration

  def change do
    alter table(:walletusers) do
      # NRB validation fields
      add :surname, :string
      add :other_names, :string
      add :gender, :string
      add :date_of_birth_string, :string
      add :date_of_issue_string, :string
      add :date_of_expiry_string, :string
      add :place_of_birth_district_name, :string
      add :nrb_validation, :boolean
      add :nrb_response_code, :integer
      add :nrb_response_message, :string
      add :nrb_status, :string
      add :nrb_status_reason, :string
      add :nrb_status_code, :string
    end
  end
end
