defmodule ServiceManager.Repo.Migrations.AlterLoanMakerAndCheckerIds do
  use Ecto.Migration

  @tables_and_columns %{
    loan_products: [:maker_id, :checker_id],
    loan_partnerships: [:maker_id, :checker_id],
    loan_customer_batch: [:maker_id, :checker_id],
    loan_customers: [:maker_id, :checker_id],
    loan_terms: [:maker_id, :checker_id]
  }

  def up do
    Enum.each(@tables_and_columns, fn {table, columns} ->
      Enum.each(columns, fn column ->
        # Drop existing foreign key constraints and indexes
        execute "ALTER TABLE #{table} DROP CONSTRAINT IF EXISTS #{table}_#{column}_fkey"
        execute "DROP INDEX IF EXISTS #{table}_#{column}_index"

        # Drop the columns
        alter table(table) do
          remove column
        end

        # Recreate the columns with new foreign key constraints
        alter table(table) do
          if column == :maker_id do
            add column, references(:tbl_system_users, on_delete: :restrict), null: false
          else
            add column, references(:tbl_system_users, on_delete: :restrict)
          end
        end

        # Recreate indexes
        create index(table, [column])
      end)
    end)
  end

  def down do
    Enum.each(@tables_and_columns, fn {table, columns} ->
      Enum.each(columns, fn column ->
        # Drop existing foreign key constraints and indexes
        execute "ALTER TABLE #{table} DROP CONSTRAINT IF EXISTS #{table}_#{column}_fkey"
        execute "DROP INDEX IF EXISTS #{table}_#{column}_index"

        # Drop the columns
        alter table(table) do
          remove column
        end

        # Recreate the columns with original foreign key constraints
        alter table(table) do
          if column == :maker_id do
            add column, references(:accounts_users, on_delete: :restrict), null: false
          else
            add column, references(:accounts_users, on_delete: :restrict)
          end
        end
      end)
    end)
  end
end
