defmodule ServiceManager.Repo.Migrations.CreateMobileFormsV2Tables do
  use Ecto.Migration

  def change do
    # Ensure UUID generation is available for backfill
    execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")

    # Screens
    create table(:mobile_screens, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :name, :string, null: false
      add :version, :string, null: false
      add :order, :integer, null: false, default: 0
      add :active, :boolean, null: false, default: true
      timestamps()
    end

    create unique_index(:mobile_screens, [:name, :version], name: :mobile_screens_unique_name_version)
    create index(:mobile_screens, [:active])
    create index(:mobile_screens, [:order])

    # Pages
    create table(:mobile_pages, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :screen_id, references(:mobile_screens, type: :uuid, on_delete: :delete_all), null: false
      add :name, :string, null: false
      add :order, :integer, null: false, default: 0
      add :active, :boolean, null: false, default: true
      timestamps()
    end

    create unique_index(:mobile_pages, [:screen_id, :name], name: :mobile_pages_unique_screen_name)
    create index(:mobile_pages, [:screen_id])
    create index(:mobile_pages, [:active])
    create index(:mobile_pages, [:order])

    # Forms (V2)
    create table(:mobile_forms_v2, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :page_id, references(:mobile_pages, type: :uuid, on_delete: :delete_all), null: false
      add :name, :string, null: false
      add :order, :integer, null: false, default: 0
      add :active, :boolean, null: false, default: true
      add :submit_to, :string
      timestamps()
    end

    create unique_index(:mobile_forms_v2, [:page_id, :name], name: :mobile_forms_v2_unique_page_name)
    create index(:mobile_forms_v2, [:page_id])
    create index(:mobile_forms_v2, [:active])
    create index(:mobile_forms_v2, [:order])
    create index(:mobile_forms_v2, [:submit_to])

    # Fields (V2)
    create table(:mobile_form_fields_v2, primary_key: false) do
      add :id, :uuid, primary_key: true
      add :form_id, references(:mobile_forms_v2, type: :uuid, on_delete: :delete_all), null: false
      add :field_name, :string, null: false
      add :field_type, :string, null: false
      add :label, :string, null: false
      add :is_required, :boolean, null: false, default: false
      add :field_order, :integer, null: false, default: 0
      add :active, :boolean, null: false, default: true
      timestamps()
    end

    create unique_index(:mobile_form_fields_v2, [:form_id, :field_name], name: :mobile_form_fields_v2_unique_form_field)
    create index(:mobile_form_fields_v2, [:form_id])
    create index(:mobile_form_fields_v2, [:active])
    create index(:mobile_form_fields_v2, [:field_order])
  end
end

