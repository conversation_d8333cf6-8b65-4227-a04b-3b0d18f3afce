defmodule ServiceManager.Repo.Migrations.CreateFundRequestsConfig do
  use Ecto.Migration

  def change do
    create table(:fund_requests_config) do
      add :validate_receiver_account, :boolean, default: true, null: false

      timestamps()
    end

    # Insert default configuration
    execute """
    INSERT INTO fund_requests_config (validate_receiver_account, inserted_at, updated_at)
    VALUES (true, NOW(), NOW())
    """
  end
end
