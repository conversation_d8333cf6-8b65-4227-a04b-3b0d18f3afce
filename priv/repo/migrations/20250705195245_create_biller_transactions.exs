defmodule ServiceManager.Repo.Migrations.CreateBillerTransactions do
  use Ecto.Migration

  def change do
    create table(:biller_transactions) do
      # Biller Information
      add :biller_type, :string, null: false
      add :biller_name, :string, null: false
      
      # Account Information
      add :account_number, :string, null: false
      add :account_type, :string
      
      # Transaction Details
      add :our_transaction_id, :string, null: false
      add :amount, :decimal, precision: 15, scale: 2
      add :currency, :string, default: "MWK", null: false
      
      # Account References
      add :credit_account, :string
      add :credit_account_type, :string
      add :debit_account, :string
      add :debit_account_type, :string
      
      # Customer Information
      add :customer_account_number, :string
      add :customer_account_name, :string
      
      # Transaction Type (account_details, post_transaction, get_invoice, confirm_invoice, bundle_details, confirm_bundle)
      add :transaction_type, :string, null: false
      
      # Specific fields for certain billers
      add :bundle_id, :string  # For TNM bundles
      
      # Status and Processing
      add :status, :string, default: "pending", null: false
      
      # API Information
      add :api_endpoint, :string
      add :request_payload, :map
      add :response_payload, :map
      add :error_message, :text
      
      # Timestamps
      add :processed_at, :utc_datetime

      timestamps()
    end

    # Create unique index for our_transaction_id
    create unique_index(:biller_transactions, [:our_transaction_id])
    
    # Create indexes for common queries
    create index(:biller_transactions, [:biller_type])
    create index(:biller_transactions, [:status])
    create index(:biller_transactions, [:account_number])
    create index(:biller_transactions, [:transaction_type])
    create index(:biller_transactions, [:bundle_id])
    create index(:biller_transactions, [:processed_at])
    create index(:biller_transactions, [:inserted_at])
    
    # Composite indexes for common query patterns
    create index(:biller_transactions, [:biller_type, :status])
    create index(:biller_transactions, [:account_number, :status])
    create index(:biller_transactions, [:biller_type, :inserted_at])
    
    # Add check constraints
    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT biller_type_valid 
    CHECK (biller_type IN (
      'register_general',
      'bwb_postpaid',
      'lwb_postpaid',
      'srwb_postpaid',
      'srwb_prepaid',
      'masm',
      'airtel_validation',
      'tnm_bundles'
    ))
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT biller_type_valid
    """

    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT transaction_type_valid 
    CHECK (transaction_type IN (
      'account_details',
      'post_transaction',
      'get_invoice',
      'confirm_invoice',
      'bundle_details',
      'confirm_bundle',
      'validation'
    ))
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT transaction_type_valid
    """

    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT status_valid 
    CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'))
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT status_valid
    """

    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT currency_valid 
    CHECK (currency IN ('MWK', 'USD', 'EUR', 'GBP', 'ZAR'))
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT currency_valid
    """

    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT account_type_valid 
    CHECK (account_type IN ('account', 'wallet'))
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT account_type_valid
    """

    execute """
    ALTER TABLE biller_transactions ADD CONSTRAINT amount_positive 
    CHECK (amount IS NULL OR amount > 0)
    """, """
    ALTER TABLE biller_transactions DROP CONSTRAINT amount_positive
    """
  end
end
