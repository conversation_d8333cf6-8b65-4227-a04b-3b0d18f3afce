defmodule ServiceManager.Repo.Migrations.CreatSystemUsers do
  use Ecto.Migration

  def change do
    create table(:tbl_system_users) do
      add :email, :citext, null: false
      add :nickname, :string
      add :hashed_password, :string, null: false
      add :first_name, :string
      add :last_name, :string
      add :phone_number, :string
      add :date_of_birth, :date
      add :address, :string
      add :city, :string
      add :state, :string
      add :zip, :string
      add :country, :string
      add :status, :string, default: "inactive"
      add :approved, :boolean, default: false
      add :first_time_login, :boolean, default: true
      add :account_balance, :float, default: 0.0
      add :account_number, :string
      add :confirmed_at, :utc_datetime
      add :roles_and_permission_id, :integer
      add :memorable_word, :string
      add :otp, :string
      add :email_notifications, :boolean, default: true
      add :sms_notifications, :boolean, default: true
      add :push_notifications, :boolean, default: true
      add :customer_no, :string
      add :created_by, :integer
      add :updated_by, :integer

      timestamps(type: :utc_datetime)
    end

    create unique_index(:tbl_system_users, [:email, :phone_number])

    create table(:tbl_system_users_tokens) do
      add :user_id, references(:tbl_system_users, on_delete: :delete_all), null: false
      add :token, :binary, null: false
      add :context, :string, null: false
      add :sent_to, :string

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create index(:tbl_system_users_tokens, [:user_id])
    create unique_index(:tbl_system_users_tokens, [:context, :token])
  end
end
