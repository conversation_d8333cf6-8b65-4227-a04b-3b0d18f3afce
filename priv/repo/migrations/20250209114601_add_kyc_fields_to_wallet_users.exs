defmodule ServiceManager.Repo.Migrations.AddKycFieldsToWalletUsers do
  use Ecto.Migration

  def change do
    alter table(:walletusers) do
      # KYC fields
      add_if_not_exists :date_of_birth, :date
      add_if_not_exists :address, :string
      add_if_not_exists :city, :string
      add_if_not_exists :occupation, :string
      add_if_not_exists :employer_name, :string
      add_if_not_exists :source_of_funds, :string
      add_if_not_exists :kyc_complete, :boolean, default: false
      add_if_not_exists :kyc_verified_at, :utc_datetime
      add_if_not_exists :wallet_tier_id, references(:wallet_tiers)
    end

    create_if_not_exists index(:walletusers, [:wallet_tier_id])
    create_if_not_exists index(:walletusers, [:kyc_complete])
  end
end
