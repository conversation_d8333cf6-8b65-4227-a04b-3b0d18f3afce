defmodule ServiceManager.Repo.Migrations.AddNotificationTablesAndUserPreferences do
  use Ecto.Migration

  def change do
    # Add notification preference fields to users table
    alter table(:accounts_users) do
      add :email_notifications, :boolean, default: true
      add :sms_notifications, :boolean, default: true
      add :push_notifications, :boolean, default: true
    end

    # Create email_notifications table
    create_if_not_exists table(:email_notifications) do
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false
      add :subject, :string, null: false
      add :body, :text, null: false
      add :sent_at, :utc_datetime
      add :status, :string

      timestamps()
    end

    # Create sms_notifications table
    create_if_not_exists table(:sms_notifications) do
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false
      add :message, :text, null: false
      add :sent_at, :utc_datetime
      add :status, :string

      timestamps()
    end

    # Create push_notifications table
    create_if_not_exists table(:push_notifications) do
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false
      add :title, :string, null: false
      add :body, :text, null: false
      add :sent_at, :utc_datetime
      add :status, :string

      timestamps()
    end

    # Add indexes
    create index(:email_notifications, [:user_id])
    create index(:sms_notifications, [:user_id])
    create index(:push_notifications, [:user_id])
  end
end
