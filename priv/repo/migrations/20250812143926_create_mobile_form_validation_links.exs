defmodule ServiceManager.Repo.Migrations.CreateMobileFormValidationLinks do
  use Ecto.Migration

  def change do
    create table(:mobile_form_validation_links, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :mobile_form_id, references(:mobile_forms_v2, on_delete: :delete_all, type: :binary_id), null: false
      add :dynamic_form_id, references(:dynamic_forms, on_delete: :delete_all), null: false

      timestamps()
    end

    create index(:mobile_form_validation_links, [:mobile_form_id])
    create index(:mobile_form_validation_links, [:dynamic_form_id])
    create unique_index(:mobile_form_validation_links, [:mobile_form_id, :dynamic_form_id], name: :unique_mobile_form_validation_link)
  end
end
