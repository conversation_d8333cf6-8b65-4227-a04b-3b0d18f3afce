defmodule ServiceManager.Repo.Migrations.CreateBankAccounts do
  use Ecto.Migration

  def change do
    create table(:bank_accounts) do
      add :tag, :string
      add :name, :string, null: false
      add :number, :string, null: false
      add :type, :string, null: false
      add :currency, :string, null: false
      add :balance, :decimal, null: false
      add :user_id, references(:accounts_users, on_delete: :delete_all), null: false

      timestamps()
    end

    create unique_index(:bank_accounts, [:number])
    create index(:bank_accounts, [:user_id])
  end
end
