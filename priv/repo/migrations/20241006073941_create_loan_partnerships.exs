defmodule ServiceManager.Repo.Migrations.CreateLoanPartnerships do
  use Ecto.Migration

  def change do
    create table(:loan_partnerships) do
      add :name, :string, null: false
      add :description, :string
      add :partner_type, :string, null: false
      add :contact_person, :string, null: false
      add :contact_email, :string, null: false
      add :contact_phone, :string, null: false
      add :partnership_start_date, :date, null: false
      add :partnership_end_date, :date
      add :status, :string, null: false
      add :maker_id, references(:accounts_users, on_delete: :restrict)
      add :checker_id, references(:accounts_users, on_delete: :restrict)

      timestamps()
    end

    create index(:loan_partnerships, [:maker_id])
    create index(:loan_partnerships, [:checker_id])
    create index(:loan_partnerships, [:status])
  end
end
