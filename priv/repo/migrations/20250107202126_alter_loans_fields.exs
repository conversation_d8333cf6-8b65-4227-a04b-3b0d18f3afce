defmodule ServiceManager.Repo.Migrations.AlterLoansFields do
  use Ecto.Migration

  def up do
    drop_if_exists index(:loans, [:bank_account_id])
    drop constraint(:loans, "loans_bank_account_id_fkey")

    alter table(:loans) do
      modify :bank_account_id, references(:fund_accounts, on_delete: :restrict)
    end

    create index(:loans, [:bank_account_id])
  end

  def down do
    drop_if_exists index(:loans, [:bank_account_id])
    drop constraint(:loans, "loans_bank_account_id_fkey")

    alter table(:loans) do
      modify :bank_account_id, references(:bank_accounts, on_delete: :restrict), null: false
    end
  end
end
