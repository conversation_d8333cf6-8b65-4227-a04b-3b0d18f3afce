defmodule ServiceManager.Repo.Migrations.CreateMobileAppFormsTable do
  use Ecto.Migration

  def change do
    create table(:mobile_app_forms, primary_key: false) do
      add :field_id, :uuid, primary_key: true
      add :form, :string, null: false
      add :screen, :string
      add :page, :string
      add :version, :string, null: false
      add :field_name, :string, null: false
      add :field_type, :string, null: false
      add :label, :string, null: false
      add :is_required, :boolean, default: false, null: false
      add :field_order, :integer, default: 0, null: false
      add :active, :boolean, default: true, null: false

      timestamps()
    end

    create unique_index(:mobile_app_forms, [:form, :screen, :page, :field_name, :version], 
                       name: :mobile_app_forms_unique_field_constraint)
    
    create index(:mobile_app_forms, [:form])
    create index(:mobile_app_forms, [:form, :screen])
    create index(:mobile_app_forms, [:form, :screen, :page])
    create index(:mobile_app_forms, [:form, :version])
    create index(:mobile_app_forms, [:active])
    create index(:mobile_app_forms, [:field_order])
  end
end
