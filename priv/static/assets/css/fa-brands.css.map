{"version": 3, "sources": ["fa-brands.scss", "fa-brands.css"], "names": [], "mappings": "AAAA;;;ECGE;ADGF;EACE,oCAAoC;EACpC,kBAAkB;EAClB,mBAAmB;EACnB,yCAA8C;EAC9C,mTAImE,EAAA;;AAGrE;EACE,oCAAoC,EAAA", "file": "fa-brands.css", "sourcesContent": ["/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\r\n@import 'variables';\r\n\r\n@font-face {\r\n  font-family: 'Font Awesome 5 Brands';\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  src: url('#{$fa-font-path}/fa-brands-400.eot');\r\n  src: url('#{$fa-font-path}/fa-brands-400.eot?#iefix') format('embedded-opentype'),\r\n  url('#{$fa-font-path}/fa-brands-400.woff2') format('woff2'),\r\n  url('#{$fa-font-path}/fa-brands-400.woff') format('woff'),\r\n  url('#{$fa-font-path}/fa-brands-400.ttf') format('truetype'),\r\n  url('#{$fa-font-path}/fa-brands-400.svg#fontawesome') format('svg');\r\n}\r\n\r\n.fab {\r\n  font-family: 'Font Awesome 5 Brands';\r\n}\r\n", "/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\n@font-face {\n  font-family: 'Font Awesome 5 Brands';\n  font-style: normal;\n  font-weight: normal;\n  src: url(\"../webfonts/fa-brands-400.eot\");\n  src: url(\"../webfonts/fa-brands-400.eot?#iefix\") format(\"embedded-opentype\"), url(\"../webfonts/fa-brands-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-brands-400.woff\") format(\"woff\"), url(\"../webfonts/fa-brands-400.ttf\") format(\"truetype\"), url(\"../webfonts/fa-brands-400.svg#fontawesome\") format(\"svg\"); }\n\n.fab {\n  font-family: 'Font Awesome 5 Brands'; }\n"]}