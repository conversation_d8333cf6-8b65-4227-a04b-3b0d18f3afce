{"version": 3, "sources": ["lightgallery.bundle.css", "../../../scss/_modules/variables.scss", "../../../../node_modules/lightgallery/src/sass/lg-fonts.scss", "../../../../node_modules/lightgallery/src/sass/lg-theme-default.scss", "_variables.scss", "../../../../node_modules/lightgallery/src/sass/lg-mixins.scss", "../../../../node_modules/lightgallery/src/sass/lg-thumbnail.scss", "../../../../node_modules/lightgallery/src/sass/lg-video.scss", "../../../../node_modules/lightgallery/src/sass/lg-autoplay.scss", "../../../../node_modules/lightgallery/src/sass/lg-zoom.scss", "../../../../node_modules/lightgallery/src/sass/lg-pager.scss", "../../../../node_modules/lightgallery/src/sass/lg-fullscreen.scss", "../../../../node_modules/lightgallery/src/sass/lg-share.scss", "../../../../node_modules/lightgallery/src/sass/lg-core.scss", "custom-styles.scss"], "names": [], "mappings": "AAAA;;;;;EAKE;AACF;EACE,WAAW;EACX,kBAAkB;EAClB,gBAAgB,EAAA;;AAElB;;;EAGE,kBAAkB;EAClB,qBAAqB;EACrB,gBAAgB;EAChB,gHAAA;EACA,2BAA2B;EAC3B,YAAY;EACZ,SAAS;EACT,UAAU,EAAA;;AAEZ;;;;;;EAME,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,SAAS;EACT,UAAU;EACV,YAAY;EACZ,0BAA0B;EAC1B,UAAU,EAAA;;AAEZ;;;EAGE,aAAa;EACb,kBAAkB;EAClB,SAAS;EACT,YAAY;EACZ,yBAAyB;EACzB,OAAO;EACP,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,eAAe;EACf,gBAAgB;EAChB,uBAAuB,EAAA;;AAEzB;;;EAGE,gBAAgB;EAChB,2BAA2B;EAC3B,YAAY;EACZ,yCAAyC;EAGzC,iCAAiC,EAAA;;AAEnC;EACE,4BAA4B;EAC5B,UAAU;EACV,gBAAgB,EAAA;;AAElB;;EAEE,4BAA4B;EAC5B,UAAU;EACV,yCAAyC;EAGzC,iCAAiC,EAAA;;AAEnC;EACE,aAAa,EAAA;;AAEf;EACE,kBAAkB;EAClB,SAAS;EACT,kBAAkB;EAClB,sBAAsB;EACtB,SAAS;EACT,4BAA4B;EAC5B,UAAU;EACV,iBAAiB,EAAA;;AAEnB;EACE,qBAAqB;EACrB,0BAA0B;EAC1B,UAAU;EACV,UAAU;EACV,WAAW;EACX,mBAAmB;EACnB,sBAAsB;EACtB,kBAAkB,EAAA;;ACpGpB;4EDuG4E;ACrG5E,+CAAA;AAQA;;;;;;kFDqGkF;AC7FlF;4ED+F4E;ACzF5E;4ED2F4E;ACzF5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AA6EA;4EDnD4E;ACuD5E;4EDrD4E;ACoER,kGAAA;AACG,2EAAA;AAavE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EDpG4E;AC6H5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAKA,iBAAA;AASA,WAAA;AAEA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;CD7OC;AC0QD,UAAA;AAuBA,aAAA;AAIA;4EDjS4E;ACyS5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EDzS4E;AC2SlE,mFAAA;AAOV;4ED/S4E;ACiTG,mEAAA;AAE/E;4EDhT4E;ACsT5E,oEAAA;AAUA;4ED7T4E;ACiU5E;4ED/T4E;ACiU5B,0BAAA;AACH,iBAAA;AAG7C;4EDjU4E;ACsU5E;4EDpU4E;AC0U5E;4EDxU4E;AC4U5E;4ED1U4E;AC6U5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACC,8DAAA;AACC,qDAAA;AAEtD,gCAAA;AAGA,qBAAA;AAC4D,uBAAA;AAO5D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4ED7V4E;AC4X5E,oBAAA;AACA,eAAA;AAMA,uBAAA;AAOA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4ED7a4E;AC+a5E,eAAA;AAIA;4EDhb4E;ACub5E;4EDrb4E;ACyb5E;4EDvb4E;ACoc5E;4EDlc4E;ACyc5E;4EDvc4E;AC+c5E;4ED7c4E;ACqd5E;4EDnd4E;ACwd5E,oBAAA;AChwBA;EACI,iBAAiB;EACjB,iCAA2C;EAC3C,iNAAyP;EACzP,mBAAmB;EACnB,kBAAkB,EAAA;;AAItB;EACI,iBAAiB;EACjB,WAAW;EACX,kBAAkB;EAClB,mBAAmB;EACnB,oBAAoB;EACpB,oBAAoB;EACpB,cAAc;EACd,sCAAA;EACA,mCAAmC;EACnC,kCAAkC,EAAA;;ACnBtC;EAEQ,qCCIwB;EDHxB,kBCFmB;EDGnB,WCGY;EDFZ,eAAe;EACf,cAAc;EACd,eAAe;EACf,iBAAiB;EACjB,qBAAqB;EACrB,kBAAkB;EAClB,QAAQ;EACR,aCoCc;EDnCd,YAAY;EACZ,aAAa,EAAA;EAdrB;IAiBY,oBAAoB;IACpB,YAAY,EAAA;EAlBxB;IAsBY,WCdc,EAAA;;ADR1B;EA2BQ,WAAW,EAAA;EA3BnB;IA8BY,gBAAgB,EAAA;;AA9B5B;EAmCQ,UAAU,EAAA;EAnClB;IAsCY,gBAAgB,EAAA;;AEqBxB;EFfA;IACI,OAAO,EAAA;EAGX;IACI,WAAW,EAAA;EAGf;IACI,OAAO,EAAA,EAAA;;AEkBX;EF3BA;IACI,OAAO,EAAA;EAGX;IACI,WAAW,EAAA;EAGf;IACI,OAAO,EAAA,EAAA;;AEMX;EFAA;IACI,OAAO,EAAA;EAGX;IACI,UAAU,EAAA;EAGd;IACI,OAAO,EAAA,EAAA;;AEGX;EFZA;IACI,OAAO,EAAA;EAGX;IACI,UAAU,EAAA;EAGd;IACI,OAAO,EAAA,EAAA;;AAKf;EEvDI,oCF0D4C;EExD5C,4BFwD4C;EACpC,kBAAkB,EAAA;;AAJ9B;EEvDI,mCFiE2C;EE/D3C,2BF+D2C;EACnC,kBAAkB,EAAA;;AAM9B;EACI,aC1CiB;ED2CjB,OAAO;EACP,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,qCChG+B,EAAA;ED0FnC;IASQ,WC5FY;ID6FZ,eAAe;IACf,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,WAAW;IACX,gCAAgC;IAChC,oBAAoB;IE+GxB,qCF9GyC;IEgHzC,6BFhHyC,EAAA;IApB7C;MAuBY,WCzGc,EAAA;EDkF1B;IA6BY,gBAAgB,EAAA;EA7B5B;IAmCY,gBAAgB,EAAA;;AAM5B;EACI,6BChGwB;EDiGxB,SAAS;EACT,WCjGoB;EDkGpB,eAAe;EACf,OAAO;EACP,kBAAkB;EAClB,eAAe;EACf,QAAQ;EACR,kBAAkB;EAClB,aC3FiB,EAAA;EDiFrB;IAaQ,SAAS;IACT,eAAe;IACf,iBAAiB,EAAA;EAfzB;IAmBQ,eAAe;IACf,eAAe,EAAA;;AAKvB;EACI,WCtJgB;EDuJhB,qBAAqB;EACrB,eCnJuB;EDoJvB,kBAAkB;EAClB,iBAAiB;EACjB,sBAAsB,EAAA;;AAI1B;EACI,UAAU;EEgIV,2IF/HyI;EEkIzI,2IFlIyI;EEkIzI,mIFlIyI;EEkIzI,2HFlIyI;EEkIzI,mLFlIyI,EAAA;;AAG7I;EAEQ,UAAU;EEbd,2CAA0C;EAC1C,mCAAkC,EAAA;;AFUtC;EAOQ,UAAU;EElBd,0CAA0C;EAC1C,kCAAkC,EAAA;;AFUtC;EAYQ,UAAU;EEvBd,2CAA0C;EAC1C,mCAAkC,EAAA;;AF4BtC;EExBI,yCAAsC;EACtC,iCAA8B;EF4BlB,UAAU;EEqGtB,gIFpG0I;EEuG1I,gIFvG0I;EEuG1I,wHFvG0I;EEuG1I,gHFvG0I;EEuG1I,wKFvG0I;EEQ1I,iCFP6C;EEU7C,yBFV6C,EAAA;;AAPjD;EExBI,mCAAsC;EACtC,2BAA8B;EFmCd,UAAU,EAAA;;AG1M9B;EAEQ,yBFuCoB;EEtCpB,SAAS;EACT,kBAAkB;EAClB,WAAW;EACX,aF8Ce;EE7Cf,iBAAiB;ED0JrB,0CAA0C;EAC1C,kCAAkC;EAsIlC,0EC/R4E;EDkS5E,kEClS4E;EDkS5E,0DClS4E;EDkS5E,kHClS4E,EAAA;EAThF;ID2TI,oBAAoB;IAEpB,eAAe;IACf,gBAAgB;IAChB,YAAY,EAAA;EC/ThB;IDmUI,YAAY;IACZ,wBAAwB;IAExB,mBAAmB;IACnB,oBAAoB;IACpB,gBAAgB,EAAA;ECxUpB;ID6OI,0CCpNsD;IDqNtD,kCCrNsD,EAAA;;AAzB1D;EDiKI,wCAA0C;EAC1C,gCAAkC,EAAA;;AClKtC;EAoCQ,eAAe;EACf,YAAY;EACZ,mBAAmB,EAAA;;AAtC3B;EA0CQ,kBAAkB;EAClB,eAAe;EACf,WAAW;EACX,gBAAgB;EAChB,YAAY;EACZ,sBAAsB;EACtB,kBAAkB;EAClB,kBAAkB,EAAA;EAClB;IAlDR;MD6NI,2CC1KmD;MD4KnD,mCC5KmD,EAAA,EAYlD;EA/DL;IAuDY,qBFpDuB,EAAA;EEHnC;IA2DY,WAAW;IACX,YAAY;IACZ,oBAAiB;OAAjB,iBAAiB,EAAA;;AA7D7B;EAmEY,qBAAqB,EAAA;;AAnEjC;EAyEY,iBAAiB,EAAA;;AAzE7B;ED6NI,qCC/I6C;EDiJ7C,6BCjJ6C,EAAA;;AA9EjD;EAkFgB,aAAa,EAAA;;AAlF7B;EAwFQ,yBF/CoB;EEgDpB,0BAAgE;EAChE,WFlFY;EEmFZ,eAAe;EACf,eAAe;EACf,YAAY;EACZ,iBAAiB;EACjB,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,kBAAkB;EAClB,UAAU;EACV,WAAW,EAAA;EApGnB;IAuGY,gBAAgB,EAAA;EAvG5B;IA2GY,WFlGc,EAAA;;AGT1B;EAEQ,qBAAqB;EACrB,sBAAsB;EACtB,iBAAiB;EACjB,gBAAgB;EAChB,WAAW;EACX,cAAc,EAAA;;AAPtB;EAWQ,WAAW;EACX,SAAS;EACT,sBAAsB;EACtB,gBAAgB;EAChB,kBAAkB,EAAA;EAf1B;IAkBY,qBAAqB;IACrB,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,sBAAsB;IACtB,uBAAuB,EAAA;EAvBnC;IA2BY,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,iBAAiB;IACjB,aHoBY;IGnBZ,eAAe,EAAA;;AAnC3B;EAyCY,iCAAiC;EACjC,cAAc,EAAA;;AA1C1B;EAgDY,+EAAqF,EAAA;;AAhDjG;EAoDgB,mFAAyF,EAAA;;AApDzG;EA4DY,+EAAqF;EACrF,YAAY;EACZ,kBAAkB;EAClB,iBAAiB;EACjB,WAAW;EACX,YAAY,EAAA;;AAjExB;EAqEgB,UACJ,EAAA;;AAtEZ;EA6EY,iFAAuF,EAAA;;AA7EnG;EAiFgB,qFAA2F,EAAA;;AAjF3G;EAuFQ,sBAAsB;EACtB,uBAAuB;EACvB,kBAAkB;EAClB,MAAM;EACN,OAAO,EAAA;;AA3Ff;EAgGY,kBAAkB,EAAA;;AAhG9B;EAqGgB,aAAa,EAAA;;AArG7B;EAyGgB,mBAAmB,EAAA;;ACzGnC;EACI,sBJwBqB;EIvBrB,WJyBwB;EIxBxB,OAAO;EACP,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,aJyCqB;EIxCrB,UAAU;EHgSV,yCG/R+C;EHkS/C,iCGlS+C,EAAA;EATnD;IAYQ,yBJT2B;IIU3B,WJcoB;IIbpB,QAAQ,EAAA;EAdhB;IAmBY,WAAW,EAAA;EAInB;IACI,UAAU,EAAA;;AAIlB;EAKQ,gBAAgB,EAAA;EAHhB;IACI,gBAAgB,EAAA;;AC/B5B;EJ6OI,+BIxO2C;EJyO3C,uBIzO2C,EAAA;;AAL/C;EJwSI,yEI5RwG;EJ+RxG,iEI/RwG;EJ+RxG,yDI/RwG;EJ+RxG,gHI/RwG,EAAA;;AAZ5G;EJwSI,qGIrRiK;EJwRjK,6FIxRiK,EAAA;;AAnBrK;EJiKI,uCAA0C;EAC1C,+BAAkC;EAhFlC,mCItD2C;EJwD3C,2BIxD2C,EAAA;;AA5B/C;EJsKI,mCAAsC;EACtC,2BAA8B;EAiI9B,mGItQ8H;EJyQ9H,mGIzQ8H;EJyQ9H,2FIzQ8H;EJyQ9H,mFIzQ8H;EJyQ9H,0IIzQ8H;EJ0K9H,6BIzKqC;EJ4KrC,qBI5KqC;EJ+CrC,mCI9C2C;EJgD3C,2BIhD2C,EAAA;;AAO/C;EAEQ,gBAAgB,EAAA;;AAIxB;EACI,eAAe,EAAA;EADnB;IAGQ,gBAAgB,EAAA;;AAIxB;EACI,YAAY;EACZ,oBAAoB,EAAA;EAFxB;IAKQ,gBAAgB,EAAA;EAGpB;IACI,UAAU;IACV,oBAAoB,EAAA;;AClE5B;EAEQ,YAAY;EACZ,OAAO;EACP,kBAAkB;EAClB,QAAQ;EACR,kBAAkB;EAClB,aN8CW;EM7CX,YAAY,EAAA;EARpB;IAYgB,iBAAiB,EAAA;;AAZjC;EAkBQ,eAAe;EACf,qBAAqB;EACrB,gBAAgB;EAChB,kBAAkB;EAClB,mBAAmB;EACnB,aAAa,EAAA;EAvBrB;IA2BgB,UAAU;ILsItB,uCAA0C;IAC1C,+BAAkC,EAAA;EKlKtC;IAkCgB,yCAAiC;YAAjC,iCAAiC,EAAA;;AAlCjD;EAwCQ,sBAAsB;EACtB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,OAAO;EACP,mBAAmB;EACnB,kBAAkB;EAClB,UAAU;EACV,YAAY;EACZ,kBAAkB;EAClB,YAAY;EACZ,kBAAkB;ELqPtB,0EKpP4E;ELuP5E,kEKvP4E;ELuP5E,0DKvP4E;ELuP5E,2FKvP4E;EL6G5E,yCAA0C;EAC1C,iCAAkC,EAAA;EKlKtC;IAwDY,WAAW;IACX,YAAY,EAAA;;AAzDxB;EA8DQ,0CAA0C;EAC1C,kBAAkB;EAClB,4DAAoD;UAApD,oDAAoD;EACpD,cAAc;EACd,YAAY;EL2JhB,2CK1J+C;EL4J/C,mDK5J+C;EL4J/C,2CK5J+C;EL4J/C,mCK5J+C;EL4J/C,oEK5J+C;EAC3C,WAAW,EAAA;EApEnB;IAuEY,yCAAiC;YAAjC,iCAAiC,EAAA;;AAvE7C;EA4EQ,mCAAmC;EACnC,oCAAoC;EACpC,uBAAuB;EACvB,aAAa;EACb,qBAAqB;EACrB,SAAS;EACT,SAAS;EACT,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,QAAQ,EAAA;;ACtFhB;EAEQ,gBAAgB,EAAA;EAEhB;IACI,gBAAgB,EAAA;;ACH5B;EAGQ,qCAAqC;EACrC,SAAS;EACT,eAAe;EACf,OAAO;EACP,eAAe;EACf,QAAQ;EACR,MAAM;EACN,aRsCa;EQrCb,UAAU;EACV,kBAAkB;EP+MtB,uEO9M2E;EPgN3E,+DOhN2E,EAAA;;AAb/E;EPsOI,4BOrNoC;EPsNpC,oBOtNoC;EPmMpC,yCOlMiD;EPmMjD,iCOnMiD;EACzC,UAAU;EACV,mBAAmB,EAAA;;AApB/B;EAuBY,WAAW,EAAA;;AAvBvB;EA2BQ,sBAAsB;EACtB,kBAAkB;EAClB,eAAe;EACf,qBAAqB;EACrB,SAAS;EACT,eAAe;EACf,kBAAkB;EAClB,QAAQ;EACR,gBAAgB;EAChB,SAAS;EACT,UAAU;EACV,kBAAkB;EP8KtB,yCO7K6C;EP8K7C,iCO9K6C;EP+P7C,yGO9P2G;EPiQ3G,yGOjQ2G;EPiQ3G,iGOjQ2G;EPiQ3G,yFOjQ2G;EPiQ3G,4HOjQ2G,EAAA;EAxC/G;IA2CY,WAAW;IACX,cAAc;IACd,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,6BAA6B;IAC7B,yBAAyB;IACzB,WAAW;IACX,UACJ,EAAA;EApDR;IAuDgB,kBAAkB,EAAA;EAvDlC;IA2DoB,WAAW,EAAA;EA3D/B;IAgEY,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,oEAAoE;IACpE,eAAe,EAAA;IArE3B;MAuEgB,qCAAqC,EAAA;EAvErD;IA4EY,qBAAqB;IACrB,cAAc;IACd,gBAAgB;IAChB,sBAAsB,EAAA;EA/ElC;IAkFY,WAAW;IACX,qBAAqB;IACrB,WAAW;IACX,eAAe;IACf,YAAY;IACZ,cAAc;IACd,iBAAiB;IACjB,UAAU;IACV,sBAAsB;IACtB,WAAW,EAAA;;AA3FvB;EA+FQ,kBAAkB,EAAA;EA/F1B;IAiGY,gBAAgB,EAAA;;AAjG5B;EAsGY,cAAc,EAAA;EAtG1B;IAwGgB,gBAAgB,EAAA;;AAxGhC;EA8GY,cAAc,EAAA;EA9G1B;IAgHgB,gBAAgB,EAAA;;AAhHhC;EAsHY,cAAc,EAAA;EAtH1B;IAwHgB,gBAAgB,EAAA;;AAxHhC;EA8HY,cAAc,EAAA;EA9H1B;IAgIgB,gBAAgB,EAAA;;ACjIhC;EACI,WAAW;EACX,cAAc;EACd,WAAW,EAAA;;AAIf;EACI,WAAW;EACX,YAAY;EACZ,eAAe;EACf,MAAM;EACN,OAAO;EACP,aTiCe;EShCf,gBAAgB;EAChB,UAAU;ER6MV,yCQ3MyC;ER6MzC,iCQ7MyC,EAAA;EAV7C;IR2FI,8BQ9EkC;IRgFlC,sBQhFkC,EAAA;EAbtC;IAiBQ,UAAU,EAAA;EAjBlB;IRqOI,+CQ7M2D;IR8M3D,uCQ9M2D;IRkN3D,sDQjNkE;IRkNlE,8CQlNkE,EAAA;EAzBtE;IRqOI,0CQnMsD;IRoMtD,kCQpMsD;IAC1C,UAAU,EAAA;EAnC1B;IRmTI,oBAAoB;IAEpB,eAAe;IACf,gBAAgB;IAChB,YAAY,EAAA;EQvThB;IR2TI,YAAY;IACZ,wBAAwB;IAExB,mBAAmB;IACnB,oBAAoB;IACpB,gBAAgB,EAAA;EQhUpB;IAsDQ,YAAY;IACZ,WAAW;IACX,kBAAkB;IAClB,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,eAAe;IACf,gBAAgB,EAAA;EA7DxB;IAiEQ,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,OAAO;IACP,MAAM;IACN,mBAAmB,EAAA;EAtE3B;IA0EQ,sFAA4F;IAC5F,wBAAwB,EAAA;EA3EhC;IA+EY,gCAAgC,EAAA;EA/E5C;IAoFY,gCAAgC,EAAA;EApF5C;IAyFQ,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,WAAW;IACX,YAAY,EAAA;IA7FpB;MAgGY,WAAW;MACX,qBAAqB;MACrB,WAAW;MACX,UAAU;MACV,kBAAkB,EAAA;EApG9B;IAyGQ,kBAAkB;IAClB,cAAc;IACd,OAAO;IACP,QAAQ;IACR,MAAM;IACN,SACJ,EAAA;EA/GJ;IAmHY,sBAAsB,EAAA;EAnHlC;IAuHY,aTxEM,EAAA;ES/ClB;IA4HQ,qBAAqB;IACrB,sBAAsB;IACtB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,uBAAuB,EAAA;EAjI/B;IAuIgB,UAAU;IR8EtB,yCQ7EqD;IR+ErD,iCQ/EqD,EAAA;EAxIzD;IA6IoB,UAAU,EAAA;EA7I9B;IAqJQ,aAAa,EAAA;EArJrB;IA0JY,aAAa,EAAA;;AAKzB;EACI,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,aTrHkB;ESsHlB,sBAAsB;EACtB,UAAU;ER8CV,yCQ7CyC;ER+CzC,iCQ/CyC,EAAA;EAT7C;IAWQ,YTlLe,EAAA;;ASuLvB;ERiHI,8CQ5G4D;ER+G5D,sCQ/G4D,EAAA;;AALhE;ERrGI,mCQgH2C;ER9G3C,2BQ8G2C,EAAA;;AAX/C;ERrGI,mCQsH2C;ERpH3C,2BQoH2C,EAAA;;AAjB/C;EAwBY,UAAU,EAAA;EAxBtB;IA2BgB,UAAU,EAAA;EA3B1B;IRiHI,wCQjF0D;IRoF1D,gCQpF0D,EAAA;;AAhC9D;EAwCgB,UAAU,EAAA;EAxC1B;IRtBI,2CAA0C;IAC1C,mCAAkC,EAAA;EQqBtC;IRtBI,0CAA0C;IAC1C,kCAAkC,EAAA;EQqBtC;IRtBI,uCAA0C;IAC1C,+BAAkC;IQyElB,UAAU,EAAA;EApD9B;IRiHI,6FQxD2G;IR2D3G,6FQ3D2G;IR2D3G,qFQ3D2G;IR2D3G,6EQ3D2G;IR2D3G,kIQ3D2G,EAAA;;AAzD/G;EAgEgB,UAAU;EACV,kBAAkB;EAClB,OAAO,EAAA;EAlEvB;IAqEoB,WAAW,EAAA;EArE/B;IAyEoB,UAAU,EAAA;EAzE9B;IA6EoB,OAAO;IACP,UAAU,EAAA;EA9E9B;IRiHI,gFQ9BsG;IRiCtG,wEQjCsG,EAAA;;AC1Q1G;EACC,iCAAiC,EAAA;;AAGlC;EAEK,gBAAgB,EAAA;;AAFrB;EAKE,gBAAgB,EAAA;;AAIlB;EAEE,gBAAgB,EAAA;;AAIlB;EAEE,gBAAgB,EAAA;;AAFlB;EAKE,gBAAgB,EAAA;;AALlB;EAQE,gBACD,EAAA;;AAID;EACC,gBACD,EAAA;;AAEA;EACC,gBACD,EAAA;;AAEA;EACC,gBACD,EAAA;;AAEA;EACC,gBACD,EAAA;;AAEA;EACC,gBACD,EAAA;;AAEA;EACC,gBACD,EAAA;;AAEA;EACC,2BAA2B,EAAA;;AAG5B;EACE,8FAA6E;EAA7E,uEAA6E;EAC7E,mBAAmB,EAAA;EAFrB;IAKE,YAAY;IACZ,iBAAiB;IACjB,UAAU,EAAA;;AAKZ;EACC,iBAAiB,EAAA;;AAGlB;EACC,eAAe;EACf,YAAY;EACZ,UAAU;EACP,iBAAiB;EACjB,UAAU;EACV,0BAA0B,EAAA;EAN9B;IASK,eAAe,EAAA;;AAIpB;;EAEC,YAAY;EACZ,WAAW;EACX,iBAAiB;EACjB,UAAU;EACV,kBAAkB;EAEf,iCAAyB;EAAzB,yBAAyB,EAAA;EAR7B;;IAWE,yBb/FuB,EAAA;EaoFzB;;IAeE,8BAAsB;YAAtB,sBAAsB,EAAA;;AAKxB;;EAEC,qBbxG0B,EAAA;;Aa2G3B;EACC,iBAAiB,EAAA", "file": "lightgallery.bundle.css", "sourcesContent": ["/*!\n * justifiedGallery - v3.7.0\n * http://miromannino.github.io/Justified-Gallery/\n * Copyright (c) 2018 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n.justified-gallery {\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n}\n.justified-gallery > a,\n.justified-gallery > div,\n.justified-gallery > figure {\n  position: absolute;\n  display: inline-block;\n  overflow: hidden;\n  /* background: #888888; To have gray placeholders while the gallery is loading with waitThumbnailsLoad = false */\n  filter: \"alpha(opacity=10)\";\n  opacity: 0.1;\n  margin: 0;\n  padding: 0;\n}\n.justified-gallery > a > img,\n.justified-gallery > div > img,\n.justified-gallery > figure > img,\n.justified-gallery > a > a > img,\n.justified-gallery > div > a > img,\n.justified-gallery > figure > a > img {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  margin: 0;\n  padding: 0;\n  border: none;\n  filter: \"alpha(opacity=0)\";\n  opacity: 0;\n}\n.justified-gallery > a > .caption,\n.justified-gallery > div > .caption,\n.justified-gallery > figure > .caption {\n  display: none;\n  position: absolute;\n  bottom: 0;\n  padding: 5px;\n  background-color: #000000;\n  left: 0;\n  right: 0;\n  margin: 0;\n  color: white;\n  font-size: 12px;\n  font-weight: 300;\n  font-family: sans-serif;\n}\n.justified-gallery > a > .caption.caption-visible,\n.justified-gallery > div > .caption.caption-visible,\n.justified-gallery > figure > .caption.caption-visible {\n  display: initial;\n  filter: \"alpha(opacity=70)\";\n  opacity: 0.7;\n  -webkit-transition: opacity 500ms ease-in;\n  -moz-transition: opacity 500ms ease-in;\n  -o-transition: opacity 500ms ease-in;\n  transition: opacity 500ms ease-in;\n}\n.justified-gallery > .entry-visible {\n  filter: \"alpha(opacity=100)\";\n  opacity: 1;\n  background: none;\n}\n.justified-gallery > .entry-visible > img,\n.justified-gallery > .entry-visible > a > img {\n  filter: \"alpha(opacity=100)\";\n  opacity: 1;\n  -webkit-transition: opacity 500ms ease-in;\n  -moz-transition: opacity 500ms ease-in;\n  -o-transition: opacity 500ms ease-in;\n  transition: opacity 500ms ease-in;\n}\n.justified-gallery > .jg-filtered {\n  display: none;\n}\n.justified-gallery > .spinner {\n  position: absolute;\n  bottom: 0;\n  margin-left: -24px;\n  padding: 10px 0 10px 0;\n  left: 50%;\n  filter: \"alpha(opacity=100)\";\n  opacity: 1;\n  overflow: initial;\n}\n.justified-gallery > .spinner > span {\n  display: inline-block;\n  filter: \"alpha(opacity=0)\";\n  opacity: 0;\n  width: 8px;\n  height: 8px;\n  margin: 0 4px 0 4px;\n  background-color: #000;\n  border-radius: 6px;\n}\n\n@import \"./src/scss/_modules/variables\";\r\n@import \"./src/custom/plugins/lightgallery/_variables\";\r\n@import \"./node_modules/lightgallery/src/sass/lightgallery\";\r\n@import \"./src/custom/plugins/lightgallery/custom-styles\";\r\n", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n\r\n/* select */\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\tauto !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\tauto !default \t\t\t\t\t\t\t\t\t\t    /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t\t      /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t/* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\t#d58100 !default;\r\n$settings-incompat-desc:\t\t\t\t#ec9f28 !default;\r\n$settings-incompat-bg:\t\t\t\t\t$warning-50 !default;\r\n$settings-incompat-border:\t\t\t\t$warning-700 !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;", "// font icons support\n@font-face {\n    font-family: 'lg';\n    src: url(\"#{$lg-path-fonts}/lg.eot?n1z373\");\n    src: url(\"#{$lg-path-fonts}/lg.eot?#iefixn1z373\") format(\"embedded-opentype\"), url(\"#{$lg-path-fonts}/lg.woff?n1z373\") format(\"woff\"), url(\"#{$lg-path-fonts}/lg.ttf?n1z373\") format(\"truetype\"), url(\"#{$lg-path-fonts}/lg.svg?n1z373#lg\") format(\"svg\");\n    font-weight: normal;\n    font-style: normal;\n}\n\n\n.lg-icon {\n    font-family: 'lg';\n    speak: none;\n    font-style: normal;\n    font-weight: normal;\n    font-variant: normal;\n    text-transform: none;\n    line-height: 1;\n    /* Better Font Rendering =========== */\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}", "// default theme\n.lg-actions {\n    .lg-next, .lg-prev {\n        background-color: $lg-next-prev-bg;\n        border-radius: $lg-border-radius-base;\n        color: $lg-next-prev-color;\n        cursor: pointer;\n        display: block;\n        font-size: 22px;\n        margin-top: -10px;\n        padding: 8px 10px 9px;\n        position: absolute;\n        top: 50%;\n        z-index: $zindex-controls;\n        border: none;\n        outline: none;\n\n        &.disabled {\n            pointer-events: none;\n            opacity: 0.5;\n        }\n\n        &:hover {\n            color: $lg-next-prev-hover-color;\n        }\n    }\n\n    .lg-next {\n        right: 20px;\n\n        &:before {\n            content: \"\\e095\";\n        }\n    }\n\n    .lg-prev {\n        left: 20px;\n\n        &:after {\n            content: \"\\e094\";\n        }\n    }\n}\n\n@include keyframes(lg-right-end) {\n    0% {\n        left: 0;\n    }\n\n    50% {\n        left: -30px;\n    }\n\n    100% {\n        left: 0;\n    }\n}\n\n\n@include keyframes(lg-left-end) {\n    0% {\n        left: 0;\n    }\n\n    50% {\n        left: 30px;\n    }\n\n    100% {\n        left: 0;\n    }\n}\n\n\n.lg-outer {\n    &.lg-right-end {\n        .lg-object {\n            @include animation(lg-right-end 0.3s);\n            position: relative;\n        }\n    }\n\n    &.lg-left-end {\n        .lg-object {\n            @include animation(lg-left-end 0.3s);\n            position: relative;\n        }\n    }\n}\n\n// lg toolbar\n.lg-toolbar {\n    z-index: $zindex-toolbar;\n    left: 0;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    background-color: $lg-toolbar-bg;\n\n    .lg-icon {\n        color: $lg-toolbar-icon-color;\n        cursor: pointer;\n        float: right;\n        font-size: 24px;\n        height: 47px;\n        line-height: 27px;\n        padding: 10px 0;\n        text-align: center;\n        width: 50px;\n        text-decoration: none !important;\n        outline: medium none;\n        @include transition(color 0.2s linear);\n\n        &:hover {\n            color: $lg-toolbar-icon-hover-color;\n        }\n    }\n\n    .lg-close {\n        &:after {\n            content: \"\\e070\";\n        }\n    }\n\n    .lg-download {\n        &:after {\n            content: \"\\e0f2\";\n        }\n    }\n}\n\n// lightGallery title\n.lg-sub-html {\n    background-color: $lg-sub-html-bg;\n    bottom: 0;\n    color: $lg-sub-html-color;\n    font-size: 16px;\n    left: 0;\n    padding: 10px 40px;\n    position: fixed;\n    right: 0;\n    text-align: center;\n    z-index: $zindex-subhtml;\n\n    h4 {\n        margin: 0;\n        font-size: 13px;\n        font-weight: bold;\n    }\n\n    p {\n        font-size: 12px;\n        margin: 5px 0 0;\n    }\n}\n\n// lg image counter\n#lg-counter {\n    color: $lg-icon-color;\n    display: inline-block;\n    font-size: $lg-counter-font-size;\n    padding-left: 20px;\n    padding-top: 12px;\n    vertical-align: middle;\n}\n\n// for idle state\n.lg-toolbar, .lg-prev, .lg-next {\n    opacity: 1;\n    @include transitionCustom(transform 0.35s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.35s cubic-bezier(0, 0, 0.25, 1) 0s, color 0.2s linear);\n}\n\n.lg-hide-items {\n    .lg-prev {\n        opacity: 0;\n        @include translate3d(-10px, 0, 0);\n    }\n\n    .lg-next {\n        opacity: 0;\n        @include translate3d(10px, 0, 0);\n    }\n\n    .lg-toolbar {\n        opacity: 0;\n        @include translate3d(0, -10px, 0);\n    }\n}\n\n// Starting effect\nbody:not(.lg-from-hash){\n    .lg-outer {\n        &.lg-start-zoom{\n            .lg-object{\n                @include scale3d(0.5, 0.5, 0.5);\n                opacity: 0;\n                @include transitionCustom(transform 250ms cubic-bezier(0, 0, 0.25, 1) 0s, opacity 250ms cubic-bezier(0, 0, 0.25, 1) !important);\n                @include transform-origin(50% 50%);\n            }\n            .lg-item.lg-complete{\n                .lg-object{\n                    @include scale3d(1, 1, 1);\n                    opacity: 1;\n                }\n            }\n        }\n    }\n}", "$backdrop-opacity: 0.90;\r\n$lg-toolbar-bg: rgba(0, 0, 0, 0.45) !default;\r\n$lg-border-radius-base: 2px !default;\r\n$lg-theme-highlight: rgb(169, 7, 7) !default;\r\n$lg-theme: #0D0A0A !default;\r\n\r\n// basic icon colours\r\n$lg-icon-bg: rgba(0, 0, 0, 0.45) !default;\r\n$lg-icon-color: #999 !default;\r\n$lg-icon-hover-color: #FFF !default;\r\n\r\n// counter\r\n$lg-counter-color: #e6e6e6 !default;\r\n$lg-counter-font-size: 1rem !default;\r\n\r\n// Next prev icons\r\n$lg-next-prev-bg: $lg-icon-bg !default;\r\n$lg-next-prev-color: $lg-icon-color !default;\r\n$lg-next-prev-hover-color: $lg-icon-hover-color !default;\r\n\r\n// toolbar icons\r\n$lg-toolbar-icon-color: $lg-icon-color !default;\r\n$lg-toolbar-icon-hover-color: $lg-icon-hover-color !default;\r\n\r\n// autoplay progress bar\r\n$lg-progress-bar-bg: #333 !default;\r\n$lg-progress-bar-active-bg: $lg-theme-highlight !default;\r\n$lg-progress-bar-height: 5px !default;\r\n\r\n// paths\r\n$lg-path-fonts: './fonts'!default;\r\n$lg-path-images: '../../../img'!default;\r\n\r\n// Zoom plugin\r\n$zoom-transition-duration: 0.3s !default;\r\n\r\n// Sub html - titile\r\n$lg-sub-html-bg: transparent !default;\r\n$lg-sub-html-color: #EEE !default;\r\n\r\n// thumbnail toggle button\r\n$lg-thumb-toggle-bg: #232323;\r\n$lg-thumb-toggle-color: $lg-icon-color !default;\r\n$lg-thumb-toggle-hover-color: $lg-icon-hover-color !default;\r\n$lg-thumb-bg: $lg-thumb-toggle-bg;\r\n\r\n// z-index\r\n$zindex-outer: 2150 !default;\r\n$zindex-progressbar: 2183 !default;\r\n$zindex-controls: 2180 !default;\r\n$zindex-toolbar: 2182 !default;\r\n$zindex-subhtml: 2180 !default;\r\n$zindex-thumbnail: 2180 !default;\r\n$zindex-pager: 2180 !default;\r\n$zindex-playbutton: 2180 !default;\r\n$zindex-item: 2160 !default;\r\n$zindex-backdrop: 2140 !default;\r\n", "// Vendor Prefixes\n//\n// All vendor mixins are deprecated as of v3.2.0 due to the introduction of\n// Autoprefixer in our Gruntfile. They will be removed in v4.\n\n// - Animations\n// - Backface visibility\n// - Box shadow\n// - Box sizing\n// - Content columns\n// - Hyphens\n// - Placeholder text\n// - Transformations\n// - Transitions\n// - User Select\n// - cursor grab\n\n// Animations\n@mixin animation($animation) {\n    -webkit-animation: $animation;\n    -o-animation: $animation;\n    animation: $animation;\n}\n\n@mixin animation-name($name) {\n    -webkit-animation-name: $name;\n    animation-name: $name;\n}\n\n@mixin animation-duration($duration) {\n    -webkit-animation-duration: $duration;\n    animation-duration: $duration;\n}\n\n@mixin animation-timing-function($timing-function) {\n    -webkit-animation-timing-function: $timing-function;\n    animation-timing-function: $timing-function;\n}\n\n@mixin animation-delay($delay) {\n    -webkit-animation-delay: $delay;\n    animation-delay: $delay;\n}\n\n@mixin animation-iteration-count($iteration-count) {\n    -webkit-animation-iteration-count: $iteration-count;\n    animation-iteration-count: $iteration-count;\n}\n\n@mixin animation-direction($direction) {\n    -webkit-animation-direction: $direction;\n    animation-direction: $direction;\n}\n\n@mixin animation-fill-mode($fill-mode) {\n    -webkit-animation-fill-mode: $fill-mode;\n    animation-fill-mode: $fill-mode;\n}\n\n@mixin keyframes($name) {\n    @-webkit-keyframes #{$name} {\n        @content;\n    }\n\n    @-moz-keyframes #{$name} {\n        @content;\n    }\n\n    @-ms-keyframes #{$name} {\n        @content;\n    }\n\n    @keyframes #{$name} {\n        @content;\n    }\n}\n\n// Backface visibility\n// Prevent browsers from flickering when using CSS 3D transforms.\n// Default value is `visible`, but can be changed to `hidden`\n\n@mixin backface-visibility($visibility) {\n    -webkit-backface-visibility: $visibility;\n    -moz-backface-visibility: $visibility;\n    backface-visibility: $visibility;\n}\n\n// Drop shadows\n//\n// Note: Deprecated `.box-shadow()` as of v3.1.0 since all of Bootstrap's\n// supported browsers that have box shadow capabilities now support it.\n\n@mixin box-shadow($shadow...) {\n    -webkit-box-shadow: $shadow; // iOS <4.3 & Android <4.1\n    box-shadow: $shadow;\n}\n\n// Box sizing\n@mixin box-sizing($boxmodel) {\n    -webkit-box-sizing: $boxmodel;\n    -moz-box-sizing: $boxmodel;\n    box-sizing: $boxmodel;\n}\n\n// CSS3 Content Columns\n@mixin content-columns($column-count, $column-gap: $grid-gutter-width) {\n    -webkit-column-count: $column-count;\n    -moz-column-count: $column-count;\n    column-count: $column-count;\n    -webkit-column-gap: $column-gap;\n    -moz-column-gap: $column-gap;\n    column-gap: $column-gap;\n}\n\n// Optional hyphenation\n@mixin hyphens($mode: auto) {\n    word-wrap: break-word;\n    -webkit-hyphens: $mode;\n    -moz-hyphens: $mode;\n    -ms-hyphens: $mode; // IE10+\n    -o-hyphens: $mode;\n    hyphens: $mode;\n}\n\n// Transformations\n@mixin scale($ratio...) {\n    -webkit-transform: scale($ratio);\n    -ms-transform: scale($ratio); // IE9 only\n    -o-transform: scale($ratio);\n    transform: scale($ratio);\n}\n\n@mixin scaleX($ratio) {\n    -webkit-transform: scaleX($ratio);\n    -ms-transform: scaleX($ratio); // IE9 only\n    -o-transform: scaleX($ratio);\n    transform: scaleX($ratio);\n}\n\n@mixin scaleY($ratio) {\n    -webkit-transform: scaleY($ratio);\n    -ms-transform: scaleY($ratio); // IE9 only\n    -o-transform: scaleY($ratio);\n    transform: scaleY($ratio);\n}\n\n@mixin skew($x, $y) {\n    -webkit-transform: skewX($x) skewY($y);\n    -ms-transform: skewX($x) skewY($y); // See https://github.com/twbs/bootstrap/issues/4885; IE9+\n    -o-transform: skewX($x) skewY($y);\n    transform: skewX($x) skewY($y);\n}\n\n@mixin translate($x, $y) {\n    -webkit-transform: translate($x, $y);\n    -ms-transform: translate($x, $y); // IE9 only\n    -o-transform: translate($x, $y);\n    transform: translate($x, $y);\n}\n\n@mixin translate3d($x, $y, $z) {\n    -webkit-transform: translate3d($x, $y, $z);\n    transform: translate3d($x, $y, $z);\n}\n\n@mixin scale3d($x, $y, $z) {\n    -webkit-transform: scale3d($x, $y, $z);\n    transform: scale3d($x, $y, $z);\n}\n\n@mixin rotate($degrees) {\n    -webkit-transform: rotate($degrees);\n    -ms-transform: rotate($degrees); // IE9 only\n    -o-transform: rotate($degrees);\n    transform: rotate($degrees);\n}\n\n@mixin rotateX($degrees) {\n    -webkit-transform: rotateX($degrees);\n    -ms-transform: rotateX($degrees); // IE9 only\n    -o-transform: rotateX($degrees);\n    transform: rotateX($degrees);\n}\n\n@mixin rotateY($degrees) {\n    -webkit-transform: rotateY($degrees);\n    -ms-transform: rotateY($degrees); // IE9 only\n    -o-transform: rotateY($degrees);\n    transform: rotateY($degrees);\n}\n\n@mixin perspective($perspective) {\n    -webkit-perspective: $perspective;\n    -moz-perspective: $perspective;\n    perspective: $perspective;\n}\n\n@mixin perspective-origin($perspective) {\n    -webkit-perspective-origin: $perspective;\n    -moz-perspective-origin: $perspective;\n    perspective-origin: $perspective;\n}\n\n@mixin transform-origin($origin) {\n    -webkit-transform-origin: $origin;\n    -moz-transform-origin: $origin;\n    -ms-transform-origin: $origin; // IE9 only\n    transform-origin: $origin;\n}\n\n@mixin transform($transforms) {\n    -moz-transform: $transforms;\n    -o-transform: $transforms;\n    -ms-transform: $transforms;\n    -webkit-transform: $transforms;\n    transform: $transforms;\n}\n\n// Transitions\n\n@mixin transition($transition...) {\n    -webkit-transition: $transition;\n    -o-transition: $transition;\n    transition: $transition;\n}\n\n@mixin transition-property($transition-property...) {\n    -webkit-transition-property: $transition-property;\n    transition-property: $transition-property;\n}\n\n@mixin transition-delay($transition-delay) {\n    -webkit-transition-delay: $transition-delay;\n    transition-delay: $transition-delay;\n}\n\n@mixin transition-duration($transition-duration...) {\n    -webkit-transition-duration: $transition-duration;\n    transition-duration: $transition-duration;\n}\n\n@mixin transition-timing-function($timing-function) {\n    -webkit-transition-timing-function: $timing-function;\n    transition-timing-function: $timing-function;\n}\n\n@mixin transition-transform($transition...) {\n    -webkit-transition: -webkit-transform $transition;\n    -moz-transition: -moz-transform $transition;\n    -o-transition: -o-transform $transition;\n    transition: transform $transition;\n}\n\n// transition custom\n\n@function prefix($property, $prefixes: webkit moz o ms) {\n    $vendor-prefixed-properties: transform background-clip background-size;\n    $result: ();\n\n    @each $prefix in $prefixes {\n        @if index($vendor-prefixed-properties, $property) {\n            $property: -#{$prefix}-#{$property};\n        }\n        $result: append($result, $property);\n    }\n    @return $result;\n}\n\n@function trans-prefix($transition, $prefix: moz) {\n    $prefixed: ();\n\n    @each $trans in $transition {\n        $prop-name: nth($trans, 1);\n        $vendor-prop-name: prefix($prop-name, $prefix);\n        $prop-vals: nth($trans, 2);\n        $prefixed: append($prefixed, $vendor-prop-name $prop-vals, comma);\n    }\n    @return $prefixed;\n}\n\n@mixin transitionCustom($values...) {\n    $transitions: ();\n\n    @each $declaration in $values {\n        $prop: nth($declaration, 1);\n        $prop-opts: ();\n        $length: length($declaration);\n\n        @if $length >= 2 {\n            @for $i from 2 through $length {\n                $prop-opts: append($prop-opts, nth($declaration, $i));\n            }\n        }\n        $trans: $prop, $prop-opts;\n        $transitions: append($transitions, $trans, comma);\n    }\n    -webkit-transition: trans-prefix($transitions, webkit);\n    -moz-transition: trans-prefix($transitions, moz);\n    -o-transition: trans-prefix($transitions, o);\n    transition: $values;\n}\n\n// User select\n// For selecting text on the page\n\n@mixin user-select($select) {\n    -webkit-user-select: $select;\n    -moz-user-select: $select;\n    -ms-user-select: $select; // IE10+\n    user-select: $select;\n}\n\n// mouse grab\n\n@mixin grab-cursor {\n    cursor: -webkit-grab;\n    cursor: -moz-grab;\n    cursor: -o-grab;\n    cursor: -ms-grab;\n    cursor: grab;\n}\n\n@mixin grabbing-cursor {\n    cursor: move;\n    cursor: -webkit-grabbing;\n    cursor: -moz-grabbing;\n    cursor: -o-grabbing;\n    cursor: -ms-grabbing;\n    cursor: grabbing;\n}\n", ".lg-outer {\n    .lg-thumb-outer {\n        background-color: $lg-thumb-bg;\n        bottom: 0;\n        position: absolute;\n        width: 100%;\n        z-index: $zindex-thumbnail;\n        max-height: 350px;\n        @include translate3d(0, 100%, 0);\n        @include transitionCustom(transform 0.25s cubic-bezier(0, 0, 0.25, 1) 0s);\n\n        &.lg-grab {\n            .lg-thumb-item {\n                @include grab-cursor;\n            }\n        }\n\n        &.lg-grabbing {\n            .lg-thumb-item {\n                @include grabbing-cursor;\n            }\n        }\n\n        &.lg-dragging {\n            .lg-thumb {\n                @include transition-duration(0s !important);\n            }\n        }\n    }\n    &.lg-thumb-open{\n        .lg-thumb-outer {\n            @include translate3d(0, 0%, 0);\n        }\n    }\n\n    .lg-thumb {\n        padding: 10px 0;\n        height: 100%;\n        margin-bottom: -5px;\n    }\n\n    .lg-thumb-item {\n        border-radius: 5px;\n        cursor: pointer;\n        float: left;\n        overflow: hidden;\n        height: 100%;\n        border: 2px solid #FFF;\n        border-radius: 4px;\n        margin-bottom: 5px;\n        @media (min-width: 1025px) {\n            @include transition(border-color 0.25s ease);\n        }\n\n        &.active, &:hover {\n            border-color: $lg-theme-highlight;\n        }\n\n        img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n        }\n    }\n\n    &.lg-has-thumb {\n        .lg-item {\n            padding-bottom: 120px;\n        }\n    }\n\n    &.lg-can-toggle {\n        .lg-item {\n            padding-bottom: 0;\n        }\n    }\n    &.lg-pull-caption-up{\n        .lg-sub-html {\n            @include transition(bottom 0.25s ease);\n        }\n        &.lg-thumb-open{\n            .lg-sub-html {\n                bottom: 100px;\n            }\n        }\n    }\n\n    .lg-toogle-thumb {\n        background-color: $lg-thumb-toggle-bg;\n        border-radius: $lg-border-radius-base $lg-border-radius-base 0 0;\n        color: $lg-thumb-toggle-color;\n        cursor: pointer;\n        font-size: 24px;\n        height: 39px;\n        line-height: 27px;\n        padding: 5px 0;\n        position: absolute;\n        right: 20px;\n        text-align: center;\n        top: -39px;\n        width: 50px;\n\n        &:after {\n            content: \"\\e1ff\";\n        }\n\n        &:hover {\n            color: $lg-thumb-toggle-hover-color;\n        }\n    }\n}", ".lg-outer {\n    .lg-video-cont {\n        display: inline-block;\n        vertical-align: middle;\n        max-width: 1140px;\n        max-height: 100%;\n        width: 100%;\n        padding: 0 5px;\n    }\n\n    .lg-video {\n        width: 100%;\n        height: 0;\n        padding-bottom: 56.25%;\n        overflow: hidden;\n        position: relative;\n\n        .lg-object {\n            display: inline-block;\n            position: absolute;\n            top: 0;\n            left: 0;\n            width: 100% !important;\n            height: 100% !important;\n        }\n\n        .lg-video-play {\n            width: 84px;\n            height: 59px;\n            position: absolute;\n            left: 50%;\n            top: 50%;\n            margin-left: -42px;\n            margin-top: -30px;\n            z-index: $zindex-playbutton;\n            cursor: pointer;\n        }\n    }\n\n    .lg-has-iframe {\n        .lg-video {\n            -webkit-overflow-scrolling: touch;\n            overflow: auto;\n        }\n    }\n\n    .lg-has-vimeo{\n        .lg-video-play{\n            background: url(\"#{$lg-path-images}/vimeo-play.png\") no-repeat scroll 0 0 transparent;\n        }\n        &:hover{\n            .lg-video-play{\n                background: url(\"#{$lg-path-images}/vimeo-play.png\") no-repeat scroll 0 -58px transparent;\n            } \n\n        }  \n    }\n\n    .lg-has-html5{\n        .lg-video-play{\n            background: transparent url(\"#{$lg-path-images}/video-play.png\") no-repeat scroll 0 0;\n            height: 64px;\n            margin-left: -32px;\n            margin-top: -32px;\n            width: 64px;\n            opacity: 0.8;\n        }  \n        &:hover{\n            .lg-video-play{\n                opacity: 1\n            } \n\n        } \n    }\n\n    .lg-has-youtube{\n        .lg-video-play{\n            background: url(\"#{$lg-path-images}/youtube-play.png\") no-repeat scroll 0 0 transparent;\n        }\n        &:hover{\n            .lg-video-play{\n                background: url(\"#{$lg-path-images}/youtube-play.png\") no-repeat scroll 0 -60px transparent;\n            } \n\n        }  \n    }\n    .lg-video-object {\n        width: 100% !important;\n        height: 100% !important;\n        position: absolute;\n        top: 0;\n        left: 0;\n    }\n\n    .lg-has-video {\n        .lg-video-object {\n            visibility: hidden;\n        }\n\n        &.lg-video-playing {\n            .lg-object, .lg-video-play {\n                display: none;\n            }\n\n            .lg-video-object {\n                visibility: visible;\n            }\n        }\n    }\n}", ".lg-progress-bar {\n    background-color: $lg-progress-bar-bg;\n    height: $lg-progress-bar-height;\n    left: 0;\n    position: absolute;\n    top: 0;\n    width: 100%;\n    z-index: $zindex-progressbar;\n    opacity: 0;\n    @include transitionCustom(opacity 0.08s ease 0s);\n\n    .lg-progress {\n        background-color: $lg-progress-bar-active-bg;\n        height: $lg-progress-bar-height;\n        width: 0;\n    }\n\n    &.lg-start {\n        .lg-progress {\n            width: 100%;\n        }\n    }\n\n    .lg-show-autoplay & {\n        opacity: 1;\n    }\n}\n\n.lg-autoplay-button {\n    &:after {\n        .lg-show-autoplay & {\n            content: \"\\e01a\";\n        }\n        content: \"\\e01d\";\n    }\n}", ".lg-outer {\n    // reset transition duration\n    &.lg-css3.lg-zoom-dragging {\n        .lg-item.lg-complete.lg-zoomable {\n            .lg-img-wrap, .lg-image {\n                @include transition-duration(0s);\n            }\n        }\n    }\n    &.lg-use-transition-for-zoom {\n        .lg-item.lg-complete.lg-zoomable {\n            .lg-img-wrap {\n                @include transitionCustom(transform $zoom-transition-duration cubic-bezier(0, 0, 0.25, 1) 0s);\n            }\n        }\n    }\n    &.lg-use-left-for-zoom {\n        .lg-item.lg-complete.lg-zoomable {\n            .lg-img-wrap {\n                @include transitionCustom(left $zoom-transition-duration cubic-bezier(0, 0, 0.25, 1) 0s, top $zoom-transition-duration cubic-bezier(0, 0, 0.25, 1) 0s);\n            }\n        }\n    }\n\n    .lg-item.lg-complete.lg-zoomable{\n\n        .lg-img-wrap {\n            @include translate3d(0, 0, 0);\n            @include backface-visibility(hidden);\n        }\n\n        .lg-image {\n            // Translate required for zoom\n            @include scale3d(1, 1, 1);\n            @include transitionCustom(transform $zoom-transition-duration cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.15s !important);\n            @include transform-origin(0 0);\n            @include backface-visibility(hidden);\n        }\n    }\n\n}\n\n// zoom buttons\n#lg-zoom-in {\n    &:after {\n        content: \"\\e311\";\n    }\n}\n\n#lg-actual-size {\n    font-size: 20px;\n    &:after {\n        content: \"\\e033\";\n    }\n}\n\n#lg-zoom-out {\n    opacity: 0.5;\n    pointer-events: none;\n\n    &:after {\n        content: \"\\e312\";\n    }\n\n    .lg-zoomed & {\n        opacity: 1;\n        pointer-events: auto;\n    }\n}", ".lg-outer {\n    .lg-pager-outer {\n        bottom: 60px;\n        left: 0;\n        position: absolute;\n        right: 0;\n        text-align: center;\n        z-index: $zindex-pager;\n        height: 10px;\n\n        &.lg-pager-hover {\n            .lg-pager-cont {\n                overflow: visible;\n            }\n        }\n    }\n\n    .lg-pager-cont {\n        cursor: pointer;\n        display: inline-block;\n        overflow: hidden;\n        position: relative;\n        vertical-align: top;\n        margin: 0 5px;\n\n        &:hover {\n            .lg-pager-thumb-cont {\n                opacity: 1;\n                @include translate3d(0, 0, 0);\n            }\n        }\n\n        &.lg-pager-active {\n            .lg-pager {\n                box-shadow: 0 0 0 2px white inset;\n            }\n        }\n    }\n\n    .lg-pager-thumb-cont {\n        background-color: #fff;\n        color: #FFF;\n        bottom: 100%;\n        height: 83px;\n        left: 0;\n        margin-bottom: 20px;\n        margin-left: -60px;\n        opacity: 0;\n        padding: 5px;\n        position: absolute;\n        width: 120px;\n        border-radius: 3px;\n        @include transitionCustom(opacity 0.15s ease 0s, transform 0.15s ease 0s);\n        @include translate3d(0, 5px, 0);\n\n        img {\n            width: 100%;\n            height: 100%;\n        }\n    }\n\n    .lg-pager {\n        background-color: rgba(255, 255, 255, 0.5);\n        border-radius: 50%;\n        box-shadow: 0 0 0 8px rgba(255, 255, 255, 0.7) inset;\n        display: block;\n        height: 12px;\n        @include transition(box-shadow 0.3s ease 0s);\n        width: 12px;\n\n        &:hover, &:focus {\n            box-shadow: 0 0 0 8px white inset;\n        }\n    }\n\n    .lg-caret {\n        border-left: 10px solid transparent;\n        border-right: 10px solid transparent;\n        border-top: 10px dashed;\n        bottom: -10px;\n        display: inline-block;\n        height: 0;\n        left: 50%;\n        margin-left: -5px;\n        position: absolute;\n        vertical-align: middle;\n        width: 0;\n    }\n}", ".lg-fullscreen {\n    &:after {\n        content: \"\\e20c\";\n\n        .lg-fullscreen-on & {\n            content: \"\\e20d\";\n        }\n    }\n}", "@import \"lg-variables\";\n@import \"lg-mixins\";\n.lg-outer {\n\n    #lg-dropdown-overlay {\n        background-color: rgba(0, 0, 0, 0.25);\n        bottom: 0;\n        cursor: default;\n        left: 0;\n        position: fixed;\n        right: 0;\n        top: 0;\n        z-index: $zindex-toolbar - 1;\n        opacity: 0;\n        visibility: hidden;\n        @include transition(visibility 0s linear 0.18s, opacity 0.18s linear 0s);\n    }\n    &.lg-dropdown-active{\n        .lg-dropdown, #lg-dropdown-overlay {\n            @include transition-delay(0s);\n            @include transform(translate3d(0, 0px, 0));\n            opacity: 1;\n            visibility: visible;\n        }\n        #lg-share {\n            color: #FFF;\n        }\n    }\n    .lg-dropdown {\n        background-color: #fff;\n        border-radius: 2px;\n        font-size: 14px;\n        list-style-type: none;\n        margin: 0;\n        padding: 10px 0;\n        position: absolute;\n        right: 0;\n        text-align: left;\n        top: 50px;\n        opacity: 0;\n        visibility: hidden;\n        @include transform(translate3d(0, 5px, 0));\n        @include transitionCustom(transform 0.18s linear 0s, visibility 0s linear 0.5s, opacity 0.18s linear 0s);\n\n        &:after {\n            content: \"\";\n            display: block;\n            height: 0;\n            width: 0;\n            position: absolute;\n            border: 8px solid transparent;\n            border-bottom-color: #FFF;\n            right: 16px;\n            top: -16px\n        }\n        > li {\n            &:last-child {\n                margin-bottom: 0px;\n            }\n            &:hover {\n                a, .lg-icon {\n                    color: #333;\n                }\n            }\n        }\n        a {\n            color: #333;\n            display: block;\n            white-space: pre;\n            padding: 4px 12px;\n            font-family: \"Open Sans\",\"Helvetica Neue\",Helvetica,Arial,sans-serif;\n            font-size: 12px;\n            &:hover {\n                background-color: rgba(0, 0, 0, 0.07);\n            }\n\n        }\n        .lg-dropdown-text {\n            display: inline-block;\n            line-height: 1;\n            margin-top: -3px;\n            vertical-align: middle;\n        }\n        .lg-icon {\n            color: #333;\n            display: inline-block;\n            float: none;\n            font-size: 20px;\n            height: auto;\n            line-height: 1;\n            margin-right: 8px;\n            padding: 0;\n            vertical-align: middle;\n            width: auto;\n        }\n    }\n    #lg-share {\n        position: relative;\n        &:after {\n            content: \"\\e80d\";\n        }\n    }\n    #lg-share-facebook {\n        .lg-icon{\n            color: #3b5998;\n            &:after {\n                content: \"\\e901\";\n            }\n        }   \n    }\n    #lg-share-twitter {\n        .lg-icon{\n            color: #00aced;\n            &:after {\n                content: \"\\e904\";\n            }\n        }  \n    }\n    #lg-share-googleplus {\n        .lg-icon{\n            color: #dd4b39;\n            &:after {\n                content: \"\\e902\";\n            }\n        }  \n    }\n    #lg-share-pinterest {\n        .lg-icon{\n            color: #cb2027;\n            &:after {\n                content: \"\\e903\";\n            }\n        }  \n    }\n}", "// Clearfix\n.lg-group:after {\n    content: \"\";\n    display: table;\n    clear: both;\n}\n\n// lightgallery core\n.lg-outer {\n    width: 100%;\n    height: 100%;\n    position: fixed;\n    top: 0;\n    left: 0;\n    z-index: $zindex-outer;\n    text-align: left;\n    opacity: 0;\n    // For start/end transition\n    @include transition(opacity 0.15s ease 0s);\n\n    * {\n        @include box-sizing(border-box);\n    }\n\n    &.lg-visible {\n        opacity: 1;\n    }\n\n    // Set transition speed and timing function\n    &.lg-css3 {\n        .lg-item {\n            &.lg-prev-slide, &.lg-next-slide, &.lg-current {\n                @include transition-duration(inherit !important);\n                @include transition-timing-function(inherit !important);\n            }\n        }\n    }\n\n    // Remove transition while dragging\n    &.lg-css3.lg-dragging {\n        .lg-item {\n            &.lg-prev-slide, &.lg-next-slide, &.lg-current {\n                @include transition-duration(0s !important);\n                opacity: 1;\n            }\n        }\n    }\n\n    // Set cursor grab while dragging\n    &.lg-grab {\n        img.lg-object {\n            @include grab-cursor;\n        }\n    }\n\n    &.lg-grabbing {\n        img.lg-object {\n            @include grabbing-cursor;\n        }\n    }\n\n    .lg {\n        height: 100%;\n        width: 100%;\n        position: relative;\n        overflow: hidden;\n        margin-left: auto;\n        margin-right: auto;\n        max-width: 100%;\n        max-height: 100%;\n    }\n\n    .lg-inner {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n        left: 0;\n        top: 0;\n        white-space: nowrap;\n    }\n\n    .lg-item {\n        background: url(\"#{$lg-path-images}/loading.gif\") no-repeat scroll center center transparent;\n        display: none !important;\n    }\n    &.lg-css3 {\n        .lg-prev-slide, .lg-current, .lg-next-slide {\n            display: inline-block !important;\n        }\n    }\n    &.lg-css {\n        .lg-current {\n            display: inline-block !important;\n        }\n    }\n\n    .lg-item, .lg-img-wrap {\n        display: inline-block;\n        text-align: center;\n        position: absolute;\n        width: 100%;\n        height: 100%;\n\n        &:before {\n            content: \"\";\n            display: inline-block;\n            height: 50%;\n            width: 1px;\n            margin-right: -1px;\n        }\n    }\n\n    .lg-img-wrap {\n        position: absolute;\n        padding: 0 5px;\n        left: 0;\n        right: 0;\n        top: 0;\n        bottom: 0\n    }\n\n    .lg-item {\n        &.lg-complete {\n            background-image: none;\n        }\n\n        &.lg-current {\n            z-index: $zindex-item;\n        }\n    }\n\n    .lg-image {\n        display: inline-block;\n        vertical-align: middle;\n        max-width: 100%;\n        max-height: 100%;\n        width: auto !important;\n        height: auto !important;\n    }\n\n    &.lg-show-after-load {\n        .lg-item {\n            .lg-object, .lg-video-play {\n                opacity: 0;\n                @include transition(opacity 0.15s ease 0s);\n            }\n\n            &.lg-complete {\n                .lg-object, .lg-video-play {\n                    opacity: 1;\n                }\n            }\n        }\n    }\n\n    // Hide title div if empty\n    .lg-empty-html {\n        display: none;\n    }\n\n    &.lg-hide-download {\n        #lg-download {\n            display: none;\n        }\n    }\n}\n\n.lg-backdrop {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: $zindex-backdrop;\n    background-color: #000;\n    opacity: 0;\n    @include transition(opacity 0.15s ease 0s);\n    &.in {\n        opacity: $backdrop-opacity;\n    }\n}\n\n// Default slide animations. Should be placed at the bottom of the animation css\n.lg-css3 {\n\n    // Remove all transition effects\n    &.lg-no-trans {\n        .lg-prev-slide, .lg-next-slide, .lg-current {\n            @include transitionCustom(none 0s ease 0s !important);\n        }\n    }\n\n    &.lg-use-css3 {\n        .lg-item {\n            @include backface-visibility(hidden);\n        }\n    }\n\n    &.lg-use-left {\n        .lg-item {\n            @include backface-visibility(hidden);\n        }\n    }\n\n    // Fade mode\n    &.lg-fade {\n        .lg-item {\n            opacity: 0;\n\n            &.lg-current {\n                opacity: 1;\n            }\n\n            // transition timing property and duration will be over written from javascript\n            &.lg-prev-slide, &.lg-next-slide, &.lg-current {\n                @include transitionCustom(opacity 0.1s ease 0s);\n            }\n        }\n    }\n\n    &.lg-slide {\n        &.lg-use-css3 {\n            .lg-item {\n                opacity: 0;\n\n                &.lg-prev-slide {\n                    @include translate3d(-100%, 0, 0);\n                }\n\n                &.lg-next-slide {\n                    @include translate3d(100%, 0, 0);\n                }\n\n                &.lg-current {\n                    @include translate3d(0, 0, 0);\n                    opacity: 1;\n                }\n\n                // transition timing property and duration will be over written from javascript\n                &.lg-prev-slide, &.lg-next-slide, &.lg-current {\n                    @include transitionCustom(transform 1s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.1s ease 0s);\n                }\n            }\n        }\n\n        &.lg-use-left {\n            .lg-item {\n                opacity: 0;\n                position: absolute;\n                left: 0;\n\n                &.lg-prev-slide {\n                    left: -100%;\n                }\n\n                &.lg-next-slide {\n                    left: 100%;\n                }\n\n                &.lg-current {\n                    left: 0;\n                    opacity: 1;\n                }\n\n                // transition timing property and duration will be over written from javascript\n                &.lg-prev-slide, &.lg-next-slide, &.lg-current {\n                    @include transitionCustom(left 1s cubic-bezier(0, 0, 0.25, 1) 0s, opacity 0.1s ease 0s);\n                }\n            }\n        }\n    }\n}\n", ".lg-icon {\r\n\tfont-family: 'Font Awesome 5 Pro';\r\n}\r\n\r\n.lg-actions {\r\n\t.lg-next:before {\r\n    \tcontent: \"\\f178\";\r\n\t}\r\n\t.lg-prev:after {\r\n\t\tcontent: \"\\f177\";\r\n\t}\r\n}\r\n\r\n.lg-outer {\r\n\t.lg-toogle-thumb:after {\r\n\t\tcontent: \"\\f07d\";\r\n\t}\r\n}\r\n\r\n.lg-toolbar {\r\n\t.lg-close:after {\r\n\t\tcontent: \"\\f00d\";\r\n\t}\r\n\t.lg-download:after {\r\n\t\tcontent: \"\\f33d\";\r\n\t}\r\n\t.lg-fullscreen:after {\r\n\t\tcontent: \"\\f320\"\r\n\t}\r\n\t\r\n}\r\n\r\n.lg-autoplay-button:after {\r\n\tcontent: \"\\f144\"\r\n}\r\n\r\n.lg-show-autoplay .lg-autoplay-button:after {\r\n\tcontent: \"\\f28b\"\r\n}\r\n\r\n#lg-zoom-in:after {\r\n\tcontent: \"\\f00e\"\r\n}\r\n\r\n#lg-zoom-out:after {\r\n\tcontent: \"\\f010\"\r\n}\r\n\r\n#lg-actual-size:after {\r\n\tcontent: \"\\f002\"\r\n}\r\n\r\n.lg-outer #lg-share:after {\r\n\tcontent: \"\\f1e0\"\r\n}\r\n\r\n#lg-download {\r\n\tborder-bottom: 0 !important;\r\n}\r\n\r\n.lg-toolbar {\r\n\t background: linear-gradient(to bottom, rgba($black,1) 0%,rgba($black,0) 100%);\r\n\t padding-right: 1rem;\r\n\r\n\t.lg-icon {\r\n\t\theight: 5rem;\r\n\t\tline-height: 5rem;\r\n\t\tpadding: 0;\r\n\t}\r\n}\r\n\r\n\r\n#lg-counter {\r\n\tpadding-top: 26px;\r\n}\r\n\r\n.lg-outer .lg-toogle-thumb {\r\n\tfont-size: 21px;\r\n\theight: 50px;\r\n\ttop: -50px;\r\n    line-height: 50px;\r\n    padding: 0;\r\n    border-radius: 50% 50% 0 0;\r\n\r\n    .lg-thumb {\r\n    \tpadding: 1rem 0;\r\n    }\r\n}\r\n\r\n.lg-actions .lg-next, \r\n.lg-actions .lg-prev {\r\n\theight: 50px;\r\n\twidth: 50px;\r\n\tline-height: 50px;\r\n\tpadding: 0;\r\n\tborder-radius: 50%;\r\n\r\n    transition: all 0.2s ease;\r\n\r\n\t&:hover {\r\n\t\tbackground-color: $info-500;\r\n\t}\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n}\r\n\r\n\r\n.lg-outer .lg-thumb-item.active, \r\n.lg-outer .lg-thumb-item:hover {\r\n\tborder-color: $danger-500;\r\n}\r\n\r\n.lg-toolbar .lg-icon {\r\n\tfont-size: 1.3rem;\r\n}"]}