{"version": 3, "sources": ["app.bundle.css", "_imports/_global-import.scss", "_mixins/mixins.scss", "_modules/variables.scss", "_modules/_fonts.scss", "_modules/_placeholders.scss", "../../node_modules/bootstrap/scss/_variables.scss", "_modules/_root.scss", "_modules/_reset.scss", "_modules/_body.scss", "_modules/_page-header.scss", "_modules/_page-logo.scss", "_modules/_page-search.scss", "_modules/_dropdown-icon-menu.scss", "_modules/_dropdown-notification.scss", "_modules/_dropdown-app-list.scss", "_modules/_header-function-fixed.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "_modules/_left-panel.scss", "_modules/_nav.scss", "_modules/_nav-listfilter.scss", "_modules/_nav-info-card.scss", "_modules/_nav-function-top.scss", "_modules/_nav-function-hidden.scss", "_modules/_nav-function-fixed.scss", "_modules/_nav-function-minify.scss", "_modules/_nav-footer.scss", "_modules/_page-wrapper.scss", "_modules/_page-heading.scss", "_modules/_page-content.scss", "_modules/_page-footer.scss", "_modules/_page-error.scss", "_modules/_page-components-accordion.scss", "_modules/_page-components-alerts.scss", "_modules/_page-components-badge.scss", "_modules/_page-components-breadcrumb.scss", "_modules/_page-components-buttons.scss", "_modules/_page-components-cards.scss", "_modules/_page-components-carousel.scss", "_modules/_page-components-dropdowns.scss", "_modules/_page-components-icon-stack.scss", "_modules/_page-components-listfilter.scss", "_modules/_page-components-loader.scss", "_modules/_page-components-messanger.scss", "_modules/_page-components-modal.scss", "_modules/_page-components-pagination.scss", "_modules/_page-components-panels.scss", "_modules/_page-components-popovers.scss", "_modules/_page-components-progressbar.scss", "_modules/_page-components-shortcut.scss", "_modules/_page-components-side-panels.scss", "_modules/_page-components-tables.scss", "_modules/_page-components-tabs.scss", "_modules/_page-components-tooltips.scss", "_modules/_helpers.scss", "_modules/_misc.scss", "_modules/_effects.scss", "_modules/_hack.scss", "_modules/_hack-ie.scss", "_modules/_responsive.scss", "_modules/_light-levels.scss", "_modules/_forms.scss", "_modules/_translate-3d.scss", "_modules/_settings-demo-incompatiblity-list.scss", "_modules/_settings-demo-theme-colors.scss", "_modules/_settings-demo.scss", "_modules/_demo-only.scss", "_extensions/_extension-pace.scss", "_extensions/_extension-slimscroll.scss", "_extensions/_extension-waves.scss", "_modules/_keyframes-general.scss", "_modules/_keyframes-highlight.scss", "_modules/_keyframes-spinner.scss", "_modules/_keyframes-transition.scss", "_modules/_mod-bg.scss", "_modules/_mod-clean-page-bg.scss", "_modules/_mod-colorblind.scss", "_modules/_mod-disable-animation.scss", "_modules/_mod-hide-info-card.scss", "_modules/_mod-hide-nav-icons.scss", "_modules/_mod-high-contrast.scss", "_modules/_mod-lean-page-header.scss", "_modules/_mod-main-boxed.scss", "_modules/_mod-nav-accessibility.scss", "_modules/_mod-text-size.scss", "_modules/_colors.scss", "_modules/_app-custom-scrollbar.scss", "_modules/_app-fullscreen.scss", "_modules/_app-print.scss"], "names": [], "mappings": "AAAA,gBAAgB;AAAhB;4EAE4E;ACF5E;4EDI4E;AEJ5E;;;;;sDFUsD;AEuNtD;;;;;;;;;;;;yBF1MyB;AEsOzB;;;yBFlOyB;AEsQzB;;;;;;;;;;;yBF1PyB;AEoRzB;;;yBFhRyB;AE0TzB,wBAAA;AAQA,0BAAA;ADtWA;4EDwC4E;AG/C5E;4EHiD4E;AG/C5E,+CAAA;AAQA;;;;;;kFH+CkF;AGvClF;4EHyC4E;AGnC5E;4EHqC4E;AGnC5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AA6EA;4EHzG4E;AG6G5E;4EH3G4E;AG0HR,kGAAA;AACG,2EAAA;AAavE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EH1J4E;AGmL5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAKA,iBAAA;AASA,WAAA;AAEA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;CHnSC;AGgUD,UAAA;AAuBA,aAAA;AAIA;4EHvV4E;AG+V5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EH/V4E;AGiWlE,mFAAA;AAOV;4EHrW4E;AGuWG,mEAAA;AAE/E;4EHtW4E;AG4W5E,oEAAA;AAUA;4EHnX4E;AGuX5E;4EHrX4E;AGuX5B,0BAAA;AACH,iBAAA;AAG7C;4EHvX4E;AG4X5E;4EH1X4E;AGgY5E;4EH9X4E;AGkY5E;4EHhY4E;AGmY5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACC,8DAAA;AACC,qDAAA;AAEtD,gCAAA;AAGA,qBAAA;AAC4D,uBAAA;AAO5D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4EHnZ4E;AGkb5E,oBAAA;AACA,eAAA;AAMA,uBAAA;AAOA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4EHne4E;AGqe5E,eAAA;AAIA;4EHte4E;AG6e5E;4EH3e4E;AG+e5E;4EH7e4E;AG0f5E;4EHxf4E;AG+f5E;4EH7f4E;AGqgB5E;4EHngB4E;AG2gB5E;4EHzgB4E;AG8gB5E,oBAAA;ACjwBA,iFAAY;AAEZ;EACC,yDDkf8D;ECjf9D,oBFsPkC;EErPlC,qBAAqB,EAAA;;AAGtB;EACC,cD4dwC,EAAA;;ACzdzC;EACC,gBAAgB;EAChB,gBAAgB,EAAA;;AAIjB;EACC,gBAAgB,EAAA;;AAGjB;;;;;;;;;;;;EAYI,gBAAgB;EAChB,cAAc;EACjB,oBFsNkC;EErN/B,gBAAgB;EAEhB,oBAA0C,EAAA;;AAG9C;;;;EAIC,oBF4MkC,EAAA;;AEzMnC;;EAEC,mBFuMkC,EAAA;;AEpMnC;;;;EAIC,oBFgMkC,EAAA;;AE7LnC,kBAAA;AACA;EACC,cAA2B,EAAA;;AAG5B,kBAAA;AACA;EAEC,sJAAsG;EAAtG,wFAAsG;EACtG,cDnE2B;ECoExB,qBAAqB;EACrB,4BAA4B;EAC5B,6BAA6B;EAC7B,oCAAoC;EACpC,iBAAiB,EAAA;;AAGrB,+CAAA;AC9EA;;;;;;;;;;;;;;;;;;;;;;;;;;CLiVC;AKrTD;EAEC,gHAA6E;EAA7E,gFAA6E;EAC7E,yBFumBiD,EAAA;;AEpmBlD;;;;;;;;;CL8TC;AKpTD;EHpCE,yBGqCoC;EHnCpC,6FAAsD;EAAtD,2DAAsD;EGoCvD,WAAW;EACX,oCAAiC;EACjC,wBAAgB;UAAhB,gBAAgB,EAAA;EAEhB;IACC,wBAAgB;YAAhB,gBAAgB;IAChB,yBAAyB;IACzB,WAAW;IACX,UAAU,EAAA;EAGX;IACC,gCAAqC;IACrC,UAAU,EAAA;EAGX;IACC,mBFrB6C;IEsB7C,WCzDa;ID0Db,kEAA0D;YAA1D,0DAA0D,EAAA;;AAI5D;;EAuBC,gBAAgB;EAChB,kBAAkB;EAClB,iCAAiC,EAAA;EAvBjC;;IACC,6BAA6B,EAAA;EAG9B;;IACC,sBAAsB,EAAA;EAGvB;;IACC,WAAW;IACX,UAAU,EAAA;EAGX;;IACC,WAAW,EAAA;EAVZ;;IAcA,sBAAsB,EAAA;;AAkCvB;EACC,8DFgYkE;EEhYlE,sDFgYkE,EAAA;;AE7XnE;EHtCI,wCGuCiC;EHpCjC,gCGoCiC,EAAA;;AAIrC;;EH3CI,wCG4CiC;EHzCjC,gCGyCiC,EAAA;;AAOrC;EACC,4CAAoC;EAApC,oCAAoC,EAAA;;AHwMjC;EGvLH,WCvJc;EDwJd,8BAAoC,EAAA;EHsLjC;IGpLF,wBAAuB;IACvB,oBAAmB;IACnB,sBAAqB,EAAA;EHkLnB;IG/KF,uBAAuB;IACvB,mBAAkB;IAClB,qBAAoB;IACpB,2BAA4B;IAC5B,yBAA+B,EAAA;EH2K7B;IGvKF,gBAAe;IACf,cF5K0B,EAAA;;AEgL5B;EACC,sBAAsB,EAAA;;AAGvB;;;;;EACE,kBAAiB,EAAA;EAEjB;IACC,yBAA0C,EAAA;EAE3C;IACC,yBAAyC,EAAA;EAE1C;;;;;IACC,mBAAmB,EAAA;;AAiCtB;;ELmOE;AK/NF;EHzLI,kBCuRqB;EE3FxB,2BAA0C;EAC1C,eFmWoD;EElWpD,cFmW+B;EElW/B,sBAAsB;EACtB,qBAA0C;EAC1C,uBAAmD;EACnD,eFgW8B;EE/V9B,eF0d8B;EEzd9B,eAAe;EACf,cFwV8C;EEvV9C,kBAAkB;EAGnB;;;GLgOG,EK7NC;EAEH;IACC,wBAAgB;YAAhB,gBAAgB;IAChB,qBF1P0B;IE2P1B,mBFpN6C;IEqN7C,WCxPa,EAAA;;AD8Pf;EACC,wDAAgD;UAAhD,gDAAgD;EAChD,4CAAyC;EACzC,uBAAuB;EACvB,gBClQc;EDmQd,uBAAuB,EAAA;;AAGxB;EACC,mBFtQ8E;EEuQ9E,gCAA2D;EH3IxD,yDG4IkD;UH5IlD,iDG4IkD;EACrD,sBAAyC,EAAA;;AAI1C;;;;;;;;;;;;;;;;;;;;;;;;GL6OG;AKlNH;EACC,WAAW;EACX,YAAY;EACZ,WAAW;EACX,4CAAoC;UAApC,oCAAoC;EACpC,yBFlT2B;EEmT3B,+BAA+B;EAC/B,kBAAkB,EAAA;;AAGnB;;;;;EACC,cAAc;EACd,kBAAkB;EAClB,mBFlP6C;EEmP7C,eAAe;EACf,WAAW;EACX,kBAAkB;EAClB,yBF/O6C;EEgP7C,YAAY;EACZ,iBAAiB;EACjB,mBFG4B;EEF5B,WAAW;EACX,QAAQ;EACR,cF3N2C,EAAA;;AE8N5C,aAAA;AACA;;;;;;EACC,0BAA0B;EAO1B,qMAEsC;EACtC,8NAEkE,EAAA;;AAGnE;;EACC,wBAAwB;EAOxB,qMAEsC;EACtC,4NAEgE,EAAA;;AAGjE;;;;;EACC,0BAA0B;EAG1B,2JAA4F;EAA5F,6FAA4F;EAC5F,iHAAkH,EAAA;;AAGnH;;;;;EACC,0BAA0B;EAG1B,yJAAmG;EAAnG,oGAAmG;EACnG,6HAA8H,EAAA;;AAG/H;;;;;EACC,0BAA0B;EAC1B,uCAAuC;EAKvC,gTACoJ;EACpJ,oVAEoB,EAAA;;AAGrB;;;;;EACC,0BAA0B;EAK1B,iTACqJ;EACrJ,wUAEa,EAAA;;AL/Yd;;4EA+lB4E;AA5lB5E,2DAAA;AOPA;EAEI,wBAAiC;EAAjC,0BAAiC;EAAjC,wBAAiC;EAAjC,qBAAiC;EAAjC,wBAAiC;EAAjC,uBAAiC;EAAjC,mBAAiC;EAAjC,qBAAiC;EAIjC,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,wBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,yBAA0C;EAA1C,2BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,4BAA0C;EAA1C,0BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,0BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAA1C,2BAA0C;EAI1C,kBAAiC;EAAjC,sBAAiC;EAAjC,sBAAiC;EAAjC,sBAAiC;EAAjC,uBAAiC,EAAA;;APDrC,6CAAA;AQTA,WAAA;AACA;EACC,cAAa,EAAA;;AAEd,mCAAA;AACA;;;EAGI,aAAa;EACb,UAAU,EAAA;;AAEd;EACI,SAAS,EAAA;;ARDb,8BAAA;ASXA;EAEE,cAAc;EACd,kCAAkC;EAClC,sBHGa,EAAA;;ANMf,eAAA;AUbA;EACC,cPoewC;EOnexC,gBPgjB6B;EO/iB7B,cAAc;EACd,qBP8iB6B;EO7iB7B,qBAAqB;EACrB,kBAAkB,EAAA;EANnB;IASE,mBPwkBiC;IOvkBjC,kBAAkB;IAClB,iBAAiB,EAAA;IAXnB;;MAeG,cPZyB;MOatB,sBAAsB,EAAA;IAhB5B;MAmBI,eP2iBwB,EAAA;IO9jB5B;MAsBI,ePwiBwB,EAAA;IO9jB5B;;MA6BI,cPkhBgD,EAAA;IO/iBpD;MAoFG,qBAAA;MACA,8DAAA;MAWA,yBAAA,EAA0B;MAhG7B;QAsCK,YAAY;QACZ,aAAa;QACb,cAAc;QACd,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,cAAiC;QACjC,WAAW;QACX,eAAe;QACf,UAAU;QACV,qCAA6B;QAA7B,6BAA6B,EAAA;MAhDlC;QAsDI,cPyfgD;QOxfhD,mCAAA;QACA,kBAAkB;QAClB,aPsee;QOnef,gBAAgB,EAAA;QA5DpB;UAgEK,YAAY;UACZ,aAAa;UACb,cAAc;UACd,cAAkC;UAClC,WAAW;UACX,eAAe;UACf,UAAU,EAAA;QAtEf;;UA4EK,yBAA0C;UAC1C,gCAAgC;UAChC,gCAAgC;UAChC,gBAAgB,EAAA;MA/ErB;QAuFI,kBAAiB;QACjB,sBPod0B;QOnd1B,WAAW;QACX,yBAA0C;QAC1C,qBAAqB;QACrB,UAAU;QACV,SAAS,EAAA;IA7Fb;MAqGG,WPulB6B;MOtlB7B,YAAW,EAAA;EAtGd;IA4GE,eAAc;IACd,cPkckD,EAAA;;AO9bpD;EACC,sBJ3Gc;EI4Gd,4DP0b4D;UO1b5D,oDP0b4D;EOzb5D,oBAAa;EAAb,oBAAa;EAAb,aAAa;EAEb,mBAAc;MAAd,kBAAc;UAAd,cAAc;EAEd,yBAAkB;MAAlB,sBAAkB;UAAlB,mBAAkB;EAElB,gBPwb6B;EOvb7B,kBAAkB;EAClB,aPmakB;EOjalB,4BAAQ;MAAR,iBAAQ;UAAR,QAAQ,EAAA;EAbT;IAgBE,aAAa,EAAA;EAhBf;IAqBE,ePydgC;IOxdhC,ePydiF,EAAA;IO/enF;MAyBG,kBAAkB;MAClB,UAAU;MACV,WAAW;MACX,eP+VyB;MO9VzB,YAAY;MACZ,WAAW;MACX,iBAAiB;MACjB,SAAS;MACT,cAAc;MACd,oBAAa;MAAb,oBAAa;MAAb,aAAa;MACb,yBAAmB;UAAnB,sBAAmB;cAAnB,mBAAmB;MACnB,wBAAuB;UAAvB,qBAAuB;cAAvB,uBAAuB,EAAA;;AVtI1B,aAAA;AWfA;EACC,gBRijB6B;EQhjB7B,gBRuoB2B;EQnoB3B,wDAAgD;UAAhD,gDAAgD;EAEhD,gBAAgB;EAChB,kBAAkB;EAClB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EAEhB,oBAAoB;EACpB,mBAAY;UAAZ,YAAY;EAEZ,oBAAoB;EACpB,cAAc;EAEd,eAAe;EAElB,eR0nByB,EAAA;EQ/oB1B;IA0BE,WR+hB2B;IQ9hB3B,YR+hB2B,EAAA;EQ1jB7B;IA+BE,mBAAc;QAAd,kBAAc;YAAd,cAAc,EAAA;;AAKhB;EACC,mBAAmB;EACnB,gBAAgB;EAChB,eAAe;EACf,WLjCc;EKkCd,cAAc;EACd,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,gBAAgB,EAAA;;AX1BjB,eAAA;AYjBA;EACC,mBAAO;MAAP,WAAO;UAAP,OAAO,EAAA;EADR;IAIE,UAAS,EAAA;IAJX;MAOG,kBAAkB,EAAA;IAPrB;MAWG,aAAa,EAAA;;AAKhB;EACC,oBAA0C;EAC1C,gBTkjBiD;ESjjBjD,mBAAmB;EACnB,kBTgTwB;ES/SxB,uBT6iBkC;ES5iBlC,6BT2iByC;ES1iBzC,wBAAgB;UAAhB,gBAAgB;EAChB,oBVkOkC;EUjOlC,gBT6iByC,EAAA;;AHnjB1C,gCAAA;AanBA;EAGE,UAAU,EAAA;;AAHZ;EAOE,UAAU;EACV,gBAAgB;EAChB,SAAS;EACT,kBAAkB;EAClB,gBPJa;EOKb,eVikBmD;EUhkBnD,wBAA8C;EAC9C,cAAiC;EACjC,gBAAgB;EAChB,mBAA0C;EAC1C,UAAU;EACV,gBAAgB;EAChB,8EAA6D;UAA7D,sEAA6D;EX0B3D,sBA6M+B;EWnOjC,8DAAsD;EAAtD,sDAAsD,EAAA;EAvBxD;IA0BG,kBAAkB;IAClB,kBAAkB,EAAA;IA3BrB;MAmCI,gBAAe,EAAA;;AAnCnB;EA4CG,cAAc;EACd,UAAU;EACV;;;Kbi1BE;Ea70BF,eAA8D,EAAA;EAlDjE;IAqDI,iBAAiB,EAAA;;AbhCrB,4CAAA;AcrBA;EACC,aAAa,EAAA;EADd;IAIE,YAAY,EAAA;;AAKd;EACC,UAAS;EACT,SAAS;EACT,gBAAgB;EAChB,kBAAkB,EAAA;EAJnB;IAOE,kBAAkB;IAClB,gBRVa,EAAA;IQEf;MAYG,mBAAoC,EAAA;MAZvC;QAeI,gBAAe,EAAA;IAfnB;MAqBG,uBXuMiB;MWtMjB,4CRdY,EAAA;MQRf;QAyBI,qBAAqB;QACrB,2HAA+E;QAA/E,iFAA+E,EAAA;MA1BnF;QA6BI,qBAAqB,EAAA;MA7BzB;QAiCI,kBAAkB,EAAA;QAjCtB;UAoCK,WAAA;UACA,cAAc,EAAA;IArCnB;MA6CI,SAAQ,EAAA;EA7CZ;IAmDE,cAA6B;IAC7B,gBAAgB;IAChB,oBZ4LiC,EAAA;EYjPnC;;IA0DE,cAA6B,EAAA;EA1D/B;IA+DG,mBX7CoB,EAAA;IWlBvB;MAkEI,gBRpEW,EAAA;MQEf;QAoEK,iBAAiB,EAAA;IApEtB;MAwEI,kBAAkB;MAClB,4CRjEW;MQkEX,UAAU,EAAA;MA1Ed;QA6EK,uBAAuB,EAAA;QA7E5B;UA+EM,WAAW;UACX,kBAAkB;UAClB,MAAK;UACL,SAAS;UACT,OAAO;UACP,QAAQ;UAER,WAAW;UACX,0IAA0H;kBAA1H,kIAA0H,EAAA;IAvFhI;MA4FI,mBZqJ+B,EAAA;EYjPnC;IAiGG,eAAe,EAAA;EAjGlB;IAyGG,0BAA0B;IAC1B,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,UAAU;IACV,eAAe;IACf,WAAW;IACX,cAAc,EAAA;;AdlGjB,2DAAA;AevBA;EACC,cAAa;EACb,cAAc;EACd,2BAA4C;EAC5C,0BAA8C;EAC9C,YAAW;EACX,oBZ8NiB;EY7NjB,kBAAkB,EAAA;EAPnB;IAUE,qBAAqB;IACrB,kBAAkB;IAClB,UAAS,EAAA;;AAIX;EACC,iBZokB4C;EYnkB5C,cZokByC;EYnkBzC,cAAc;EACd,qBAAqB;EACrB,cZgdwC;EY/cxC,gBAAgB;EAChB,wCAAwC;EACxC,aAAa;EbqBV,kBapBgC;EACnC,gBAAgB;EAEhB,mBAAmB;EAGnB,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB,EAAA;EAjBvB;IAoBE,mBZujBoC;IYtjBpC,eAAc,EAAA;EArBhB;IAyBE,yBAAoD;IACpD,gBAAgB,EAAA;IA1BlB;MA6BG,oBAAgD,EAAA;EA7BnD;IAmCE,qBZhD0B;IYiD1B,gBAAgB,EAAA;IApClB;MAuCG,mBZoiBmC,EAAA;;AY/hBtC;EACC,kBAAkB;EAClB,oBb4LkC;Ea3LlC,uBAAuB;EACvB,cAAc;EACd,mBAAmB;EACnB,gBAAgB,EAAA;;AfzCjB,2BAAA;AgBzBA;EAKG,OAAM;EACN,0BAAyB;EACzB,QAAO;EACP,MAAK,EAAA;;AARR;EAcG,oBboiB2B,EAAA;;AaljB9B;EAkCG,+CAAA,EAAgD;EAlCnD;IAoBI,gBbqnBwB;IapnBxB,eAAc;IACd,MAAK;IACL,Yb0ee,EAAA;EajgBnB;IA8BK,oBbohByB,EAAA;EaljB9B;IAqCK,kBAAkB;IAClB,mCAA2B;IAA3B,2BAA2B,EAAA;;ACsB5B;ED5DJ;IAoDI,sBbqlBwB,EAAA;Ea1lB3B;IAWK,gBb+mB2B,EAAA;Ea1nBhC;IA6BE;;KhBq9BE,EgBn9BC;IA/BL;MAmBG,0BAA0B;MAC1B;;ahBm+BS;MgB/9BT,4DbseyD;catezD,oDbseyD;MapezD,6BAAA;MACA,gCAAgC,EAAA,EAChC;;AhBhDJ,uBAAA;AkB3BA;EAIC,kBAAkB;EAClB,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,gBfmoB2B;EeloB3B,oBfkoB2B;EejoBxB,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EAEhB,afohBkB;EelhBlB,wBAAwB,EAAA;;AlBgBzB,mBAAA;AmB7BA;EACC,cAAc;EACd,kBAAkB;EAClB,iCAAiC;EACjC,mCAAmC;EAClC,2BAA2B,EAAA;EAL7B;IAQE,SAAS,EAAA;EARX;IAYE,gBAAgB;IAChB,mBAAmB,EAAA;;AAIrB;EACC,yBAAyB;EACzB,SAAQ;EACR,chB8oBoD;EgB7oBpD,kBhB0nByB;EgBznBzB,kBAAkB;EAClB,iBjBmOkC;EiBlOlC,mBAAmB;EACnB,gBAAgB,EAAA;;AAGjB;EACC,UAAU;EACV,gBAAgB;EAChB,SAAS,EAAA;EAHV;;;IjB6DI,qCiBrD+B;IjBwD/B,6BiBxD+B,EAAA;EARnC;IAcE,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IAEvB,chBH4C,EAAA;IgBf9C;;MAsBG,kBhB4lBoC,EAAA;EgBlnBvC;IA2BE,eAAe;IACf,gBAAgB;IAChB,aAAY,EAAA;EA7Bd;IAiCE,kBAAkB,EAAA;IAjCpB;MjBuRI,YAA0B,EAAA;IiBvR9B;MAgEG,qDAAA,EAAsD;MAhEzD;QjBuRI,YAA0B;QiB1O1B,2CblEW;QJ6HX,yCCjIwB;gBDiIxB,iCCjIwB;QgBwExB,gBAAgB,EAAA;QA/CpB;;UAqDS,chB8kB+D,EAAA;MgBnoBxE;QA6DI,cAAc,EAAA;MA7DlB;QAkEI,gBAAgB;QAChB,4BAA4B;QAC5B,kBAAkB;QAClB,oBAAoB;QACpB,WAAW;QACX,cAAc;QACd,YAAY;QACZ,WAAW;QACX,cAAc;QACd,oBAAa;QAAb,oBAAa;QAAb,aAAa;QACb,0BAAqB;YAArB,qBAAqB;QACrB,yBAAmB;YAAnB,sBAAmB;gBAAnB,mBAAmB,EAAA;IA7EvB;MAkFG,oBAAa;MAAb,oBAAa;MAAb,aAAa;MAAE,MAAA;MACf,yBAAkB;UAAlB,sBAAkB;cAAlB,mBAAkB;MAAE,MAAA;MACpB,UAAU;MACV,uBhB8hBuB;MgB7hBvB,mBjBwIgC;MiBvIhC,chByhBiD;MgBxhBjD,gBAAgB;MAChB,qBAAqB;MAErB,kBAAkB;MAAE,+BAAA;MAwBpB;;;;;;;;;KnBwgCE;MmBr/BF;;KnBw/BE;MmBp/BF;;;;;;;KnB4/BE;MmB3+BF;;;;;;;;;KnBq/BE,EmB5+BC;MApKN;QA8FI,mBhBukB4B;QgBtkB5B,kBAAkB;QAClB,kBhBskBwB;QgBrkBxB,qBAAqB;QACrB,kBhBsMqB;QgBrMrB,qBAAqB;QACrB,uBhBqkByD;QgBpkBzD,uBhBmkB+B;QgBlkB/B,gBAAgB;QAChB,gBAAe;QACf,cAAc;QACd,4CAA4C,EAAA;QAzGhD;UA4GK,cAAc;UACd,eAAe;UACf,gBAAgB;UAChB,+BAA4B,EAAA;MA/GjC;;QAgII,qBhB2f+B;QgB1f/B,mBhBsf6B;QgBrf7B,chBwfyB;QgBvftB,chB+fwD,EAAA;MgBloB/D;QAqJI,mBAAO;YAAP,WAAO;gBAAP,OAAO;QAAE,MAAA;QACT,2BAAoB;QAApB,2BAAoB;QAApB,oBAAoB;QACpB,yBAAkB;YAAlB,sBAAkB;gBAAlB,mBAAkB;QAClB,mBAAmB,EAAA;MAxJvB;QjBsKE,iCAKwC;QAJhC,yBAIgC,EAAA;MiB3K1C;QjBuRI,YAA0B;QiBzG1B,qBAAqB;QACrB,oCb1LW,EAAA;QaWf;UAkLK,WbvMU,EAAA;QaqBf;;UAuLK,chB4cmE,EAAA;QgBnoBxE;UA2LK,qDAAuD;kBAAvD,6CAAuD;UACvD,yCAAmD,EAAA;MA5LxD;QjBuRI,YAA0B,EAAA;QiBvR9B;UAqMK,Wb1NU,EAAA;IaqBf;MA4MG,oCbvNY;MawNZ,iBAAiB;MACjB,oBAAoB,EAAA;MA9MvB;QAmNK,cAAkC;QAElC,sCAA+G,EAAA;QArNpH;;UA0NO,oBAA2C,EAAA;QA1NlD;;UAgOM,uBAAuB;UACvB,uBhBuc6B;UgBtc7B,chBgayD;UgB/ZzD,mBAA4C;UAC5C,chBkcsB;UgBjctB,kBAAkB,EAAA;QArOxB;UAyOM,Wb9PS;Ua+PT,yBhB/PqB;UgBgQrB,yBAA2C,EAAA;QA3OjD;UjBuRI,YAA0B;UiBtCxB,oCb5PS,EAAA;UaWf;;YAsPW,chB6Y6D,EAAA;MgBnoBxE;QjBuRI,YAA0B;QiBtBxB,6BAA4B;QAC5B,wBAAe;gBAAf,gBAAe;QAEf,gBAAgB,EAAA;QApQtB;;UjBuRI,YAA0B,EAAA;QiBvR9B;;UAiRY,chBkX4D,EAAA;MgBnoBxE;QA0RM,iBAAgB,EAAA;MA1RtB;QjBuRI,YAA0B,EAAA;MiBvR9B;QA2SO,cAAkC;QAClC,yCAA2H,EAAA;QA5SlI;UA+SQ,cAAc;UACd,wBAA0C,EAAA;QAhTlD;;UAqTQ,cAAc;UACd,wBAA0C,EAAA;QAtTlD;UjBuRI,YAA0B,EAAA;QiBvR9B;UA+TQ,WbpVO;UaqVP,yBhBrVmB;UgBsVnB,yBAA2C,EAAA;IAjUnD;MA2UG,gBAAe,EAAA;EA3UlB;IAgVE,gBAAe,EAAA;;AAIjB;;;;;;;;;;;;;;;;;;;;;;;;;EnB08BE;AmB/6BF,uBAAA;AACA;EACC,gBbtYc,EAAA;EaqYf;IAIE,kCAAkC;IAClC,4BAA4B,EAAA;EAL9B;IAUG,kCAAkC;IAClC,yBAA6B,EAAA;IAXhC;MAaI,yBAA6B,EAAA;IAbjC;MAiBI,oCAAoC,EAAA;EAjBxC;IAuBE,oCAAoC,EAAA;EAvBtC;IA2BE,mBAAmB,EAAA;;AAIrB,0BAAA;AACA;EACC,qChB7G2C,EAAA;EgB4G5C;IAIE,4ChBhH0C,EAAA;EgB4G5C;IAQE,mBAAmB,EAAA;;AAKrB,yBAAA;AACA;EACQ,+BAA+B;EAAE,gCAAgC,EAAA;;AADzE;EAEW,6BAA6B,EAAA;;AAFxC;EAGc,+BAA+B,EAAA;;AAG7C;;EAGE,0CAAuC,EAAA;;AAIzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EnB+7BE;AiB10CE;EEkbH;IASK,mBjB7P6B,EAAA,EiB8P7B;;AnBzdN,0BAAA;AoB/BA;EAEC,SAAS;EACT,UAAU;EACV,kBAAkB;EAClB,gBAAgB;EACb,WAAW;EACX,kBAAkB;EACrB,6BAAqB;UAArB,qBAAqB;EACrB,8DAAsD;EAAtD,sDAAsD;EAEtD,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB,EAAA;EAbxB;IAgBE,0BAA0B;IAC1B,gBAA2B;IAC3B,8BdDa;IcEb,WdZa,EAAA;IcPf;MAsBG,gCAAgC,EAAA;IAtBnC;MA0BG,qBAA2C,EAAA;;AAO9C;EA+BC,sCAAA,EAAuC;EA/BxC;IAIE,+BAA+D;IAC/D,8DAAsD;IAAtD,sDAAsD,EAAA;EALxD;IAUE,UAAU;IACV,mBAAmB;IACnB,YAAY;IAEZ,wDAAgD;YAAhD,gDAAgD;IAChD,2BAAmB;YAAnB,mBAAmB,EAAA;EAfrB;IAoBE,aAAa,EAAA;EApBf;IAwBE,SAAQ,EAAA;IAxBV;MA2BG,UAAU,EAAA;EA3Bb;IAiCE,aAAY,EAAA;EAjCd;IAqCE,cAAa,EAAA;;AAKf,wDAAA;AACA;EAEE,aAAa,EAAA;;AHlBX;EGuBH;;IAGE;;;KpB42CE,EoBz2CC;IANL;;;;MASG,aAAa,EAAA,EACb;;ApB5DJ,oCAAA;AqBjCA;EACC,kBAAkB;EAClB,gBlBuoB2B;EkBtoB3B,elBurBiC;EkBtrBjC,WfGc;EeFd,gBAAgB;EAEb,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,8BAAmB;EAAnB,6BAAmB;MAAnB,uBAAmB;UAAnB,mBAAmB;EACnB,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EAEnB,elBooBsB;EkBloBzB,8DAAsD;EAAtD,sDAAsD,EAAA;EAbvD;IAgBE,YAAY;IACZ,MAAM;IACN,OAAO;IACP,SAAS;IACT,QAAQ;IACR,kBAAkB;IAKlB,YAAY,EAAA;EA1Bd;IA8BE,elB6pBgC;IkB5pBhC,YAAY;IACZ,qBAAqB;IACrB,UAAU;IACV,kBAAkB,EAAA;EAlCpB;IAsCE,iBAAiB;IACjB,cAAc;IACd,uBAAyB;IACzB,UAAU;IACV,kBAAkB;IAClB,mBAAmB,EAAA;IA3CrB;MA8CG,gBAAgB,EAAA;;AJcf;EIPH;InBoCG,wCmBjCoC;InBoCpC,gCmBpCoC;IACrC,oBAAoB,EAAA;EAJtB;IASG,YAAY;InBySd,mCAAmC;IAGlC,2BAAkC,EAAA,EmB1SjC;;AAKJ;EACC,mBnBoLkC;EmBnLlC,qBAAqB;EACrB,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB,EAAA;EALlB;IAQE,mBnB6KiC;ImB5KjC,cAAc;IACd,gBAAgB,EAAA;;ArB5ClB,mCAAA;AsB9BA;;EAEC,aAAa,EAAA;;ALqDV;ED1DF;IMYA,+BAAA;IAKA,wCAAA;IAKA,+BAAA;IAuZA,YAAA,EAAa;IAnad;MAIE,mBAAc;UAAd,kBAAc;cAAd,cAAc,EAAA;IAJhB;MASE,wBAAwB,EAAA;IAT1B;MAcE,WhBnBY,EAAA;IgBKd;MAoBG,kBAAiB,EAAA;MApBpB;QAuBI,cnB4qB+B,EAAA;MmBnsBnC;QA0BI,cnB0qB8B,EAAA;MmBpsBlC;QA6BI,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAClB,mBnB+ByC;QmB9BzC,eAAe;QACf,WAAW;QACX,kBAAkB;QAClB,yBnBkCyC;QmBjCzC,YAAY;QACZ,iBAAiB;QACjB,mBnBoRwB;QmBnRxB,WAAW;QACX,QAAQ;QACR,cnBsDuC,EAAA;ImBhG3C;MAiDE,aAAa;MACb,gBnBqfiC;MmBlfjC,gHAA6E;MAA7E,gFAA6E;MAC7E,yBnBokB+C;MmBlkB/C,kBAAkB;MAClB,MAAM;MACN,QAAQ;MACR,OAAO;MAEP,4DnBoe0D;cmBpe1D,oDnBoe0D,EAAA;MmBjiB5D;QAgEG,aAAa,EAAA;MAhEhB;QAoEG,oBAAmB,EAAA;MApEtB;QAwEG,oBAAa;QAAb,oBAAa;QAAb,aAAa;QACb,WAAW;QACR,cAAc;QACjB,eAAe;QACf,uBAAuB;QACvB,wBAAgB;gBAAhB,gBAAgB,EAAA;MA7EnB;;QAmFG,cAAwC,EAAA;QAnF3C;;UAsFI,cAAwC,EAAA;MAtF5C;QA6FG,qCnB5DyC;gBmB4DzC,6BnB5DyC,EAAA;MmBjC5C;QAiGG,wBAAsC,EAAA;IAjGzC;MAuGE,oBAAoD,EAAA;IAvGtD;MA2GE,eAAc,EAAA;MA3GhB;QA8GG,WAAW,EAAA;IA9Gd;MAmHE,oBAAa;MAAb,oBAAa;MAAb,aAAa;MACb,WAAW;MACX,eAAe;MACf,cnBgjByB;MmB/iBzB,YnB4ZgB;MmB3ZhB,mBAAmB;MACnB,gBhB9HY;MgBgIZ,4DnBsa0D;cmBta1D,oDnBsa0D;MmBpa1D,kBAAkB;MAClB,anByaiC;MmBvajC,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;MAhIV;;;;QAsIG,aAAa,EAAA;MAtIhB;QA0IG,mBAAO;YAAP,WAAO;gBAAP,OAAO;QACP,oBAAa;QAAb,oBAAa;QAAb,aAAa;QACb,0BAAoB;YAApB,uBAAoB;gBAApB,oBAAoB;QACpB,YAAY;QAEZ,6DAAA;QACA,4CAA4C;QAC5C,4DAAA,EAA6D;QAjJhE;UAqJI,aAAa,EAAA;QArJjB;UAyJI,SAAQ;UACR,oBAAoB;UAAE,6GAAA;UACtB,UAAU;UACV,oBAAa;UAAb,oBAAa;UAAb,aAAa;UACb,8BAAmB;UAAnB,6BAAmB;cAAnB,uBAAmB;kBAAnB,mBAAmB;UACnB,0BAAoB;cAApB,uBAAoB;kBAApB,oBAAoB;UACpB,mBAAc;cAAd,kBAAc;kBAAd,cAAc;UAEd,2CAAmC;UAAnC,mCAAmC,EAAA;UAjKvC;YAsKK,qBAAqB;YAClB,gBAAgB,EAAA;YAvKxB;cA0KM,aAAa,EAAA;YA1KnB;cA+KO,wBAAgB;sBAAhB,gBAAgB,EAAA;cA/KvB;gBAkLQ,gBAAgB;gBAChB,4BAA4B;gBAC5B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;gBACtB,cAAc;gBACd,YAAY;gBACZ,WAAW;gBACX,cAAc,EAAA;YA1LtB;cAkMM,kCAAkC;cAClC,kBAAkB;cAElB,YAAY,EAAA;cArMlB;;gBA0MO,cAAc;gBACd,SAAU;gBACV,8BAA8B;gBAC9B,+BAAwB;gBAAxB,+BAAwB;gBAAxB,wBAAwB;gBACxB,yBAAmB;oBAAnB,sBAAmB;wBAAnB,mBAAmB;gBACnB,sBAAqB;oBAArB,mBAAqB;wBAArB,qBAAqB;gBACrB,cAAc,EAAA;cAhNrB;gBAoNO,gBAAgB;gBAChB,mBAAmB;gBACnB,uBAAuB;gBACvB,mBAAmB;gBACnB,wBAAwB;gBACxB,gBAAgB;gBAEhB,qBAAqB;gBAAE,uBAAA;gBAGvB,mBAAc;oBAAd,kBAAc;wBAAd,cAAc,EAAA;cA9NrB;;gBAmOO,SAAS,EAAA;cAnOhB;gBAuOO,mBAAmB;gBACnB,cAAoC;gBACpC,kBAAkB;gBACf,aAAa;gBAChB,MAAM;gBACN,SAAS,EAAA;gBA5OhB;kBAiPS,gBAAgB,EAAA;cAjPzB;gBAuPO,eAAe;gBACf,sBAAsB;gBACtB,uBAAuB;gBACvB,4BAA4B;gBAC5B,+BAAmC;gBACnC,yBAAyB;gBACzB,6BAA6B,EAAA;YA7PpC;cAuQM,gBAAgB;cAChB,cnBkX2C,EAAA;YmB1nBjD;cA4QM,wBAAwB;cACxB,YnB0Z2B;cmBzZ3B,uBAAuB;cACvB,WnBuZqB;cmBtZrB,kBAAkB;cAClB,mBnB2W2C;cmB1W3C,mBnByCsB;cmBxCtB,4DAAoD;sBAApD,oDAAoD;cACpD,eAAe;cACf,gBAAe,EAAA;cArRrB;gBAwRO,WAAW;gBACX,kBAAkB,EAAA;gBAzRzB;kBA4RQ,wBAAwB;kBACxB,WAAW;kBACX,cnBkW2C;kBmBjW3C,gBAAgB;kBAChB,wBAAgB;0BAAhB,gBAAgB,EAAA;kBAhSxB;oBAmSS,cAAc,EAAA;oBAnSvB;sBAsSU,cAAc;sBACd,iBAAiB;sBACjB,aAAa,EAAA;oBAxSvB;sBA4SU,gBAAgB,EAAA;gBA5S1B;kBAoTQ,mBnBwUyC;kBmBvUzC,UAAU,EAAA;kBArTlB;oBAyTU,kBAAkB,EAAA;gBAzT5B;kBAgUS,8BhB3TK;kBgB4TL,WhBtUK,EAAA;cgBKd;gBAuUO,WAAW;gBACX,cAAc;gBACd,yBAAyB;gBACzB,0BAA0B;gBAC1B,kBAAkB;gBAClB,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,uBAAuB,EAAA;cA/U9B;gBAoVO,gBAAgB;gBAChB,4BAA4B;gBAC5B,kBAAkB;gBAClB,eAAe;gBACf,cnBoS0C;gBmBnS1C,gBAAgB;gBAChB,cAAc;gBACd,YAAY;gBACZ,OAAO,EAAA;YA5Vd;cAoWO,cnB7WoB;cmB8WpB,uBAAuB,EAAA;cArW9B;gBAyWQ,yBAAwB;gBACxB,+BAA+B;gBAC7B,uCAAuC,EAAA;MA3WjD;QAyXG,aAAa,EAAA;IAzXhB;MA2YQ,aAAa,EAAA;IA3YrB;MAgZQ,UAAS,EAAA;IAhZjB;;MAqZQ,wBAAuB;UAAvB,qBAAuB;cAAvB,uBAAuB,EAAA;IArZ/B;MAqaE,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;IAraV;MAyaE,oBAAa;MAAb,oBAAa;MAAb,aAAa;MACb,4BAAsB;MAAtB,6BAAsB;UAAtB,0BAAsB;cAAtB,sBAAsB,EAAA;IA1axB;MA8aE,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ,EAAA;IA9aV;MAkbE,4BAAQ;UAAR,iBAAQ;cAAR,QAAQ;MACR,0BAAoB;UAApB,uBAAoB;cAApB,oBAAoB;MACpB,mBAAc;UAAd,kBAAc;cAAd,cAAc,EAAA,EACd;;ALrYC;EKhDH;IAwdI;;;;;;;;OtBgzCE,EsBxyCC;IAheP;MAwcO,eAAe;MACf,sBAAsB;MACtB,uBAAuB;MACvB,4BAA4B;MAC5B,6BAA6B,EAAA;IAfpC;;;MAoBO,eAAe;MACf,YAAY,EAAA,EACZ;;AtB1bR,+BAAA;AiBuBI;EM1DH;IAEE,sBpB2qBgC,EAAA;EoB7qBlC;IAKE,eAAwC;IACxC,apBuhBgB;IoBthBhB,8DpBmfgE;IoBnfhE,sDpBmfgE;IoBjfhE,kBAAkB;IAElB,MAAK;IACL,SAAQ;IAER,wBAAwB;IAExB,2CAAA,EAA4C;IAhB9C;MAkBG,WAAW;MACX,uBAAuB;MACvB,YAAY;MACZ,cAAc;MACd,eAAe;MACf,UAAU;MACV,MAAM;MACN,SAAQ;MACR,epB6mBwB;MoB5mBxB,aAAoC,EAAA;IA3BvC;MA+BG,OAAO;MACP,0DAAmD;MAAnD,kDAAmD,EAAA;MAhCtD;QAmCI,WAAW;QACX,WAAW,EAAA;EApCf;IAyCE,cAAc;IAEd,kDAAA,EAAmD;IA3CrD;MA6CG,mBpBvC2E;MoBwC3E,gCAA2D;MrBoF3D,yDqBnFqD;crBmFrD,iDqBnFqD;MACrD,sBAAyC,EAAA;EAhD5C;IAsDG,2CAAA,EAA4C;IAtD/C;MAwDI,cpBqnB8B,EAAA;EoB7mBlC;IAEE,qBpB2mBgC,EAAA,EoB1mBhC;;AvB9BH,+BAAA;AwBvCA;EAEE,0BAA0B;EAC1B,MAAK;EACL,SAAQ,EAAA;EAJV;IAOG,cAAc;IACd,kBAAkB;IAClB,iCAAiC;IACjC,8BAAkE,EAAA;EAVrE;IAeG,yCAA6E,EAAA;IAfhF;MAkBI,iBAAiB,EAAA;;AP0CjB;ED3CD;IQmBD;;IxB6zDE;IwB9yDF;;IxBizDE;IwBlyDF;;IxBqyDE,EwBnyDC;IAlCJ;MAQI,0BAA0B,EAAA;IAR9B;MAaG,uBrB0lBwB,EAAA;IqBvmB3B;MAwBI,0BAA0B;MAE1B,4DrBifwD;cqBjfxD,oDrBifwD,EAAA;IqB3gB5D;MAsCI,QAAQ;MACR,OAAO;MACP,kBAAkB;MAClB,iBAAiB;MACjB,iBAAsC,EAAA,EACtC;;APjBD;ED3CD;IQqED;;IxBoyDE,EwBlyDC;IAtDJ;MA0DI,6BAA6B,EAAA,EAC7B;;AxBpDL,kCAAA;AiBmBI;EQ1DH;IAEC,2CAAA,EAA4C;IAF7C;MAKE,wBAAwB,EAAA;IAL1B;MAUE,gBtB6pB8B;MsB5pB9B,atBkhBgB;MsBhhBhB,kBAAkB;MAElB,8DtB2egE;MsB3ehE,sDtB2egE,EAAA;MsB1flE;QAmBG,gBtBopB6B;QsBnpB7B,UAAU;QACV,wBAAuB;YAAvB,qBAAuB;gBAAvB,uBAAuB,EAAA;QArB1B;UAwBI,mBAAU;cAAV,cAAU;kBAAV,UAAU,EAAA;QAxBd;UA4BI,aAAa,EAAA;UA5BjB;YA+BK,wBAAwB,EAAA;MA/B7B;QAqCG,iBAAgC;QAChC,WAAU;QACV,qBAA6B;QAC7B,kBAAiB;QACjB,gBAAgB;QAEhB,wBAAuB;YAAvB,qBAAuB;gBAAvB,uBAAuB,EAAA;QA3C1B;UA8CI,kBAAkB;UACf,MAAM;UACN,iBAAqB;UACrB,gBAAgB;UAChB,aAAa,EAAA;MAlDpB;QAuDG,gBAAgB,EAAA;QAvDnB;UA0DI,aAAa,EAAA;QA1DjB;UA6DI,SAAQ,EAAA;UA7DZ;YAmEO,gBAAgB;YAChB,4BAA4B;YAC5B,kBAAkB;YAClB,oBAAoB;YACpB,WAAW;YACX,cAAc;YACd,YAAY;YACZ,WAAW;YACX,cAAc;YACd,oBAAa;YAAb,oBAAa;YAAb,aAAa;YACb,0BAAqB;gBAArB,qBAAqB;YACrB,yBAAmB;gBAAnB,sBAAmB;oBAAnB,mBAAmB,EAAA;UA9E1B;YAoFM;;SzBg2DE,EyB91DC;UAtFT;YA+FM,kBAAkB;YAClB,wBAAuB;gBAAvB,qBAAuB;oBAAvB,uBAAuB;YACvB,yBAAmB;gBAAnB,sBAAmB;oBAAnB,mBAAmB;YACnB,eAAe;YACf,gBAAgB,EAAA;YAnGtB;;cAuGO,oBAAoC;cACpC,SAAS,EAAA;YAxGhB;cA4GO,gBAA2B,EAAA;YA5GlC;cAgHO,eAAe,EAAA;YAhHtB;cAoHO,aAAa;cACb,kBAAiB;cACjB,gBAAgB;cAChB,sBAAsB;cACtB,sBAAwC;cACxC,WnBpHO;cmBqHP,MAAM;cACH,eAA8B;cAC9B,YAAY;cACZ,etB4iB4D;csB3iB5D,gBAAgB;cAChB,qBAAqB,EAAA;YA/H/B;cAoIO,aAAa,EAAA;YApIpB;cAyIO,kBAAkB;cAClB,etB+hB+D;csB9hB/D,eAA8B;cAC9B,yBtBwf0C;csBtf1C,iBAAiB;cACjB,oBAAoB;cAEpB,oCAAoC;cAEpC,oBAAoB,EAAA;cAnJ3B;gBAuJQ,gBAAgB;gBAChB,4BAA4B;gBAC5B,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,ctBweyC;gBsBvezC,WAAW;gBACX,iCAAyB;wBAAzB,yBAAyB;gBACzB,gBAAgB;gBAChB,cAAc;gBACd,SAAS,EAAA;cAjKjB;gBAwKS,sBAAwC;gBACxC,mBAAmB;gBACnB,sBAAsB,EAAA;gBA1K/B;;kBA+KU,gCAAgC,EAAA;gBA/K1C;kBAsLY,wBAAuC;kBACvC,mBAAmB;kBACnB,sBAAsB,EAAA;cAxLlC;;gBAiMU,aAAY,EAAA;UAjMtB;YA0MM,wBAAwB,EAAA;QA1M9B;UAiNI,iBAAiB,EAAA;UAjNrB;YAsNM,mBtB+c8C;YsB9c9C,WnBlNQ;YmBmNR,iBAAiB;YAEjB,WAAW,EAAA;YA1NjB;cA6NO,oBAAa;cAAb,oBAAa;cAAb,aAAa;cACb,gBAAe;cAEd,iCAAiC;cAChC,yCAAyC,EAAA;YAjOlD;cAqOQ,SAAS;cACT,yBAAmB;kBAAnB,sBAAmB;sBAAnB,mBAAmB;cACnB,mBtB6ZyC;csB5ZzC,iBAAiB;cACjB,gCAAgC,EAAA;cAzOxC;gBA4OY,gBAAgB;gBACnB,4BAA4B;gBAC5B,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;gBACV,ctBmZwC;gBsBlZxC,WAAW;gBACX,iCAAyB;wBAAzB,yBAAyB;gBACzB,gBAAgB;gBAChB,cAAc;gBACd,SAAS,EAAA;UAtPlB;YA8PM,yBAAyB;YACzB,UAAU;YAEV,iCAAiC;YAC/B,yCAAyC;YAEzC,4DAAoD;oBAApD,oDAAoD,EAAA;YApQ5D;cAyQO,WAAW;cACX,cAAc;cACd,kBAAkB;cAClB,0BAA0B;cAC1B,wBAAwB;cACxB,ctBkSsB;csBjStB,WAAW;cACX,WAAU,EAAA;IAhRjB;MA8RG,mBtBxR2E;MsByR3E,gCAA2D;MvB7J3D,yDuB8JqD;cvB9JrD,iDuB8JqD;MACrD,sBAAyC,EAAA;IAjS5C;MAuSG,sBtBsY+B,EAAA;IsB7qBlC;MA0SG,gBAA+C;MAC/C,iBAAiB,EAAA;MA3SpB;QA6SI,OAAO,EAAA;IA7SX;MAiTG,cAAc,EAAA;IAjTjB;MAuTG,uBtBgX6B,EAAA;IsBvqBhC;MA6TG,sBtB0W6B,EAAA;IsBvqBhC;MAkUI,qBtB2W8B,EAAA;IsB7qBlC;MAwUI,uBtB+V4B,EAAA;IsBvqBhC;MA6UK,eAAgB,EAAA,EAChB;;AzBrSN,0BAAA;A0B3CA;EAEC;;oC1BikEmC;E0B1jEhC,iBvB+uB2B;EuB9uB3B,SAAS;EACT,oBAAa;EAAb,oBAAa;EAAb,aAAa;ExB8Eb,wCwB5EoC;ExB+EpC,gCwB/EoC,EAAA;EAbxC;IAmBI,cAAc;IACd,cAAmC;IACnC,iBAAyB;IACzB,sBAA+B;IAC/B,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,exBgO+B,EAAA;EwB1PnC;IAgCE,aAAa,EAAA;;AAIf;EAIE,mBvB8lBgD;EuB7lBhD,SAAQ,EAAA;EALV;IAQG,YAAW;IACX,WAAW;IACX,iBAAgB;IAChB,cAAa;IACb,mCvBmnB2D;IuBhnB3D,yIAAqJ;IAArJ,yFAAqJ;IACrJ,YAAY,EAAA;EAhBf;IAoBG,YAAY,EAAA;;ATIX;ESKH;IAIE,yBAA6C,EAAA;IAJ/C;MAOG,cAAc;MACd,WAAU;MACV,iBvB8qB2B;MuB7qB3B,sBvB6qB2B;MuB5qB3B,oBxB8K+B;MwB7K/B,sBAAsB;MACtB,cvBglB2D;MuB/kB3D,kBAAkB;MAClB,qBAAqB;MACrB,kBAAkB;MxBQlB,wCwBNoC;MxBSpC,gCwBToC,EAAA;MAlBvC;QAqBI,kBAAkB,EAAA;MArBtB;QAyBI,SAAQ,EAAA;IAzBZ;MAgCG,yBAA8C,EAAA;MAhCjD;QAmCI,cvB2jBmE;QuB1jBnE,gBAAe,EAAA;IApCnB;MAyCG,aAAa,EAAA,EACb;;A1B9DJ,gBAAA;A2B7CA;EAEC,kBAAkB,EAAA;;AAGnB;EACC,iBAAiB,EAAA;;AAGlB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,0BAAoB;MAApB,uBAAoB;UAApB,oBAAoB;EACpB,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,WAAW,EAAA;;AAGf;EACC,yBxB0gB4D;EwBzgBzD,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,0BAAoB;MAApB,uBAAoB;UAApB,oBAAoB;EACpB,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,UAAU;EAEV,6BAAgB;MAAhB,gBAAgB;EAChB,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,QAAQ;EACR,YAAY;EACZ,eAAe;EAEf,eAAe,EAAA;;A3BkBnB,wBAAA;A4B/CA;EACC,sCAAsC;EACtC,kBAAkB;EAElB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,8BAAmB;EAAnB,6BAAmB;MAAnB,uBAAmB;UAAnB,mBAAmB;EACnB,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB,EAAA;;AAGpB;EACC,czBqpBuE;EyBppBvE,qBAAqB,EAAA;;AAGtB;EACC,mBAAmB;EACnB,gBAAgB;EAChB,czBT8E;EyBU9E,uBAAyB;EACzB,SAAS;EAET,mBAAO;MAAP,WAAO;UAAP,OAAO,EAAA;EAPR;IAUE,iBAAiB;IACjB,kBAAkB;IAClB,kBAAkB;IAClB,oBzBuMmB;IyBtMnB,cAAc;IACX,gBAAgB;IAChB,mBAAmB,EAAA;EAhBxB;IAoBE,gBAAgB;IAChB,czBiE2C;IyBhE3C,gBAAgB;IAChB,mBAAmB,EAAA;;A5BYrB,gBAAA;AIzCA;EyBNI,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,4BAAQ;MAAR,iBAAQ;UAAR,QAAQ;EAER,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,kBAAkB,EAAA;;AAKtB;;;;;;;;;;C7BssEC;AA/pED,eAAA;A8BnDA;EACC,iB3BuvB8B;E2BtvB9B,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,mBAAc;MAAd,kBAAc;UAAd,cAAc;EACd,yBAAkB;MAAlB,sBAAkB;UAAlB,mBAAkB;EAClB,gBxBEc;EwBDX,c3BivBkD;E2BhvBlD,oB5BmP+B;E4BlP/B,e3B4iB4B;E2B3iB5B,4BAAQ;MAAR,iBAAQ;UAAR,QAAQ,EAAA;;A9B4CZ,mBAAA;A+BrDA;EACI,qBAAqB;EACrB,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EAEtB,iBAAiB,EAAA;EAJrB;IAOQ,sBAAsB,EAAA;;AAI9B;EACI,0BAA0B;EAC1B,4BAA4B,EAAA;EAFhC;IAKQ,cAAa;IACb,gBAAgB,EAAA;;AAIxB;EACG,gCAAiG;EACjG,WAAW,EAAA;;AAGd;EACG,gCAAmH;EACnH,WAAW,EAAA;;AAKd;EAEM,gCAA6I,EAAA;;A/BoBnJ,wDAAA;AgCvDA;EAGG,eAAe;EACf,SAAS;EACT,UAAU;EACV,gBAAgB;EAChB,yB7B8hB6B,EAAA;E6BriBhC;IASI,kB7BqTsB;I6BpTtB,SAAS;IACT,mB7BqcyB;I6BpczB,gBAAgB;IAChB,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,uBAA2B;QAA3B,oBAA2B;YAA3B,2BAA2B;IAC3B,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,c7BbwB,EAAA;I6BH5B;MAmBK,c7BiFwC,EAAA;;A6BpG7C;;EA6BI,0BAA0B;EAC1B,2BAA2B,EAAA;;AA9B/B;EAiCI,cAAa,EAAA;;AAjCjB;EAsCG,gB1B/BY,EAAA;;A0BPf;EAyCG,cAAc;EACX,eAAe,EAAA;EA1CrB;IA6CO,aAAa,EAAA;EA7CpB;IAgDO,gBAAgB,EAAA;;AAhDvB;;;EAwDI,6BAAuC;EACvC,8BAAwC,EAAA;;AAzD5C;EA+DG,mBAAmB;EACnB,gDAA+C;EAC/C,6BAAwC,EAAA;;AAjE3C;EAsEG,8CACD;EADC,sCACD,EAAA;;AAvEF;EAyEG,gB1BlEY,EAAA;E0BPf;IA4EK,W1BrEU;I0BsEV,yB7BnC0C,EAAA;;A6B1C/C;EAkFG,W1B3EY;E0B4EZ,yB7BhFyB,EAAA;;A8BH5B;;;;;CjC4zEC;AiCrzED;EACC,cAAoC;EACpC,yBAA6D;EAC7D,qBAAyD,EAAA;;AAG1D;EACC,cAAqC;EACrC,yBAA6D;EAC7D,qBAAyD,EAAA;;AAG1D;EACC,c9B2E2C;E8B1E3C,yBAA2C;EAC3C,qBAAuC,EAAA;;AAIxC;EACC,cAA+B;EAC/B,yBAA4C;EAC5C,qBAAuC,EAAA;;AAIxC;EACC,c9BqCwC;E8BpCxC,yBAAyC;EACzC,qBAAqC,EAAA;;AAItC;EACC,c9BjC8E;E8BkC9E,yBAA0C;EAC1C,uBAAuC,EAAA;;AAIxC;EACC,aAAkB,EAAA;EADnB;IAIE,kB/BuMiC,EAAA;E+B3MnC;IAQE,qB9B2KmB,EAAA;;A+BlOrB;EACC,kBAAkB;EAClB,qBAAqB;EACrB,yB/BI0B;E+BH1B,W5BGc;EJ6HX,kCI7HW;UJ6HX,0BI7HW;E4BDd,eAAe;EACf,6BAA6B;EAC7B,mBhCkPkC;EgCjPlC,eAAe;EACf,oBAA8C;EAC9C,cAAc;EACd,sB/BklBkC;E+BjlBlC,gBAAgB;EAChB,mBAAmB;EACnB,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,0BAAA;EACA,sBAAsB,EAAA;;AAGvB,sEAAA;AACA;EACC,SAAS,EAAA;EADV;IAGE,gBAAgB,EAAA;EAHlB;IAME,mBAAmB,EAAA;EANrB;IASE,iBAAiB,EAAA;EATnB;IAYE,kBAAkB,EAAA;;ACnCpB;EACC,UAAS;EACT,uBAAsB;EACtB,kBAAiB;EACjB,kBAAkB;EAClB,uBAAyB,EAAA;;AAG1B;EAIG,gCAAgC,EAAA;;AAJnC;EAQG,gBAA2C;EAC3C,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB,EAAA;EAXnB;IAgBI,2BAA+C;IAC/C,eAAe,EAAA;;AnC63EnB;EmCt3EE,wCAAwC,EAAA;;AAG1C;EACC,ejCsNkC,EAAA;;AiCnNnC;EACC,oBjCkNkC,EAAA;;AFqqEnC;EmCn3EI,kCAAkC;EAClC,mCAAmC;EACnC,qBAAqB;EACrB,kBAAkB;EAClB,oBAAoB;EACpB,gBAAgB;EAChB,cAAc;EACd,gCAAgC,EAAA;;AAGpC;EAAqE,gBAAe,EAAA;;AACpF;EAAqE,gBAAe,EAAA;;AACpF;EAAqE,gBAAe,EAAA;;AAKpF;EAEC,UAAU;EACV,uBAAuB,EAAA;EAHxB;IAQG,gBAAgB;IAChB,YACD,EAAA;EAVF;IjCsPI,YAA0B;IiCxO3B,qBAAoB;IACpB,mBhCwB0C;IgCvB1C,qBAAqB;IACrB,kBAAiB;IACjB,aAAa;IACb,kBAAkB;IAClB,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB,EAAA;EAtBrB;IA0BI,yBhCayC,EAAA;IgCvC7C;MA6BK,qBhCUwC;MgCTxC,8BAA6B,EAAA;IA9BlC;MAiCK,0BhCMwC,EAAA;EgCvC7C;IAuCI,qBAAoB;IAEpB,0BhC8NqB,EAAA;IgCvQzB;MA4CK,YAAY,EAAA;EA5CjB;IAkDI,sBAAqB;IAErB,0BAAgD,EAAA;IApDpD;MAuDM,YAAY,EAAA;EAvDlB;IA+DI,WAAW;IACX,kBAAiB;IACjB,MAAM;IACN,uBhC3ByC;IgC4BzC,0BAAyB;IACzB,QAAQ;IACR,SAAS,EAAA;EArEb;IAwEI,WAAU;IACT,8BAA6B,EAAA;EAzElC;IA4EK,UAAS;IACT,yBAAwB;IACxB,0BhCvCwC,EAAA;EgCvC7C;IAiFK,yBhC3IuB,EAAA;IgC0D5B;MAoFK,qBhC9IuB;MgC+IvB,8BAA6B,EAAA;IArFlC;MAwFK,0BhClJuB,EAAA;EgC0D5B;IA4FI,yBhCrDyC,EAAA;IgCvC7C;MA+FK,qBhCxDwC;MgCyDxC,8BAA6B,EAAA;IAhGlC;MAmGK,0BhC5DwC,EAAA;;AiCpG7C,eAAA;AACA;EACC,mBjCoG4C;EiCnG5C,yBAAyB;EACzB,eAAe;EACf,iBAAiB;EACjB,mBAAmB;EACnB,yBAAyB;ElC4StB,YAA0B;EkC1S7B,gBAAgB;EAChB,eAAc;EACd,YAAW;EACX,cAAa;EACb,kBAAiB;EACjB,gBAAgB,EAAA;EAbjB;IlCiME,6BAUiC;IATzB,qBASyB;IAuG/B,YAA0B,EAAA;EkClT9B;IAuBE,cAAa;IACb,kBAAiB;IACjB,UAAS,EAAA;EAzBX;IA6BE,YAAY;IACZ,kBAAkB;IlCoRhB,YAA0B;IkClR5B,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,gB9B7Ba;I8B8Bb,YAAW;IACX,OAAO;IACP,MAAM;IACN,mBAAmB;IACnB,WAAW;IACX,kBAAkB;IAClB,kFAAmE;YAAnE,0EAAmE,EAAA;EA1CrE;IA8CE,W9BxCa;I8ByCb,mBjC7C0B,EAAA;IiCF5B;MAiDG,aAAY;MACZ,SAAQ;MACR,WAAU;MlC+PT,YAA0B,EAAA;IkClT9B;MAwDG,YAAY;MACZ,QAAO;MACP,UAAS;MACT,gB9BrDY;M8BsDZ,cjC1DyB,EAAA;;AiCgE5B,kDAAA;AACA;EACC,6BAA6B;EAC7B,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAkB;MAAlB,sBAAkB;UAAlB,mBAAkB;EAClB,wBAAsB;MAAtB,qBAAsB;UAAtB,uBAAsB;EACtB,gBAAgB;EAChB,kBAAmC;EACnC,W9BpEc;E8BqEd,UAAU;EACV,MAAM;EACN,OAAO;EACP,SAAS;EACT,wBAAwB;EACxB,WAAW;EACX,YAAY,EAAA;;AAGb,mDAAA;AACA;EAIE,qBjCI0C;EDtF1C,yBCqFyC;EDnFzC,6FAAsD;EAAtD,2DAAsD;EkCkFtD,W9BrFa;E8BsFb,eAAmC,EAAA;;AAKrC,eAAA;AACA;EACC,eAAe,EAAA;;AAGhB;EACC,gBAAgB,EAAA;;AAGjB;EACC,eAAe,EAAA;;AAGhB;EACC,iBAAiB,EAAA;;AAGlB,iBAAA;AACA,sCAAA;AACA;EACC,6BAA6B;EAC7B,SAAS;EACT,OAAO;EACP,QAAQ;EACR,iBAAiB;EACjB,kBAAkB;EAClB,8B9B3Gc;E8B4Gd,gBAAgB;EAChB,oC9B7Gc;E8B8Gd,4BAA4B;EAC5B,qBAAqB;EACrB,eAAe;EACf,YAAY;EACZ,WAAW;EACX,sBAAwB;EACxB,iBAAiB;EACjB,kBAAkB;EAClB,8DAAsD;EAAtD,sDAAsD;EACtD,wDjCrI2B;UiCqI3B,gDjCrI2B;EiCsI3B,UAAU,EAAA;EApBX;IAuBE,eAAe;IACf,YAAY;IACZ,WAAW;IACX,iBAAiB;IACjB,mBjC7I0B;IiC8I1B,qBjCpG2C;IiCqG3C,wCAAgC;YAAhC,gCAAgC,EAAA;;AAIlC,uBAAA;AACA;gFpC49EgF;AoC19EhF;;EAEC,+EAA+D;UAA/D,uEAA+D,EAAA;EAFhE;;;;IAME,6DAAiD;YAAjD,qDAAiD,EAAA;EANnD;;IAWG,mDAAuC;YAAvC,2CAAuC,EAAA;;AAK1C;EACC,kEAA0D;UAA1D,0DAA0D,EAAA;;AAG3D;;;;EpC09EE;AoCp9EF;EACC,iCAAiC,EAAA;EADlC;IAKE,iC9BvKa,EAAA;;A8B0Kf;EACC,iC9B3Kc,EAAA;;A8B8Kf,aAAA;AAKA;EAEC,6BAA6B;EAC7B,c9BvLiB;E8BwLjB,qBjCwE+B,EAAA;EiC5EhC;;IAUE,c9B9LgB;I8B+LhB,yBAAkD;IAClD,qBjCgE8B,EAAA;EiC5EhC;IAiBE,c9BrMgB;I8BsMhB,6BAA6B,EAAA;;AAS/B;ElClLI,mBkCmLkB,EAAA;;AAGtB,iBAAA;AACA;;EAEC,qBjCmHkC;EiClHlC,gBAAgB;EAChB,gBAAgB;EAChB,qBAAqB,EAAA;;AAGtB,gBAAA;AAKE;EAHA,wDjC3O0B;UiC2O1B,gDjC3O0B,EAAA;;AiC8O1B;EAHA,wDjC7MsB;UiC6MtB,gDjC7MsB,EAAA;;AiCgNtB;EAHA,uDjC1O0B;UiC0O1B,+CjC1O0B,EAAA;;AiC6O1B;EAHA,uDjCzOuB;UiCyOvB,+CjCzOuB,EAAA;;AiC4OvB;EAHA,uDjCxO0B;UiCwO1B,+CjCxO0B,EAAA;;AiC2O1B;EAHA,uDjCvOyB;UiCuOzB,+CjCvOyB,EAAA;;AiC4K3B;EA2DE,wD9BvOa;U8BuOb,gD9BvOa,EAAA;;A8B0Ob;EAHA,qDjCtO6E;UiCsO7E,6CjCtO6E,EAAA;;AiC8O/E,aAAA;AACA;EACC,yBAAyB;EACzB,UAAU;EACV,mBAAmB,EAAA;EAHpB;IAOG,qC9B7OY;I8B8OZ,yBAAyB,EAAA;EAR5B;IAYG,uBAAuB;IACvB,cjC3JwC,EAAA;EiC8I3C;IAiBG,8CAA+C,EAAA;EAjBlD;IAsBE,0BAA0B;IAC1B,oBAAoB,EAAA;EAvBtB;IA2BE,yBAAyB;IACzB,mBAAmB,EAAA;EA5BrB;IAgCE,uBAAuB;IACvB,iBAAiB,EAAA;;AAInB;EAEC,0CAAmC;EACnC,oCAAoC,EAAA;EAHrC;IAME,0CAAmC,EAAA;EANrC;IAUE,sBAAwB;IACxB,qDAA6C,EAAA;;ACvS/C;EACC,cAAc;EACd,yBlCmiB+B,EAAA;EkCriBhC;IAIE,SAAQ;IACR,YAAY;IACZ,gBAAgB;IAChB,cAAc,EAAA;;AAIhB,4CAAA;AACA;;EAEC,4DAA2D;UAA3D,oDAA2D,EAAA;EAF5D;;IAKE,kBAAkB,EAAA;;AAIpB;EACC,wBAAgB;UAAhB,gBAAgB,EAAA;;AAGjB;EACC,wBAAgB;UAAhB,gBAAgB,EAAA;;AAGjB,mCAAA;AACA;;EAEC,YAAY,EAAA;EAFb;;IAKE,oBlCka6B,EAAA;;AkC9Z/B,eAAA;AACA;EACC,oBlCsa6B,EAAA;;AkCna9B;EACC,qBAAqB;EACrB,UAAU;EACV,oBAAoB,EAAA;;AC/CrB;EACC,kBAAkB,EAAA;;AAGnB;EACC;oFtC0wFmF;EsCxwFnF,uHAA4E;EAA5E,mFAA4E,EAAA;;AAG7E;EACC;oFtCywFmF;EsCvwFnF,uHAA2E;EAA3E,kFAA2E,EAAA;;ACb5E;EACC,uBAAuB,EAAA;;AAGxB,4BAAA;AACA;ErC6LE,wCqC5LwC;ErC6LhC,gCqC7LgC;EACzC,8DAAsD;EAAtD,sDAAsD;EACtD,UAAU;EACV,kBAAkB;EAClB,cAAc,EAAA;;AAGf,kBAAA;AACA;EACC,uDpCkXqE;UoClXrE,+CpCkXqE;EoCjXrE,yBAAyB,EAAA;EAF1B;IAKE,gBAAgB;IAChB,eAAe,EAAA;EANjB;IAUE,WpCwW2B;IoCvW3B,YAAY,EAAA;EAXd;IAeE,YpCkW4B;IoCjW5B,YAAY,EAAA;EAhBd;IAoBE,cpC4V8B;IoC3V9B,YAAY,EAAA;EArBd;IAyBE,gBpCsVgC;IoCrVhC,YAAY,EAAA;EA1Bd;IrCmCI,4BqCFyB;IrCGzB,6BAF6C;IAG7C,4BAH4D;IAI5D,yBAJwE,EAAA;;AqCK5E,sCAAA;AACA;;EAEC,kBAAkB;EAClB,eAAe;EACf,oBAAoB;EACpB,iCAAiC;EACjC,2BAA2B;EAC3B,8BAA8B;EAC9B,kBAAkB,EAAA;;AAGnB;EACC,2BAA2B,EAAA;;AAG5B;EACC,2BAA2B,EAAA;;AAG5B;EACC,2BAA2B,EAAA;;AAI5B;EACC,iBAAiB,EAAA;;AAIlB,iBAAA;AACA;EAIG,wBAAwB,EAAA;;AAK3B,8BAAA;AACA;EAGE,SAAS;EACT,UAAU;EACV,gBAAgB;EAChB,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,gBjChGa;EiCiGb,iDAAyC;UAAzC,yCAAyC;ErC0FzC,6BqCxF8B;ErCyFtB,qBqCzFsB;EAC9B,8DAAsD;EAAtD,sDAAsD;EACtD,+BAAuB;UAAvB,uBAAuB;EACvB,UAAU;EACV,kBAAkB;EAClB,cAAc,EAAA;EAjBhB;IAoBG,8BAA8B;IAC9B,iCAAiC,EAAA;;AArBpC;EA0BE,kBAAkB;EAElB,wCAAA;EASA,cAAA,EAAe;EArCjB;IAgCI,WAAW;IACX,UAAU,EAAA;EAjCd;IAwCI,gBAAgB;IAChB,4BAA4B;IAC5B,kBAAkB;IAClB,kBAAkB;IAClB,QAAQ;IACR,MAAM;IACN,SAAS;IACT,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,qBpCwO+B,EAAA;EoCzRnC;IAwDI,mBpC1HoB;IoC2HpB,cpCzG0C,EAAA;IoCgD9C;MA4DK,6BAAqB;cAArB,qBAAqB;MrCwCxB,2BqCvC+B;MrCwCvB,mBqCxCuB;MrCgE/B,oCqC/DwC;MrCgEhC,4BqChEgC;MACrC,UAAU;MACV,mBAAmB,EAAA;;AC5JxB;EACC,kBAAkB;EAClB,qBAAqB;EACrB,UAAU;EACV,WAAW;EACX,gBAAgB;EAChB,sBAAsB;EACtB,kBAAkB,EAAA;;AAGnB;;;EAGC,kBAAkB;EAClB,OAAO;EACP,SAAQ;EACR,WAAW;EACX,kBAAkB;EAClB,+BAA+B,EAAA;;AAEhC;EACC,gBAAgB,EAAA;;AAEjB;EACC,iBAAiB;EACjB,0BAAA,EAA2B;;AAE5B;EACC,cAAc,EAAA;;AAGf;EACC,mBtCwNkC,EAAA;;AsCtNnC;EACC,iBtCqNkC,EAAA;;AsCnNnC;EACC,mBtCkNkC,EAAA;;AsChNnC;EACC,mBtC+MkC,EAAA;;AuC1PnC;EACC,cAAc;EACd,kBAAkB;EAClB,YAAY;EACZ,oBvCsPkC;EuCrPlC,0BAA0B;EAC1B,kBAAkB;EAClB,wBAAwB;EACxB,gBAAgB;EAChB,kBtC2TwB;EsC1TxB,iBAAiB,EAAA;EAVlB;IAaE,aAAa,EAAA;;AAIf;EAEC,sCAAA;EAOA;;GzCu7FE,EyCr7FC;EAXJ;IAIE,wBAAuB,EAAA;;AAYzB;EAEE,yBAAwB,EAAA;;ACnC1B;EACC,aAAa;EACb,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,oCAAiC;EACjC,cAAc;EACd,WAAW;EAEX;;;;;E1C49FC;E0Ct9FD;;6B1Cy9F4B,E0Cv9FC;;AAK9B;;;;E1Cw9FE;A0Cl9FF;EAEE,WAAW;EACX,0CpC5Ba;EoC6Bb,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS,EAAA;;AARX;EAYE,oBAAa;EAAb,oBAAa;EAAb,aAAa,EAAA;;AC5Cf;EACC,wBAA2B;EAC3B,cAAc;EACd,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,UAAU;EACV,uBAAuB;EAEvB,+BxCyuB+B;UwCzuB/B,uBxCyuB+B,EAAA;;AwCtuBhC;EACC,gBxCiuB+B;EwChuB5B,iBAAoD;EACpD,YAAY;EAGZ,+BxCguB4B;UwChuB5B,uBxCguB4B,EAAA;EwCtuBhC;IAUE,4BAAiD;IACjD,YAAY,EAAA;IAXd;MAcG,WAAU;MACV,YAAW;MACX,WAAU;MACV,oCrCtBY;MqCuBZ,kBAAiB;MACjB,YAAY;MAGZ,+BxCgtB6B;cwChtB7B,uBxCgtB6B;MwC9sB7B,UAAU;MACV,kBAAkB,EAAA;EAzBrB;IA8BK,QAAQ;IACR,qCAAkC,EAAA;IA/BvC;MAkCM,UAAU,EAAA;IAlChB;MAwCO,UAAU;MACV,mBAAmB,EAAA;;AAM1B;;;;;;;;;;E3Cm/FE;A2Cv+FF;;E3C0+FE;A2Ct+FF;;;;E3C2+FE;A2Cp+FF,YAAA;AACA;;;;E3Cy+FE;A2Cn+FF;EAEC,gBAAgB;EAChB,kBAAkB;EAElB,uBAAuB,EAAA;EALxB;IAUG,8BAA8B,EAAA;EAVjC;IAeI,8BAA8B;IAC9B,2BAA2B,EAAA;EAhB/B;IAuBG,2BAA2B,EAAA;EAvB9B;IA4BE,mBxC2nB+B;IDzb7B,yBAAsB;IyC/LxB,gBAAgB,EAAA;;AAIlB;EAEC,iBAAiB;EACjB,kBAAkB;EAElB,sBAAsB,EAAA;EALvB;IAUG,+BAA+B,EAAA;EAVlC;IAeI,+BAA+B;IAC/B,4BAA4B,EAAA;EAhBhC;IAuBG,4BAA4B,EAAA;EAvB/B;IA4BE,mBxCpJ0B;ID+SxB,YAA0B;IyCxJ5B,gBAAgB,EAAA;;AAKlB;EACC,qBAAqB;EACrB,uBAAuB;EAEvB,kBAAkB;EAElB,qBAAqB,EAAA;EANtB;IASE,oBAAwB;IACxB,SAAQ,EAAA;EAVV;IAcE,cAAc,EAAA;;AAIhB;EAEC,6BAA6B,EAAA;EAF9B;IAKE,aAAa,EAAA;EALf;IAUE,6BAA6B,EAAA;IAV/B;MAaG,aAAa,EAAA;;AC/LhB,iBAAA;AACA;EACC,iDtCec;UsCfd,yCtCec,EAAA;;AsCZf,gBAAA;AAEC;EACC,uBAAe;UAAf,eAAe,EAAA;;AAIjB,gBAAA;AACA;EACC,qBAAqB;EAAE,yCAAA;EAsCvB;;;C5C4kGA,E4CzkGC;EA1CF;IAIE,0BAA0B;IAAE,mDAAA;IAC5B,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,+BAA+B;IAC/B,kCAA0B;YAA1B,0BAA0B;IAAE,6BAAA,EAA8B;EAR5D;IAYE,oCtCRa;IsCSb,gBAAgB;IAChB,yBAAyB,EAAA;EAd3B;IAkBE,iBAAiB;IACd,gBAAgB;IAEnB,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,mBAAe;QAAf,eAAe,EAAA;EAvBjB;;;IA6BE;c5C6mGY;I4C3mGZ,WAAW;IACX,gBzCgJQ;IyC/IR,cAAc;IACd,UAAU;IACV,WtCzCa;IsC0Cb,kBAAkB,EAAA;EApCpB;IA4CE,WtClDa;IsCmDb,yBtCzCa,EAAA;;AsC6Cf,sBAAA;AACA;EAEE,yDzCjB4C;UyCiB5C,iDzCjB4C,EAAA;;AyCe9C;EAKE,kCAA2D,EAAA;;AAI7D,yBAAA;AACA;EACC,uBAAuB,EAAA;;AAGxB,qBAAA;AACA;EAEC,qBAAqB,EAAA;EAFtB;IAKE,gBAAgB;IAChB,eAAe,EAAA;EANjB;IAUE,6BAAwC;IACxC,8BAAyC,EAAA;IAX3C;MAcG,0BAAqC,EAAA;;AAOxC,QAAA;AACA;;EAEE,kBAAkB;EAClB,MAAM;EACN,SAAS;EACT,eAAe,EAAA;EALjB;;IAQE,WAAW;IACX,eAAe;IACf,SAAS,EAAA;EAVX;;IAcE,kBAAkB;IAClB,SAAQ,EAAA;;AAIV;EACC,SAAS;EACT,SAAS,EAAA;EAET;IACC,oCAA2B;YAA3B,4BAA2B,EAAA;;AAI7B,SAAA;AACA;EACC,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,oBAAoB,EAAA;EANrB;IASE,gBAAgB;IAChB,eAAe;IACf,gBAAgB,EAAA;EAGjB;IACC,qCAA6B;YAA7B,6BAA6B,EAAA;;AAI/B,UAAA;AACA;EACC,kBAAkB;EAClB,MAAM;EACN,QAAQ;EACR,WAAW;EACX,YAAY;EACZ,oBAAoB;EACpB,iBAAiB,EAAA;EAPlB;IAUE,gBAAgB;IAChB,eAAe;IACf,gBAAgB,EAAA;EAGjB;IACC,oCAA4B;YAA5B,4BAA4B,EAAA;;AA9J7B;EAmKA,kCAA0B;UAA1B,0BAA0B,EAAA;;AAG3B,eAAA;AACA;EAAY,gBAAgB,EAAA;;AC9K5B;EAEE,mBAAmB,EAAA;;AAFrB;;;EASG,mBAAqC,EAAA;;AATxC;EAcE,kB1CsTuB;E0CrTvB,iBAAiB,EAAA;EAfnB;IAkBG,oCAAyC;IACzC,WvCZY,EAAA;;AuCPf;EAyBG,sBAAsB;EACtB,kB3CgOgC,EAAA;;A4C1PnC;EACC,gBAAe;EACf,eAAe,EAAA;;AAGhB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EAEzB,kBAAkB;EAClB,sBxCHc;EwCId,2DAA2D;UAA3D,mDAA2D;EAE3D,qB3CwL8B;E2CvL9B,kB3CsTwB;E2CnTxB,qCAAqC;EACrC,gCAAgC;EAEhC,kB3CgTwB;E2C9SxB,yCAAkC;EAAlC,iCAAkC;EAElC,oBAAA;EAuBA,kBAAA;EASA,qBAAA;EAuDA,mBAAA;EACA;;;;;;G9CmrGE;E8C3qGF,iBAAA,EAAkB;EAnHnB;IAyBE,kBAAkB;IAKf,0B3CiSoB,EAAA;I2C/TzB;MAiCG,kB3CqrBuB,EAAA;M2CttB1B;QAqCI,0B3C0RqB,EAAA;E2C/TzB;IA8CG,YAAY;IACZ,YAAY,EAAA;EA/Cf;IAqDE,0BAA0B;IAAE,0EAAA;IAC5B,a3CqckB;I2CpclB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,wBAAwB;IACxB,4BAA4B;IACzB,uBAAuB;IACvB,2BAA2B;IAC9B,gBAAgB;IAChB,SAAQ;IAER;0B9CuvGwB;I8C7uGxB,6BAAA,EAA8B;IA3EhC;;;;MAwEG,aAAa,EAAA;IAxEhB;MA6EG,gB3Cge2B;M2C/d3B,gBAAgB;MAChB,yEAA0E;cAA1E,iEAA0E;MAE1E,6BAAA,EAA8B;MAjFjC;QAmFI,mB5CkK+B;Q4CjK/B,gBAAe,EAAA;IApFnB;MAyFG,aAAa,EAAA;IAzFhB;;MA8FG,aAAyB;MACzB,cAA0B,EAAA;IA/F7B;MAmGG,mBAAO;UAAP,WAAO;cAAP,OAAO;MACP,gBAAgB;MAChB,gBAAgB;MAChB,yBAAyB,EAAA;EAtG5B;IAuHG,iBAAiB,EAAA;EAvHpB;IA8HK,4BAA4B;IAC5B,gBAAgB;IAChB,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,UAAU;IACV,2BAAoB;IAApB,2BAAoB;IAApB,oBAAoB;IACpB,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IACvB,iBAAiB;IACjB,c3CtIsB,EAAA;E2CF3B;IAgJE,sFAAwE;YAAxE,8EAAwE;IACxE;;;I9CotGE,E8CjtGC;;AAIL,mCAAA;AACA;EACC,kB3C4jByB;E2C3jBzB,mBAAmB;EACnB,8B3C7J2B;E2C8J3B,mBAAmB;EACnB,YAAY;EACZ,gBAAgB;EAChB,mBAAmB;EACnB,8BAA8B,EAAA;EAR/B;;IAaE,gBAAgB,EAAA;EAblB;IAiBE,gBAAgB,EAAA;;AAIlB,iBAAA;AACA;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAkB;MAAlB,sBAAkB;UAAlB,mBAAkB;EAClB,gBxChLc;EwCiLd,gB3CqiB2B;E2CliB3B,4CAA6C;EAE7C,0BAAwD;E5C/ErD,kD4CmFgD;E5ChFhD,0C4CgFgD;EAMnD,wCAAA;EAQA,kBAAA,EAAmB;EAZnB;IACC,kB3CgIuB,EAAA;E2ChJzB;IAqBE,kB3CkhBwB,EAAA;E2CviB1B;IAwBE,mB3C+gBwB,EAAA;E2CviB1B;IA6BE,mBAAO;QAAP,WAAO;YAAP,OAAO;IACP,mB5CwCiC;I4CvCjC,SAAS;IACT,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAkB;QAAlB,sBAAkB;YAAlB,mBAAkB;IAClB,iB3CugB0B;I2CtgB1B,cAAc;IACd,WAAW;IACX,kBAAkB;IAElB,gBAAgB;IAmBhB,sBAAA,EAAuB;IA1DzB;MA0CG,wBAAmC,EAAA;IA1CtC;MA8CG,gBAAgB,EAAA;IA9CnB;MAkDG,qBAAqB;MACrB,SAAQ;MACR,YAAY;MACZ,gBAAgB;MAChB,kB5CgBgC;M4CfhC,mBAA0B,EAAA;IAvD7B;MA4DG,oB3CbiB,EAAA;;A2CoBpB;;EAGE,cAAc,EAAA;;AAMhB,wBAAA;AACA;EAMC;;;;;;;;;;;G9CorGE,E8CzqGC;EAjBJ;IAGE,iCxCnPa,EAAA;;AwCoQf;;;E9C6qGE;A8CxqGF;EACC;;;;;;;;;;G9CmrGE,E8CzqGC;;AAGJ,6CAAA;AACA;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAkB;MAAlB,sBAAkB;UAAlB,mBAAkB;EA+ElB,4EAAA,EAA6E;EAjF9E;IAKE,mB3Cib6B;I2C/a7B,UAAU;IACV,W3C6awB;I2C5axB,Y3C4awB;IDnlBtB,8B4CyK4B;Y5CzK5B,sB4CyK4B;IAE9B,kBAAkB;IAClB,YAAY;IAQZ,0CAAA;IAgBA;;;;;;;;;I9CwpGE,E8C/oGC;IA/CL;MAmBG,UAAU,EAAA;IAnBb;MAyBG,mB3C9TyB,EAAA;I2CqS5B;MA8BG,mB3CjUyB,EAAA;I2CmS5B;MAmCG,mB3CrUwB,EAAA;E2CkS3B;IAoDE,Y3CgY0B;I2C/X1B,kBAAmB;IAAE,2CAAA;IACrB,gBAAgB;IAChB,mB3C2XwB;I2C1XxB,4BAA4B;IAC5B,mBAAmB;IACnB,eAAe;IACf,uBAAuB;IACvB,SAAS;IACT,oB3C0XgC;I2CzXhC,cAAc;IACd,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB,EAAA;IAjEzB;MAoEG,uDAA+C;cAA/C,+CAA+C,EAAA;IApElD;MAwEG,QAAQ;MACR,S3C2WyB;M2C1WzB,qBAAqB;MACrB,SAAQ;MACR,gBAAe,EAAA;EA5ElB;IAmFE,4BAA6C,EAAA;;AAK/C;EACC,YAAY,EAAA;;AAGb,gBAAA;AACA;EACC,yB3C4VsE;E2C3VtE,8BAAsB;UAAtB,sBAAsB;EACtB,qB3CpM8B;E2CqM9B,kBAAiC;EACjC,kBAAkB;EAClB,gBAAgB;EAEhB,6BAA6B;EAC7B,iCAAA,EAAkC;EATnC;IAYE,YAAW;IACX,6dAA6d;IAC7d,kBAAiC;IACjC,kBAAkB;IAClB,MAAK;IACL,QAAO;IACP,SAAQ;IACR,OAAM;IACN,YAAY,EAAA;EApBd;IAwBE,mB3CqUqE;I2CpUrE,WAAW;IACX,8BAAsB;YAAtB,sBAAsB;IACtB,UAAU;IACV,UAAU,EAAA;;AAIZ;EAcC;;;;G9C0nGE,E8CtnGC;EAlBJ;IAGE,gBxClaa;IwCmab,0FAA4D;IAA5D,2DAA4D;IAC5D,wBAAgB;YAAhB,gBAAgB,EAAA;IALlB;MAQG,WAAW;MACX,gBAAgB,EAAA;;AAanB;EACC,0CAAA;EACA;IAEE,mB5CtMgC;I4CwMhC,iBAAiC,EAAA;IAJnC;MAOG,kBAAkB,EAAA;IAPrB;MAUK,mBAAmB,EAAA;IAVxB;MAcG,mBAAmB,EAAA;EAdtB;IAsBG,aAAa,EAAA,EACb;;AAKJ,iDAAA;AACA;EACC;;;IAIE,aAAa,EAAA;EAJf;;;;IAWE,YAAY;IACZ,qBAAqB,EAAA;EAZvB;IAgBE,SAAS,EAAA,EACT;;AC7eH;EACC,uD5CgYqE;U4ChYrE,+C5CgYqE;E4C/XrE,YAAY,EAAA;EAFb;IAKE,qB5CsYoC,EAAA;E4C3YtC;IAUE,gBAAgB;IAChB,mB7C+OiC;I6C9OjC,gCAAgE;IAChE,wBAAuB,EAAA;EAbzB;IAiBE,UAAU,EAAA;;AAKZ;EACC,oB5C8WiC,EAAA;;A4C3WlC;EACC,iB5C0WiC,EAAA;;A6CrYlC;EAAe,WAAY,EAAA;;AAC3B;EAAe,WAAY,EAAA;;AAC3B;EAAe,YAAa,EAAA;;AAC5B;EAAe,YAAa,EAAA;;AAC5B;EAAe,YAAa,EAAA;;ACG5B;;EACE,mB9CL0B;E8CM1B,kBAAiB;EACjB,WARmB;EASnB,YATmB;EAUnB,6BAA4B;EAC5B,UAAS;EACT,QAAO;EACP,SAAQ;EACR,sBAAuB;EACvB,kBAAiB;EACjB,iBAhBmB;EAiBnB,uCAA4B;UAA5B,+BAA4B;EAC5B,oDAAmC;EAAnC,4CAAmC;EAAnC,oCAAmC;EAAnC,sEAAmC;EACnC,gFACwC;UADxC,wEACwC,EAAA;EAEtC;;IACE,mB9CqBwC,EAAA;;A8CjB9C;EACE,eAAc;EACd,a9CsK6B;E8CrK7B,iBAA2C;EAC3C,Y9CgeiB,EAAA;;A8C7dnB;EACE,aAAY,EAAA;;AAGd;;EAGE,eAAe,EAAA;;AAGjB;EACE,Y9CkdiB;E8CjdjB,2EAAoE;UAApE,mEAAoE;EACpE,kCAA0B;UAA1B,0BAA0B;EAC1B,eAAc;EACd,SAAQ;EAER,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB,EAAA;;AAGzB;EACE,UAvDkB;EAwDlB,WAxDkB;EAyDlB,mBAAmB;EACnB,cAAc;EACd,uBAAe;EAAf,eAAe;EACf,wFAAgF;UAAhF,gFAAgF,EAAA;;AAGlF;EACE,0CAAkC;UAAlC,kCAAkC;EAClC,kCAA0B;UAA1B,0BAA0B;EAC1B,uDAA4C;UAA5C,+CAA4C;EAC5C,mB9C/D6E,EAAA;E8C2D/E;IAOI,yFAAiF;YAAjF,iFAAiF;IACjF,mEAA2D;IAC3D,2DAAmD,EAAA;;AAIvD;EACE,sEAAmE;UAAnE,8DAAmE,EAAA;EADrE;IAOU,kCAAmC;YAAnC,0BAAmC;IACnC,2CAAkD;YAAlD,mCAAkD,EAAA;EAR5D;IAOU,kCAAmC;YAAnC,0BAAmC;IACnC,2CAAkD;YAAlD,mCAAkD,EAAA;EAR5D;IAOU,kCAAmC;YAAnC,0BAAmC;IACnC,4CAAkD;YAAlD,oCAAkD,EAAA;EAR5D;IAOU,kCAAmC;YAAnC,0BAAmC;IACnC,4CAAkD;YAAlD,oCAAkD,EAAA;EAR5D;IAOU,kCAAmC;YAAnC,0BAAmC;IACnC,4CAAkD;YAAlD,oCAAkD,EAAA;;ACxF5D,+BAAA;AACA;EACK,Y/C6vBqB,EAAA;;A+C1vB1B;EACI,gBAAA;EALJ;IhDkLE,uCAA0C;IAElC,+BAAkC;IgD5KpC,Y/CwfW;I+CvfX,6BAA6B;IAC7B,MAAK;IACL,SAAQ;IAER,yB/CcgB;I+CZhB,8D/C4e2D;I+C5e3D,sD/C4e2D,EAAA;E+Cze/D;IACI,uBAAuB;IACvB,oCAA4B;IAA5B,4BAA4B,EAAA;EAGhC;IAEI,2C5CTO;I4CWP,iBAAA;IACA,Y/CkuBkB,EAAA;E+C/tBtB;IAEI,OAAM,EAAA;EAGV;IAEI,0C5CtBO;I4CwBP,a/CstBkB,EAAA;E+CntBtB;IAEI,QAAQ,EAAA;EAGZ,iDAAA;EACA;;IAGQ,+B5CpCG;I4CqCH,kBAAkB;IAClB,Y/C0cO;I+CzcP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,MAAM,EAAA,EACT;;AC5DT,oCAAA;AACA;;;EAKI,SAAS,EAAA;;AALb;;EAWE,aAAa;EACb,gBAAgB,EAAA;;AAZlB;;;;EAmBE,wBAAuB,EAAA;;AAIzB;EjDhBE,yBiDiB4B;EjDf5B,6FAAsD;EAAtD,2DAAsD,EAAA;;AiDkBxD;EjDpBE,yBCkG0C;EDhG1C,6FAAsD;EAAtD,2DAAsD,EAAA;;AiDwBxD;;;;;EAOE,0C7ClCa,EAAA;;A6C2Bf;;;;EAcE,oC7C/Ba,EAAA;;A6CkCf,gBAAA;AACA;EAII,0IAA0H;UAA1H,kIAA0H;EAC1H,UAAU,EAAA;;AAMd,yBAAA;AACA;EACI,gBAAgB,EAAA;;AAGpB;EACC,uBAAuB;EACvB,YAAY,EAAA;EAFb;;;IAOE,YAAY;IACZ,gBAAgB,EAAA;;AAIlB;EACC,wCAA4C,EAAA;;AAE7C;EACC,2CAA+C,EAAA;;ACpFhD,mBAAA;AACA;EAEC,YjDgmBkC;EiD/lBlC,2CAA2C,EAAA;EAH5C;IAQG,gBAAgB;IAChB,SAAS;IACT,YjDwlBgC;IiDtlBhC;qBpD0yHkB;IoDvyHlB,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,oCAAoC,EAAA;IAjBvC;MAoBI,gCjDlBwB;MiDmBxB,cjDnBwB,EAAA;IiDF5B;MAyBI,cjDvBwB,EAAA;;AiD6B5B,wCAAA;AACA;;;EpDoyHE;AoD/xHF,sBAAA;AACA;EAEE,cAAc,EAAA;;AAFhB;EAME,WAAW,EAAA;;AANb;EASE,cAAc,EAAA;;AAIhB;EACC,gBAAgB;EAChB,oBlDoMkC,EAAA;;AkDjMnC;;EAEC,cjDxD2B,EAAA;;AkDH5B;EACC,yDlDof8D;EkDnf9D,gBAAgB;EAChB,2D/Ccc;U+Cdd,mD/Ccc,EAAA;;A+CZf;;ErD01HE;AAhxHF;4EAkxH4E;AsDj2H5E,wBAAA;AACA;EAAa,uBAAwB,EAAA;;AACrC;EAAa,sBAAwB,EAAA;;AACrC;EAAoB,8BAA8B,EAAA;;AAClD;EAAmB,0BAA0B,EAAA;;AAC7C;EAAoB,2BAA2B,EAAA;;AAE/C,iBAAA;AACA;EAAe,YAAa,EAAA;;AAE5B;EAAa,QAAQ,EAAA;;AACrB;EAAa,aAAc,EAAA;;AAC3B;EAAa,WAAY,EAAA;;AACzB;EAAa,aAAc,EAAA;;AAC3B;EAAa,cAAe,EAAA;;AAC5B;EAAa,WAAY,EAAA;;AACzB;EAAa,cAAe,EAAA;;AAC5B;EAAa,aAAc,EAAA;;AAC3B;EAAa,cAAe,EAAA;;AAC5B;EAAa,WAAY,EAAA;;AACzB;EAAa,cAAe,EAAA;;AAE5B;EAAa,eAAe,EAAA;;AAC5B;EAAa,gBAAgB,EAAA;;AAC7B;EAAa,gBAAgB,EAAA;;AAC7B;EAAa,gBAAgB,EAAA;;AAE7B;EAAa,SAAS,EAAA;;AACtB;EAAa,cAAe,EAAA;;AAC5B;EAAa,YAAa,EAAA;;AAC1B;EAAa,cAAe,EAAA;;AAC5B;EAAa,eAAe,EAAA;;AAC5B;EAAa,YAAa,EAAA;;AAC1B;EAAa,eAAgB,EAAA;;AAC7B;EAAa,cAAe,EAAA;;AAC5B;EAAa,eAAgB,EAAA;;AAC7B;EAAa,YAAa,EAAA;;AAC1B;EAAa,eAAgB,EAAA;;AAE7B;EAAc,gBAAgB,EAAA;;AAC9B;EAAc,iBAAiB,EAAA;;AAC/B;EAAc,iBAAiB,EAAA;;AAC/B;EAAc,iBAAiB,EAAA;;AAE/B,iBAAA;AACA;EAAS,mBAAmB,EAAA;;AAE5B,gBAAA;AACA;EAAe,iCAAiC,EAAA;;AAEhD,SAAA;AACA;EAAU,mBAAO;MAAP,WAAO;UAAP,OAAO,EAAA;;AAEjB,YAAA;AACA;EAAW,cnDsJqB,EAAA;;AmDrJhC;EAAW,gCAA0D,EAAA;;AACrE;EAAW,mBnD4IqB,EAAA;;AmD1IhC,aAAA;AACA;EAAU,enDiJsB,EAAA;;AmD/IhC,oBAAA;AACA;EAAwB,oBAAwD,EAAA;;AAChF;EAAoB,oBAAsB,EAAA;;AAC1C;EAAoB,gBAAiB,EAAA;;AACrC;EAAoB,gBAAgB,EAAA;;AACpC;EAAyB,gBAAiB,EAAA;;AAC1C;EAAoB,gBAAiB,EAAA;;AAErC,cAAA;AACA;EACC,6BAA6B;EAC7B,uCAAoC,EAAA;;AAErC;EACC,6CnDxE2B,EAAA;;AmD2E5B;wEtDw7HwE;AsDt7HxE;EAAc,MAAU,EAAA;;AACxB;EAAc,OAAU,EAAA;;AACxB;EAAc,QAAU,EAAA;;AACxB;EAAc,SAAU,EAAA;;AAExB,iBAAA;AACA;EAAW,2BAA4B,EAAA;;AACvC;EAAW,2BAA4B,EAAA;;AACvC;EAAW,2BAA4B,EAAA;;AACvC;EAAW,2BAA4B,EAAA;;AACvC;EAAW,2BAA4B,EAAA;;AACvC;EAAW,8BAA8B,EAAA;;AAEzC,eAAA;AACA;EAAW,+BAAmC,EAAA;;AAAS,SAAA;AACvD;EAAW,6BAAiC,EAAA;;AAAW,SAAA;AACvD;EAAW,gCAAiC,EAAA;;AAAW,WAAA;AACvD;EAAW,+BAAmC,EAAA;;AAAM,SAAA;AACpD;EAAW,8BAAiC,EAAA;;AAAW,SAAA;AACvD;EAAW,+BAAiC,EAAA;;AAAW,SAAA;AACvD;EAAW,0BAAiC,EAAA;;AAAW,SAAA;AACvD;EAAW,6BAAkC,EAAA;;AAAU,gBAAA;AAEvD,WAAA;AACA;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,YAAc,EAAA;;AAC7B;EAAe,aAAc,EAAA;;AAC7B;EAAe,UAAc,EAAA;;AAE7B,gBAAA;AACA;EAAY,sBhDvHG;EgDuHuB,cnDuWG,EAAA;;AmDtWzC;EAAY,yBnDsaoB,EAAA;;AmDrahC;EpDxHE,sBIDa;EJGb,0FAAsD;EAAtD,wDAAsD,EAAA;;AoDuHxD;EAAkB,uBAA8C,EAAA;;AAChE;EpD1HE,sBIDa;EJGb,0FAAsD;EAAtD,wDAAsD,EAAA;;AoDyHxD;EAAgB,yBAA0C,EAAA;;AAG1D;EAAe,yBnD3GQ,EAAA;;AmD4GvB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD4GxB;EAAe,yBnD3GS,EAAA;;AmD6GxB,YAAA;AACA;EAAgB,wCnDtC4B,EAAA;;AmDuC5C;EAAqB,wCAAwC,EAAA;;AAE7D,kBAAA;AAEA;EAA6B,oCAAoC,EAAA;;AACjE;EAA+B,uCAAuC,EAAA;;AACtE;EAA6B,qCAAqC,EAAA;;AAClE;EAAgC,wCAAwC,EAAA;;AACxE;EAAgB,mBnD4Ka,EAAA;;AmD3K7B;EAAkB,sBnDwKO,EAAA;;AmDvKzB;EAAe,0BAAwD,EAAA;;AAEvE,iBAAA;AACA;;;;8BtD4jI8B;AsDtjI9B,WAAA;AACA;EpD2BE,gCAKwC;EAJhC,wBAIgC,EAAA;;AoD/B1C;EpD0BE,iCAKwC;EAJhC,yBAIgC,EAAA;;AoD9B1C;EpDyBE,iCAKwC;EAJhC,yBAIgC,EAAA;;AoD5B1C,YAAA;AACA;EAAY,mCAA2B;UAA3B,2BAA2B,EAAA;;AACvC;EAAmC,+EAAwE;UAAxE,uEAAwE,EAAA;;AAC3G;EAAmC,uDAA+C;UAA/C,+CAA+C,EAAA;;AAClF;EAAmC,yDAAiD;UAAjD,iDAAiD,EAAA;;AACpF;EAAmC,yDAAiD;UAAjD,iDAAiD,EAAA;;AACpF;EAAmC,4DAAoD;UAApD,oDAAoD,EAAA;;AAEvF;EAA+C,sEAA2D;UAA3D,8DAA2D,EAAA;;AAC1G;EAA+C,+DAAuD;UAAvD,uDAAuD,EAAA;;AACtG;EAA+C,+DAAuD;UAAvD,uDAAuD,EAAA;;AACtG;EAA+C,8DAAsD;UAAtD,sDAAsD,EAAA;;AACrG;EAA+C,8DAAsD;UAAtD,sDAAsD,EAAA;;AAErG;EAEE,sEAAqC;UAArC,8DAAqC,EAAA;;AAGvC;EAEE,gEAAkC;UAAlC,wDAAkC,EAAA;;AAGpC;EAEE,+DAAqC;UAArC,uDAAqC,EAAA;;AAKvC;;EtDslIE;AsDjlIF,iBAAA;AACA,4CAAA;AACA;EACC,gBhD3Mc;EgD4Md,mCAA2B;EAA3B,2BAA2B;EAC3B,cAAc,EAAA;EAHf;IAME,mBAAmB;IACnB,cAAc,EAAA;;AAIhB,uBAAA;AACA,sCAAA;AACA,iEAAA;AACA;EACC,kBAAkB,EAAA;EADnB;IAGE,WAAW;IACX,8BAA8B;IAC9B,SAAS;IACT,UAAU;IACV,cAAc;IACd,kBAAkB;IAClB,QAAQ;IACR,SAAS;IACT,mBAAmB;IACnB,qCAA6B;IAA7B,6BAA6B,EAAA;EAZ/B;IAiBG,UAAU;IACV,WAAW;IACX,QAAQ;IACR,SAAS,EAAA;;AAKZ,0BAAA;AACA;EAEE,2HAA+E;EAA/E,iFAA+E,EAAA;;AAFjF;EAKE,2HAA+E;EAA/E,iFAA+E,EAAA;;AAIjF,iCAAA;AACA;EAEI,aAAY,EAAA;;AAFhB;EAOG,cAAc,EAAA;;AAKjB;EAEI,cAAc,EAAA;;AAFlB;EAOG,aAAa,EAAA;;AAKhB,cAAA;AACA;EAAgB,anDoOI,EAAA;;AmDnOpB;EAAgB,YnDoOG,EAAA;;AmDnOnB;EAAiB,UnDoOA,EAAA;;AmDnOjB;EAAgB,YnDoOG,EAAA;;AmDlOnB;;CtDmkIC;AsD1iIC;EACD,uBAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,4BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,0BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,2BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,uBAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,uBAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,2BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,4BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,4BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,0BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,2BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,2BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,4BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,4BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,uBAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,2BAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,uBAA6B;EAAE,sEAAA,EAAuE;;AADrG;EACD,0BAA6B;EAAE,sEAAA,EAAuE;;AAIvG,WAAA;AACA;EACC,8BAA8C,EAAA;;AAG/C,gDAAA;AtD8mIA;EsD3mIE,aAAa,EAAA;;AtD8mIf;EsD3mIE,aAAa,EAAA;;AtD8mIf;EsD3mIE,cAAc,EAAA;;AtD8mIhB;EsD3mIE,cAAc,EAAA;;AtD8mIhB;EsDxmIE,cAAc,EAAA;;AtD2mIhB;EsDxmIE,cAAc,EAAA;;AtD2mIhB;EsDxmIE,aAAa,EAAA;;AtD2mIf;EsDxmIE,aAAa,EAAA;;AAIf,gBAAA;AACA;EACC,kBAAkB;EAClB,UAAU;EACV,gBAAgB;EAChB,iBAAiB;EACjB,kDAAuC;UAAvC,0CAAuC;EACvC,yBAAiB;KAAjB,sBAAiB;MAAjB,qBAAiB;UAAjB,iBAAiB,EAAA;EANlB;;;IAYE,WAAW;IACX,kBAAkB;IAClB,cAAc,EAAA;EAdhB;IAkBE,MAAM;IACN,QAAQ;IACR,OAAO;IACP,UAAU;IACV,YAAY;IACZ,mBAAmB,EAAA;EAvBrB;;;IA6BE,UAAU;IACV,gBAAgB;IAChB,QAAQ;IACR,UAAU;IACV,WAAW;IACX,YAAY;IACZ,kBAAkB,EAAA;EAnCpB;IAuCE,WAAW,EAAA;IAvCb;MA0CG,cAAc;MACd,WAAW,EAAA;IA3Cd;MA+CG,UAAU,EAAA;IA/Cb;MAkDG,UAAU,EAAA;;AAKb,oBAAA;AACA;EAME,qBAAqB,EAAA;EANvB;;IAIG,aAAa,EAAA;;AAMhB,uBAAA;AACA;EAEC,gCAAA;EACA;IAME,qBAAqB,EAAA;IANvB;;MAIG,aAAa,EAAA;EAMhB,6BAAA;EACA;IACC,6BAA6B,EAAA;EAE9B;IACC,6BAA6B,EAAA;EAE9B;IACC,2BAA2B,EAAA;EAG5B,kBAAA;EACA;IACC,YAAY;IACZ,gBAAgB;IAChB,gBAAgB,EAAA;EAGjB;IACC,WAAW;IACX,eAAe;IACf,eAAe,EAAA;EAGhB,0BAAA;EACA;IACC,yCAAkD,EAAA;EAEnD;IACC,uBAAuB;IACvB,2BAA2B,EAAA,EAC3B;;AAIF,aAAA;AACA;EA4CC,gBAAgB,EAAA;EA5CjB;;;;;;;IASE,kBAAkB,EAAA;EATpB;;;;;;;IAkBE,WAAW;IACX,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,UAAU;IACV,cAAc;IACd,QAAQ;IACR,6CnDrY0C,EAAA;EmD4W5C;;;;;;;IAkCE,WAAW;IACX,kBAAkB;IAClB,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,cAAc;IACd,SAAS;IACT,4CnDrZ0C,EAAA;;AoD5G5C,eAAA;AACA;EACC,YAAY;EACZ,cAAa;EACb,SAAS;EACT,UAAS,EAAA;EAJV;IAME,mBAAmB;IACnB,sBAAsB;IACtB,kBAAkB;IAClB,UAAS,EAAA;IATX;MAYG,gBAAgB,EAAA;;AAMnB,8BAAA;AACA;;EAEC,8BAAsB;UAAtB,sBAAsB;EACtB,aAAa;EACb,mBAAmB,EAAA;EAJpB;;;;IAQE,aAAa,EAAA;;AAIf;EACC,SAAQ;EACR,UAAS;EACT,gBAAgB,EAAA;;AAGjB;ErD4JE,sCqD1JuC;ErD2J/B,8BqD3J+B;ErDmLvC,oCqDlLqC;ErDmL7B,4BqDnL6B;EACrC,UAAU;EACV,mBAAmB,EAAA;;AAIrB;EACC,YAAY;EACZ,8BAA8B,EAAA;EAF/B;IAKE,cAAc,EAAA;;AAKhB;EAEC,qDAA8F,EAAA;;AAO/F,kBAAA;AAMA,oBAAA;AACA;;EAGE,oBAAmB,EAAA;;AAIrB,mBAAA;AACA;EAEC,mBAAkB,EAAA;EAFnB;IAKE,sBAAsB;IACtB,gBAAgB,EAAA;EANlB;IAWG,cAAa,EAAA;EAXhB;IAcG,eAAc;IACd,mBAAkB,EAAA;EAfrB;IAoBE,iBAAiB;IACjB,mBAAmB;IACnB,gBAAgB;IAChB,kBAAkB,EAAA;;AAIpB,gBAAA;AACA;EAEE,eAAe;EACf,kBAAkB,EAAA;EAHpB;IAMG,aAAY,EAAA;EANf;IASG,gBAAe,EAAA;;AAIlB;EAEE,mBAA0B,EAAA;;AAI5B,mBAAA;AACA;EACC,epDyjBiC;EoDxjBjC,gBpDwjBiC,EAAA;;AoDrjBlC;EACC,WpDqjB+B;EoDpjB/B,YpDojB+B,EAAA;;AoDjjBhC;EACC,gBpDijBoC;EoDhjBpC,iBpDgjBoC,EAAA;;AoD7iBrC,gBAAA;AACA;EACC,WAAW;EACX,iBpD2iBiC,EAAA;;AoDziBlC;EACC,iBAA2D;EAC3D,iBpDuiBiC,EAAA;;AoDpiBlC;EACC,YAAY;EACZ,mBAAmB,EAAA;;AAIpB;;;;;;;;;;;;EvDmiJE;AuDrhJF,qBAAA;AACA;EACC,kBAAkB;EAClB,iBrDyEkC;EqDxElC,cAAc;EACd,mBAAmB;EACnB,yBAAyB;EACzB,kBAAkB,EAAA;;AAGnB,yBAAA;AACA;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,qBAAiB;MAAjB,iBAAiB;EACjB,gBAAgB;EAEhB,iCAAiC,EAAA;EALlC;IAQI,mBAAc;QAAd,kBAAc;YAAd,cAAc,EAAA;EARlB;IAWE,aAAa,EAAA;;AAKf,kBAAA;AACA;EACC,kBAAkB,EAAA;EADnB;IAIE,YAAY;IACZ,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,cAAc;IACd,SAAS;IACT,WAAW;IACX,mBpD7M6E;IoD8M7E,kBAAkB;IAClB,sBAAsB,EAAA;EAbxB;IAiBE,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,MAAK;IACL,QAAO,EAAA;EArBT;IAyBE,mBpD/N0B,EAAA;EoDsM5B;IA6BE,mBpDhOyB,EAAA;EoDmM3B;IAiCE,mBpDrO0B,EAAA;;AoDyO5B,eAAA;AACA;EAGE,gBpD1DQ,EAAA;;AoDuDV;EAOE,gBpD5DQ,EAAA;;AoDqDV;EAWE,gBpD9DQ,EAAA;;AoDmEV,0BAAA;AACA,gEAAA;AAEE;EAEC,wBAAwB,EAAA;;AtC5LvB;EsC+LF;IAEC,wBAAwB,EAAA,EAExB;;AtChNC;EsCuMF;IAEC,wBAAwB,EAAA,EAExB;;AtC9LC;EsC+LF;IAEC,wBAAwB,EAAA,EAExB;;AtChNC;EsCuMF;IAEC,wBAAwB,EAAA,EAExB;;AtC9LC;EsC+LF;IAEC,wBAAwB,EAAA,EAExB;;AtChNC;EsCuMF;IAEC,wBAAwB,EAAA,EAExB;;AtC9LC;EsC+LF;IAEC,wBAAwB,EAAA,EAExB;;AtChNC;EsCuMF;IAEC,wBAAwB,EAAA,EAExB;;AACD;EAEC,wBAAwB,EAAA;;AAK3B,kBAAA;AACA;EACC,oBrDvBkC;EqDwBlC,mBAAmB;EACnB,cAA8B;EAC9B,gBAAgB,EAAA;EAJjB;IAOE,oBrD7BiC;IqD8BjC,qBAAqB,EAAA;;AAGvB;EACC,iBAAiB;EACjB,UAAU;EACV,mBAAmB,EAAA;;AAEpB;EACC,2BAA0B,EAAA;;AAG3B,eAAA;AACA;EACC,qBrD5CkC;EqD6ClC,qBAAkB;EAClB,cpDnM4C;EoDoM5C,gBAAgB,EAAA;;AAGjB,iBAAA;AvDmhJA;EuDjhJC,kBAAkB;EAClB,WjDxSc,EAAA;EN2zJb;IuDjhJA,2BAA2B;IAC3B,sBAAsB;IACtB,8BjDlSa;IiDmSb,eAAe;IACf,WAAW;IACX,kBAAkB;IAClB,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB;IACvB,gBAAgB;IAChB,kBAAkB,EAAA;;AAIpB,SAAA;AACA;EACC,mBAAuD;EACvD,gBAAgB;EAChB,kBAAkB,EAAA;;AAInB,kBAAA;AACA;EACI,kBAAiB;EACjB,gBAAe;EACf,eAAc,EAAA;;AAElB;EACG,YAAS;EACT,kBAAkB;EAClB,mBAAkB,EAAA;;AAErB;EACG,YAAS;EACT,kBAAkB,EAAA;;ACpVrB;;;;EAKE,WAAW;EACX,WAAW;EACX,kBAAkB;EAClB,6mBAA6mB;EAIrmB,sBAAsB;EAC9B,SAAS;EACT,OAAO;EACP,QAAQ;EACR,aAAa,EAAA;;AAIf;;EAGE,SAAS;EACT,YAAY;EACZ,6kBAA6kB,EAAA;;AAI/kB;;EAEC,kBAAkB,EAAA;EAFnB;;IAIE,WAAW;IACX,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,QAAQ;ItD/BR,wCCA6E;IDE7E,gHAAsD;IAAtD,8EAAsD;IsDgCtD;gFxDi2J8E,EwDh2JC;;AAIjF;EAEE,WAAW;EACX,SAAS;EtD1CT,6BsD4CiC;EtD1CjC,gHAAsD;EAAtD,8EAAsD;EsD4CtD;gFxD81J8E,EwD71JC;;AAIjF,oBAAA;AACA;EtD6BI,iCsD1B2B;EtD6B3B,yBsD7B2B,EAAA;EAH/B;ItDsIE,8BAUiC;IATzB,sBASyB,EAAA;;AsDtInC;EAEE,2HAAwE;EAAxE,iFAAwE,EAAA;;AAF1E;EAKE,mBAA8C,EAAA;;AC3EhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;CzDq8JC;AyDx6JD;EACI,6BAA6B,EAAA;;AAGjC,6CAAA;AACA;;;;;EzD66JE;AyDt6JF;;;;;;;;EzD+6JE;AyDr6JF,qCAAA;AACA;EACI,8CAA8C;EAC9C,2CAA2C,EAAA;;AAE/C,sBAAA;AACA;EACE,mBtDlD6E;EsDmD7E,WnDpDa,EAAA;AmDkDf;EACE,mBtDlD6E;EsDmD7E,WnDpDa,EAAA;;AmDsDf;EACE,mBtDtD6E;EsDuD7E,WnDxDa,EAAA;;AmD0Df,kCAAA;AACA;;;;EAII,aAAa,EAAA;;AAEjB,0CAAA;AACA;EACE,QAAS;EACT,SAAS,EAAA;;AAEX,UAAA;AACA;EAAI,gCAAiC,EAAA;;AACrC,iBAAA;AACA;EACI,8BAA0B;MAA1B,0BAA0B,EAAA;;AAE9B;EACC,gBAAe;EACZ,iCAA8B;UAA9B,8BAA8B;EAC9B,qCAAqC,EAAA;;AAEzC,eAAA;AACA;;;;;;EzD86JE;AyDv6JF,eAAA;AACA;EACC;;;;sBzD46JqB;EyDv6JrB,yBAAyB;EACzB,+BAA6B;EAC7B,4BAA6B,EAAA;;AAE9B,UAAA;AACA,sCAAA;AACA;;;EzD26JE;AyDv6JF;EACI,wBAAwB;EACxB,qBAAqB;EACrB,gBAAgB;EAChB,iBAAiB,EAAA;;AAErB;EACI,aAAa,EAAA;;AAEjB,mCAAA;AACA,sFAAA;AACA;EACC,6BAA6B,EAAA;;AAE9B;EACC,6BAA6B,EAAA;;AAG9B,uCAAA;AACA;;EAEI,cAAc,EAAA;;AAGlB,iCAAA;AACA;EACC,0CnD1Hc;EmD2Hd,yCnD3Hc,EAAA;;AmD8Hf;EACC,yCnD/Hc,EAAA;;AmDiIf;EACC,yCnDlIc,EAAA;;AmDqIf;;EAEC,wCnDvIc,EAAA;;AoDjBf;EACI,6BAAA;EtDkEJ;IsDhEK,uBAAuB,EAAA;EAGxB;IACC,yBAAyB,EAAA;EAG1B;IACC,gCAAgC,EAAA;EAGpC,gBAAA;EPsCD;IOjCK,wBAAe;YAAf,gBAAe;IACf,yBAA0C,EAAA;EAM9C;;;;;;E1DsjKC;E0D/iKD;;;IAGC,mCAAmC;IACnC,gCAAgC;IAChC,+BAA+B;IAC/B,8BAA8B,EAAA,EAC9B;;A1D+CF;4EAmgK4E;AAjgK5E,uDAAA;A2DzFA;;;;;;;;;;;;;;;;;;;yD3D+mKyD;AiBnjKrD;EPqDJ;IiD1FE,exD6hB8B,EAAA;EC5iBhC;IuDkBE,oBAAoB,EAAA;IADrB;MAIE,qBAAqB,EAAA,EACrB;;AAIH;;;;;;;;;;;E3DimKE;A2DllKF;EAGC;;G3DmlKE;EuCziKH;IoBrCE,gBAAgB;IAGhB,yBAAyB;IACzB,WAAW;IACX,SAAS;IACT,wBAAgB;YAAhB,gBAAgB;IzDmIhB,2ByDjI4B;IzDkIpB,mByDlIoB;IAC5B,UAAU;IAEV,wBAAgB;IAAhB,gBAAgB,EAAA;EAGjB;IACC,mBAAmB,EAAA;EpBsBrB;IoBlBE,aAAa,EAAA;EvD1Ef;IuD8EE,kBAAkB,EAAA;EhD5CpB;IgDgDE,eAAe,EAAA;EAGhB;IACC,uBAAuB;IACvB,oCAA4B;IAA5B,4BAA4B;IAC5B,eAAe;IACf,axDociB,EAAA;EwB/hBnB;IgC+FE,eAAc;IACd,gBrDzFa,EAAA;IqDuFd;MAKE,iBAAmC;MACnC,WAAU;MACV,4CrDpFY,EAAA;MqD6Ed;QAUG,aAAa,EAAA;IAVhB;MAgBE,axDkZiB;MwDjZjB,8DxD6YgE;MwD7YhE,sDxD6YgE;MDzUjE,gDAA0C;MAElC,wCAAkC;MyDnEzC,0BAA0B;MAC1B,MAAK;MACL,SAAQ,EAAA;MAtBV;QAyBG,cAAc;QACd,kBAAkB;QAClB,iCAAiC;QACjC,8BAAkE,EAAA;QA5BrE;UAgCK,wBAAwB,EAAA;IAhC7B;MAuCE,sBAAmE;MACnE,WAAW;MACX,eAAuB;MAEvB,mCAA4D,EAAA;MA3C9D;QA8CG,eAA6C,EAAA;MA9ChD;QAkDG,qBAAqB,EAAA;QAlDxB;UAqDI,iBAAiB;UACjB,gBAAgB;UAChB,cAAc,EAAA;UAvDlB;YA0DK,cAAc;YACd,sBAAsB;YACtB,eAAe;YACf,cAAc;YACd,gBAAgB;YAChB,wBAAwB;YACxB,gBAAgB,EAAA;MAhErB;QAuEG,exDmC4B,EAAA;IwD1G/B;MA4EE,yCrDzJY,EAAA;EK6BX;IgDmJF,mFAAA;IACA;;;;;;;;;I3D6hKE,E2DphKC;IA5BH;MAGC,cAAc;MACd,OAAO;MACP,eAAe;MACf,QAAQ;MACR,MAAM;MAEH,8DxDoU6D;MwDpU7D,sDxDoU6D,EAAA;IwD7UjE;;MAcC,mCAA2B;cAA3B,2BAA2B,EAAA;IAd5B;MA+BC,oBxDoW2B,EAAA;EwDhW7B,iBAAA;EACC;IzDhCA,gDAA0C;IAElC,wCAAkC,EAAA;EyD8B1C;;;;IAUE,8DxD+R+D;IwD/R/D,sDxD+R+D,EAAA;EwDxRlE,eAAA;EAjBiB;IAoBhB,4BAA4B;IAAE,MAAA,EAAO;IAFrC;MAME,UAAU;MACV,wBAAe;MAAf,gBAAe;MzDzDjB,yCAA0C;MAElC,iCAAkC,EAAA;IyDgD1C;;;;MAec,8DxDwQmD;MwDxQnD,sDxDwQmD;MDzUjE,uCAA0C;MAElC,+BAAkC,EAAA;IyDgD1C;MAoBE,gEAAA;MACA,mBxDiSyD,EAAA;EwD5R5D,kCAAA;EACA,YAAA;EACA;IAEC,sBAAkB;QAAlB,kBAAkB;IAElB,gBAAgB;IAChB,aAAa;IAOb;;;;;;;;;;I3D8gKE;I2Dp+JF,iBAAA;IAqBA,wBAAA;IAqCA,2BAAA,EAA4B;IAhH5B;MAQC,2CAAuC;MzDrItC,sDyDsImD;czDtInD,8CyDsImD,EAAA;IATrD;MAyBC,MAAM;MACN,QAAQ;MACR,SAAS;MACT,OAAO;MACP,+BrD7QY,EAAA;IqDgPb;MAoCA,oDAAoD;MAE5C,4CAA4C,EAAA;IAtCpD;MA8CA,oDAAoD;MAE5C,4CAA4C,EAAA;IAhDpD;MA4DG,OAAM;MzD1IT,yCAA0C;MAElC,iCAAkC,EAAA;IyD4E1C;;;;MzD9EA,+CAA0C;MAElC,uCAAkC,EAAA;IyD4E1C;MAgFE,gBAAgB;MAShB;;M3Ds+JE,E2Dp+JC;MA3FL;;;;QzD9EA,+CAA0C;QAElC,uCAAkC,EAAA;MyD4E1C;QA8FG,kCAAkC;QAClC,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO;QACP,exDqSuB,EAAA;MwDxY1B;;;;QA0GG,8IAAwI;gBAAxI,sIAAwI,EAAA;IA1G3I;MAqHE;;M3Di+JE,E2D/9JC;MAvHL;QA0HG,uBAAuB;QACvB,MAAM;QACN,QAAQ;QACR,SAAS;QACT,OAAO,EAAA,EACP;;AASL;EAEC,oDAAA;EACA;;G3Du9JE;E2Dn9JF,sBAAA;EACA;IAGE,wBAAwB,EAAA;EAH1B;IAOE,+BAAwB;IAAxB,+BAAwB;IAAxB,wBAAwB;IACxB,mBAAO;QAAP,WAAO;YAAP,OAAO,EAAA;IART;MAWG,yBAAyB;MACzB,6BAA6B;MAC7B,WAAW,EAAA;MAbd;QAgBI,+BAAwB;QAAxB,+BAAwB;QAAxB,wBAAwB;QACxB,WAAU;QACV,WAAW;QACX,YAAY;QACZ,eAAe,EAAA;MApBnB;QAwBI,yBxDtauB;QwDuavB,kBAAkB;QAClB,mBAAmB;QACnB,WAAW;QACX,eAAe;QACf,gBrDvaU,EAAA;QqD0Yd;UAiCK,qBxD/asB,EAAA;EwD8Y3B;IAwCE,wBAAwB,EAAA;EAK1B;IAGE,mCAA+D;IAC/D,sBAA6C,EAAA;EAI/C;;IAEC,6BAAkD;IAClD,8BAAmD,EAAA;EAGpD;IACC,0BAA0B,EAAA;EAI3B;;IAGE,aAAa,EAAA;EAGf;;;IAGC,aAAa,EAAA,EACb;;A3DhYF,qFAAA;A4D3FA,iFAAA;AAEA;+D5Di5K+D;A4D34K/D;;oC5D84KoC;A4Dv4KpC;6D5Dy4K6D;AA1zK7D;4EA4zK4E;A6D15K5E,cAAA;AACA;EACC,cAAa;EACb,8CAA8C;EAE9C,mBAAmB,EAAA;EAJpB;IAOE,UAAU,EAAA;EAPZ;IAWE,+BAA+B;IAC/B,c1DsF2C,EAAA;E0DlG7C;IAgBE,kCAAiC;IACjC,WvDDa,EAAA;EuDhBf;IAgBE,kCAAiC;IACjC,WvDDa,EAAA;EuDhBf;IAqBE,kCAAiC;IACjC,WvDNa,EAAA;;AuDWf;;;;E7Do5KE;A6D94KF;EACC,gBAAgB,EAAA;;AAGjB,iBAAA;AAEA;;;;;;;;;;;;;;E7D25KE;A6D34KF,QAAA;AACA;EACC,wBAAwB;EACxB,qBAAqB,EAAA;;AAEtB;EACC,qBAAqB,EAAA;;AAEtB;EACC,wBAAwB,EAAA;;AAGzB,uDAAA;AACA;EACC,mB1DnE2B,EAAA;;A0DqE5B;EAEE,qB1DvE0B,EAAA;E0DqE5B;IAIG,yCvD3DY,EAAA;;AuDuDf;EAQE,mB1D7E0B;E0D8E1B,sBAAwB,EAAA;;AAI1B;EACC,yCAAiC;EAAjC,iCAAiC,EAAA;;AAGlC,8CAAA;AACA;EAGE,gEAAwD;EAAxD,wDAAwD,EAAA;EAH1D;IAKG,UAAS,EAAA;;AAKZ,wCAAA;AACA;EACC,wB1DgMmD,EAAA;;A0D7LpD,oBAAA;AACA;EAEE,kBAAkB,EAAA;;AAGpB,kBAAA;AACA;EAEE,kBAAkB,EAAA;;AAIpB,8BAAA;AACA;;E7D63KE;A6Dz3KF,4BAAA;AACA;;EAEC,gBAAgB,EAAA;;AAGjB,4CAAA;AACA;EACC,c1DhC4C,EAAA;;A0DmC7C,sBAAA;AACA;EACC,oB3DkHkC;E2DjHlC,oBAAoB,EAAA;;AAGrB,kDAAA;AACA;;EAEC,gBAAgB,EAAA;;AAGjB,iDAAA;AACA;EACC,aAAa,EAAA;;AAEd;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB,EAAA;;AAEpB;EACC,aAAa,EAAA;;A7DzDd;4EAk7K4E;AAh7K5E,qCAAA;AiBxCI;EC5DJ;IhBmLE,uCAA0C;IAElC,+BAAkC,EAAA;E4D7K1C;;;;;;IAKU,eAAe;IACvB,uBAAuB;IACnB,mBAAmB,EAAA,EACvB;;A9DuFJ;4EA+7K4E;A+D/gL5E;EAEC;;;;;IAMO,kBAAiB,EAAA;IANxB;;;;;MASO,yBAA0C,EAAA;IATjD;;;;;MAYO,yBAAyC,EAAA;IAZhD;;;;;MAeO,mBAAmB;MACnB,eAAe;MACf,kBAAkB;MAClB,mB5DgCsC;M4D/BtC,WAAW;MACX,kBAAkB;MAClB,yB5DoCsC;M4DnCtC,YAAY;MACZ,iBAAiB;MACjB,mB5DsRqB;M4DrRrB,cAAc;MACd,WAAW;MACX,QAAQ;MACR,c5DuDoC,EAAA,E4DtDrC;;ACtDP;EAGG,cAAc;EACd,wBAAgB;UAAhB,gBAAgB;EAChB,SAAS;EACT,uBAAuB;EA8BvB,sIAAA,EAAuI;EApC1I;IAWI,YAAY,EAAA;IAXhB;MAkBM,WAAW;MACX,YAAY;MACZ,iBAAiB;MACjB,kBAAkB;MAClB,kBAAkB,EAAA;MAtBxB;QAyBO,UAAU,EAAA;IAzBjB;MAgCK,eAAc,EAAA;EAhCnB;IAuCI,mBAAmB;IACnB,2CAA6C;YAA7C,mCAA6C,EAAA;EAxCjD;IA2CI,mBAAmB,EAAA;EA3CvB;IA8CI,mBAAmB,EAAA;EA9CvB;IAiDI,mBAAmB,EAAA;EAjDvB;IAoDI,mBAAmB,EAAA;EApDvB;IAuDI,mBAAmB,EAAA;EAvDvB;IA0DI,mBAAmB,EAAA;EA1DvB;IA6DI,mBAAmB,EAAA;EA7DvB;IAgEI,mBAAmB,EAAA;EAhEvB;IAmEI,mBAAmB,EAAA;EAnEvB;IAsEI,mBAAmB,EAAA;EAtEvB;IAyEI,mBAAmB,EAAA;EAzEvB;IA4EI,mBAAmB,EAAA;EA5EvB;IA+EI,mBAAmB,EAAA;EA/EvB;IAkFI,mBAAmB,EAAA;EAlFvB;IAqFI,mBAAmB,EAAA;;ACrFvB;EAIG,aAAY;EACZ,gBAAe,EAAA;;AALlB;EAUE,SAAQ;EACR,gBAAe;EACf,mB/D8OiC;E+D7OjC,sBAAsB;EACtB,+BAAuB;UAAvB,uBAAuB;EACvB,cAAc;EACd,gBAAe;EACf,qBAAqB;EACrB,eAAe;EACf,c9DX6E;E8DY7E,gBAAgB,EAAA;EApBlB;IAuBG,eAAe,EAAA;;AAvBlB;EA4BE,gBAAgB;EAChB,gBAAgB;EAChB,kCAAkC;EAClC,cAAyB;EACzB,kBAAkB;EAElB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB,EAAA;EApCzB;IAyCG,cAAyB;IACzB,oC3DnCY,EAAA;I2DPf;M/DkME,8BAUiC;MATzB,sBASyB,EAAA;E+D5MnC;IAuDG,kBAAkB;IAClB,WAAW;IACX,SAAS;IACT,QAAQ,EAAA;EA1DX;IA8DG,iBAAgB;IAChB,oB/D2LgC;I+D1LhC,cAAc,EAAA;EAhEjB;IAoEG,cAAc;IACd,kB/DqLgC;I+DpLhC,cAAc;IACd,yBAAyB,EAAA;;AAvE5B;EA8EE,kBAAkB;EAClB,aAAa,EAAA;EA/Ef;IAkFG,4B9DqByC;I8DpBzC,WAAW,EAAA;EAnFd;IAuFG,UAAU;IACV,SAAS;IACT,kBAAkB;IAClB,YAAY,EAAA;IA1Ff;MA6FI,qBAAqB;MACrB,SAAQ;MACR,UAAS,EAAA;MA/Fb;QAkGK,WAAW;QACX,YAAY;QACZ,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,eAAe;QACf,kBAAkB;QAElB,gFAAkE;gBAAlE,wEAAkE,EAAA;EA1GvE;IAiHG,UAAU;IACV,gBAAgB;IAChB,uBAAuB,EAAA;IAnH1B;MAsHI,eAAc;MACd,SAAS;MACT,QAAQ,EAAA;IAxHZ;MA4HI,uBAAsB,EAAA;;AAM1B;EACC,kBAAkB;EAClB,UAAU;EACV,MAAM;EACN,WAAW;EACX,aAAa,EAAA;;AAGd,wCAAA;AACA;EAIE,cAAc,EAAA;;AAJhB;EAQE,YAAY;EACZ,8BAA8B,EAAA;;ACpJhC;EAKI,aAAa;EACb,2DAAgD;UAAhD,mDAAgD,EAAA;;AANpD;EAUG,YAAW;EACX,gBAAgB;EAChB,mBAAmB,EAAA;EAZtB;IhEkME,6BgEjLgC;IhEkLxB,qBgElLwB;IAC9B,gBAAgB,EAAA;;AAMpB;EACC,WAAW;EACX,YAAY;EACZ,cAAc;EACd,kBAAkB,EAAA;;AAGnB;EACC,gBAAgB;EAChB,UAAS,EAAA;EAFV;IAIE,yBAAwB;IACxB,aAAY;IACZ,qBAAqB;IACrB,eAAc;IACd,WAAU;IACV,YAAW;IAEX,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,gBAAe,EAAA;IAdjB;MhEmKE,6BgEhJ+B;MhEiJvB,qBgEjJuB,EAAA;;AAOhC;;EAEC,wBAAwB,EAAA;;AAG1B;;EAGE,yBAAyB,EAAA;;AAI3B;EACC,eAAc;EACd,WAAU,EAAA;;AAGX;EACI,kBAAkB;EAClB,eAAe,EAAA;;AAGnB;EACI,iBAAiB;EACjB,sCAAkC,EAAA;;AAEtC;;EAEI,mBAAmB;EACnB,sBAAsB;EACtB,yCAAqC;EACrC,wCAAoC,EAAA;;AAGxC;EACI,gBAAgB,EAAA;;AAGpB;EACE,wCAAoC;EACpC,wCAAoC,EAAA;;AAKtC;EACC,SAAS,EAAA;EADV;IAIE,mCAAmC,EAAA;EAJrC;IASE,2BAA2B,EAAA;;AAM7B;;;EAIE,+BAA+B;EAC/B,2BAA2B,EAAA;;AAL7B;;;EASE,2BAA2B,EAAA;;AAM7B;EAEE,2BAA2B,EAAA;;AAI7B;EAEE,6BAA6B,EAAA;;AAI/B;EACC,6BAA6B,EAAA;;AAG9B;;ElE8rLE;AApuLF;4EAsuL4E;AmEj1L5E;EAEE,4BAA4B;EAC5B,oBAAoB;EAEpB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAiB;MAAjB,iBAAiB,EAAA;EAPnB;IAUG,mBhEVyB;IgEWzB,eAAe;IACf,aAAa;IACb,MAAM;IACN,WAAW;IACX,WAAW;IACX,WAnBc,EAAA;;AAGjB;EAqBG,aAAa,EAAA;;AAKhB;EAuEC;;;;;;;GnE2wLE,EmEpwLC;EA9EJ;IAGE,4BAA4B;IAC5B,oBAAoB;IAEpB,yBAAyB;IACzB,sBAAsB;IACtB,qBAAiB;QAAjB,iBAAiB;IAEd,aAAa;IACb,eAAe;IACf,YAAY;IACZ,SAAS;IACT,OAAO;IACP,QAAQ;IAER,YA7CuB;IA8CvB,sB7DxCU;I6DyCV,kBAAyB;IACzB,gB7D1CU;I6D2CV,gBAAgB,EAAA;IArBrB;MAwBM,8BAAsB;cAAtB,sBAAsB;MjE8H1B,uCAA0C;MAElC,+BAAkC;MiE5HtC,sBAA6B;MAC7B,aAAa;MACb,cAAc;MACd,kBAAkB;MAClB,MAAM;MACN,WAAW;MACX,YAAY;MACZ,WAAW;MACX,yBhE9DsB;MgE+DtB,iJACiH;MACjH,yBAAyB;MACzB,wBAAwB;MACxB,0BArEsB;MAsEtB,4BAA4B;MAE5B,uCAAuC;MACvC,0CAA0C;MAC1C,4CAA4C;MAC5C,+CAA+C,EAAA;EA/CrD;IAqDG,aAAa,EAAA;EArDhB;IA2DI,WAAW;IACR,eAAe;IACf,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ;IACR,yBhE6bsD;IgE5btD,UAAU,EAAA;;AAgBjB;EACC;IACC,wBAAwB,EAAA;EAEzB;IACC,4BAA8C,EAAA,EAAA;;AAGhD;EACC;IACC,wBAAwB,EAAA;EAEzB;IACC,4BAA6C,EAAA,EAAA;;AC5H/C;EACC,6BAA6B,EAAA;;AAG9B;EAGE,gBAAgB,EAAA;;ACPlB;;;;;;uDrEw8LuD;AqE56LvD;EACI,kBAAkB;EAClB,eAAe;EACf,qBAAqB;EACrB,gBAAgB;EAChB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB;EACjB,wCAAwC,EAAA;EAT5C;IAYQ,kBAAkB;IAClB,kBAAkB;IAClB,YAAY;IACZ,aAAa;IACb,iBAAgB;IAChB,kBAAiB;IACjB,UAAU;IAEhB,oC/DzCa;I+D4Cb,6KAAsC;IA1CpC,qCA2C+C;IAxC/C,6BAwC+C;IAC3C,uDAAuD;IAGvD,uDAAuC;IAAvC,+CAAuC;IAAvC,uCAAuC;IAAvC,0DAAuC;IAxC3C,2CAyCoD;IArCpD,mCAqCoD;IAChD,oBAAoB,EAAA;;AAK5B;EAtDI,mCAuD8C;EApD9C,2BAoD8C,EAAA;;AAIlD;EAOG,8B/D1DY;E+D6DZ,+IAAsC,EAAA;;AAVzC;EA0BG,oC/DvFY;E+D0FZ,6KAAsC,EAAA;;AAOzC;EAEC,iCADmC;EAInC,8JAAsC,EAAA;;AAGvC;;EAIE,oClEjH0B;EkEoH1B,6KAAsC,EAAA;;ArEHxC;4EAq5L4E;AAn5L5E,qCAAA;AAEA;4EAo5L4E;AsE3gM5E;EACE;IACE,UAAU,EAAA;EAEZ;IACE,UAAU,EAAA,EAAA;;AAId;EACE;IACE,UAAU,EAAA;EAEZ;IACE,UAAU,EAAA,EAAA;;AAKd;EACE;IACE,kBAAkB,EAAA;EAEpB;IACE,mBAAmB,EAAA,EAAA;;AAGvB;EACE;IACE,kBAAkB,EAAA;EAEpB;IACE,mBAAmB,EAAA,EAAA;;AAIvB;EACI;IAAK,UAAS,EAAA;EACd;IAAO,YAAW,EAAA,EAAA;;AAGtB;EACI;IAAK,UAAS,EAAA;EACd;IAAO,YAAW,EAAA,EAAA;;AC3CtB;EACI;IAAO,mBpEyEmC,EAAA;EoExE1C;IAAK,uBAAuB,EAAA,EAAA;;AAGhC;EACI;IAAO,mBpEoEmC,EAAA;EoEnE1C;IAAK,uBAAuB,EAAA,EAAA;;AAGhC;EACI,iCAAiC;EAEzB,yBAAyB,EAAA;;ACbrC;EACI;IACE,+BAA+B,EAAA;EAGjC;IACE,iCAAiC,EAAA,EAAA;;AAIvC;EACI;IACI,+BAAsB;YAAtB,uBAAsB,EAAA;EAG1B;IACI,iCAAwB;YAAxB,yBAAwB,EAAA,EAAA;;AAMhC;;;;;;;;ExE+jME;AwEpjMF;EACE,4CAAoC;UAApC,oCAAoC,EAAA;;AAGtC;EACE,0CAAkC;UAAlC,kCAAkC,EAAA;;ACpCpC,iCAAA;AAEA,uBAAA;AACA;EACC;IACC,UAAU;IACV,0CAAkE;IAClE,kCAA0D,EAAA;EAG3D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAGjB;EACC;IACC,UAAU;IACV,0CAAkE;IAClE,kCAA0D,EAAA;EAG3D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAIjB,sBAAA;AACA;EACC;IACC,UAAU;IACV,yCAAiE;IACjE,iCAAyD,EAAA;EAG1D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAGjB;EACC;IACC,UAAU;IACV,yCAAiE;IACjE,iCAAyD,EAAA;EAG1D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAIjB,uBAAA;AACA;EACC;IACC,UAAU;IACV,0CAAkE;IAClE,kCAA0D,EAAA;EAG3D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAGjB;EACC;IACC,UAAU;IACV,0CAAkE;IAClE,kCAA0D,EAAA;EAG3D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAIjB,yBAAA;AACA;EACC;IACC,UAAU;IACV,yCAAiE;IACjE,iCAAyD,EAAA;EAG1D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAGjB;EACC;IACC,UAAU;IACV,yCAAiE;IACjE,iCAAyD,EAAA;EAG1D;IACC,UAAU;IACV,uBAAuB;IACvB,eAAe,EAAA,EAAA;;AAIjB,4BAAA;AACA;EACC,wCAAgC;UAAhC,gCAAgC,EAAA;;AAGjC,uCAAA;AACA;EACC,+BAA+B;EAC/B,uCAAuC,EAAA;;AAGxC;;EAEC,+BAA+B;EAC/B,uCAAuC,EAAA;;AAGxC;EACC,+BAA+B;EAC/B,uCAAuC,EAAA;;AAGxC,yBAAA;AACA;EACC,+BAA+B;EAC/B,uCAAuC,EAAA;;AAExC;EACC,iCAAiC;EACjC,yCAAyC,EAAA;;AAE1C;EACC,iCAAiC;EACjC,yCAAyC,EAAA;;AAE1C;EACC,kCAAkC;EAClC,0CAA0C,EAAA;;AzE3B3C;4EAulM4E;AiB1pMxE;EyD9CH;;;;IAKE,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,mBAAmB;IACnB,sBAAsB;IACtB,oCAAiC;IACjC,kBAAkB;IAClB,SAAS;IACT,UAAU;IACV,YAAY;IACZ,WAAW,EAAA;EAfb;;;;IAkBE,YAAY;IACZ,eAAe;IACf,cAAc;IACd,oCAAiC;IACjC,kBAAkB;IAClB,OAAO;IACP,MAAM;IACN,kBAAkB,EAAA;EAKpB;IAGG,cAAc,EAAA;IAHjB;MAQK,qBAAqB;MACrB,SAAQ;MACR,UAAS,EAAA;MAVd;QAeO,yDA1DuD,EAAA;MA2C9D;QAkBO,yDA1DuD,EAAA;MAwC9D;QAqBO,yDA1DuD,EAAA;MAqC9D;QAwBO,yDA1DuD,EAAA;MAkC9D;QA8BO,eAAc,EAAA;EA9BrB;IAsCE,oDAlFkD,EAAA;EA4CpD;IAyCE,oDAlFkD,EAAA;EAyCpD;IA4CE,oDAlFkD,EAAA;EAsCpD;IA+CE,oDAlFkD,EAAA;EAmCpD;IAkDE,4BAA4B,EAAA,EAC5B;;AC/FH;EAEE,2BAA6B,EAAA;;AAF/B;EAKE,8CAA8D,EAAA;;ACLhE;EACC,8BAA8B;EAC9B,sBAAsB,EAAA;;AAiDvB;EACC,iCAAiC,EAAA;;ACpDlC;;;EAGE,mCAAmC;EAInC,2BAA2B;EAE3B,kCAAmC;EACnC,0BAA0B,EAAA;;ACV5B;EACC;;G9EwyME,E8EtyMC;EAHJ;IAMG,aAAa,EAAA;;ACNhB;;;EASM,aAAa,EAAA;;AATnB;EAYM,WAAW;EACX,UAAU;EACV,QAAQ,EAAA;;AAdd;;;EAsBM,aAAa,EAAA;;AAtBnB;EA2BK,aAAa,EAAA;;AA3BlB;EA+BK,uBAAwC,EAAA;EA/B7C;IAiCM,uBAAwC,EAAA;;ACjC9C;;;;;;EAQE,+E1ESa;E0EJb,sBAAwB;EACxB,2BAA0B,EAAA;;AAd5B;;;;;;;;;;;;;EA8BE,+E1EvBa;E0E4Bb,sBAAwB;EACxB,gBAAe,EAAA;;AApCjB;;;;;;;;;;;;;;EAsDE,iBAAiB;EACjB,sBAAwB;EACxB,8BAA8B,EAAA;;ACxDhC;EAEE,SAAQ,EAAA;EAFV;IAMG,aAAa;IACb,eAAc;IACd,qBAAqB;IACrB,gBAAgB;IAChB,0BAA0B;IAC1B,qBAAqB,EAAA;IAXxB;MAcI,gBAAgB,EAAA;IAdpB;MAmBI,mBAAmB;MACnB,qBAAqB;MACrB,0BAA0B,EAAA;MArB9B;QAwBK,cAAa,EAAA;EAxBlB;IA8BG,kBAAkB;IAClB,MAAM,EAAA;;AhE6BL;EwCgEA;IyBjBF;;;;;;;;;;;;;IlF0yME,EkF7xMC;IAnHJ;MAEE,iB/EwLQ;M+EvLR,cAAc,EAAA;IAHhB;MASG,kBAAiB,EAAA;MATpB;QAYI,c/E8rB+B,EAAA;M+E1sBnC;QAeI,c/E4rB8B,EAAA;M+E3sBlC;QAkBI,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAClB,mB/EiDyC;Q+EhDzC,mBhF+N8B;QgF9N9B,WAAW;QACX,kBAAkB;QAClB,yB/EoDyC;Q+EnDzC,YAAY;QACZ,iBAAiB;QACjB,mB/EsSwB;Q+ErSxB,WAAW;QACX,QAAQ;QACR,c/EwEuC,EAAA;I+EvG3C;MAwCI,WAAW;MACX,iBAAsC;MACtC,yBAAyB,EAAA;IA1C7B;MAiDK,6BAA6B;MAC7B,MAAK;MACL,SAAQ,EAAA;IAnDb;MA4DK,uBAAkD,EAAA;IA5DvD;MAkEK,sB/EkkBsB,EAAA;I+EpoB3B;MA0EK,uBAAwD,EAAA;IA1E7D;MA6EK,sB/EulB2B,EAAA;I+EpqBhC;MAwFI,kBAAiB;MACjB,e/E2iBuB,EAAA;I+EpoB3B;MA+FI,kBAAiB;MACjB,e/EokB4B,EAAA;I+EpqBhC;MAyHK,kBAAiB,EAAA;MAzHtB;QA4HM,eAAc;QACd,MAAK;QACL,Y/E8XY;QD7Xf,qDgFAoD;gBhFApD,6CgFAoD,EAAA;MA/HvD;QAmIM,oB/E0auB,EAAA;I+E7iB7B;MA4IK,6BAA6B;MAC7B,wBAAgB;MAAhB,gBAAgB,EAAA;IA7IrB;MAoJE,0C5ExIY;M4EyIZ,2C5EzIY;M4E0IZ,yFAAiF;cAAjF,iFAAiF;MACjF,gBAAgB;MAChB,kBAAkB,EAAA;IAxJpB;MA6JG,2CAAmC;cAAnC,mCAAmC,EAAA,EACnC;;ACnKJ,4BAAA;AAEA;EAQK,yBAAwB,EAAA;;AAR7B;EAwBI,0CAAA,EAA2C;EAxB/C;IAcK,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,UAAU;IACV,eAAgC;IAChC,SAAS;IACT,SAAS;IACT,8BAAmD,EAAA;EArBxD;IA4BO,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,aAAa;IACb,cAAc;IACd,yBhF2nBwD;IgF1nBxD,YAA6B;IAC7B,uBAAkC;IAClC,sBAAsB;IACtB,kBAAkB;IAClB,UAAU,EAAA;EAtCjB;IA2CO,yBAAyB,EAAA;EA3ChC;IAkDQ,WAAW;IACX,aAAa,EAAA;EAnDrB;IA4DO,yBAAyB,EAAA;;AC9DhC;EACC,wDAAgD;UAAhD,gDAAgD,EAAA;;AAD7C;EAKH,eAAe,EAAA;EADhB;IAIE,mEAA2D;YAA3D,2DAA2D,EAAA;;AARtC;EAatB,eAAe,EAAA;EADhB;IAIE,mEAA2D;YAA3D,2DAA2D,EAAA;;AAhBnB;EAqBzC,eAAe,EAAA;EADhB;IAIE,mEAA2D;YAA3D,2DAA2D,EAAA;;AAI7D;EACC,eAAe,EAAA;;ApFgHhB;4EAq4M4E;AE/sMxE;EACE,yBC7RwC;EDgR1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC5RyC;ED+Q3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC3RyC;ED8Q3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC1RyC;EDyQ3C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCzRwC;EDwQ1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCjUsB;EDgTxB,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCvRuC;EDsQzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCtRwC;EDqQ1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCrRwC;EDoQ1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCpRwC;EDmQ1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AA0B1B;EACE,cCvSwC,EAAA;;ADsS1C;EACE,cCtSyC,EAAA;;ADqS3C;EACE,cCrSyC,EAAA;;ADoS3C;EACE,cCpSyC,EAAA;;ADmS3C;EACE,cCnSwC,EAAA;;ADkS1C;EACE,cC3UsB,EAAA;;AD0UxB;EACE,cCjSuC,EAAA;;ADgSzC;EACE,cChSwC,EAAA;;AD+R1C;EACE,cC/RwC,EAAA;;AD8R1C;EACE,cC9RwC,EAAA;;ADmR1C;EACE,yBCjRwC;EDoQ1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBChRyC;EDmQ3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC/QyC;EDkQ3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC9QyC;EDiQ3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC7QwC;EDgQ1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBChUsB;ED+SxB,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC3QuC;ED0PzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC1QwC;EDyP1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCzQwC;EDwP1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCxQwC;EDuP1C,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AA0B1B;EACE,cC3RwC,EAAA;;AD0R1C;EACE,cC1RyC,EAAA;;ADyR3C;EACE,cCzRyC,EAAA;;ADwR3C;EACE,cCxRyC,EAAA;;ADuR3C;EACE,cCvRwC,EAAA;;ADsR1C;EACE,cC1UsB,EAAA;;ADyUxB;EACE,cCrRuC,EAAA;;ADoRzC;EACE,cCpRwC,EAAA;;ADmR1C;EACE,cCnRwC,EAAA;;ADkR1C;EACE,cClRwC,EAAA;;ADuQ1C;EACE,yBCrQmC;EDwPrC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCpQoC;EDuPtC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCnQoC;EDsPtC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBClQoC;EDiPtC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCjQmC;EDgPrC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC/TmB;ED8SrB,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC/PkC;ED8OpC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC9PmC;ED6OrC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC7PmC;ED4OrC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC5PmC;ED2OrC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AA0B1B;EACE,cC/QmC,EAAA;;AD8QrC;EACE,cC9QoC,EAAA;;AD6QtC;EACE,cC7QoC,EAAA;;AD4QtC;EACE,cC5QoC,EAAA;;AD2QtC;EACE,cC3QmC,EAAA;;AD0QrC;EACE,cCzUmB,EAAA;;ADwUrB;EACE,cCzQkC,EAAA;;ADwQpC;EACE,cCxQmC,EAAA;;ADuQrC;EACE,cCvQmC,EAAA;;ADsQrC;EACE,cCtQmC,EAAA;;AD2PrC;EACE,yBCzPwC;ED4O1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCxPyC;ED2O3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCvPyC;ED0O3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCtPyC;EDyO3C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCrPwC;EDwO1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC9TsB;EDiTxB,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCnPuC;EDsOzC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBClPwC;EDqO1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCjPwC;EDoO1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBChPwC;EDmO1C,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAsBtB;EACE,cCnQwC,EAAA;;ADkQ1C;EACE,cClQyC,EAAA;;ADiQ3C;EACE,cCjQyC,EAAA;;ADgQ3C;EACE,cChQyC,EAAA;;AD+P3C;EACE,cC/PwC,EAAA;;AD8P1C;EACE,cCxUsB,EAAA;;ADuUxB;EACE,cC7PuC,EAAA;;AD4PzC;EACE,cC5PwC,EAAA;;AD2P1C;EACE,cC3PwC,EAAA;;AD0P1C;EACE,cC1PwC,EAAA;;AD+O1C;EACE,yBC7OuC;EDgOzC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC5OuC;ED+NzC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC3OuC;ED8NzC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBC1OuC;ED6NzC,yBAAsB,EAAA;EAYtB;IAZA,yBAAsB,EAAA;;AAYtB;EACE,yBCzOsC;EDwNxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC7TqB;ED4SvB,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCvOqC;EDsNvC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCtOsC;EDqNxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCrOsC;EDoNxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCpOsC;EDmNxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AA0B1B;EACE,cCvPuC,EAAA;;ADsPzC;EACE,cCtPuC,EAAA;;ADqPzC;EACE,cCrPuC,EAAA;;ADoPzC;EACE,cCpPuC,EAAA;;ADmPzC;EACE,cCnPsC,EAAA;;ADkPxC;EACE,cCvUqB,EAAA;;ADsUvB;EACE,cCjPqC,EAAA;;ADgPvC;EACE,cChPsC,EAAA;;AD+OxC;EACE,cC/OsC,EAAA;;AD8OxC;EACE,cC9OsC,EAAA;;ADmOxC;EACE,yBCjOuC;EDgNzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBChOuC;ED+MzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC/NuC;ED8MzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC9NuC;ED6MzC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC7NsC;ED4MxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC5TyE;ED2S3E,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC3NqC;ED0MvC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBC1NsC;EDyMxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCzNsC;EDwMxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AAgB1B;EACE,yBCxNsC;EDuMxC,YAA0B,EAAA;EAgB1B;IAhBA,YAA0B,EAAA;;AA0B1B;EACE,cC3OuC,EAAA;;AD0OzC;EACE,cC1OuC,EAAA;;ADyOzC;EACE,cCzOuC,EAAA;;ADwOzC;EACE,cCxOuC,EAAA;;ADuOzC;EACE,cCvOsC,EAAA;;ADsOxC;EACE,cCtUyE,EAAA;;ADqU3E;EACE,cCrOqC,EAAA;;ADoOvC;EACE,cCpOsC,EAAA;;ADmOxC;EACE,cCnOsC,EAAA;;ADkOxC;EACE,cClOsC,EAAA;;ADiOxC;EACE,WIvUS,EAAA;;AJsUX;EACE,cmFlM+B,EAAA;;AnFkNjC;EAEE,8EAAyE,EAAA;;AAF3E;EAEE,6EAAyE,EAAA;;AAF3E;EAEE,6EAAyE,EAAA;;AAF3E;EAEE,8EAAyE,EAAA;;AAF3E;EAEE,+EAAyE,EAAA;;AAF3E;EAEE,6EAAyE,EAAA;;AF/M/E;4EA67N4E;AsF9kO5E,4BAAA;AACA,uDAAA;AAOA;;EAGQ,WAL4B;EAM5B,UAN4B,EAAA;;AAEpC;;EAQK,qCAAgC,EAAA;;AARrC;;EAYQ,yBAjBgC,EAAA;;AAKxC;;EAgBQ,yBAA+C,EAAA;;AAhBvD;;EAoBQ,yBnFwEqC,EAAA;;AmF5F7C;;EAwBQ,yBA3BgD,EAAA;;AA+BxD;;EtFwkOE;AsFpkOF;EACI,yBAtCoC,EAAA;;AAyCxC;EACI,sBAAsB,EAAA;;AAG1B;EACI,WAA0B;EAC1B,UAAyB,EAAA;;AAG7B;EACI,WAAW,EAAA;;AAdf;EAkBC,yBAvDuC,EAAA;;AAyCxC;EAkBC,sBAAsB,EAAA;;AC9DvB;EAEE,cpFoBkC,EAAA;;AoFhBpC;EAEE,cpFckC,EAAA;;AqFtBpC;EACI;IACI,qBrFkhBwB;IqFjhBxB,WrFmhBsB;IqFlhB5B,UAAU;IACV,YAAY;IACZ,yBAAyB,EAAA;EAGvB;IACC,sBAAqB;IACrB,kCAAkC;IAClC,oDAAoD;IACpD,iCAAiC;IACjC,0BAA0B;IAC1B,2BAA2B;IAC3B,+BAA+B;IAC/B,mCAA2B;IAA3B,2BAA2B,EAAA;EAG5B;IAAQ,cAAc,EAAA;EAEtB;;;IAGC,eAAe;IACf,UAAU;IACV,SAAS;IACT,SAAS,EAAA;EAIb;IACC,iBAAiB;IACjB,0BAA0B;IAC1B,WAAW,EAAA;E5DrBb;I4DyBE,0BAA0B,EAAA;I5DzB5B;M4D4BG,0BAA0B,EAAA;EpF9B7B;IoFmCE,4BAA4B,EAAA;EAG7B;;;;IAIC,wBAAwB,EAAA;EAGzB;;;IAGI,UAAU;IACV,cAAc;IACd,SAAQ;IACR,oBAAmB;IACnB,WAAU;IACV,WAAW,EAAA;EAEf;IACC,4BAA4B,EAAA;EAE7B;IACC,6BAA6B,EAAA;EAG9B;IACI,WAAW;IACX,WAAW;IACX,cAAc,EAAA,EACjB;;AxFyEF;4EA0iO4E", "file": "app.bundle.css", "sourcesContent": ["/* #GLOBAL IMPORTS\r\n========================================================================== */\r\n@import '_imports/_global-import';\r\n\r\n/* #FRAMEWORK - Structure and layout files. (**DO NOT** change order)\r\n                DOC: you can disable unused _modules\r\n========================================================================== */\r\n/* contains root variables to be used with css (see docs) */\r\n@import '_modules/_root';\r\n/* resets DOM elements to its natural state */\r\n@import '_modules/_reset';\r\n/* html and body base styles */\r\n@import '_modules/_body';\r\n/* app header */\r\n@import '_modules/_page-header';\r\n/* app logo */\r\n@import '_modules/_page-logo';\r\n/* app search */\r\n@import '_modules/_page-search';\r\n/* icon menu with user options */\r\n@import '_modules/_dropdown-icon-menu';\r\n/* dropdown notification in the app header */\r\n@import '_modules/_dropdown-notification';\r\n/* icon menu with stacked icons located in the app header */\r\n@import '_modules/_dropdown-app-list';\r\n/* app header stays fixed */\r\n@import '_modules/_header-function-fixed';\r\n/* app far left panel */\r\n@import '_modules/_left-panel';\r\n/* app navigation */\r\n@import '_modules/_nav';\r\n/* app navigation filter */\r\n@import '_modules/_nav-listfilter';\r\n/* app info card inside navigation */\r\n@import '_modules/_nav-info-card';\r\n/* app navigation made horizontal */\r\n@import '_modules/_nav-function-top';\r\n/* app navgation stays hidden */\r\n@import '_modules/_nav-function-hidden';\r\n/* app navigation stays fixed */\r\n@import '_modules/_nav-function-fixed';\r\n/* app navigation stays minified */\r\n@import '_modules/_nav-function-minify';\r\n/* app navigation footer */\r\n@import '_modules/_nav-footer';\r\n/* app wrapper */\r\n@import '_modules/_page-wrapper';\r\n/* app content heading */\r\n@import '_modules/_page-heading';\r\n/* app content */\r\n@import '_modules/_page-content';\r\n/* app footer */\r\n@import '_modules/_page-footer';\r\n/* app error page */\r\n@import '_modules/_page-error';\r\n/* various app components (see docs for the full list) */\r\n@import \"_modules/_page-components-accordion.scss\";\n@import \"_modules/_page-components-alerts.scss\";\n@import \"_modules/_page-components-badge.scss\";\n@import \"_modules/_page-components-breadcrumb.scss\";\n@import \"_modules/_page-components-buttons.scss\";\n@import \"_modules/_page-components-cards.scss\";\n@import \"_modules/_page-components-carousel.scss\";\n@import \"_modules/_page-components-dropdowns.scss\";\n@import \"_modules/_page-components-icon-stack.scss\";\n@import \"_modules/_page-components-listfilter.scss\";\n@import \"_modules/_page-components-loader.scss\";\n@import \"_modules/_page-components-messanger.scss\";\n@import \"_modules/_page-components-modal.scss\";\n@import \"_modules/_page-components-pagination.scss\";\n@import \"_modules/_page-components-panels.scss\";\n@import \"_modules/_page-components-popovers.scss\";\n@import \"_modules/_page-components-progressbar.scss\";\n@import \"_modules/_page-components-shortcut.scss\";\n@import \"_modules/_page-components-side-panels.scss\";\n@import \"_modules/_page-components-tables.scss\";\n@import \"_modules/_page-components-tabs.scss\";\n@import \"_modules/_page-components-tooltips.scss\";\r\n\r\n/* #MISC - misc styles, helpers, effects and hacks\r\n========================================================================== */ \r\n@import '_modules/_helpers';\r\n@import '_modules/_misc';\r\n@import '_modules/_effects';\r\n@import '_modules/_hack';\r\n@import '_modules/_hack-ie';\r\n\r\n/* #MOBILE - mobile media related styles\r\n========================================================================== */ \r\n/* contains most of the responsive styles for the app */\r\n@import '_modules/_responsive';\r\n/* changes content colors based on ambience light source of the user (experimental) */\r\n@import '_modules/_light-levels';\r\n\r\n/* #FORMS (customized bootstrap form elems)\r\n========================================================================== */ \r\n@import '_modules/_forms';\r\n\r\n/* #COMPONENTS (can be removed but may or may not impact other components)\r\n========================================================================== */ \r\n/*@import '_modules/_form-switches';*/\r\n@import '_modules/_translate-3d';\r\n\r\n/* #DEMO ELEMS - elements mostly used for demo (can be removed)\r\n========================================================================== */ \r\n@import \"_modules/_settings-demo-incompatiblity-list.scss\";\n@import \"_modules/_settings-demo-theme-colors.scss\";\n@import \"_modules/_settings-demo.scss\";\r\n@import '_modules/_demo-only';\r\n\r\n/* #_extensions - Components imported in alphabetical order (remove extensions from directory if not needed)\r\n========================================================================== */ \r\n@import \"_extensions/_extension-pace.scss\";\n@import \"_extensions/_extension-slimscroll.scss\";\n@import \"_extensions/_extension-waves.scss\";\r\n\r\n/* #_plugins - Components imported in alphabetical order (remove plugins from directory if not needed)\r\n========================================================================== */ \r\n/*@import '_plugins/_plugin-*.scss';*/\r\n\r\n/* #ANIMATION - CSS animations and keyframes\r\n========================================================================== */ \r\n@import \"_modules/_keyframes-general.scss\";\n@import \"_modules/_keyframes-highlight.scss\";\n@import \"_modules/_keyframes-spinner.scss\";\n@import \"_modules/_keyframes-transition.scss\";\r\n\r\n/* #MODS - Layout manipulation\r\n========================================================================== */ \r\n@import \"_modules/_mod-bg.scss\";\n@import \"_modules/_mod-clean-page-bg.scss\";\n@import \"_modules/_mod-colorblind.scss\";\n@import \"_modules/_mod-disable-animation.scss\";\n@import \"_modules/_mod-hide-info-card.scss\";\n@import \"_modules/_mod-hide-nav-icons.scss\";\n@import \"_modules/_mod-high-contrast.scss\";\n@import \"_modules/_mod-lean-page-header.scss\";\n@import \"_modules/_mod-main-boxed.scss\";\n@import \"_modules/_mod-nav-accessibility.scss\";\n@import \"_modules/_mod-text-size.scss\";\r\n\r\n/* #COLORS - we place this here so it can override other colors as needed\r\n========================================================================== */ \r\n@import '_modules/_colors';\r\n\r\n/* #APP related modules (print, fullscreen, etc)\r\n========================================================================== */ \r\n@import \"_modules/_app-custom-scrollbar.scss\";\n@import \"_modules/_app-fullscreen.scss\";\n@import \"_modules/_app-print.scss\";\r\n\r\n/* #OVERRIDE - You can override any of the variables through this file\r\n========================================================================== */ \r\n@import '_modules/_overrides';", "/* #BOOTSTRAP AND MIXINS - Base Unmodified Bootstrap file with theme mixins\r\n========================================================================== */\r\n@import './node_modules/bootstrap/scss/functions';\r\n@import './node_modules/bootstrap/scss/variables'; \r\n@import './node_modules/bootstrap/scss/mixins';\r\n@import './src/scss/_mixins/mixins';\r\n\r\n/* #BASE - Base Variable file along with font library, and colors.\r\n========================================================================== */\r\n@import './src/scss/_modules/variables';\r\n@import './src/scss/_modules/_fonts';\r\n@import './src/scss/_modules/_placeholders';\r\n@import './src/scss/_modules/_custom';", "/*---------------------------------------------------\r\n    SASS ELements (based on LESS Elements 0.9 http://lesselements.com) \r\n  -------------------------------- -------------------\r\n    LESS ELEMENTS made by <PERSON> (http://fadeyev.net)\r\n    SASS port by <PERSON> (http://samuelbeek.com) \r\n  ---------------------------------------------------*/\r\n \r\n@mixin gradient-img($start: #EEE,$stop: #FFF) {\r\n  background-color: $start;\r\n  background-image: -webkit-linear-gradient(top,$start,$stop);\r\n  background-image: linear-gradient(to top,$start,$stop);\r\n}\r\n\r\n@mixin gradient($color: #F5F5F5,$start: #EEE,$stop: #FFF) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,$start),color-stop(1,$stop));\r\n    background:-ms-linear-gradient(bottom,$start,$stop);\r\n    background:-moz-linear-gradient(center bottom,$start 0%,$stop 100%);\r\n    background:-o-linear-gradient($stop,$start);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=$start,endColorstr=$stop)\r\n}\r\n\r\n@mixin bw-gradient($color: #F5F5F5,$start: 0,$stop: 255) {\r\n    background:$color;\r\n    background:-webkit-gradient(linear,left bottom,left top,color-stop(0,#000),color-stop(1,#000));\r\n    background:-ms-linear-gradient(bottom,#000 0%,#000 100%);\r\n    background:-moz-linear-gradient(center bottom,#000 0%,#000 100%);\r\n    background:-o-linear-gradient(#000,#000);\r\n    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=rgb($start,$start,$start),endColorstr=rgb($stop,$stop,$stop))\r\n}\r\n\r\n@mixin bordered($top-color: #EEE,$right-color: #EEE,$bottom-color: #EEE,$left-color: #EEE) {\r\n    border-top:solid 1px $top-color;\r\n    border-left:solid 1px $left-color;\r\n    border-right:solid 1px $right-color;\r\n    border-bottom:solid 1px $bottom-color\r\n}\r\n\r\n@mixin drop-shadow($x-axis: 0,$y-axis: 1px,$blur: 2px,$alpha: 0.1) {\r\n    //-webkit-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    //-moz-box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha);\r\n    box-shadow:$x-axis $y-axis $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin rounded($radius: 2px) {\r\n    border-radius:$radius\r\n}\r\n\r\n@mixin border-radius($topright: 0,$bottomright: 0,$bottomleft: 0,$topleft: 0) {\r\n    border-top-right-radius:$topright;\r\n    border-bottom-right-radius:$bottomright;\r\n    border-bottom-left-radius:$bottomleft;\r\n    border-top-left-radius:$topleft\r\n}\r\n\r\n@mixin opacity($opacity: 0.5) {\r\n    -moz-opacity:$opacity;\r\n    -khtml-opacity:$opacity;\r\n    -webkit-opacity:$opacity;\r\n    opacity:$opacity;\r\n    $opperc:$opacity * 100\r\n/*\r\n  -ms-filter: ~\"progid:DXImageTransform.Microsoft.Alpha(opacity=${opperc})\";\r\n  filter: ~\"alpha(opacity=${opperc})\";\r\n*/\r\n}\r\n\r\n@mixin transition-duration($duration: 0.2s) {\r\n    -moz-transition-duration:$duration;\r\n    -webkit-transition-duration:$duration;\r\n    -o-transition-duration:$duration;\r\n    transition-duration:$duration\r\n}\r\n\r\n@mixin transform($arguments) {\r\n    -webkit-transform:$arguments;\r\n    -moz-transform:$arguments;\r\n    -o-transform:$arguments;\r\n    -ms-transform:$arguments;\r\n    transform:$arguments\r\n}\r\n\r\n@mixin rotation($deg:5deg) {\r\n}\r\n\r\n@mixin scale($ratio:1.5) {\r\n}\r\n\r\n@mixin transition($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:all $duration $ease;\r\n    -moz-transition:all $duration $ease;\r\n    -o-transition:all $duration $ease;\r\n    transition:all $duration $ease\r\n}\r\n\r\n@mixin transition-color($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:color $duration $ease;\r\n    -moz-transition:color $duration $ease;\r\n    -o-transition:color $duration $ease;\r\n    transition:color $duration $ease\r\n}\r\n\r\n@mixin transition-border($duration:0.2s,$ease:ease-out) {\r\n    -webkit-transition:border $duration $ease;\r\n    -moz-transition:border $duration $ease;\r\n    -o-transition:border $duration $ease;\r\n    transition:border $duration $ease\r\n}\r\n\r\n@mixin transition-background-color($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:background-color $duration $ease;\r\n    -moz-transition:background-color $duration $ease;\r\n    -o-transition:background-color $duration $ease;\r\n    transition:background-color $duration $ease\r\n}\r\n\r\n@mixin transition-fill($duration:0.2s,$ease:ease) {\r\n    -webkit-transition:fill $duration $ease;\r\n    -moz-transition:fill $duration $ease;\r\n    -o-transition:fill $duration $ease;\r\n    transition:fill $duration $ease\r\n}\r\n\r\n@mixin inner-shadow($horizontal:0,$vertical:1px,$blur:2px,$alpha: 0.4) {\r\n    -webkit-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    -moz-box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha);\r\n    box-shadow:inset $horizontal $vertical $blur rgba(0,0,0,$alpha)\r\n}\r\n\r\n@mixin box-shadow($arguments) {\r\n    //-webkit-box-shadow:$arguments;\r\n    //-moz-box-shadow:$arguments;\r\n    box-shadow:$arguments\r\n}\r\n\r\n@mixin box-sizing($sizing: border-box) {\r\n    //-ms-box-sizing:$sizing;\r\n    //-moz-box-sizing:$sizing;\r\n    //-webkit-box-sizing:$sizing;\r\n    box-sizing:$sizing\r\n}\r\n\r\n@mixin user-select($argument: none) {\r\n    -webkit-user-select:$argument;\r\n    -moz-user-select:$argument;\r\n    -ms-user-select:$argument;\r\n    user-select:$argument\r\n}\r\n\r\n@mixin columns($colwidth: 250px,$colcount: 0,$colgap: 50px,$columnRuleColor: #EEE,$columnRuleStyle: solid,$columnRuleWidth: 1px) {\r\n    -moz-column-width:$colwidth;\r\n    -moz-column-count:$colcount;\r\n    -moz-column-gap:$colgap;\r\n    -moz-column-rule-color:$columnRuleColor;\r\n    -moz-column-rule-style:$columnRuleStyle;\r\n    -moz-column-rule-width:$columnRuleWidth;\r\n    -webkit-column-width:$colwidth;\r\n    -webkit-column-count:$colcount;\r\n    -webkit-column-gap:$colgap;\r\n    -webkit-column-rule-color:$columnRuleColor;\r\n    -webkit-column-rule-style:$columnRuleStyle;\r\n    -webkit-column-rule-width:$columnRuleWidth;\r\n    column-width:$colwidth;\r\n    column-count:$colcount;\r\n    column-gap:$colgap;\r\n    column-rule-color:$columnRuleColor;\r\n    column-rule-style:$columnRuleStyle;\r\n    column-rule-width:$columnRuleWidth\r\n}\r\n\r\n@mixin translate($x:0,$y:0) {\r\n    -webkit-transform: translate($x,$y);\r\n    -moz-transform: translate($x,$y);\r\n    -ms-transform: translate($x,$y);\r\n    -o-transform: translate($x,$y);\r\n    transform: translate($x,$y);\r\n}\r\n\r\n@mixin translate3d($x:0,$y:0,$z:0) {\r\n  -webkit-transform: translate3d($x, $y, $z);\r\n      -ms-transform: translate3d($x, $y, $z); \r\n          transform: translate3d($x, $y, $z); \r\n}\r\n\r\n@mixin background-clip($argument: padding-box) {\r\n    -moz-background-clip:$argument;\r\n    -webkit-background-clip:$argument;\r\n    background-clip:$argument\r\n}\r\n\r\n@mixin transform($transforms) {\r\n     -moz-transform: $transforms;\r\n       -o-transform: $transforms;\r\n      -ms-transform: $transforms;\r\n  -webkit-transform: $transforms;\r\n          transform: $transforms;\r\n}\r\n// rotate\r\n@mixin rotate ($deg) {\r\n  @include transform(rotate(#{$deg}deg));\r\n}\r\n \r\n// scale\r\n@mixin scale($scale) {\r\n   @include transform(scale($scale));\r\n} \r\n// translate\r\n@mixin translate ($x, $y) {\r\n   @include transform(translate($x, $y));\r\n}\r\n// skew\r\n@mixin skew ($x, $y) {\r\n   @include transform(skew(#{$x}deg, #{$y}deg));\r\n}\r\n//transform origin\r\n@mixin transform-origin ($origin) {\r\n    -moz-transform-origin: $origin;\r\n       -o-transform-origin: $origin;\r\n      -ms-transform-origin: $origin;\r\n  -webkit-transform-origin: $origin;\r\n          transform-origin: $origin;\r\n}\r\n\r\n//Rem size support\r\n\r\n/*------------------------\r\n    Usage\r\n\r\n    h1 {\r\n      font-size: rem(32);\r\n    }\r\n\r\n    OR:\r\n\r\n    h1 {\r\n      font-size: rem(32px);\r\n    }\r\n------------------------*/\r\n\r\n$browser-context: 16;\r\n\r\n@function rem($pixels, $context: $browser-context) {\r\n  @if (unitless($pixels)) {\r\n    $pixels: $pixels * 1px;\r\n  }\r\n\r\n  @if (unitless($context)) {\r\n    $context: $context * 1px;\r\n  }\r\n\r\n  @return $pixels / $context * 1rem;\r\n}\r\n\r\n/*------------------------\r\n  FADE IN\r\n  e.g. @include fadeIn( 2s );\r\n------------------------*/\r\n\r\n//$prefix:'-moz-', '-webkit-', '-o-', '-ms-', '';\r\n//\r\n//@mixin keyframe-fadeIn {\r\n//  0%   { opacity:0; }\r\n//  100% { opacity:1; }\r\n//}\r\n//\r\n//@-moz-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-webkit-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-o-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@-ms-keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//@keyframes fadeIn {\r\n//  @include keyframe-fadeIn;\r\n//}\r\n//\r\n//@mixin fadeIn( $arg ) {\r\n//  $keyframe-name:fadeIn;\r\n//  $duration:$arg;\r\n//  @each $p in $prefix {\r\n//    #{$p}animation:$keyframe-name $duration;\r\n//  }\r\n//}\r\n\r\n/*------------------------\r\nmixin that calculates if text needs to be light or dark\r\ndepending on the background color passed.\r\n\r\nFrom this W3C document: http://www.webmasterworld.com/r.cgi?f=88&d=9769&url=http://www.w3.org/TR/AERT#color-contrast\r\n\r\nusage:\r\n@include text-contrast($bgcolor)\r\n      \r\nColor brightness is determined by the following formula: \r\n((Red value X 299) + (Green value X 587) + (Blue value X 114)) / 1000\r\n------------------------*/\r\n\r\n@mixin text-contrast($n:#333) {\r\n  $color-brightness: round((red($n) * 299) + (green($n) * 587) + (blue($n) * 114) / 1000);\r\n  $light-color: round((red(#ffffff) * 299) + (green(#ffffff) * 587) + (blue(#ffffff) * 114) / 1000);\r\n  \r\n  @if abs($color-brightness) < ($light-color/1.70){\r\n    color: rgba(255,255,255,1);\r\n  }\r\n\r\n  @else {\r\n    color: rgba(0,0,0,0.8);\r\n  }\r\n}\r\n\r\n/*------------------------\r\n color factory \r\n  eg: @include paint($blue-grey-50, bg-blue-grey-50);\r\n------------------------*/\r\n\r\n\r\n@mixin paint($paint:#333333,$make:bg-blue-grey-50) {\r\n\r\n    .#{$make} {\r\n      background-color: $paint;\r\n      @include text-contrast($paint)\r\n      &:hover {\r\n        @include text-contrast($paint)\r\n      }\r\n    }\r\n}\r\n\r\n@mixin brush($brush: #333,$make: red-50) {\r\n    .#{$make} {\r\n      color: $brush;\r\n    }\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin set-settings($class-element: nav-function-fixed) {\r\n\r\n    .#{$class-element} .btn-switch[data-class=\"#{$class-element}\"] {\r\n      @extend %set-settings;\r\n    }\r\n\r\n}\r\n\r\n//mixen for settings side buttons\r\n@mixin paint-gradient($paint: $fusion-500, $make:bg-fusion-gradient) {\r\n\r\n    .#{$make} {\r\n      background-image: -webkit-linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n      background-image: linear-gradient(250deg, rgba($paint, 0.7), transparent);\r\n    }\r\n\r\n}\r\n\r\n/* backface visibility */\r\n@mixin backface-visibility($argument: none) {\r\n  -webkit-backface-visibility: hidden;\r\n  -moz-backface-visibility:    hidden;\r\n  -ms-backface-visibility:     hidden;\r\n   backface-visibility:        hidden;\r\n}\r\n\r\n/* generate theme button */\r\n@mixin theme-button-color ($theme-fusion:none, $theme-primary:none, $theme-info:none, $theme-success:none, $theme-warning:none, $theme-danger:none) {\r\n  background-image: -webkit-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -moz-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: -ms-linear-gradient(left, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n  background-image: linear-gradient(to right, #{$theme-fusion}, #{$theme-fusion} 70%, #{$theme-primary} 70%, #{$theme-primary} 76%, #{$theme-info} 76%, #{$theme-info} 82%, #{$theme-success} 82%, #{$theme-success} 88%, #{$theme-warning} 88%, #{$theme-warning} 94%, #{$theme-danger} 94%, #{$theme-danger} 94%, #{$theme-danger} 100%);\r\n}\r\n\r\n// IE flexbox details:\r\n//\r\n// - Flexbox in IE 10:\r\n//   https://msdn.microsoft.com/en-us/library/hh673531(v=vs.85).aspx\r\n//\r\n// - IE 11 flexbox changes (includes property/value names for IE 10)\r\n//   https://msdn.microsoft.com/library/dn265027(v=vs.85).aspx\r\n\r\n@mixin flexbox ($important: false) {\r\n  display: unquote(\"-ms-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin inline-flexbox ($important: false) {\r\n  display: unquote(\"-ms-inline-flexbox #{if($important, '!important', null)}\");\r\n  display: unquote(\"inline-flex #{if($important, '!important', null)}\");\r\n}\r\n\r\n@mixin align-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-line-pack: map-get($ms-map, $value) or $value;\r\n  align-content: $value;\r\n}\r\n\r\n@mixin align-items ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-align: map-get($ms-map, $value) or $value;\r\n  align-items: $value;\r\n}\r\n\r\n@mixin align-self ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end\r\n  );\r\n  -ms-flex-item-align: map-get($ms-map, $value) or $value;\r\n  align-self: $value;\r\n}\r\n\r\n@mixin flex ($value) {\r\n  -ms-flex: $value;\r\n  flex: $value;\r\n}\r\n\r\n@mixin flex-direction ($value) {\r\n  -ms-flex-direction: $value;\r\n  flex-direction: $value;\r\n}\r\n\r\n@mixin flex-wrap ($value) {\r\n  $ms-map: (\r\n    nowrap: none\r\n  );\r\n  -ms-flex-wrap: map-get($ms-map, $value) or $value;\r\n  flex-wrap: $value;\r\n}\r\n\r\n@mixin justify-content ($value) {\r\n  $ms-map: (\r\n    flex-start: start,\r\n    flex-end: end,\r\n    space-around: distribute,\r\n    space-between: justify\r\n  );\r\n  -ms-flex-pack: map-get($ms-map, $value) or $value;\r\n  justify-content: $value;\r\n}\r\n\r\n@mixin order ($value) {\r\n  -ms-flex-order: $value;\r\n  order: $value;\r\n}", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n\r\n/* select */\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\tauto !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\tauto !default \t\t\t\t\t\t\t\t\t\t    /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t\t      /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t/* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\t#d58100 !default;\r\n$settings-incompat-desc:\t\t\t\t#ec9f28 !default;\r\n$settings-incompat-bg:\t\t\t\t\t$warning-50 !default;\r\n$settings-incompat-border:\t\t\t\t$warning-700 !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;", "@import url($font-import);\r\n\r\nbody {\r\n\tfont-family: $page-font;\r\n\tfont-size: rem($fs-base);\r\n\tletter-spacing: 0.1px;\r\n}\r\n\r\n.page-content {\r\n\tcolor: $base-text-color;\r\n}\r\n\r\nh1, h2, h3, h4, h5, h6 {\r\n\tline-height: 1.3;\r\n\tfont-weight: 400;\r\n\t//color:$fusion-500;\r\n}\r\n\r\nstrong {\r\n\tfont-weight: 500;\r\n}\r\n\r\nh1 small, \r\nh2 small, \r\nh3 small, \r\nh4 small, \r\nh5 small, \r\nh6 small, \r\n.h1 small, \r\n.h2 small, \r\n.h3 small, \r\n.h4 small, \r\n.h5 small, \r\n.h6 small {\r\n    font-weight: 300;\r\n    display: block;\r\n\tfont-size: rem($fs-lg);\r\n    line-height: 1.5;\r\n    //letter-spacing: -0.2px;\r\n    margin:2px 0 ($grid-gutter-width-base / 2);\r\n}\r\n\r\nh2 small, \r\nh3 small, \r\n.h2 small, \r\n.h3 small, {\r\n\tfont-size: rem($fs-lg);\r\n}\r\n\r\nh4 small, \r\n.h4 small {\r\n\tfont-size: rem($fs-md);\r\n}\r\n\r\nh5 small, \r\nh6 small, \r\n.h5 small, \r\n.h6 small {\r\n\tfont-size: rem($fs-base);\t\r\n}\r\n\r\n/* contrast text */\r\n.text-contrast {\r\n\tcolor: lighten($black, 20%);\r\n}\r\n\r\n/* text-gradient */\r\n.text-gradient {\r\n\tbackground: -webkit-linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tbackground: linear-gradient(180deg, $primary-700 25%, $primary-800 50%, $info-700 75%, $info-900 100%);\r\n\tcolor: $primary-500;\r\n    background-clip: text;\r\n    text-fill-color: transparent;\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    text-shadow: none;\r\n}\r\n\r\n/* looking for font size? Check _helpers.scss */", "/* PLACEHOLDER \r\n============================================= \r\n\r\nEXAMPLE:\r\n\r\n%bg-image {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n.image-one {\r\n\t\t@extend %bg-image;\r\n\t\tbackground-image:url(/img/image-one.jpg\");\r\n}\r\n\r\nRESULT:\r\n\r\n.image-one, .image-two {\r\n\t\twidth: 100%;\r\n\t\tbackground-position: center center;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n}\r\n\r\n*/\r\n\r\n%nav-bg {\r\n\tbackground-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\tbackground-color: $nav-background;\r\n}\r\n\r\n/*\r\n%shadow-hover {\r\n\tbox-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 0 2px rgba(0,0,0,0.24);\r\n\ttransition: all 0.2s ease-in-out;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 -1px 6px rgba(0,0,0,0.23);\r\n\t}\r\n}\r\n*/\r\n%btn-default {\r\n\t@include gradient-img($start: #f5f5f5,$stop: #f1f1f1);\r\n\tcolor: #444;\r\n\tborder: 1px solid rgba(0,0,0,0.1);\r\n\tbox-shadow: none;\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder: 1px solid #c6c6c6;\r\n\t\tcolor: #333;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t&:focus {\r\n\t\tborder-color: $primary-200 !important;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground: $primary-300;\r\n\t\tcolor: $white;\r\n\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;\r\n\t}\r\n}\r\n\r\n%custom-scroll {\r\n\r\n\t&::-webkit-scrollbar-track-piece {\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\r\n &::-webkit-scrollbar-thumb:vertical {\r\n\t\tbackground-color: #666;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar {\r\n\t\theight: 4px;\r\n\t\twidth: 4px;\r\n\t}\r\n\r\n &::-webkit-scrollbar-corner {\r\n\t\twidth: 40px;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar-thumb:vertical {\r\n\tbackground-color: #666;\r\n\t}\r\n\r\n\toverflow: hidden;\r\n\toverflow-y: scroll;\r\n\t-webkit-overflow-scrolling: touch;\r\n\r\n}\r\n\r\n%user-select {\r\n\t\t-webkit-user-select: none; \r\n\t\t\t -moz-user-select: none; \r\n\t\t\t\t-ms-user-select: none;\r\n}\r\n\r\n%content-box {\r\n\tbox-sizing: content-box;\r\n}\r\n\r\n%flex-0-0-auto {\r\n\tflex: 0 0 auto;\r\n}\r\n\r\n%transform-3d {\r\n\t@include translate3d(0,0,0);\r\n}\r\n\r\n\r\n%stop-transform-3d {\r\n\t\t\t\t\t\ttransform: none;\r\n\t\t-webkit-transform: none;\r\n\t\t\t\t-ms-transform: none;\r\n}\r\n\r\n%general-animation {\r\n\ttransition: $nav-hide-animate;      \r\n}\r\n\r\n%common-animation-slow {\r\n\t@include transition(0.3s,ease-in-out);\r\n\r\n}\r\n\r\n%common-animation {\r\n\t@include transition(0.2s,ease-in-out);\r\n}\r\n\r\n%common-animation-easeout {\r\n\t@include transition(0.4s,ease-out);\r\n}\r\n\r\n%common-animation-opacity {\r\n\ttransition: opacity 0.5s ease-in-out;\r\n}\r\n\r\n%common-animation-opacity-faster {\r\n\ttransition: opacity 0.1s ease-in-out;\r\n}\r\n\r\n%stop-animation {\r\n\ttransition: none;\r\n}\r\n\r\n%font-smoothing {\r\n\t\t -webkit-font-smoothing: antialiased;\r\n\t\t-moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n%set-settings {\r\n\tcolor:$white;\r\n\tbackground:$color-primary !important;\r\n\t&:before {\r\n\t\tcontent:\"ON\" !important;\r\n\t\tleft:7px !important;\r\n\t\tright:auto !important;\r\n\t}\r\n\t&:after {\r\n\t\tcontent: \" \" !important;\r\n\t\tright:0 !important;\r\n\t\tleft:auto !important;\r\n\t\tbackground:$white !important;\r\n\t\tcolor:$color-primary !important;\r\n\t}\r\n\r\n\t+ .onoffswitch-title {\r\n\t\tfont-weight:500;\r\n\t\tcolor: $primary-500;\r\n\t}\r\n}\r\n\r\n%bg-img-cover {\r\n\tbackground-size: cover;\r\n}\r\n\r\n%not-compatible {\r\n\t\tposition:relative;\r\n\t\t\r\n\t\t.onoffswitch-title {\r\n\t\t\tcolor: $settings-incompat-title !important;\r\n\t\t}\r\n\t\t.onoffswitch-title-desc {\r\n\t\t\tcolor: $settings-incompat-desc !important;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \"DISABLED\";\r\n\t\t\t@extend %incompatible;\r\n\t\t}\r\n}\r\n\r\n%not-compatible-override {\r\n\t\t&:before {\r\n\t\t\tdisplay:none !important;\r\n\t\t}\r\n}\r\n\r\n%ping-badge {\r\n\tposition: absolute;\r\n\tdisplay: block;\r\n\tborder-radius: 1rem;\r\n\tbackground-color: $nav-badge-bg-color;\r\n\tcolor: $nav-badge-color;\r\n\ttext-align: center;\r\n\tcursor: pointer;\r\n\t@include box-shadow(0 0 0 1px $nav-background);\r\n\tborder: 1px solid $nav-background;\r\n\tmin-width: 2rem;\r\n\tmax-width: 1.5rem;\r\n\tpadding: 2px;\r\n\tfont-weight: 500;\r\n\tline-height: normal;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n}\r\n\r\n\r\n\r\n/*%fixed-header-shadow {\r\n\t@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));\r\n}*/\r\n\r\n%header-btn {\r\n\t//@extend %btn-default;\r\n\t@include rounded($header-btn-border-radius);\r\n\tborder: 1px solid lighten($fusion-50, 30%);\r\n\theight: $header-btn-height;\r\n\twidth: $header-btn-width;\r\n\tvertical-align: middle;\r\n\tline-height: $header-btn-height - 0.125rem;\r\n\tmargin-right: $grid-gutter-width-base/4 + 0.1875rem;\r\n\tfont-size: $header-btn-font-size;\r\n\tpadding: $list-table-padding-y $list-table-padding-x;\r\n\tcursor: default;\r\n\tcolor:$header-btn-color;\r\n\tposition: relative;\r\n\t\t//background: $primary-200;\r\n\t\t//color:$primary-200;\r\n/*\r\n\t&.active {\r\n\t\t@extend %header-btn-active;\r\n\t}*/\r\n\r\n\t&:hover {\r\n\t\tbox-shadow: none;\r\n\t\tborder-color: $primary-500;\r\n\t\tbackground: $primary-300;\r\n\t\tcolor:$white;\r\n\r\n\t}\r\n\r\n}\r\n\r\n%expanded-box {\r\n\tbox-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.125);\r\n\tborder-bottom: 1px solid rgba(0,0,0,0.06);\r\n\tborder-width: 0 0 1px 0;\r\n\tbackground: $white;\r\n\tpadding: 16px 16px 10px;\r\n}\r\n\r\n%header-btn-active {\r\n\tbackground: $header-btn-active-bg;\r\n\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t@include box-shadow(inset 0 0 3px 1px rgba(0,0,0,.37));\r\n\tcolor:$header-btn-active-color !important;\r\n}\r\n\r\n//@include media-breakpoint-up($mobile-breakpoint) {\r\n/*  %selected-dot {\r\n\t\t&:before {\r\n\t\t\tcontent: \" \";\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: inherit;\r\n\t\t\tbackground-image: none;\r\n\t\t\tborder: 2px solid rgba(0,0,0,0.2);\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 15px;\r\n\t\t\tleft: 15px;\r\n\t\t\theight: 20px;\r\n\t\t\twidth: 20px;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\theight: inherit;\r\n\t\t\twidth: inherit;\r\n\t\t\tborder: 5px solid rgba(0,0,0,0.1);\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tborder-radius: 50%;\r\n\t\t} \r\n\t}*/\r\n//}\r\n\r\n%spin-loader {\r\n\tmargin: 5px;\r\n\theight: 20px;\r\n\twidth: 20px;\r\n\tanimation: spin 0.5s infinite linear;\r\n\tborder: 2px solid $color-primary;\r\n\tborder-right-color: transparent;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n%incompatible {\r\n\tdisplay: block;\r\n\tposition: absolute;\r\n\tbackground: $settings-incompat-bg;\r\n\tfont-size: 10px;\r\n\twidth: 65px;\r\n\ttext-align: center;\r\n\tborder: 1px solid $settings-incompat-border;\r\n\theight: 22px;\r\n\tline-height: 20px;\r\n\tborder-radius: $border-radius-plus;\r\n\tright: 13px;\r\n\ttop: 26%;\r\n\tcolor:$fusion-900;\r\n}\r\n\r\n/* patterns */\r\n%pattern-0 {\r\n\tbackground-size: 10px 10px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .07) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .07) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .05) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .05) 50%, rgba(255, 255, 255, .05) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 10px 10px transparent;\r\n}\r\n\r\n%pattern-1 {\r\n\tbackground-size: 5px 5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .04) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .04) 50%, rgba(255, 255, 255, .04) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 5px 5px transparent;\r\n}\r\n\r\n%pattern-2 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px transparent;\r\n}\r\n\r\n%pattern-3 {\r\n\tbackground-size: 15px 15px;\r\n\tbackground-image: -webkit-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: -moz-linear-gradient(0deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\tbackground-image: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent);\r\n\t-pie-background: linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%, transparent) 0 0 / 15px 15px transparent;\r\n}\r\n\r\n%pattern-4 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-position: 0 0, 18.5px 18.5px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 37px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 37px 37px / 74px,\r\n\t\t\t\t\t\t\t\t\t transparent;\r\n}\r\n\r\n%pattern-5 {\r\n\tbackground-size: 37px 37px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-webkit-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\t-moz-linear-gradient(-45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)),\r\n\t\t\t\t\t\t\t\t\t\tlinear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2));\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t linear-gradient(135deg, rgba(255, 255, 255, .2) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, .2) 75%, rgba(255, 255, 255, .2)) 0 0 / 60px,\r\n\t\t\t\t\t\t\t\t\t #eee;\r\n}\r\n\r\n%pattern-6 {\r\n\tbackground-size: 50px 50px;\r\n\tbackground-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\tbackground-image: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t\ttransparent 75%, transparent);\r\n\t-pie-background: linear-gradient(45deg, rgba(255, 255, 255, .2) 25%, transparent 25%,\r\n\t\t\t\t\t\t\t\t\t transparent 50%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, .2) 75%,\r\n\t\t\t\t\t\t\t\t\t transparent 75%, transparent) 0 0 / 50px 50px transparent;\r\n}\r\n\r\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n$table-dark-color:            $white !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    calc(#{$input-line-height * 1em} + #{$input-padding-y * 2}) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-line-height * 1em} + #{$input-padding-y * 2} + #{$input-height-border}) !default;\n$input-height-sm:                       calc(#{$input-line-height-sm * 1em} + #{$input-btn-padding-y-sm * 2} + #{$input-height-border}) !default;\n$input-height-lg:                       calc(#{$input-line-height-lg * 1em} + #{$input-btn-padding-y-lg * 2} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   none !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  calc(#{$custom-control-indicator-size} - #{$custom-control-indicator-border-width * 4}) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$custom-select-background:          $custom-select-indicator no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: calc((1em + #{2 * $custom-select-padding-y}) * 3 / 4 + #{$custom-select-padding-x + $custom-select-indicator-padding}) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$form-feedback-icon-invalid-color}' viewBox='-2 -2 7 7'%3e%3cpath stroke='#{$form-feedback-icon-invalid-color}' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E\"), \"#\", \"%23\") !default;\n\n$form-validation-states: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:      str-replace(url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e\"), \"#\", \"%23\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", ":root {\r\n  @each $theme-colors, $value in $theme-colors {\r\n    --theme-#{$theme-colors}: #{$value};\r\n  }\r\n\r\n  @each $theme-colors-extended, $value in $theme-colors-extended {\r\n    --theme-#{$theme-colors-extended}: #{$value};\r\n  }\r\n  \r\n  @each $bp, $value in $grid-breakpoints {\r\n    --breakpoint-#{$bp}: #{$value};\r\n  }\t\r\n}", "/* for IE */ \r\nmain {\r\n\tdisplay:block;\r\n}\r\n/* removes dotted lines for focus */\r\na, a:active, a:focus, \r\nbutton, button:focus, button:active, \r\n.btn, .btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn.focus:active, .btn.active.focus {\r\n    outline: none;\r\n    outline: 0;\r\n}\r\ninput::-moz-focus-inner {\r\n    border: 0;\r\n} ", "html {\r\n\tbody {\t\r\n\t\tdirection: ltr;\r\n\t\ttext-rendering: optimizeLegibility;\r\n\t\tbackground-color: $body-background-color;\r\n\t}\r\n}", ".header-icon {\t\t\r\n\tcolor: $base-text-color;\r\n\theight: $header-height;\r\n\tdisplay: block;\r\n\tline-height: $header-height;\r\n\ttext-decoration: none;\r\n\tposition: relative;\r\n\r\n\t&:not(.btn) {\r\n\t\tmin-width: $header-non-btn-width;\r\n\t\ttext-align: center;\r\n\t\toverflow: visible;\r\n\r\n\t\t>[class*='fa-']:first-child,\r\n\t\t>.#{$cust-icon-prefix}:first-child {\r\n\t\t\tcolor: $header-link-color;\r\n\t\t    vertical-align: middle;\r\n\t\t\t}\r\n\t\t\t>[class*='fa-']:first-child {\r\n\t\t\t\tfont-size: $header-icon-size;\r\n\t\t\t}\r\n\t\t\t>.#{$cust-icon-prefix}:first-child {\r\n\t\t\t\tfont-size: $header-icon-size;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\r\n\t\t\t\t&>[class*='fa-']:only-child,\r\n\t\t\t\t&>.#{$cust-icon-prefix} {\r\n\t\t\t\tcolor: $header-link-hover-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t&[data-toggle=\"dropdown\"] {\r\n\r\n\t\t\t&[data-toggle=\"dropdown\"] {\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: \" \";\r\n\t\t\t\t\twidth: 1.5rem;\r\n\t\t\t\t\theight: 1.5rem;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbackground: #dae1e8;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\ttop: ($header-height - 1.5rem) /2;\r\n\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\tleft: 0.9375rem;\r\n\t\t\t\t\topacity: 0;\r\n\t\t\t\t\ttransition: all 100ms ease-in;\r\n\t\t\t\t}\t\r\n\t\t\t}\r\n\r\n\r\n\t\t\t&[aria-expanded=\"true\"] {\r\n\t\t\t\tcolor: $header-link-hover-color;\r\n\t\t\t\t/* new lines for arrow visibility */\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: $depth-header + 1;\r\n\r\n\t\t\t\t\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: \" \";\r\n\t\t\t\t\twidth: 2.5rem;\r\n\t\t\t\t\theight: 2.5rem;\r\n\t\t\t\t\ttop: ($header-height - 2.5rem ) /2;\r\n\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\tleft: 0.3125rem;\r\n\t\t\t\t\topacity: 1;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t>[class*='fa-']:first-child,\r\n\t\t\t\t>.#{$cust-icon-prefix}:first-child {\r\n\t\t\t\t\tcolor: $header-link-hover-color !important;\r\n\t\t\t\t\t-webkit-background-clip: initial;\r\n\t\t\t\t\t-webkit-text-fill-color: initial;\r\n\t\t\t\t\tbackground: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t/* header dropdowns */\r\n\t\t\t/* note: important rules to override popper's inline classes */\r\n\t\t\t& + .dropdown-menu {\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\tborder: $theme-border-utility-size solid $header-border-color;\r\n\t\t\t\tright: 2rem; //same as page padding\r\n\t\t\t\ttop: $header-height - 0.0625rem !important;\r\n\t\t\t\tleft: auto !important;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t}\r\n\r\n\t\t\t/* end header dropdowns */\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t.profile-image {\r\n\t\t\twidth: $profile-image-width-md;\r\n\t\t\theight:auto;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&:hover{\r\n\t\tcursor:default;\r\n\t\tcolor:$header-link-hover-color;\r\n\t}\r\n}\r\n\r\n.page-header {\r\n\tbackground-color: $header-bg;\r\n\tbox-shadow: 0px 0px 28px 0px $header-border-bottom-color;\r\n\tdisplay: flex;\r\n\r\n\tflex: 0 0 auto;\r\n\r\n\talign-items:center;\r\n\r\n\theight: $header-height;\r\n\tposition: relative;\r\n\tz-index: $depth-header;\r\n\r\n\torder: 1;\r\n\r\n\t.page-logo {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.badge-icon {\r\n\t\t\r\n\t\tleft: $header-badge-left;\r\n\t\ttop: $header-badge-top;\t\r\n\r\n\t\t&:only-child {\r\n\t\t\tposition: relative;\r\n\t\t\tleft: auto;\r\n\t\t\tright: auto;\r\n\t\t\tfont-size: $fs-md;\r\n\t\t\theight: 26px;\r\n\t\t\twidth: 26px;\r\n\t\t\tline-height: 21px;\r\n\t\t\ttop: 20px;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\t}\r\n\t\r\n}", ".page-logo {\r\n\theight: $header-height;\r\n\twidth: $nav-width;\r\n\r\n\t@extend %nav-bg;\r\n\r\n\tbox-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);\r\n\r\n\toverflow: hidden; \r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\r\n    -ms-flex-positive: 0;\r\n    flex-grow: 0;\r\n\r\n    -ms-flex-negative: 0;\r\n    flex-shrink: 0;\r\n\r\n    min-height: 1px;\r\n\r\n\tpadding: 0 $nav-padding-x;\r\n\r\n\t@extend %general-animation;\r\n\r\n\timg {\r\n\t\twidth: $header-logo-width;\r\n\t\theight: $header-logo-height;\r\n\t}\r\n\r\n\t.page-logo-link {\r\n\t\tflex: 1 0 auto;\r\n\t}\r\n\r\n}\r\n\r\n.page-logo-text {\r\n\tmargin-left: 0.5rem;\r\n\tfont-weight: 300;\r\n\tfont-size: 1rem; //1.125rem; this is the old size for the previous name\r\n\tcolor: $white;\r\n\tdisplay: block;\r\n\tflex: 1 0 auto;\r\n\ttext-align: left;\r\n}\r\n\r\nbody:not(.header-function-fixed) {\r\n\t.page-logo {\r\n\t\t@extend %general-animation;\r\n\t}\r\n}\r\n\r\n", ".search{\r\n\tflex: 1;\r\n\r\n\t.app-forms {\r\n\t\tpadding:0;\r\n\r\n\t\t&.has-length {\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\r\n\t\t&:before {\r\n\t\t\tcontent: none;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n#search-field {\r\n\tmax-width: $header-search-width - 3.125rem;\r\n\theight: $header-search-height;\r\n\tline-height: normal;\r\n\tborder-radius: $header-btn-border-radius;\r\n\tbackground: $header-search-bg;\r\n\tborder: 1px solid $header-search-border-color;\r\n\tbox-shadow: none;\r\n\tfont-size: rem($header-search-font-size);\r\n\tpadding: $header-search-padding;\r\n}", ".dropdown-icon-menu {\r\n\r\n\t> .btn {\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t> ul {\r\n\t\topacity: 0;\r\n\t\tlist-style: none;\r\n\t\tmargin: 0;\r\n\t\tposition: absolute;\r\n\t\tbackground: $white;\r\n\t\theight:$header-btn-height;\r\n\t\tpadding: ($header-btn-height + 0.5rem) 4px 5px;\r\n\t\twidth: $header-btn-width + 0.5rem;\r\n\t\tmargin-left: 1px;\r\n\t\tmargin-top: -($header-btn-height + 0.25rem);\r\n\t\tleft: -5px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 3px 3px rgba(0,0,0,.12),0 0 3px rgba(0,0,0,.24);\r\n\r\n\t\t@include rounded(rem($header-btn-border-radius));\r\n\r\n\t\ttransition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\r\n\t\t> li {\r\n\t\t\tmargin-bottom: 4px;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.btn {\r\n\t\t\t\t@extend %header-btn;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom:0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&:hover {\r\n\r\n\t\t> ul {\r\n\t\t\tdisplay: block;\r\n\t\t\topacity: 1;\r\n\t\t\t/*\r\n\t\t\t * n = number of buttons minus 1 \r\n\t\t\t *     eg. $header-btn-height * 2n\r\n\t\t\t */\r\n\t\t\theight: ($header-btn-height + ($header-btn-height * 2) + 1rem); \t\t\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\toverflow: visible;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", ".tab-notification {\r\n\theight: 363px;\r\n\r\n\t.tab-pane {\r\n\t\theight: 100%;\r\n\t}\r\n}\r\n\r\n\r\n.notification {\r\n\tpadding:0;\r\n\tmargin: 0;\r\n\tlist-style: none;\r\n\tposition: relative;\r\n\r\n\tli {\r\n\t\tposition: relative;\r\n\t\tbackground: $white;\r\n\r\n\t\t&.unread {\r\n\t\t\t\r\n\t\t\tbackground: lighten($warning-50, 9%);\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-weight:500;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t> :first-child {\r\n\t\t\tpadding: $p-1+$p-2 $p-4;\r\n\t\t\tborder-bottom: 1px solid rgba($black, 0.06);\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t\tbackground-image: linear-gradient(rgba(29, 33, 41, .03), rgba(29, 33, 41, .04));\r\n\t\t\t}\r\n\t\t\t&:focus {\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t}\r\n\r\n\t\t\t> span {\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t> span {\r\n\t\t\t\t\t/* IE fix */\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&:last-child {\r\n\t\t\t> a {\r\n\t\t\t\tborder:0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.name {\r\n\t\tcolor: lighten($black, 13.5%);\r\n\t\tfont-weight: 400;\r\n\t\tfont-size: rem($fs-base);\r\n\t}\r\n\r\n\t.msg-a,\r\n\t.msg-b {\r\n\t\tcolor: lighten($black, 33.5%);\r\n\t}\r\n\r\n\t&.notification-layout-2 {\r\n\t\tli {\r\n\t\t\tbackground: $gray-50;\r\n\r\n\t\t\t&.unread {\r\n\t\t\t\tbackground: $white;\r\n\t\t\t\t.name {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t> :first-child {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-bottom: 1px solid rgba($black, 0.04);\r\n\t\t\t\tz-index: 1;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\t&:after {\r\n\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop:0;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t//background: #ed1c24;\r\n\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\tbox-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: rem($fs-base + 1px);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover {\r\n\t\t\tcursor: pointer;\r\n\t\t}\r\n\t}\r\n\r\n\t\r\n\t&:not(.notification-loading) {\r\n\r\n\t\t&:before {\r\n\t\t\tcontent: \"No new messages\";\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tz-index: 0;\r\n\t\t\tpadding: 1.5rem;\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t}\r\n\r\n}", ".app-list {\r\n\tmargin:0 auto;\r\n\tdisplay: block;\r\n\twidth: $header-applist-rows-width !important;\r\n\theight: $header-applist-rows-height !important;\r\n\tfont-size:0; //removes wierd spacing\r\n\tpadding: $header-applist-box-padding-x $header-applist-box-padding-y;\r\n\ttext-align: center;\r\n\r\n\t> li {\r\n\t\tdisplay: inline-block;\r\n\t\ttext-align: center;\r\n\t\tpadding:0;\r\n\t}\r\n}\r\n\r\n.app-list-item {\r\n\theight: $header-applist-link-block-height;\r\n\twidth: $header-applist-link-block-width;\r\n\tdisplay: block;\r\n\ttext-decoration: none;\r\n\tcolor:$base-text-color;\r\n\tmargin: 10px 2px;\r\n\tborder: 1px solid transparent !important;\r\n\toutline: none;\r\n\t@include rounded($border-radius - 1);\r\n\tpadding-top: 8px;\r\n\r\n\tborder-radius: 100%;\r\n\t\r\n\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tflex-direction: column;\r\n\r\n\t.icon-stack {\r\n\t\tfont-size: $header-applist-icon-size;\r\n\t\tmargin-top:4px;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\tborder: 1px solid lighten(lighten($black, 75%), 14%);\r\n\t\tpadding-top: 7px;\r\n\r\n\t\t> .icon-stack {\r\n\t\t\tfont-size: $header-applist-icon-size + 0.0625rem;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&:active {\r\n\t\tborder-color: $primary-500;\r\n\t\tpadding-top: 8px;\r\n\r\n\t\t> .icon-stack {\r\n\t\t\tfont-size: $header-applist-icon-size;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.app-list-name {\r\n\ttext-align: center;\r\n\tfont-size: rem($fs-base);\r\n\ttext-overflow: ellipsis;\r\n\tdisplay: block;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n}", ".header-function-fixed {\r\n\r\n\t&:not(.nav-function-top) {\r\n\r\n\t\t.page-header {\r\n\t\t\tleft:0;\r\n\t\t\tposition:fixed !important;\r\n\t\t\tright:0;\r\n\t\t\ttop:0;\r\n\t\t\t\r\n\t\t\t@extend %general-animation;\r\n\t\t}\r\n\r\n\t\t.page-content {\r\n\t\t\tmargin-top: $header-height;\r\n\t\t}\r\n\r\n\t\t&:not(.nav-function-fixed) {\r\n\r\n\t\t\t.page-logo {\r\n\t\t\t\twidth:$nav-width;\r\n\t\t\t\tposition:fixed;\r\n\t\t\t\ttop:0;\r\n\t\t\t\tz-index: $cloud;\r\n\r\n\t\t\t\t//@extend %fixed-header-shadow;\r\n\t\t\t}\r\n\r\n\t\t\t.page-sidebar {\r\n\t\t\t\t.primary-nav {\r\n\t\t\t\t\tmargin-top:$header-height;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/* bug fix for nav hidden other than chrome...*/\r\n\t\t\t&.desktop.nav-function-hidden {\r\n\t\t\t\t.page-logo {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttransition: none !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.header-function-fixed {\r\n\r\n\t\t&:not(.nav-function-top) {\r\n\r\n\t\t\t.page-header {\r\n\t\t\t\tmargin-left: $nav-width;\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-minify {\r\n\t\t\t\t.page-sidebar {\r\n\t\t\t\t\t.page-logo {\r\n\t\t\t\t\t\twidth: $nav-minify-width;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.nav-function-top {\r\n\t\t\t.page-header {\r\n\t\t\t\tposition: fixed !important;\r\n\t\t\t\t/*top: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tleft: 0;*/\r\n\r\n\t\t\t\tbox-shadow: 0px 0px 28px 2px $header-border-bottom-color;\r\n\r\n\t\t\t\t/*chrome flickering solution*/\r\n\t\t\t\t-webkit-transform: translateZ(0);\r\n\t\t\t}\r\n\t\t\t/*.page-wrapper {\r\n\t\t\t\tpadding-top: $header-height-nav-top;\r\n\t\t\t}*/\t\t\t\r\n\t\t}\r\n\t}\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", ".page-sidebar {\r\n\r\n\t@extend %nav-bg;\r\n\r\n\tposition: relative;\r\n\tflex: 1 0 auto;\r\n\twidth: $nav-width;\r\n\tmax-width: $nav-width;\r\n    flex-direction: column;\r\n    display: flex;\r\n\r\n\tz-index: $depth-nav;\r\n\r\n\twill-change: left, right;\r\n}", ".primary-nav { \r\n\toverflow: auto;\r\n\toverflow-x: hidden;\r\n\t-webkit-overflow-scrolling: touch;\r\n\t-webkit-backface-visibility: hidden; \r\n\t\tbackface-visibility: hidden;\r\n\r\n\t.nav-menu:last-of-type {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.nav-menu:first-of-type {\r\n\t\tmargin-top: 1rem;\r\n\t\tmargin-bottom: 1rem;\r\n\t}\t\r\n}\r\n\r\n.nav-title {\r\n\ttext-transform: uppercase;\r\n\tmargin:0;\r\n\tcolor: $nav-title-color;\r\n\tpadding: 1rem $nav-padding-x;\r\n\tmargin-top: 1.5rem;\r\n\tfont-size: rem($nav-title-font-size);\r\n\tletter-spacing: 1px;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.nav-menu {\r\n\tpadding: 0;\r\n\tlist-style: none;\r\n\tmargin: 0;\r\n\r\n\ta,\r\n\ta > [class*='fa-'],\r\n\ta > .#{$cust-icon-prefix} {\r\n\t\t@include transition(0.3s,ease-out);\r\n\t}\r\n\r\n\r\n\tb.collapse-sign {\r\n\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\tcolor: $primary-400;\r\n\r\n\t\t> [class*='fa-'],\r\n\t\t>.#{$cust-icon-prefix} {\r\n\t\t\tfont-size: $nav-collapse-sign-font-size;\r\n    \t}\r\n\t}\r\n\r\n\tul {\r\n\t\tpadding-left: 0;\r\n\t\tlist-style: none;\r\n\t\tdisplay:none;\r\n\t}\r\n\r\n\tli {\r\n\t\tposition: relative;\r\n\r\n\t\t&.open {\r\n\t\t\t> a {\r\n\t\t\t\t@include text-contrast($nav-background);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.active {\r\n\r\n\t\t\t> a {\r\n\t\t\t\t@include text-contrast($nav-background);\r\n\t\t\t\tbackground-color: rgba($white,0.04);\r\n\t\t\t\t@include box-shadow(inset 3px 0 0 $color-primary);\r\n\t\t\t\tfont-weight: 400;\r\n\r\n\t\t\t\t&:hover {\r\n\r\n\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t    color: $nav-icon-hover-color;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t> ul {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t/* arrow that appears next to active/selected items */\r\n\t\t\t&:not(.open) > a:before {\r\n\t\t\t\tcontent: '\\f413';\r\n\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: calc(50% - 5px);\r\n\t\t\t\tright: 11px;\r\n\t\t\t\tfont-size: 7px;\r\n\t\t\t\theight: 10px;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tcolor: #24b3a4;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\ta {\r\n\t\t\tdisplay: flex; /*new*/\r\n\t\t\talign-items:center; /*new*/\r\n\t\t\toutline: 0;\r\n\t\t\tpadding: $nav-padding-y $nav-padding-x;\r\n\t\t\tfont-size: rem($nav-font-link-size);\r\n\t\t\tcolor: $nav-link-color;\r\n\t\t\tfont-weight: 400;\r\n\t\t\ttext-decoration: none;\r\n\r\n\t\t\tposition: relative; /* needed for mod-nav-hiarchiy*/\r\n\r\n\t\t\t.dl-ref {\r\n\t\t\t\tfont-size: $nav-dl-font-size;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmin-width: $nav-dl-width;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tborder-radius: $border-radius;\r\n\t\t\t\tletter-spacing: 0.5px;\r\n\t\t\t\tmargin-left: -$nav-dl-margin-left;\r\n\t\t\t\tmargin-right: $nav-dl-margin-right;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\toverflow:hidden;\r\n\t\t\t\tpadding: 0 4px;\r\n\t\t\t\t-webkit-font-smoothing: subpixel-antialiased;\r\n\r\n\t\t\t\t&.label {\r\n\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: rgba(255,255,255,0.7);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t/*> .badge {\r\n\r\n\t\t\t\t@extend %ping-badge;\r\n\r\n\t\t\t\t& + [class*='fa-'],\r\n\t\t\t\t& + .#{$cust-icon-prefix} {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t}*/\r\n\r\n\t\t\t>[class*='fa-'],\r\n\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\tmargin-right: $nav-icon-margin-right;\r\n\t\t\t\tfont-size: $nav-font-icon-size;\r\n\t\t\t\twidth: $nav-icon-width;\r\n\t\t\t    color:$nav-icon-color;\r\n\t\t\t} \r\n\r\n\t\t\t/*> [class*='fa-'] {\r\n\t\t\t\tfont-size: $nav-font-icon-size - 2;\r\n\t\t\t}*/\r\n\r\n\t\t\t/*> img {\r\n\t\t\t\tbackground: $primary-500;\r\n\t\t\t\tpadding: 0.125rem;\r\n\t\t\t\twidth: 20px;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tmargin-left: 5px;\r\n\t\t\t\tmargin-right: $nav-icon-margin-right + 0.1875rem;\r\n\t\t\t}*/\r\n\r\n\t\t\t> .nav-link-text {\r\n\r\n\t\t\t\tflex: 1; /*new*/\r\n\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\talign-items:center;\r\n\t\t\t\tline-height: normal;\r\n\t\t\t}\r\n\r\n\t\t\t/*> .badge:not(.clear-badge):first-child  {\r\n\t\t\t\tposition: static;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tborder-radius: 5px;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t\twidth: 28px;\r\n\t\t\t\theight: auto;\r\n\t\t\t\tpadding: 4px 0;\r\n\t\t\t\tfont-size: rem($fs-base);\r\n\t\t\t}*/\r\n\r\n\t\t\t&.collapsed {\r\n\t\t\t\t.nav-menu-btn-sub-collapse {\r\n\t\t\t\t\t@include rotate(180);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\t@include text-contrast(rgba(lighten($nav-base-color, 1%), (80/100)))\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t\tbackground-color: rgba($black, 0.1);\r\n\r\n\t\t\t\t.badge {\r\n\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\tcolor:$nav-icon-hover-color;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t> .badge {\r\n\t\t\t\t\tbox-shadow: 0 0 0 1px rgba(lighten($nav-base-color, 1%), (80/100));\r\n\t\t\t\t\tborder: 1px solid rgba(lighten($nav-base-color, 1%), (80/100));\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t}\r\n\r\n\t\t\t&:focus {\r\n\t\t\t\t@include text-contrast( rgba(darken($nav-base-color, 5% ), (50/100)) );\r\n\r\n\t\t\t\t.badge {\r\n\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} \r\n\r\n\t\t// Sub nav level 1\r\n\t\t> ul {\r\n\t\t\tbackground-color: rgba($black,0.1);\r\n\t\t\tpadding-top: 10px;\r\n\t\t\tpadding-bottom: 10px;\r\n\r\n\t\t\tli {\r\n\r\n\t\t\t\ta {\r\n\t\t\t\t\tcolor: darken($nav-link-color, 5%);\r\n\r\n\t\t\t\t\tpadding: $nav-padding-y $nav-padding-x $nav-padding-y $nav-padding-x + $nav-icon-width + $nav-icon-margin-right;\r\n\r\n\t\t\t\t\tb.collapse-sign {\r\n\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\tfont-size: $nav-collapse-sign-font-size - 2; \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\tmargin-left: -2.1875rem;\r\n\t\t\t\t\t\tmargin-right: $nav-dl-margin-right;\r\n\t\t\t\t\t\tcolor: $nav-icon-color;\r\n\t\t\t\t\t\tfont-size: $nav-font-icon-size-sub - 0.25rem;\r\n\t\t\t\t\t\twidth: $nav-dl-width;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t\t\tbackground-color: $nav-badge-bg-color;\r\n\t\t\t\t\t\tborder: 1px solid darken($color-fusion, 0%);\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t@include text-contrast(rgba(($black), (10/100)));\r\n\t\t\t\t\t\tbackground-color: rgba(($black), (10/100));\r\n\r\n\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t    color:$nav-icon-hover-color;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\t\r\n\t\t\t\t\t> a {\r\n\t\t\t\t\t\t@include text-contrast(rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)))\r\n\r\n\t\t\t\t\t\tbackground-color:transparent;\r\n\t\t\t\t\t\tbox-shadow:none;\r\n\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\r\n\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t    @include text-contrast(rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\t\r\n\r\n\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t\t    color:$nav-icon-hover-color;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\t> ul {\r\n\t\t\t\t\t\tpadding-bottom:0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Sub nav level 2\r\n\t\t\t\t> ul {\r\n\r\n\r\n\t\t\t\t\tli {\r\n\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\t@include text-contrast( rgba(lighten(darken($nav-base-color, 11%), 5%), (20/100)) )\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\tcolor: darken($nav-link-color, 7%);\r\n\t\t\t\t\t\t\tpadding: $nav-padding-y $nav-padding-x $nav-padding-y ($nav-padding-x + $nav-icon-width + $nav-icon-margin-right + 0.75rem);\r\n\r\n\t\t\t\t\t\t\t.dl-ref {\r\n\t\t\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t\t\t\tmargin-right: $nav-icon-margin-right / 1.2;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t\t\t\tmargin-right: $nav-icon-margin-right / 1.2;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t@include text-contrast( rgba(lighten(darken($nav-base-color, 11%), 5%), (45/100)) )\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\t\t\tcolor: $nav-badge-color;\r\n\t\t\t\t\t\t\t\tbackground-color: $nav-badge-bg-color;\r\n\t\t\t\t\t\t\t\tborder: 1px solid darken($color-fusion, 0%);\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} \r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom:0;\r\n\t\t}\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom:0;\r\n\t}\r\n} \r\n\r\n/* nav hover elements \r\n.nav-menu-hover {\r\n\r\n\tli > ul {\r\n\t\tbackground-color: rgba(0,0,0,0.17) !important;\r\n\t}\r\n\r\n\tli {\r\n\t\ta {\r\n\t\t\tcolor: rgba(255,255,255,0.90);\r\n\t\t\tspan {\r\n\t\t\t\tcolor: rgba(255,255,255,0.90);\r\n\t\t\t}\r\n\t\t\t\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground:rgba(255,255,255,0.09) !important;\r\n\t\t\t\tcolor: $white !important;\r\n\t\t\t\tspan {\r\n\t\t\t\t\tcolor: $white;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}*/\r\n\r\n/* nav clean elements */\r\n.nav-menu-clean {\r\n\tbackground:$white;\r\n\t\r\n\tul {\r\n\t\tbackground: transparent !important;\r\n\t\tpadding-bottom: 0 !important;\r\n\t}\r\n\r\n\tli {\r\n\t\ta {\r\n\t\t\tbackground: transparent !important;\r\n\t\t\tcolor: $fusion-500 !important;\r\n\t\t\tspan {\r\n\t\t\t\tcolor: $fusion-500 !important;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: #f4f4f4 !important;\r\n\t\t\t}\r\n\t\t}\t\r\n\t}\r\n\r\n\tli a {\r\n\t\tborder-bottom: 1px solid transparent;\r\n\t}\r\n\r\n\tli > ul li > ul > li:not(:last-child) a {\r\n\t\tborder-bottom: none;\r\n\t}\r\n}\r\n\r\n/* nav bordered elements */\r\n.nav-menu-bordered {\r\n\tborder: 1px solid $card-border-color;\r\n\r\n\tli a {\r\n\t\tborder-bottom: 1px solid $card-border-color;\r\n\t}\r\n\r\n\tli > ul li > ul > li:not(:last-child) a {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n}\r\n\r\n/* nav compact elements */\r\n.nav-menu-compact {\r\n\tli a { padding-left: 1.5rem !important; padding-right: 1.5rem !important;}\r\n\tli li a { padding-left: 2rem !important; }\r\n\tli li li a { padding-left: 2.5rem !important; }\r\n}\r\n\r\n.nav-menu.nav-menu-reset {\r\n\tli a:not(:hover),\r\n\t.collapse-sign {\r\n\t\tcolor: rgba(255,255,255,0.7) !important;\r\n\t}\r\n}\r\n\r\n/*body:not(.nav-function-top) {\r\n\r\n\t.primary-nav {\r\n\r\n\t\t.nav-menu {\r\n\r\n\t\t\ta,\r\n\t\t\ta:hover,\r\n\t\t\ta:focus {\r\n\r\n\t\t\t\t.badge-detached {\r\n\t\t\t\t\t\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tfont-family: 'helvetica neue', helvetica, arial, sans-serif;\r\n\t\t\t\t\tfont-size: rem($fs-nano);\r\n\t\t\t\t\tmin-height: 13px;\r\n\t\t\t\t\tmin-width: $nav-badge-height + 4;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\tborder: 1px solid #33383e;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t\tcolor: #33383E;\r\n\t\t\t\t\tpadding: 1px 5px;\r\n\t\t\t\t\tright: 15px;\r\n\t\t\t\t\tleft: auto;\r\n\t\t\t\t\ttop: 13px;\r\n\t\t\t\t\twidth: auto;\r\n\t\t\t\t\tmax-width: 0;\r\n\t\t\t\t\theight: auto;\r\n\r\n\t\t\t\t\t-webkit-font-smoothing: subpixel-antialiased;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}*/\r\n\r\n\r\n@include media-breakpoint-up(xl) {\r\n\t\r\n\t.page-sidebar {\r\n\r\n\t\t.primary-nav {\r\n\r\n\t\t\t.nav-menu {\r\n\r\n\t\t\t\t> li {\r\n\r\n\t\t\t\t\t> a {\r\n\t\t\t\t\t\tfont-size: rem($nav-font-link-size);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", ".nav-filter {\r\n\t//background: lighten($nav-background, 5%);\r\n\tmargin: 0;\r\n\topacity: 0;\r\n\tvisibility: hidden;\r\n\toverflow: hidden;\r\n    height: 0px;\r\n    position: relative;\r\n\ttransform: scale(0.3);\r\n\ttransition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\r\n\tinput[type=\"text\"] {\r\n\t\tpadding: 8px 40px 8px 14px; \r\n\t\twidth: $nav-width - 2.25rem;\r\n\t\tbackground: rgba($black, 0.4); \r\n\t\tcolor: $white;\r\n\r\n\t\t&:not(:focus) {\r\n\t\t\tborder-color: rgba(0, 0, 0, 0.1);\r\n\t\t}\r\n\r\n\t\t&:focus {\r\n\t\t\tborder-color: lighten($nav-background, 13%);\r\n\t\t}\r\n\t\t\r\n\t}\r\n}\r\n\r\n// when the dropdown button is clicked for the filter input\r\n.list-filter-active {\r\n\r\n\t//hides info card temporarily\r\n\t.info-card {\r\n\t\theight: calc(#{$nav-infocard-height} - #{$nav-infocard-height});\r\n\t\ttransition: all 400ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\t}\r\n\r\n\t//nav-filter visibility\r\n\t.nav-filter {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t\theight: 60px;\r\n\t\t//border-bottom: 1px solid;\r\n\t\tbox-shadow: 0px 0px 28px 0px rgba(0, 0, 0, 0.13);\r\n\t\ttransform: scale(1);\r\n\t}\r\n\r\n\t//hides all nav title\r\n\t.nav-title {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.nav-menu {\r\n\t\tmargin:0;\r\n\r\n\t\tli > ul {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n\r\n\t/* these classes are triggered by JS */\r\n\t.js-filter-hide {\r\n\t\tdisplay:none;\r\n\t}\r\n\r\n\t.js-filter-show {\r\n\t\tdisplay:block;\r\n\t}\r\n\r\n}\r\n\r\n/* only show filter message if lister filter is active */\r\n.page-sidebar:not(.list-filter-active) {\r\n\t.filter-message {\r\n\t\tdisplay: none;\r\n\t}\t\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n\t.nav-function-top,\r\n\t.nav-function-minify {\r\n\t\t.page-sidebar {\r\n\t\t\t/*.js-filter-hide,\r\n\t\t\t.js-filter-show {\r\n\t\t\t\tdisplay:block;\r\n\t\t\t}*/\r\n\t\t\t.filter-message,\r\n\t\t\t.nav-filter  {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n}", ".info-card {\r\n\tposition: relative;\r\n\twidth: $nav-width;\r\n\theight: $nav-infocard-height;\r\n\tcolor:$white;\r\n\toverflow: hidden;\r\n\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n\r\n    padding: 0 $nav-padding-x;\r\n\r\n\ttransition: all 700ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\r\n\timg.cover {\r\n\t\topacity: 0.5;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tposition: absolute;\r\n\t\t@extend %bg-img-cover;\r\n\t\t@extend %common-animation-opacity;\r\n\r\n\t\t//height: 100%; //it was pixelating\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.profile-image {\r\n\t\twidth: $profile-image-width;\r\n\t\theight: auto;\r\n\t\tdisplay: inline-block;\r\n\t\tz-index: 2;\r\n\t\tposition: relative;\r\n\t}\t\r\n\r\n\t.info-card-text {\r\n\t\tmargin-left: 1rem;\r\n\t\tcolor: inherit;\r\n\t\ttext-shadow: $black 0 1px;\r\n\t\tz-index: 1;\r\n\t\tposition: relative;\r\n\t\tline-height: normal;\r\n\r\n\t\t> span {\r\n\t\t\tfont-weight: 300;\r\n\t\t}\r\n\t}\r\n\t\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\t.info-card {\r\n\r\n\t\t&:hover {\r\n\t\t\t@include transition(0.10s,ease-in-out); \r\n\t\t\twill-change: opacity;\r\n\t\t}\r\n\r\n\t\t&:hover {\r\n\t\t\timg.cover {\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t\t@include backface-visibility;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.info-card-text {\r\n\tfont-size: rem($fs-md);\r\n\tdisplay: inline-block;\r\n\tvertical-align: middle;\r\n\tfont-weight: 500;\r\n\tline-height: 1.35;\r\n\r\n\t> span {\r\n\t\tfont-size: rem($fs-md);\r\n\t\tdisplay: block;\r\n\t\tfont-weight: 300;\r\n\t}\r\n}\r\n", "$nav-function-top-item-padding: 8px;\r\n$nav-function-top-logo-width-sm: 28px;\r\n$nav-function-top-logo-width-lg: 28px;\r\n$nav-function-top-menu-item-bg: $primary-500;\r\n\r\n.nav-padel-left,\r\n.nav-padel-right {\r\n\tdisplay: none;\r\n}\r\n\r\n@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-top {\r\n\r\n\t\t/* digitally created elements */\r\n\t\t.nav-menu-wrapper {\r\n\t\t\tflex: 0 1 100%;\r\n\t\t}\r\n\r\n\t\t/* hide elements when nav-function-top */\r\n\t\t.hidden-nav-function-top {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\r\n\r\n\t\t/* correct search field color */\r\n\t\t#search-field {\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\r\n\t\t&:not(.header-function-fixed) {\r\n\t\t\t\t\r\n\t\t\t#nff {\r\n\t\t\t\tposition:relative;\r\n\r\n\t\t\t\t.onoffswitch-title {\r\n\t\t\t\t\tcolor: $settings-incompat-title;\r\n\t\t\t\t}\r\n\t\t\t\t.onoffswitch-title-desc {\r\n\t\t\t\t\tcolor: $settings-incompat-desc;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: \"DISABLED\";\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbackground: $settings-incompat-bg;\r\n\t\t\t\t\tfont-size: 10px;\r\n\t\t\t\t\twidth: 65px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tborder: 1px solid $settings-incompat-border;\r\n\t\t\t\t\theight: 22px;\r\n\t\t\t\t\tline-height: 20px;\r\n\t\t\t\t\tborder-radius: $border-radius-plus;\r\n\t\t\t\t\tright: 13px;\r\n\t\t\t\t\ttop: 26%;\r\n\t\t\t\t\tcolor:$fusion-900;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.page-header {\r\n\t\t\tmargin-top: 0;\r\n\t\t\theight: $header-height-nav-top;\r\n\r\n\t\t\tbackground-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\t\t\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\t\t\tbackground-color: $nav-background; \r\n\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\tleft: 0;\r\n\r\n\t\t\tbox-shadow: 0px 0px 14px 0px $header-border-bottom-color;\r\n\r\n\t\t\t.dropdown-icon-menu {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\r\n\t\t\t#search-field {\r\n\t\t\t\tmargin:0 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.page-logo {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: auto;\r\n    \t\t\twidth: initial; //for IE, cause you know, they suck...\r\n\t\t\t\tpadding-left: 0;\r\n\t\t\t\tbackground: transparent;\r\n\t\t\t\tbox-shadow: none;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.header-icon:not(.btn) > [class*='fa-']:first-child, \r\n\t\t\t.header-icon:not(.btn) > .ni:first-child {\r\n\t\t\t\tcolor:  lighten($header-link-color, 10%);\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tcolor:  lighten($header-link-color, 20%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.badge.badge-icon {\r\n\t\t\t\tbox-shadow: 0 0 0 1px $primary-600;\r\n\t\t\t}\r\n\r\n\t\t\t.header-icon:not(.btn)[data-toggle=\"dropdown\"] + .dropdown-menu {\r\n\t\t\t\ttop: $header-height-nav-top !important;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t.page-content-wrapper {\r\n\t\t\tmargin-top: $nav-top-height + $header-height-nav-top;\r\n\t\t}\r\n\r\n\t\t.page-wrapper {\r\n\t\t\tpadding-left:0;\r\n\r\n\t\t\t.page-footer {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-sidebar {\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 100%;\r\n\t\t\tmax-width: 100%;\r\n\t\t\theight: $nav-top-height;\r\n\t\t\tz-index: $depth-header - 1;\r\n\t\t\tpadding: 0 0.625rem;\r\n\t\t\tbackground: $white;\r\n\r\n\t\t\tbox-shadow: 0px 0px 14px 0px $header-border-bottom-color;\r\n\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $header-height-nav-top;\r\n\r\n\t\t\torder: 2;\t\t\r\n\r\n\t\t\t.page-logo,\r\n\t\t\t.nav-filter,\r\n\t\t\t.info-card,\r\n\t\t\t.nav-title {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\r\n\t\t\t.primary-nav {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: stretch; \r\n\t\t\t\tfont-size: 0;\r\n\r\n\t\t\t\t/* Make an auto-hiding scroller for the 3 people using a IE */\r\n\t\t\t\t-ms-overflow-style: -ms-autohiding-scrollbar;\r\n\t\t\t\t/* Remove the default scrollbar for WebKit implementations */\r\n\r\n\r\n\t\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.nav-menu {\r\n\t\t\t\t\tmargin:0;\r\n\t\t\t\t\tmargin-left: 2.90rem; /* this will get overriden with JS script, but we add it here as a counter weight for the flickering effect */\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\talign-items: stretch;\r\n\t\t\t\t\tflex: 0 1 100%;\r\n\r\n\t\t\t\t\ttransition: margin 0.5s ease-out 0s;\r\n\r\n\r\n\t\t\t\t\t> li {\r\n\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n    \t\t\t\t\tposition: static; //beacuse initial don't work in fking IE\r\n\r\n\t\t\t\t\t\t&.nav-title {\r\n\t\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\tbox-shadow: none;\r\n\r\n\t\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\t\tcontent: '\\f413';\r\n\t\t\t\t\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\ttop: calc(50% + 15px);\r\n\t\t\t\t\t\t\t\t\tright: calc(50% - 5px);\r\n\t\t\t\t\t\t\t\t\tfont-size: 7px;\r\n\t\t\t\t\t\t\t\t\theight: 10px;\r\n\t\t\t\t\t\t\t\t\twidth: auto;\r\n\t\t\t\t\t\t\t\t\tcolor: #24b3a4;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\r\n\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\tpadding: .75rem 1.5rem .75rem 1rem;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix},\r\n\t\t\t\t\t\t\t>[class*='fa-'] {\r\n\t\t\t\t\t\t\t\twidth: inherit;\r\n\t\t\t\t\t\t\t\tmargin: 0 ;\r\n\t\t\t\t\t\t\t\tmargin-right: .5rem !important;\r\n\t\t\t\t\t\t\t\tdisplay: flex !important;\t\t\t\t\t\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\tjustify-content: left;\r\n\t\t\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\t\tline-height: normal;\r\n\t\t\t\t\t\t\t\tvertical-align: text-top;\r\n\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\r\n\t\t\t\t\t\t\t\tdisplay: inline-block; /*override inline-flex*/\r\n\r\n\t\t\t\t\t\t\t\t// this is needed\r\n\t\t\t\t\t\t\t\tflex: 0 1 auto;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t>.badge,\r\n\t\t\t\t\t\t\t>.badge.clear-badge {\r\n\t\t\t\t\t\t\t\tleft: 53%;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t>.collapse-sign {\r\n\t\t\t\t\t\t\t\tmargin-left: 0.5rem;\r\n\t\t\t\t\t\t\t\tcolor: lighten($nav-background, 30%);\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n    \t\t\t\t\t\t\tright: 0.5rem;\r\n\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t\tbottom: 0;\r\n\r\n\t\t\t\t\t\t\t\t//force down arrow on all parent\r\n\t\t\t\t\t\t\t\t> em {\r\n\t\t\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\t\t\tcontent: \"\\f107\";\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .badge:first-child {\r\n\t\t\t\t\t\t\t\tmax-width: none;\r\n\t\t\t\t\t\t\t\twidth: 25px !important;\r\n\t\t\t\t\t\t\t\theight: 25px !important;\r\n\t\t\t\t\t\t\t\tline-height: 16px !important;\r\n\t\t\t\t\t\t\t\tfont-size: rem($fs-base) !important;\r\n\t\t\t\t\t\t\t\tdisplay: block !important;\r\n\t\t\t\t\t\t\t\tmargin: 0 auto 4px !important;\r\n\r\n\t\t\t\t\t\t\t\t// unfortunately we do need '!important' here :(\r\n\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t}\t\r\n\r\n\t\t\t\t\t\t// all children\r\n\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\tfont-size: .9rem;\r\n\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t> ul {\r\n\t\t\t\t\t\t\tdisplay: none !important; //force invisibility to override plugin clicks\r\n\t\t\t\t\t\t\twidth: $nav-top-drowndown-width;\r\n\t\t\t\t\t\t\theight: auto !important; //counters the click issue\r\n\t\t\t\t\t\t\ttop: $nav-top-height;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tbackground: $nav-top-drowndown-background;\r\n\t\t\t\t\t\t\tborder-radius: $border-radius-plus;\r\n\t\t\t\t\t\t\tbox-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15);\r\n\t\t\t\t\t\t\tpadding: 1rem 0;\r\n\t\t\t\t\t\t\tmargin-top:1rem;\r\n\r\n\t\t\t\t\t\t\tli {\r\n\t\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\t\t\tpadding: 0.65rem 1.25rem;\r\n\t\t\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-color;\r\n\t\t\t\t\t\t\t\t\tmax-height: none;\r\n\t\t\t\t\t\t\t\t\tbox-shadow: none;\r\n\r\n\t\t\t\t\t\t\t\t\t.nav-link-text {\r\n\t\t\t\t\t\t\t\t\t\tdisplay: block;\r\n\r\n\t\t\t\t\t\t\t\t\t\t> .dl-ref {\r\n\t\t\t\t\t\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t\t\t\t\t\t\tmargin-right: 5px;\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t> .#{$cust-icon-prefix}\r\n\t\t\t\t\t\t\t\t\t\t> [class*='fa-'] {\r\n\t\t\t\t\t\t\t\t\t\t\tmargin-left: 6px;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tul {\r\n\t\t\t\t\t\t\t\t\tbackground: $nav-top-drowndown-background;\r\n\t\t\t\t\t\t\t\t\tpadding: 0;\r\n\r\n\t\t\t\t\t\t\t\t\tli {\r\n\t\t\t\t\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\t\t\t\t\tpadding-left: 2rem;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\t\t\tbackground: $nav-top-drowndown-hover;\r\n\t\t\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-hover-color;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t\twidth: calc(100% + 100px);\r\n\t\t\t\t\t\t\t\theight: calc(100% + 120px);\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\t\t\tleft: -50px;\r\n\t\t\t\t\t\t\t\ttop: -1rem;\r\n\t\t\t\t\t\t\t\tbackground: transparent;\r\n\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\tcontent: \"\\f1c8\";\r\n\t\t\t\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\tfont-size: 5rem;\r\n\t\t\t\t\t\t\t\tcolor: $nav-top-drowndown-background;\r\n\t\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t\ttop: -1.7rem;\r\n\t\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t//first child hover\r\n\t\t\t\t\t\t&:hover {\r\n\r\n\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\tcolor: $primary-500;\r\n\t\t\t\t\t\t\t\tbackground: transparent;\r\n\r\n\t\t\t\t\t\t\t\t& + ul {\r\n\r\n\t\t\t\t\t\t\t\t\tdisplay:block !important;\r\n\t\t\t\t\t\t\t\t\tanimation: animateFadeInUp 0.5s;\r\n  \t\t\t\t\t\t\t\t\t-webkit-animation: animateFadeInUp 0.5s;\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.nav-footer {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t&.nav-function-minify {\r\n\r\n\t\t\t.page-sidebar {\r\n\r\n\t\t\t\t.primary-nav {\r\n\t\t\t\t\t \r\n\t\t\t\t\t.nav-menu {\r\n\r\n\t\t\t\t\t\t> li{\r\n\r\n\t\t\t\t\t\t\t> a {\r\n\r\n\t\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\t\tdisplay: none;\r\n\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\t\t\t\tleft:24px;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t>.#{$cust-icon-prefix},\r\n\t\t\t\t\t\t\t\t>[class*='fa-'] {\r\n\t\t\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t/* reorder */\r\n\t\t.page-header {\r\n\t\t\torder: 1;\r\n\t\t}\r\n\r\n\t\t.page-wrapper {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.page-sidebar {\r\n\t\t\torder: 2;\r\n\t\t}\r\n\r\n\t\t.page-content {\r\n\t\t\torder: 3;\r\n\t\t\talign-items: stretch;\r\n\t\t\tflex: 1 1 auto;\r\n\t\t}\r\n \r\n\t}\r\n\t\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n\t.nav-function-top {\r\n\t\t\r\n\t\t.page-sidebar {\r\n\r\n\t\t\t.primary-nav {\r\n\r\n\t\t\t\t.nav-menu {\r\n\r\n\t\t\t\t\t> li {\r\n\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t> .badge:first-child {\r\n\t\t\t\t\t\t\t\tmax-width: none;\r\n\t\t\t\t\t\t\t\twidth: 27px !important;\r\n\t\t\t\t\t\t\t\theight: 27px !important;\r\n\t\t\t\t\t\t\t\tline-height: 18px !important;\r\n\t\t\t\t\t\t\t\tmargin: 0 auto 2px !important;\r\n\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix},\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t> img {\r\n\t\t\t\t\t\t\t\tfont-size: 22px;\r\n\t\t\t\t\t\t\t\theight: 22px;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t/*li {\r\n\t\t\t\t\t\tul {\r\n\t\t\t\t\t\t\t li {\r\n\t\t\t\t\t\t\t \ta {\r\n\t\t\t\t\t\t\t \t\tfont-size: rem($fs-base);\r\n\t\t\t\t\t\t\t \t}\r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}*/\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\t\r\n\r\n\t\t}\r\n\t}\r\n}", "@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-hidden:not(.nav-function-top) {\r\n\t\t.page-wrapper {\r\n\t\t\tpadding-left: $nav-hidden-visiblity;\r\n\t\t}\r\n\t\t.page-sidebar {\r\n\t\t\tleft: $nav-hidden-visiblity - $nav-width;\r\n\t\t\tz-index: $depth-header + 1;\r\n\t\t\ttransition: $nav-hide-animate;\r\n\r\n\t\t\tposition: absolute;\r\n\r\n\t\t\ttop:0;\r\n\t\t\tbottom:0;\r\n\r\n\t\t\twill-change: left, right;\r\n\r\n\t\t\t/* apply invisible hit area to reveal nav */\r\n\t\t\t&:after {\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tbackground: transparent;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbottom:0;\r\n\t\t\t\tleft: $nav-width;\r\n\t\t\t\twidth: $grid-gutter-width-base * 0.7;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttransition: 450ms cubic-bezier(0.90, 0.01, 0.09, 1);\r\n\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\tz-index: -1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.page-header {\r\n\t\t\tmargin-left: 0;\r\n\r\n\t\t\t/* active button state for \"nav-function-hidden\" */\r\n\t\t\t[data-class=\"nav-function-hidden\"] {\r\n\t\t\t\tbackground: $header-btn-active-bg;\r\n\t\t\t\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t\t\t\t@include box-shadow(inset 0 0 3px 1px rgba(0,0,0,.37));\r\n\t\t\t\tcolor:$header-btn-active-color !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.nav-function-fixed {\r\n\t\t\t.page-sidebar {\r\n\t\t\t\t/* apply invisible hit area to reveal nav */\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tleft: $nav-hidden-visiblity;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\r\n\t.nav-function-hidden.header-function-fixed:not(.nav-function-top) {\r\n\t\t.page-header {\r\n\t\t\tmargin-left: $nav-hidden-visiblity; \r\n\t\t}\r\n\t}\r\n\t\r\n}", ".nav-function-fixed:not(.nav-function-top)  {\r\n\t.page-sidebar{\r\n\t\tposition: fixed !important;\r\n\t\ttop:0;\r\n\t\tbottom:0;\r\n\r\n\t\t.primary-nav {\r\n\t\t\toverflow: auto;\r\n\t\t\toverflow-x: hidden;\r\n\t\t\t-webkit-overflow-scrolling: touch;\r\n\t\t\theight: unquote(\"calc(100% - #{$header-height + $footer-height})\");\r\n\t\t}\r\n\r\n\t\t.slimScrollDiv {\r\n\r\n\t\t\theight: unquote(\"calc(100% - #{$header-height + $footer-height})\") !important;\r\n\r\n\t\t\t.primary-nav {\r\n\t\t\t\tpadding-bottom: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\t\r\n\t.page-header {\r\n\t\t[data-class=\"nav-function-fixed\"] {\r\n\t\t\t@extend %header-btn-active;\r\n\t\t}\r\n\t}\r\n\r\n}\t\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n\t.nav-function-fixed {\r\n\r\n\t\t/*\r\n\t\t * top navigation fixed for larger screens with nav on LEFT\r\n\t\t */\r\n\t\t&:not(.nav-function-top){\r\n\t\t\t&.mod-main-boxed {\r\n\t\t\t\t.page-sidebar{\r\n\t\t\t\t\tposition: fixed !important;\r\n\t\t\t\t}\t\r\n\t\t\t}\r\n\r\n\t\t\t&:not(.nav-function-hidden):not(.nav-function-minify) .page-content-wrapper {\r\n\t\t\t\tpadding-left: $nav-width;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/*\r\n\t\t * top navigation fixed for larger screens with nav on TOP\r\n\t\t */\r\n\t\t&.nav-function-top {\r\n\t\t\t&.header-function-fixed {\r\n\r\n\t\t\t\t.page-sidebar {\r\n\t\t\t\t\tposition: fixed !important;\r\n\r\n\t\t\t\t\tbox-shadow: 0px 0px 28px 2px $header-border-bottom-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t/*\r\n\t\t * center for left nav fixed with boxed layout \r\n\t\t */\r\n\t\t&.nav-function-top {\r\n\t\t\t&.mod-main-boxed {\r\n\t\t\t\t.page-sidebar {\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tmargin-right: auto;\r\n\t\t\t\t\tmargin-left: auto;\r\n\t\t\t\t\tmax-width: $mod-main-boxed-width - 2px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\t.nav-function-fixed {\r\n\r\n\t\t/*\r\n\t\t * top navigation fixed for extra large screens with nav on LEFT\r\n\t\t */\r\n\t\t&:not(.nav-function-top){\r\n\t\t\t&.mod-main-boxed {\r\n\t\t\t\t.page-sidebar{\r\n\t\t\t\t\tposition: absolute !important;\r\n\t\t\t\t}\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-minify:not(.nav-function-top) {\r\n\r\n\t\t/* hide elements when nav-function-minify */\r\n\r\n\t\t.hidden-nav-function-minify {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\r\n\r\n\t\t.page-sidebar {\r\n\t\t\t\r\n\t\t\twidth: $nav-minify-width;\r\n\t\t\tz-index: $depth-header + 1;\r\n\r\n\t\t\twill-change: width;\r\n\t\t\t\r\n\t\t\ttransition: $nav-hide-animate;\r\n\r\n\t\t\t.page-logo {\r\n\r\n\t\t\t\twidth:$nav-minify-width;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.page-logo-link {\r\n\t\t\t\t\tflex: none;\r\n\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t.page-logo-text {\r\n\t\t\t\t\tdisplay: none;\r\n\r\n\t\t\t\t\t& + * {\r\n\t\t\t\t\t\tdisplay: none !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.info-card {\r\n\t\t\t\theight: $nav-width/3 + 0.3125rem;\r\n\t\t\t\twidth:100%;\r\n\t\t\t\tpadding: $header-height/3.4 0;\r\n\t\t\t\ttext-align:center;\r\n\t\t\t\toverflow: hidden; \r\n\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.profile-image + div {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t    top: 0;\r\n\t\t\t\t    width: $nav-width/1.2;\r\n\t\t\t\t    text-align: left; \r\n\t\t\t\t    display: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.primary-nav {\r\n\t\t\t\toverflow: hidden; \r\n\r\n\t\t\t\t.nav-title {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\t\t\t\t.nav-menu {\r\n\t\t\t\t\tmargin:0;\r\n\r\n\t\t\t\t\tli {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.active {\r\n\t\t\t\t\t\t\t&.open > a:before {\r\n\t\t\t\t\t\t\t\tcontent: '\\f413';\r\n\t\t\t\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\ttop: calc(50% - 5px);\r\n\t\t\t\t\t\t\t\tright: 11px;\r\n\t\t\t\t\t\t\t\tfont-size: 7px;\r\n\t\t\t\t\t\t\t\theight: 10px;\r\n\t\t\t\t\t\t\t\twidth: auto;\r\n\t\t\t\t\t\t\t\tcolor: #24b3a4;\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-content: center;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\tul {\r\n\t\t\t\t\t\t\t/*.dl-ref {\r\n\t\t\t\t\t\t\t\tdisplay:none !important;\r\n\t\t\t\t\t\t\t}*/\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// first level\r\n\t\t\t\t\t> li {\r\n\r\n\t\t\t\t\t\t> a {\r\n\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tpadding-left: 0;\r\n\t\t\t\t\t\t\tpadding-right: 0;\r\n\r\n\t\t\t\t\t\t\t>[class*='fa-'],\r\n\t\t\t\t\t\t\t>.#{$cust-icon-prefix} {\t\r\n\t\t\t\t\t\t\t\tfont-size: $nav-font-icon-size * 1.1;\r\n\t\t\t\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\t\t\tleft: $nav-minify-width / 2;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t&:not(.livicon) > .badge:not(.clear-badge):first-child  {\r\n\t\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> .nav-link-text {\r\n\t\t\t\t\t\t\t\tdisplay: none;\t\r\n\t\t\t\t\t\t\t\tposition:absolute;\r\n\t\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t\t\tbackground: trasparent;\r\n\t\t\t\t\t\t\t\tpadding-left: $nav-minify-sub-width / 10;\r\n\t\t\t\t\t\t\t\tcolor: $white;\r\n\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t    left: $nav-minify-width + 1rem;\r\n\t\t\t\t\t\t\t    height: 100%;\r\n\t\t\t\t\t\t\t    width: $nav-minify-sub-width;\r\n\t\t\t\t\t\t\t    font-weight: 500;\r\n\t\t\t\t\t\t\t    margin-top: -1.563rem;\r\n\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t> b.collapse-sign {\r\n\t\t\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// sub 1\r\n\t\t\t\t\t\t\t& + ul {\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\twidth: $nav-minify-sub-width;\r\n\t\t\t\t\t\t\t\tleft: $nav-minify-width + 1rem;\r\n\t\t\t\t\t\t\t\tbackground-color: $nav-background;\r\n\r\n\t\t\t\t\t\t\t\tmargin-top: -5rem;\r\n\t\t\t\t\t\t\t\tpadding-top: 3.75rem;\r\n\r\n\t\t\t\t\t\t\t\tborder-radius: 4px .5rem .5rem .5rem;\r\n\r\n\t\t\t\t\t\t\t\tpadding-bottom: 1rem;\r\n\r\n\t\t\t\t\t\t\t\t//arrow\r\n\t\t\t\t\t\t\t    &:before {\r\n\t\t\t\t\t\t\t\t\tcontent: \"\\f1c8\";\r\n\t\t\t\t\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\tfont-size: 3.5rem;\r\n\t\t\t\t\t\t\t\t\tleft: -0.4125rem;\r\n\t\t\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\t\t\t\ttransform: rotate(270deg);\r\n\t\t\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t\t\ttop: 1rem;\r\n\t\t\t\t\t\t\t    }\r\n\r\n\t\t\t\t\t\t\t\t> li {\r\n\r\n\t\t\t\t\t\t\t\t\t> a {\r\n\r\n\t\t\t\t\t\t\t\t\t\tpadding-left: $nav-minify-sub-width / 10;\r\n\t\t\t\t\t\t\t\t\t\tpadding-top: 0.6rem;\r\n\t\t\t\t\t\t\t\t\t\tpadding-bottom: 0.6rem;\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t\t> b.collapse-sign > [class*='fa-'],\r\n\t\t\t\t\t\t\t\t\t\t> b.collapse-sign > .#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: inline-block !important;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t//sub 2\r\n\t\t\t\t\t\t\t\t\t\t& + ul {\r\n\t\t\t\t\t\t\t\t\t\t\t> li {\r\n\t\t\t\t\t\t\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpadding-left: $nav-minify-sub-width / 8;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpadding-top: 0.6rem;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tpadding-bottom: 0.6rem;\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t.nav-link-text {\r\n\t\t\t\t\t\t\t\t\t\t> [class*='fa-'],\r\n\t\t\t\t\t\t\t\t\t\t> .#{$cust-icon-prefix} {\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay:none;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t& > ul {\r\n\t\t\t\t\t\t\tdisplay: none !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\toverflow: visible;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.nav-menu > li:hover {\r\n\r\n\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\tbackground: $nav-minify-hover-bg;\r\n\t\t\t\t\t\t\tcolor: $white;\r\n\t\t\t\t\t\t\toverflow: visible;\r\n\r\n\t\t\t\t\t\t\tz-index: 10;\r\n\r\n\t\t\t\t\t\t\t>.nav-link-text {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\toverflow:hidden;\r\n\r\n\t\t\t\t\t\t\t\t animation: animateFadeInLeft 0.5s;\r\n  \t\t\t\t\t\t\t\t-webkit-animation: animateFadeInLeft 0.5s;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t>.nav-link-text:last-child {\r\n\t\t\t\t\t\t\t\t\ttop: 26px;\r\n\t\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\t\tbackground: $nav-background;\r\n\t\t\t\t\t\t\t\t\toverflow: visible;\r\n\t\t\t\t\t\t\t\t\tborder-radius: 4px 10px 10px 4px;\r\n\r\n\t\t\t\t\t\t\t\t&:before {\r\n\t\t\t\t\t\t\t\t\t    content: \"\\f1c8\";\r\n\t\t\t\t\t\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\t\tfont-size: 3.5rem;\r\n\t\t\t\t\t\t\t\t\t\tleft: -7px;\r\n\t\t\t\t\t\t\t\t\t\tcolor: $nav-background;\r\n\t\t\t\t\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\t\t\t\t\ttransform: rotate(270deg);\r\n\t\t\t\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t\t\t\ttop: -9px;\r\n\t\t\t\t\t\t\t    }\r\n\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t> ul {\r\n\t\t\t\t\t\t\tdisplay: block !important;\r\n\t\t\t\t\t\t\tz-index: 1;\r\n\r\n\t\t\t\t\t\t\tanimation: animateFadeInLeft 0.5s;\r\n  \t\t\t\t\t\t\t-webkit-animation: animateFadeInLeft 0.5s;\r\n\r\n  \t\t\t\t\t\t\tbox-shadow: 0px 0px 40px 0px rgba(82, 63, 105, 0.15);\r\n\r\n  \t\t\t\t\t\t\t// increase hit area\r\n  \t\t\t\t\t\t\t// decreases user error if mouse goes out of menu\r\n\t\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\theight: calc(100% + 180px);\r\n\t\t\t\t\t\t\t\twidth: calc(100% + 80px);\r\n\t\t\t\t\t\t\t\ttop: -$header-height;\r\n\t\t\t\t\t\t\t\tz-index: -1;\r\n\t\t\t\t\t\t\t\tleft:-1rem;\r\n\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} \r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.page-header {\r\n\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\tbackground: $header-btn-active-bg;\r\n\t\t\t\tborder-color: darken($header-btn-active-bg, 10%) !important;\r\n\t\t\t\t@include box-shadow(inset 0 0 3px 1px rgba(0,0,0,.37));\r\n\t\t\t\tcolor:$header-btn-active-color !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.nav-function-hidden {\r\n\t\t\t.page-wrapper {\r\n\t\t\t\tpadding-left: $nav-hidden-visiblity;\r\n\t\t\t}\r\n\t\t\t.page-sidebar {\r\n\t\t\t\tleft: $nav-hidden-visiblity - $nav-minify-width;\r\n\t\t\t\toverflow: visible;\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.page-header {\r\n\t\t\t\tmargin-left: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.nav-function-fixed:not(.nav-function-hidden) {\r\n\t\t\t.page-content-wrapper {\r\n\t\t\t\tpadding-left: $nav-minify-width;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.header-function-fixed {\r\n\t\t\t.page-header {\r\n\t\t\t\tmargin-left: $nav-minify-width;\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-hidden {\r\n\t\t\t\t.page-header {\r\n\t\t\t\t\tmargin-left: $nav-hidden-visiblity;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-fixed:not(.nav-function-hidden) {\r\n\t\t\t\t.page-content-wrapper {\r\n\t\t\t\t\tpadding-left:  $nav-minify-width;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.mod-main-boxed {\r\n\t\t\t\t\t.page-content-wrapper {\r\n\t\t\t\t\t\tpadding-left:  0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\t\r\n\r\n\t}\r\n\r\n}\r\n", ".nav-footer {\r\n\t//position: absolute;\r\n\t/*background-image: -webkit-linear-gradient(270deg, $nav-background-shade, transparent);\r\n\tbackground-image: linear-gradient(270deg, $nav-background-shade, transparent); \r\n\tbackground-color: $nav-background;*/\r\n\t\r\n\t@extend %nav-bg;\r\n\r\n    //width: 100%;\r\n    height: $footer-height;\r\n    bottom: 0;\r\n    display: flex;\r\n\r\n    @include transition(0.3s,ease-in-out);\r\n\r\n\t.nav-footer-buttons {\r\n\r\n\t\t> li {\r\n\t\t\t> a {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tcolor:lighten($nav-background, 25%);\r\n\t\t\t\theight:$footer-height - 1;\r\n\t\t\t\tline-height: $footer-height - 1;\r\n\t\t\t\tmargin-top: 1px;\r\n\t\t\t\tpadding: 0 13px;\r\n\t\t\t\toverflow: visible;\r\n\t\t\t\tfont-size: rem($fs-xl);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t[data-class=\"nav-function-minify\"] {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n.nav-function-fixed {\r\n\r\n\t.nav-footer {\r\n\r\n\t\tbackground: $nav-background;\r\n\t\tborder:0;\r\n\r\n\t\t&:before {\r\n\t\t\tcontent:' ';\r\n\t\t\theight: 1px;\r\n\t\t\tposition:inherit;\r\n\t\t\twidth:inherit;\r\n\t\t\tbackground: rgba($nav-title-border-bottom-color, 0.2);\r\n\t\t\tbackground: -moz-linear-gradient(left, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t\tbackground: -webkit-linear-gradient(left, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t\tbackground: linear-gradient(to right, $nav-background 0%, lighten($nav-background, 15%) 50%, lighten($nav-background, 15%) 50%, $nav-background 100%);\r\n\t\t\topacity: 0.5;\r\n\t\t}\r\n\r\n\t    &:after {\r\n\t\t\topacity: 0.1;\t\t    \t\r\n\t    }\r\n\r\n\t}\r\n\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n\t.nav-function-minify {\r\n\r\n\t\t.nav-footer {\r\n\r\n\t\t\tbackground-color: darken($nav-background, 2%);\r\n\r\n\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth:100%;\r\n\t\t\t\theight:$footer-height;\r\n\t\t\t\tline-height: $footer-height;\r\n\t\t\t\tfont-size: rem($nav-font-link-size+5);\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tcolor: $nav-icon-color;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t@include transition(0.3s,ease-in-out);\r\n\r\n\t\t\t\t> :first-child {\r\n\t\t\t\t\tmargin-right: -4px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t> :only-child {\r\n\t\t\t\t\tmargin:0;\r\n\t\t\t\t}\r\n\t\t\r\n\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: lighten($nav-background, 3%);\r\n\r\n\t\t\t\t[data-class=\"nav-function-minify\"] {\r\n\t\t\t\t\tcolor: $nav-icon-hover-color;\r\n\t\t\t\t\tmargin-left:7px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav-footer-buttons {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", ".page-wrapper {\r\n\t//overflow: hidden; //disabled because the menu items was not showing \r\n\tposition: relative;\r\n}\r\n\r\n.page-inner {\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.page-wrapper, .page-inner {\r\n    display: flex;\r\n    align-items: stretch;\r\n    flex: 1 1 auto;\r\n    width: 100%;\r\n}\r\n\r\n.page-content-wrapper {\r\n\tbackground-color: $page-bg;\r\n    display: flex;\r\n    align-items: stretch;\r\n    flex: 1 1 auto;\r\n    padding: 0;\r\n\r\n    flex-basis: 100%;\r\n    flex-direction: column;\r\n    width: 0;\r\n    min-width: 0;\r\n    max-width: 100%;\r\n\r\n    min-height: 1px;        \r\n}", ".subheader {\r\n\tmargin-bottom: calc(1.5rem + 0.625rem); //moved the 0.625rem; from .subheader-title > small\r\n\tposition: relative;\r\n\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center; \r\n}\r\n\r\n.subheader-icon {\r\n\tcolor: $nav-icon-hover-color;\r\n\tmargin-right: 0.25rem;\r\n}\r\n\r\n.subheader-title {\r\n\tfont-size: 1.375rem; //unquote(\"calc(16px + 5 * ((100vw - 320px) / 680))\") //$fs-xxl;\r\n\tfont-weight: 500;\r\n\tcolor: $fusion-500;\r\n\ttext-shadow: $white 0 1px;\r\n\tmargin: 0;\r\n\r\n\tflex: 1;\r\n\r\n\tsup.badge {\r\n\t\ttext-shadow: none;\r\n\t\tposition: absolute;\r\n\t\tmargin-top: 0.4rem;\r\n\t\tmargin-left: $p-1;\r\n\t\tfont-size: 40%;\r\n    \tpadding: 2px 5px;\r\n    \tline-height: normal;\r\n\t}\r\n\r\n\tsmall {\r\n\t\tfont-weight: 400;\r\n\t\tcolor: $fusion-100; //$fusion-100;\r\n\t\tmargin-bottom: 0;\r\n\t\tfont-size: 0.875rem;\r\n\t}\r\n}", ".page-content {\r\n\t//align-items: stretch;\r\n    flex: 1 1 auto;\r\n    order: 3;\r\n\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n\r\n    //min-height: calc(100vh - #{$header-height + $footer-height}) //spoonfeeding IE\r\n}\r\n\r\n/*@include media-breakpoint-up($mobile-breakpoint) {\r\n\r\n\t.nav-function-top {\r\n\t\t.page-content {\r\n\t\t\tmin-height: calc(100vh - #{$header-height-nav-top + $nav-top-height + $footer-height})\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n*/", ".page-footer {\r\n\theight: $footer-height;\r\n\tdisplay: flex;\r\n\tflex: 0 0 auto;\r\n\talign-items:center;\r\n\tbackground: $footer-bg;\r\n    color:$footer-text-color;\r\n    font-size: rem($footer-font-size);\r\n    padding: 0 $header-inner-padding-x;\r\n    order: 4;\r\n}\r\n", ".alt {\r\n    padding: 0 !important;\r\n    flex-direction: column;\r\n\r\n    min-height: 100vh;\r\n\r\n    .page-footer {\r\n        width: 100% !important;\r\n    }\r\n}\r\n\r\n.page-error {\r\n    font-size: 600% !important;\r\n    font-weight: bold !important;\r\n\r\n    small {\r\n        font-size:40%;\r\n        font-weight: 500;\r\n    }\r\n}\r\n\r\n.h-alt-f {\r\n   height: calc(100vh - #{$footer-height + ( (map-get($grid-gutter-widths, xl) + 2.8125rem ) * 2) });\r\n   width: 100%;\r\n}\r\n\r\n.h-alt-hf {\r\n   height: calc(100vh - #{ $footer-height + $header-height + ( (map-get($grid-gutter-widths, xl) + 2.8125rem ) * 2) }); \r\n   width: 100%;\r\n\r\n\r\n}\r\n\r\n.nav-function-top {\r\n    .h-alt-hf {\r\n      height: calc(100vh - #{ $footer-height + $header-height-nav-top + $nav-top-height + ( (map-get($grid-gutter-widths, xl) + 2.8125rem ) * 2) }); \r\n    }\r\n}", ".accordion {\r\n\t.card {\r\n\t\t.card-header {\r\n\t\t\tcursor: pointer;\r\n\t\t\tmargin: 0;\r\n\t\t\tpadding: 0;\r\n\t\t\tborder-bottom: 0;\r\n\t\t\tbackground-color: $frame-border-color;\r\n\t\t\t.card-title {\r\n\t\t\t\tpadding: 1rem $card-spacer-x;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tfont-size: $h6-font-size;\t\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tcolor: $primary-500;\r\n\r\n\t\t\t\t&.collapsed {\r\n\t\t\t\t\tcolor: $fusion-100;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.accordion-clean {\r\n\r\n\t\t&:not(.accordion-outline):not(.accordion-hover) {\r\n\t\t\t.card-title,\r\n\t\t\t.card-body {\r\n\t\t\t\tpadding-left: 0 !important;\r\n\t\t\t\tpadding-right: 0 !important;\r\n\t\t\t}\r\n\t\t\t.card-body {\r\n\t\t\t\tpadding-top:0;\r\n\t\t\t}\t\t\t\r\n\t\t}\r\n\r\n\t\t.card-header {\r\n\t\t\tbackground: $white;\r\n\t\t}\r\n\t\t.card {\r\n\t\t\tborder-left: 0;\r\n    \t\tborder-right: 0;\r\n\r\n    \t\t&:first-child {\r\n    \t\t\tborder-top: 0;\r\n    \t\t}\r\n    \t\t&:last-child {\r\n    \t\t\tborder-bottom: 0;\r\n    \t\t}\r\n\t\t}\r\n\r\n\t\t&.accordion-outline,\r\n\t\t&.accordion-hover {\r\n\t\t\t.card-title,\r\n\t\t\t.card-body {\r\n\t\t\t\tpadding-left: $card-spacer-x !important;\r\n\t\t\t\tpadding-right: $card-spacer-x !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.accordion-outline {\r\n\t\t.card {\r\n\t\t\tmargin-bottom: 1rem;\r\n\t\t\tborder: 2px solid $card-border-color !important;\r\n\t\t\tborder-radius: $border-radius !important;\r\n\t\t}\r\n\t}\r\n\t&.accordion-hover {\r\n\t\t.card-title {\r\n\t\t\ttransition: background-color 0.5s ease\r\n\t\t}\r\n\t\t.card-header {\r\n\t\t\tbackground:$white;\r\n\t\t\t&:hover {\r\n\t\t\t\t.card-title.collapsed {\r\n\t\t\t\t\tcolor: $white;\r\n\t\t\t\t\tbackground-color: $primary-300;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.card-title:not(.collapsed) {\r\n\t\t\tcolor: $white;\r\n\t\t\tbackground-color: $primary-500;\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}\r\n", "/* \tDEV NOTE: The reason why we had to add this layer for alert colors is because BS4 \r\n\tdoes not allow you to add your own alert colors via variable control rather \r\n\tthrough a systemetic agent that changes the theme colors. \r\n\r\n\tREF: https://github.com/twbs/bootstrap/issues/24341#issuecomment-337457218\r\n*/\r\n\r\n.alert-primary {\r\n\tcolor: desaturate($primary-800, 45%);\r\n\tbackground-color: desaturate(lighten($primary-500, 39%), 17%);\r\n\tborder-color: desaturate(lighten($primary-500, 28%), 25%);\r\n}\r\n\r\n.alert-success {\r\n\tcolor:  desaturate($success-500, 35%);\r\n\tbackground-color: desaturate(lighten($success-500, 53%), 15%);\r\n\tborder-color: desaturate(lighten($success-500, 33%), 10%);\r\n}\r\n\r\n.alert-danger {\r\n\tcolor: $danger-800;\r\n\tbackground-color: lighten($danger-500, 34%);\r\n\tborder-color: lighten($danger-500, 20%);\r\n}\r\n\r\n\r\n.alert-warning {\r\n\tcolor: darken($warning-900, 5%);\r\n\tbackground-color: lighten($warning-500, 33%);\r\n\tborder-color: lighten($warning-500, 7%);\r\n}\r\n\r\n\r\n.alert-info {\r\n\tcolor: $info-800;\r\n\tbackground-color: lighten($info-500, 40%);\r\n\tborder-color: lighten($info-500, 20%);\r\n}\r\n\r\n\r\n.alert-secondary {\r\n\tcolor: $fusion-500;\r\n\tbackground-color: lighten($fusion-50, 42%);\r\n\tborder-color: lighten($fusion-500, 55%);\r\n}\r\n\r\n\r\n.alert-icon {\r\n\twidth: $p-4 + $p-3;\r\n\r\n\t> i {\r\n\t\tfont-size: rem($fs-xxl);\r\n\t}\r\n\r\n\t& + div {\r\n\t\tpadding-left: $p-1;\r\n\t}\r\n}", ".badge.badge-icon {\r\n\tposition: absolute;\r\n\tdisplay: inline-block;\r\n\tbackground-color: $nav-badge-bg-color;\r\n\tcolor: $nav-badge-color;\t\r\n\t@include box-shadow(0 0 0 1px $header-bg);\r\n\tcursor: default;\r\n\tborder: 1px solid transparent;\r\n\tfont-size: rem(10px);\r\n\tmin-width: 1rem;\r\n\tmax-width: $header-badge-min-width + 0.4375rem;\r\n\tpadding: 0 3px;\r\n\tborder-radius: $header-badge-min-width;\r\n\tfont-weight: 500;\r\n\tline-height: normal;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\t/* when self is relative */\r\n\tvertical-align: middle;\r\n}\r\n\r\n/* parent position needs to be relative, and turn off waves function */\r\n.btn-icon .badge {\r\n\ttop: auto;\r\n\t&.pos-top {\r\n\t\tmargin-top: -4px;\r\n\t}\r\n\t&.pos-bottom {\r\n\t\tmargin-bottom: -4px;\r\n\t}\r\n\t&.pos-left {\r\n\t\tmargin-left: -4px;\r\n\t}\r\n\t&.pos-right {\r\n\t\tmargin-right: -4px;\r\n\t}\r\n}", ".page-breadcrumb {\r\n\tpadding:0;\r\n\tbackground:transparent;\r\n\tmargin:0 0 1.5rem;\r\n\tposition: relative;\r\n\ttext-shadow: $white 0 1px;\r\n}\r\n\r\n.breadcrumb {\r\n\t> li {\r\n\r\n\t\t> a {\r\n\t\t\ttext-decoration: none !important;\r\n\t\t}\r\n\r\n\t\t&.breadcrumb-item {\r\n\t\t\tmax-width: $page-breadcrumb-maxwidth - 70px;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\twhite-space: nowrap;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t@extend %common-animation-slow;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tmax-width: $page-breadcrumb-maxwidth !important;\r\n\t\t\t\tcursor: default;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n[data-breadcrumb-seperator] + [data-breadcrumb-seperator]:before {\r\n\t\tcontent: attr(data-breadcrumb-seperator);\r\n}\r\n\r\n.breadcrumb-lg > li {\r\n\tfont-size: rem($fs-xl);\r\n}\r\n\r\n.breadcrumb-sm > li {\r\n\tfont-size: rem($fs-nano);\r\n}\r\n\r\n[class*='breadcrumb-seperator-'] .breadcrumb-item + .breadcrumb-item:before {\r\n    -moz-osx-font-smoothing: grayscale;\r\n    -webkit-font-smoothing: antialiased;\r\n    display: inline-block;\r\n    font-style: normal;\r\n    font-variant: normal;\r\n    font-weight: 400;\r\n    line-height: 1;\r\n    font-family: Font Awesome\\ 5 Pro;  \r\n}\r\n\r\n.breadcrumb-seperator-1 .breadcrumb-item + .breadcrumb-item:before { content:\"\\f105\"; }\r\n.breadcrumb-seperator-2 .breadcrumb-item + .breadcrumb-item:before { content:\"\\f178\"; }\r\n.breadcrumb-seperator-3 .breadcrumb-item + .breadcrumb-item:before { content:\"\\f054\"; }\r\n\r\n$breadcrumb-arrow-color: $fusion-100;\r\n$breadcrumb-arrow-color-hover: $primary-500;\r\n\r\n.breadcrumb-arrow {\r\n\r\n\tpadding: 0;\r\n\tbackground: transparent;\r\n\r\n\tli{\r\n\r\n\t\t&.active {\r\n\t\t\tfont-weight: 500;\r\n\t\t\topacity: 0.5\r\n\t\t}\r\n\r\n\t\ta {\r\n\t\t\t@include text-contrast($breadcrumb-arrow-color)\r\n\t\t\tdisplay:inline-block;\r\n\t\t\tbackground: $breadcrumb-arrow-color;\r\n\t\t\ttext-decoration: none;\r\n\t\t\tposition:relative;\r\n\t\t\theight: 2.5em;\r\n\t\t\tline-height: 2.5em;\r\n\t\t\tpadding: 0 10px 0 5px;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-right: 22px;\r\n\t\t}\r\n\t\t&:nth-child(even){\r\n\t\t\ta {\r\n\t\t\t\tbackground-color: $breadcrumb-arrow-color;\r\n\r\n\t\t\t\t&:before{\r\n\t\t\t\t\tborder-color: $breadcrumb-arrow-color;\r\n\t\t\t\t\tborder-left-color:transparent;\r\n\t\t\t\t}\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tborder-left-color: $breadcrumb-arrow-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&:first-child{\r\n\t\t\ta {\r\n\t\t\t\tpadding-left:0.938em;\r\n\r\n\t\t\t\tborder-radius: $border-radius 0 0 $border-radius;\r\n\r\n\t\t\t\t&:before{\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&:last-child{\r\n\t\t\ta {\r\n\t\t\t\tpadding-right:0.938em;\r\n\r\n\t\t\t\tborder-radius: 0 $border-radius $border-radius 0;\r\n\r\n\t\t\t\t\t&:after{\r\n\t\t\t\t\t\tborder: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\ta {   \r\n\t\t\t&:before,\r\n\t\t\t&:after{\r\n\t\t\t\tcontent: \"\";\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tborder:0 solid $breadcrumb-arrow-color;\r\n\t\t\t\tborder-width:1.250em 10px;\r\n\t\t\t\twidth: 0;\r\n\t\t\t\theight: 0;\r\n\t\t\t}\r\n\t\t\t&:before{\r\n\t\t\t\tleft:-20px;\r\n\t\t\t\t\tborder-left-color:transparent;\r\n\t\t\t\t}\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tleft:100%;\r\n\t\t\t\t\tborder-color:transparent;\r\n\t\t\t\t\tborder-left-color: $breadcrumb-arrow-color;\r\n\t\t\t\t}\r\n\t\t\t\t&:hover{\r\n\t\t\t\t\tbackground-color: $breadcrumb-arrow-color-hover;\r\n\r\n\t\t\t\t&:before{\r\n\t\t\t\t\tborder-color: $breadcrumb-arrow-color-hover;\r\n\t\t\t\t\tborder-left-color:transparent;\r\n\t\t\t\t }\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tborder-left-color: $breadcrumb-arrow-color-hover;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&:active{\r\n\t\t\t\tbackground-color: $breadcrumb-arrow-color;\r\n\r\n\t\t\t\t&:before{\r\n\t\t\t\t\tborder-color: $breadcrumb-arrow-color;\r\n\t\t\t\t\tborder-left-color:transparent;\r\n\t\t\t\t}\r\n\t\t\t\t&:after{\r\n\t\t\t\t\tborder-left-color: $breadcrumb-arrow-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}", "/* btn switch */\r\n.btn-switch {\r\n\tbackground: $fusion-300;\r\n\tpadding: 2px 8px 1px 22px;\r\n\tfont-size: 10px;\r\n\tline-height: 15px;\r\n\tborder-radius: 20px;\r\n\ttext-transform: uppercase;\r\n\t@include text-contrast($fusion-300);\r\n\tfont-weight: 500;\r\n\tmin-width:55px;\r\n\theight:20px;\r\n\tmargin-top:5%;\r\n\tposition:relative;\r\n\toverflow: hidden;\r\n\t\r\n\t@extend %common-animation;\r\n\r\n\t&:hover{\r\n\t\t@include scale(1.10);\r\n\t\t@include text-contrast($fusion-300);\r\n\t}\r\n\r\n\t&:before {\r\n\t\tcontent:\"OFF\";\r\n\t\tposition:absolute;\r\n\t\tright:7px;\r\n\t}\r\n\r\n\t&:after{\r\n\t\tcontent: \" \";\r\n\t\ttext-align: center;\r\n\t\t@include text-contrast(#828282);\r\n\t\twidth: 16px;\r\n\t\theight: 16px;\r\n\t\tposition: absolute;\r\n\t\tbackground: $white;\r\n\t\tpadding:1px;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tline-height: normal;\r\n\t\tmargin: 1px;\r\n\t\tborder-radius: 50%;\r\n\t\tbox-shadow: 0 10px 20px rgba(0,0,0,0.19),0 6px 6px rgba(0,0,0,0.23);\r\n\t}\r\n\r\n\t&.active {\r\n\t\tcolor:$white;\r\n\t\tbackground:$color-primary;\r\n\t\t&:before {\r\n\t\t\tcontent:\"ON\";\r\n\t\t\tleft:7px;\r\n\t\t\tright:auto;\r\n\t\t\t@include text-contrast($color-primary);\r\n\t\t}\r\n\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\tright:0;\r\n\t\t\tleft:auto;\r\n\t\t\tbackground:$white;\r\n\t\t\tcolor:$color-primary;\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\n/* button used to close filter and mobile search */\r\n.btn-search-close {\r\n\tposition: absolute !important;\r\n\tdisplay: flex;\r\n\talign-items:center;\r\n\tjustify-content:center;\r\n\tpadding: 0px 5px;\r\n\tborder-radius: $border-radius - 1px;\r\n\tcolor: $white;\r\n\tright: 8px;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tmargin: auto 0 auto auto;\r\n\twidth: 20px;\r\n\theight: 20px;\r\n}\r\n\r\n/* buttons used in the header section of the page */\r\n.header-btn {\r\n\t@extend %header-btn;\r\n\r\n\t&[data-class='mobile-nav-on'] {\r\n\t\tborder-color: $danger-700;\r\n\t\t@include gradient-img($start: $danger-600,$stop: $danger-800);\r\n\t\tcolor:$white;\r\n\t\twidth: $header-btn-width + 0.625rem;\r\n\t}\r\n\r\n}\r\n\r\n/* btn widths */\r\n.btn-w-m {\r\n\tmin-width: 85px;\r\n}\r\n\r\n.btn-w-l {\r\n\tmin-width: 130px;\r\n}\r\n\r\n.btn-m-s {\r\n\tmargin: 3px 1px;\r\n}\r\n\r\n.btn-m-l {\r\n\tmargin: 3px 2.5px;\r\n}\r\n\r\n/* dropdown btn */\r\n/* used on info card pulldown filter */\r\n.pull-trigger-btn {\r\n\tposition: absolute !important;\r\n\ttop: -5px;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tmargin-left: auto;\r\n\tmargin-right: auto;\r\n\tbackground: rgba($black, .4);\r\n\tpadding: 0px 9px;\r\n\tborder: 1px solid rgba($black, .4);\r\n\tborder-radius: 0 0 20px 20px;\r\n\ttext-decoration: none;\r\n\tfont-size: 17px;\r\n\theight: 21px;\r\n\twidth: 31px;\r\n\tcolor: $white !important;\r\n\tline-height: 20px;\r\n\ttext-align: center;\r\n\ttransition: all 200ms cubic-bezier(0.34, 1.25, 0.6, 1);\r\n\tbox-shadow: 0px 0px 2px rgba($primary-500, 0.3);\r\n\topacity: 1;\r\n\r\n\t&:hover {\r\n\t\tfont-size: 23px;\r\n\t\theight: 25px;\r\n\t\twidth: 35px;\r\n\t\tline-height: 23px;\r\n\t\tbackground: $primary-500;\r\n\t\tborder-color: $primary-600;\r\n\t\tbox-shadow: 0px 0px 10px #5790b3;\r\n\t}\r\n}\r\n\r\n/* buttons dropshadow */\r\n/*[class*='btn-']:not(.btn-switch):not(.btn-group):not([class*='btn-w-']),\r\n[class*='btn-']:not(.btn-switch):not(.btn-group):not([class*='btn-w-']):focus {*/\r\n.btn-shadow,\r\n.btn-shadow:focus {\t\r\n\tbox-shadow: 0 2px 6px rgba(0,0,0,.2), 0 2px 3px rgba(0,0,0,.05);\r\n\r\n\t&:active,\r\n\t.active {\r\n\t\tbox-shadow: 0 0px 0px 0 rgba(0,0,0,.3) !important;\r\n\t}\r\n\r\n\t&:not(.disabled):not([disabled]) {\r\n\t\t&:hover {\r\n\t\t\tbox-shadow: 0 6px 17px 0 rgba(0,0,0,.3);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.btn:active {\r\n\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset !important;\r\n}\r\n\r\n/*.btn-group {\r\n\t[class*='btn-'] {\r\n\t\tbox-shadow: 0 0px 0px 0 rgba(0,0,0,.3) !important;\r\n\t}\r\n}*/\r\n\r\n.btn-light {\r\n\tborder-color: rgba(0, 0, 0, 0.15);\r\n\t&:not(:disabled):not(.disabled):active,\r\n\t&:not(:disabled):not(.disabled).active,\r\n\t&:hover {\r\n\t\tborder-color: rgba($black, 0.25);\r\n\t}\r\n}\r\n.show > .btn-light.dropdown-toggle {\r\n\tborder-color: rgba($black, 0.25);\r\n}\r\n\r\n/* btn misc */\r\n.btn-default {\r\n\t@extend %btn-default;\r\n}\r\n\r\n.btn-outline-default {\r\n\t//@include button-outline-variant($body-color, $body-color, lighten($input-border-color, 8%), $input-border-color);\r\n\tbackground-color: transparent;\r\n\tcolor: $body-color;\r\n\tborder-color: $input-border-color;\r\n\r\n\t&:hover,\r\n\t&:not(:disabled):not(.disabled):active,\r\n\t&:not(:disabled):not(.disabled).active,\r\n\t.show > &.dropdown-toggle {\r\n\t\tcolor: $body-color;\r\n\t\tbackground-color: lighten($input-border-color, 8%);\r\n\t\tborder-color: $input-border-color;\r\n\t}\r\n\r\n\t&.disabled,\r\n\t&:disabled {\r\n\t\tcolor: $body-color;\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\r\n}\r\n\r\n[class*=\"btn-outline-\"] {\r\n\t@extend %common-animation;\r\n}\r\n\r\n.btn-pills {\r\n\t@include rounded(15px);\r\n}\r\n\r\n/* new btn size */\r\n.btn-xs, \r\n.btn-group-xs > .btn {\r\n\tpadding: 1px $input-btn-padding-x-sm;\r\n\tfont-size: .7rem;\r\n\tline-height: 1.5;\r\n\tborder-radius: .25rem;\r\n}\r\n\r\n/* btn shadows */\r\n@mixin button-shadow($value) {\r\n  box-shadow: 0 2px 6px 0 rgba($value, .5);\r\n}\r\n@each $color, $value in $theme-colors {\r\n  .btn-#{$color} {\r\n\t@include button-shadow($value);\r\n  }\r\n}\r\n\r\n/* btn icon */\r\n.btn-icon {\r\n\twidth: calc(2.1rem + 2px);\r\n\tpadding: 0;\r\n\tline-height: 2.1rem;\r\n\r\n\t&:not([class*=\"-primary\"]):not([class*=\"-secondary\"]):not([class*=\"-default\"]):not([class*=\"-success\"]):not([class*=\"-info\"]):not([class*=\"-warning\"]):not([class*=\"-danger\"]):not([class*=\"-dark\"]):not([class*=\"-light\"]):not(.nav-item) {\r\n\t\t&:hover {\r\n\t\t\tbackground-color: rgba($black,0.05);\r\n\t\t\tborder-color: transparent;\r\n\t\t}\r\n\r\n\t\t&:not(.active):not(:active):not(:hover):not(:focus) {\r\n\t\t\tbackground: transparent;\r\n\t\t\tcolor: $fusion-600;\r\n\t\t}\r\n\r\n\t\t&:focus {\r\n\t\t\tborder-color: rgba($fusion-500, 0.1) !important;\r\n\t\t}\r\n\t}\r\n\r\n\t&.btn-xs {\r\n\t\twidth: calc(1.15rem + 2px);\r\n\t\tline-height: 1.15rem;\r\n\t}\r\n\r\n\t&.btn-sm {\r\n\t\twidth: calc(1.5rem + 2px);\r\n\t\tline-height: 1.5rem;\r\n\t}\r\n\r\n\t&.btn-lg {\r\n\t\twidth: calc(3rem + 2px);\r\n\t\tline-height: 3rem;\r\n\t}\r\n}\r\n\r\n.btn-icon-light {\r\n\r\n\tcolor: rgba($white, 0.7) !important;\r\n\tborder-color: transparent !important;\r\n\r\n\t&:not(.active):not(:active):not(:hover):not(:focus) {\r\n\t\tcolor: rgba($white, 0.7) !important;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\tcolor: $white !important;\r\n\t\tbackground-color: rgba($white,0.2) !important;\r\n\t\t\r\n\t}\t\r\n}", ".card-header {\r\n\tcolor: inherit;\r\n\tbackground-color: $frame-border-color;\r\n\tsmall {\r\n\t\tmargin:0;\r\n\t\topacity: 0.8;\r\n\t\tfont-weight: 400;\r\n\t\tfont-size: 85%;\r\n\t}\r\n} \r\n\r\n/* remove extra margin in card child items */\r\n.card,\r\n.card-group {\r\n\tbox-shadow: 0px 0px 13px 0px rgba(darken($primary-800, 10%), (8/100));\r\n\r\n\t> :last-child {\r\n\t\tmargin-bottom: 0px;\r\n\t}\r\n}\r\n\r\n.accordion > .card {\r\n\tbox-shadow: none;\r\n}\r\n\r\n.card-group > .card {\r\n\tbox-shadow: none;\r\n}\r\n\r\n/* remove wierd line height issue */\r\n.card-header-pills,\r\n.card-header-tabs {\r\n\tfont-size: 0;\r\n\r\n\t.nav-link {\r\n\t\tfont-size: $font-size-base;\r\n\t}\r\n}\r\n\r\n/* card title */\r\n.card-title {\r\n\tfont-size: $h5-font-size;\t\r\n}\r\n\r\n.card-header .card-title {\r\n\tdisplay: inline-block;\r\n\tpadding: 0;\r\n\tmargin: 0 0.5rem 0 0;\r\n}", ".carousel-indicators li {\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.carousel-control-prev:hover {\r\n\t/*background: -moz-linear-gradient(left, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 45%);\r\n\tbackground: -webkit-linear-gradient(left, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); */\r\n\tbackground: linear-gradient(to right, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); \r\n}\r\n\r\n.carousel-control-next:hover {\r\n\t/*background: -moz-linear-gradient(right, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 45%); \r\n\tbackground: -webkit-linear-gradient(right, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%);*/ \r\n\tbackground: linear-gradient(to left, rgba(0,0,0,0.25) 0%,rgba(0,0,0,0) 45%); \r\n}", ".dropdown-header.bg-trans-gradient {\r\n\tpadding: 1.25rem 1.5rem;\r\n}\r\n\r\n/* dropdown-item hover menu*/\r\n.dropdown-menu-animated {\r\n\t@include transform( scale(0.8) !important);\r\n\ttransition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\topacity: 0;\r\n\tvisibility: hidden;\r\n\tdisplay: block;\r\n}\r\n\r\n/* various sizes */\r\n.dropdown-menu {\r\n\tbox-shadow: $dropdown-shadow;\r\n\t-webkit-user-select: text;\r\n\r\n\t.dropdown-item {\r\n\t\tfont-weight: 400;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t&.dropdown-sm {\r\n\t\twidth: $dropdown-sm-width;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t&.dropdown-md {\r\n\t\twidth: $dropdown-md-width;\r\n\t\theight: auto;\r\n\t}  \r\n\r\n\t&.dropdown-lg {\r\n\t\twidth: $dropdown-lg-width;\r\n\t\theight: auto;\r\n\t} \r\n\r\n\t&.dropdown-xl {\r\n\t\twidth: $dropdown-xl-width;\r\n\t\theight: auto;\r\n\t}\t\r\n\r\n\t.dropdown-item {\r\n\r\n\t\t&:first-child,\r\n\t\t&:last-child {\r\n\t\t\t@include border-radius(0px);\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\n/* replace bootstrap's default arrow */\r\n.dropdown-toggle:after,\r\n.dropleft .dropdown-toggle:before {\r\n\ttext-align: center;\r\n\tdisplay: inline;\r\n\tborder: 0 !important;\r\n\tfont-family: 'Font Awesome 5 Pro';\r\n\tcontent: \"\\f107\" !important;\r\n\tvertical-align: top !important;\r\n\tposition: relative;\t\r\n}\r\n\r\n.dropup .dropdown-toggle:after {\r\n\tcontent: \"\\f106\" !important;\r\n}\r\n\r\n.dropright .dropdown-toggle:after {\r\n\tcontent: \"\\f105\" !important;\r\n}\r\n\r\n.dropleft .dropdown-toggle:before {\r\n\tcontent: \"\\f104\" !important;\r\n}\r\n\r\n//very wierd bug... \r\n.nav-item .dropdown-toggle:after {\r\n\tfont-size: 0.90em;\r\n}\r\n\r\n\r\n/* remove arrow */\r\n.dropdown-toggle {\r\n\t&.no-arrow {\r\n\t\t&:before,\r\n\t\t&:after {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* dropdown menu multi-level */\r\n.dropdown-menu {\r\n\r\n\t.dropdown-menu {\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t\tborder-radius: 0;\r\n\t\tposition: absolute;\r\n\t\ttop: -1px;\r\n\t\tleft: 100%;\r\n\t\tbackground:$white;\r\n\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n\r\n\t\t@include transform( scale(0.8) );\r\n\t\ttransition: all 270ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\t\ttransition-delay: 500ms;\r\n\t\topacity: 0;\r\n\t\tvisibility: hidden;\r\n\t\tdisplay: block;\r\n\r\n\t\t.dropdown-item {\r\n\t\t\tpadding-top: 0.5rem !important;\r\n\t\t\tpadding-bottom: 0.5rem !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.dropdown-multilevel {\r\n\t\tposition: relative;\r\n\r\n\t\t/* it is displayed on right by default */\r\n\t\t&.dropdown-multilevel-left {\r\n\r\n\t\t\t> .dropdown-menu {\r\n\t\t\t\tright: 100%;\r\n\t\t\t\tleft: auto;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* add arrow */\r\n\t\t> .dropdown-item:first-child {\r\n\t\t\t&:after {\r\n\t\t\t\tcontent: \"\\f2fb\";\r\n\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding-right: $dropdown-item-padding-x;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover {\r\n\t\t\t> .dropdown-item:not(.disabled) {\r\n\r\n\t\t\t\tbackground: $gray-100;\r\n\t\t\t\tcolor: $dropdown-link-hover-color;\r\n\r\n\t\t\t\t& + .dropdown-menu {\r\n\t\t\t\t\ttransition-delay: 0ms;\r\n\t\t\t\t\t@include transform( scale(1) );\r\n\t\t\t\t\t@include transform-origin( 29px -50px )\r\n\t\t\t\t\topacity: 1;\r\n\t\t\t\t\tvisibility: visible;\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n}", "$icon-stack-size: 3em;\r\n\r\n.icon-stack {\r\n\tposition: relative;\r\n\tdisplay: inline-block;\r\n\twidth: 1em;\r\n\theight: 1em;\r\n\tline-height: 1em;\r\n\tvertical-align: middle;\r\n\ttext-align: center;\r\n\t//transition: $nav-hide-animate;\r\n}\r\n.icon-stack-1x,\r\n.icon-stack-2x,\r\n.icon-stack-3x {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tbottom:0;\r\n\twidth: 100%;\r\n\ttext-align: center;\r\n\tline-height: inherit !important;\r\n}\r\n.icon-stack-1x {\r\n\tfont-size: 0.5em;\r\n}\r\n.icon-stack-2x {\r\n\tfont-size: 0.70em;\r\n\t/*padding-right: 0.025em;*/\r\n}\r\n.icon-stack-3x {\r\n\tfont-size: 1em;\r\n}\r\n\r\n.icon-stack-xl {\r\n\tfont-size: rem(50px);\r\n}\r\n.icon-stack-lg {\r\n\tfont-size: rem(40px);\r\n}\r\n.icon-stack-md {\r\n\tfont-size: rem(34px);\r\n}\r\n.icon-stack-sm {\r\n\tfont-size: rem(30px);\r\n}", ".filter-message {\r\n\tdisplay: block;\r\n\ttext-align: center;\r\n\tpadding: 2px;\r\n\tfont-size: rem($fs-nano);\r\n\ttext-transform: capitalize;\r\n\tfont-style: italic;\r\n\twidth: calc(100% - 60px);\r\n\tmax-width: 180px;\r\n\tborder-radius: $border-radius;\r\n\tmargin: $p-3 auto;\r\n\r\n\t&:empty {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n.js-list-filter:not(.primary-nav) {\r\n\r\n\t/* these classes are triggered by JS */\r\n\t.js-filter-hide {\r\n\t\tdisplay:none !important;\r\n\t}\r\n\r\n\t// this is not needed as it will already be visible\r\n\t// -- actually its needed for nested elements\r\n\t/*.js-filter-show {\r\n\t\tdisplay:block !important;\r\n\t}*/\r\n\r\n}\r\n\r\n// we do this for nav-menu items to display parent as well\r\n.js-list-filter.nav-menu:not(.primary-nav) {\r\n\t.js-filter-show {\r\n\t\tdisplay:block !important;\r\n\t}\t\r\n}", ".loader {\r\n\tdisplay: none;\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: rgba(255,255,255,0.5);\r\n\tcolor: inherit;\r\n\tz-index: 10;\r\n\r\n\t/*\r\n\tThe use of translate3d pushes CSS animations into hardware acceleration.\r\n\tEven if you're looking to do a basic 2d translation, use translate3d for more power!\r\n\tIf your animation is still flickering after switching to the transform above,\r\n\tyou can use a few little-known CSS properties to try to fix the problem:\r\n\t*/\r\n\t/*-webkit-transform: translate3d(0, 0, 0);\r\n\t-webkit-backface-visibility: hidden;\r\n\t-webkit-perspective: 1000;\t*/\r\n\r\n}\r\n\r\n\r\n/*:not(.enable-loader) .loader {\r\n\t> * {\r\n\t\tanimation: pause;\r\n\t}\r\n}*/\r\n\r\n.enable-loader {\r\n\t&:before {\r\n\t\tcontent: '';\r\n\t\tbackground-color: rgba($white, 0.7);\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.loader {\r\n\t\tdisplay: flex;\r\n\t}\r\n}\r\n\r\n\r\n", "#msgr_listfilter_input {\r\n\twidth: calc(100% - #{$p-3});\r\n\tmargin: 0 auto;\r\n\tmargin-top: -41px;\r\n\tpadding-left: 41px;\r\n\tmargin-bottom: 10px;\r\n\topacity: 0; \r\n\tbackground: transparent;\r\n\t@extend %general-animation;\r\n\ttransition-delay: $msgr-animation-delay;  \t\r\n}\r\n\r\n.msgr-list {\r\n\twidth: $msgr-list-width;\r\n    right: $msgr-list-width-collapsed - $msgr-list-width;\r\n    z-index: 101;\r\n\r\n    @extend %general-animation;\r\n    transition-delay: $msgr-animation-delay;\r\n\r\n\t& + .msgr {\r\n\r\n\t\twidth: calc(100% - #{$msgr-list-width-collapsed});\r\n\t\theight: 100%;\r\n\r\n\t\t&:before {\r\n\t\t\tcontent:'';\r\n\t\t\theight:100%;\r\n\t\t\twidth:100%;\r\n\t\t\tbackground:rgba($white, 0.4);\t\r\n\t\t\tposition:absolute;\r\n\t\t\tz-index: 100;\r\n\r\n\t\t\t@extend %general-animation;\r\n\t\t\ttransition-delay: $msgr-animation-delay;\r\n\r\n\t\t\topacity: 0;\r\n\t\t\tvisibility: hidden;\r\n\t\t}\r\n\t}\r\n\r\n    &:hover {\r\n    \tright: 0;\r\n    \tborder-left-color: rgba(0,0,0,0.1);\r\n\r\n    \t#msgr_listfilter_input {\r\n    \t\topacity: 1;\r\n    \t}\r\n\r\n    \t& + .msgr {\r\n\r\n    \t\t&:before {\r\n    \t\t\topacity: 1;\r\n    \t\t\tvisibility: visible;\r\n    \t\t}\r\n    \t}\r\n    }\r\n}\r\n\r\n/*.msgr-chatinput {\r\n\r\n\tmin-height: 110px;\r\n\tmax-height: 160px;\r\n\r\n\t[contenteditable=\"true\"] {\r\n\t\tmin-height:50px;\r\n\t\tmax-height:110px;\r\n\t}\r\n\r\n}*/\r\n\r\n/*.msgr-chatinput-icons {\r\n\theight: 40px;\r\n}*/\r\n\r\n/*.msgr-chatinput-container {\r\n\t> div {\r\n\t\tborder-top: 1px solid rgba($black,0.07);\r\n\t}\r\n}*/\r\n\r\n\r\n/* IE HACK */\r\n/*@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\r\n\t[contenteditable=\"true\"] {\r\n\t\theight: 110px;\r\n\t}\r\n}*/\r\n\r\n.chat-segment-get {\r\n\r\n\ttext-align: left;\r\n\tposition: relative;\r\n\r\n\tmargin: 0 2rem 0.5rem 0;\r\n\r\n\t&.chat-start {\r\n\r\n\t\t.chat-message {\r\n\t\t\tborder-bottom-left-radius: 3px;\r\n\t\t}\r\n\r\n\t\t+ :not(.chat-end) {\r\n\t\t\t.chat-message {\r\n\t\t\t\tborder-bottom-left-radius: 3px;\r\n\t\t\t\tborder-top-left-radius: 3px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&.chat-end {\r\n\t\t.chat-message {\r\n\t\t\tborder-top-left-radius: 3px;\r\n\t\t}\r\n\t}\r\n\r\n\t.chat-message {\r\n\t\tbackground: $msgr-get-background;\r\n\t\t@include text-contrast($msgr-get-background);\t\r\n\r\n\t\ttext-align: left;\t\t\t\r\n\t}\r\n}\r\n\r\n.chat-segment-sent {\r\n\r\n\ttext-align: right;\r\n\tposition: relative;\r\n\r\n\tmargin: 0 0 .5rem 3rem;\r\n\r\n\t&.chat-start {\r\n\r\n\t\t.chat-message {\r\n\t\t\tborder-bottom-right-radius: 3px;\r\n\t\t}\r\n\r\n\t\t+ :not(.chat-end) {\r\n\t\t\t.chat-message {\r\n\t\t\t\tborder-bottom-right-radius: 3px;\r\n\t\t\t\tborder-top-right-radius: 3px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&.chat-end {\r\n\t\t.chat-message {\r\n\t\t\tborder-top-right-radius: 3px;\r\n\t\t}\r\n\t}\r\n\r\n\t.chat-message {\r\n\t\tbackground: $msgr-sent-background;\r\n\t\t@include text-contrast($msgr-sent-background);\r\n\r\n\t\ttext-align: left;\t\t\t\r\n\t}\r\n\r\n}\r\n\r\n.chat-message {\r\n\tpadding: 0.75rem 1rem;\r\n\tborder-radius: 0.625rem;\r\n\r\n\tposition: relative;\r\n\r\n\tdisplay: inline-block;\r\n\r\n\t> p {\r\n\t\tpadding: $p-2 + $p-1 0 0;\r\n\t\tmargin:0;\r\n\t}\r\n\r\n\t> p:first-child {\r\n\t\tpadding-top: 0;\r\n\t}\r\n}\r\n\r\n.chat-start {\r\n\r\n\tmargin-bottom: 3px !important;\r\n\t\r\n\t.time-stamp {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t& + .chat-segment:not(.chat-end) {\r\n\r\n\t\tmargin-bottom: 3px !important;\r\n\r\n\t\t.time-stamp {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t}\r\n}", "/* modal shadow */\r\n.modal-content {\r\n\tbox-shadow: 0 0 20px 0 rgba($black, 0.2); \r\n}\r\n\r\n/* adjustments */\r\n.modal-dialog {\r\n\t.modal.show & {\t\t\r\n\t\ttransform: none;\t\t\r\n\t}\r\n}\r\n\r\n/* alert modal */\r\n.modal-alert {\r\n\tpadding: 0 !important; /* overriding bootstrap generated style */\r\n\r\n\t.modal-dialog {\r\n\t\tmax-width: 100% !important; /* overriding bootstrap css for all media queries */\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmin-height: calc(100% - 3.5rem);\r\n\t\ttransform: none !important; /* overriding bootstrap css */\r\n\t}\r\n\r\n\t.modal-content {\r\n\t\tbackground-color: rgba($black,0.8);\r\n\t\tborder-radius: 0;\r\n\t\tpadding: 1.5rem 1rem 1rem;\r\n\t}\r\n\r\n\t.modal-title {\r\n\t\tfont-size: 1.5rem;\r\n    \tfont-weight: 300;\r\n\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.modal-header,\r\n\t.modal-body,\r\n\t.modal-footer {\r\n\t\t/*left: 20%;\r\n\t\twidth: 60%;*/\r\n\t\twidth: 100%;\r\n\t\tmax-width: map-get($grid-breakpoints, lg);\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 0;\r\n\t\tcolor: $white;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/*.modal-body {\r\n\t\tpadding: 0.5rem 0;\r\n\t}\r\n*/\r\n\t.close {\r\n\t\tcolor: $white;\r\n\t\ttext-shadow: 0 1px 0 $black;\r\n\t}\r\n}\r\n\r\n/* transparent modal */\r\n.modal-transparent {\r\n\t.modal-content {\r\n\t\tbox-shadow: 0 1px 15px 1px rgba($primary-900, 0.3);\r\n\t}\r\n\t.modal-content {\r\n\t\tbackground: rgba(desaturate(darken($primary-800, 25%), 20%), 0.85); \r\n\t}\r\n}\r\n\r\n/* transparent backdrop */\r\n.modal-backdrop-transparent {\r\n\tbackground: transparent;\r\n}\r\n\r\n/* fullscreen modal */\r\n.modal-fullscreen {\r\n\t\r\n\tpadding: 0 !important;\r\n\r\n\t.modal-content {\r\n\t\tborder-radius: 0;\r\n\t\tborder-width: 0;\r\n\t}\r\n\r\n\t.modal-dialog {\r\n\t\tmax-width: unquote(\"calc(100vw - 40px)\");\r\n\t\tmax-height: unquote(\"calc(100vh - 80px)\");\r\n\r\n\t\t.modal-content {\r\n\t\t\theight: unquote(\"calc(100vh - 80px)\");\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n\r\n/* top */\r\n.modal-dialog-top,\r\n.modal-dialog-bottom {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tmargin: 0;\r\n\t\tmin-width: 100%;\r\n\t\r\n\t.modal-dialog {\r\n\t\twidth: 100%;\r\n\t\tmax-width: 100%;\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.modal-content {\r\n\t\tborder-radius: 0px;\r\n\t\tborder:0;\r\n\t}\r\n}\r\n\r\n.modal-dialog-bottom {\r\n\ttop: auto;\r\n\tbottom: 0;\r\n\r\n\t.modal.fade & {\r\n\t\ttransform: translate(0,25%);\r\n\t}\r\n}\r\n\r\n/* left */\r\n.modal-dialog-left {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tmargin: 0 !important;\r\n\r\n\t.modal-content {\r\n\t\tmin-height: 100%;\r\n\t\tborder-width: 0;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t.modal.fade & {\r\n\t\ttransform: translate(-25%, 0);\r\n\t}\r\n}\r\n\r\n/* right */\r\n.modal-dialog-right {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tright: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tmargin: 0 !important;\r\n\tborder-width: 0px;\r\n\r\n\t.modal-content {\r\n\t\tmin-height: 100%;\r\n\t\tborder-width: 0;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t.modal.fade & {\r\n\t\ttransform: translate(25%, 0);\r\n\t}\r\n}\r\n\r\n.modal.show .modal-dialog {\r\n\ttransform: translate(0, 0);\r\n}\r\n\r\n/* modal size */\r\n.modal-md {\tmax-width: 350px; }\r\n\r\n\r\n\r\n\t ", ".pagination {\r\n\t.page-item:not(:first-child) {\r\n\t\tmargin-left: 0.4rem;\r\n\t}\r\n\r\n\t.page-item:first-child:not(.active),\r\n\t.page-item:last-child:not(.active),\r\n\t.page-item.disabled {\r\n\t\t.page-link {\r\n\t\t\tbackground: lighten($primary-50, 10%);\r\n\t\t}\r\n\t}\r\n\r\n\t.page-link {\r\n\t\tborder-radius: $border-radius;\r\n\t\tborder-width: 0px;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground-color: $primary-500 !important;\r\n\t\t\tcolor: $white;\r\n\t\t}\r\n\t}\r\n\r\n\t&.pagination-xs {\r\n\t\t.page-link {\r\n\t\t\tpadding: 0.2rem 0.5rem;\r\n\t\t\tfont-size: rem($fs-xs)\r\n\t\t}\r\n\t} \r\n\r\n}\r\n\r\n\r\n", ".panel-fullscreen {\r\n\toverflow:hidden;\r\n\tmax-width: 100%;\r\n}\r\n\r\n.panel {\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n\tposition: relative;\r\n\tbackground-color: $white;\r\n\tbox-shadow: 0px 0px 13px 0px rgba(darken($primary-800, 15%), (8/100));\r\n \r\n\tmargin-bottom: $grid-gutter-width;\r\n\tborder-radius: $border-radius;\r\n\r\n\t//experimental ...\r\n\tborder: 1px solid rgba(0, 0, 0, 0.09);\r\n\tborder-bottom: 1px solid #e0e0e0;\r\n\r\n\tborder-radius: $panel-edge-radius;\r\n\r\n\ttransition : border 500ms ease-out;\r\n\r\n\t/* panel container */\r\n\t.panel-container {\r\n\r\n\t\t//this cosigns with header animation 'all'\r\n\t\t//@include transition-border(0.4s, ease-out);\r\n\r\n\t\tposition: relative;\r\n\r\n\t\t//border: 1px solid transparent;\r\n\t\t//border-top-width: 0;\r\n\r\n    \tborder-radius: 0 0 $panel-edge-radius $panel-edge-radius;\r\n\r\n\t\t.panel-content {\r\n\t\t\tpadding: $panel-spacer-y $panel-spacer-x;\r\n\r\n\t\t\t&:only-child,\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-radius: 0 0 $panel-edge-radius $panel-edge-radius;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* panel refresh */\r\n\t&.panel-refresh {\r\n\r\n\t\t.js-panel-refresh {\r\n\t\t\topacity: 0.5;\r\n\t\t\tcursor: wait;\r\n\t\t}\r\n\t}\r\n\r\n\t/* panel fullscreen */\r\n\t&.panel-fullscreen {\r\n\t\tposition: fixed !important; /* there is a bug with jquery ui, so we have to add !important rule here */\r\n\t\tz-index: $zindex-panel-fullscreen;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 100vh !important;\r\n\t\tmax-height: 100vh !important;\r\n    \twidth: 100vw !important;\r\n    \tmax-width: 100vw !important;\r\n\t\tborder-radius: 0;\r\n\t\tborder:0;\r\n\r\n\t\t/*display: flex;\r\n\t\tflex-direction: column;*/\r\n\r\n\t\t[data-action=\"panel-collapse\"],\r\n\t\t.js-panel-collapse,\r\n\t\t[data-action=\"panel-close\"],\r\n\t\t.js-panel-close  {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t/* make panel header bigger */\r\n\t\t.panel-hdr {\r\n\t\t\theight: $header-height;\r\n\t\t\tborder-radius: 0;\r\n\t\t\tbox-shadow: 0 0.125rem 0.125rem -0.0625rem  rgba(darken($primary-800, 10%), (10/100));\r\n\r\n\t\t\t/* make panel header bigger */\r\n\t\t\th2 {\r\n\t\t\t\tfont-size: rem($panel-hdr-font-size + 4);\r\n\t\t\t\tfont-weight:400;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.js-panel-locked {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t.btn-panel[data-action=\"panel-fullscreen\"],\r\n\t\t.js-panel-fullscreen {\r\n\t\t\twidth:$panel-hdr-height/2;\r\n\t\t\theight:$panel-hdr-height/2;\r\n\t\t}\r\n\r\n\t\t.panel-container {\r\n\t\t\tflex: 1;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tborder-radius: 0;\r\n\t\t\tdisplay: block !important; //incase user had it collapsed\r\n\t\t}\r\n\t}\r\n\r\n\t/* panel collapse */\r\n\t/*&.panel-collapsed:not(.panel-fullscreen) {\r\n\r\n\t\t.panel-container {\r\n\t\t\tdisplay:none;\r\n\t\t}\r\n\r\n\t}*/\r\n\r\n\t/* panel locked */\r\n\t&.panel-locked:not(.panel-fullscreen) {\r\n\r\n\t\t.js-panel-locked {\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.panel-hdr {\r\n\t\t\t//&:active {\r\n\r\n\t\t\t\th2:before {\r\n\t\t\t\t\tfont-family: 'nextgen-icons';\r\n\t\t\t\t\tcontent: \"\\f2ae\";\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tright: 5px;\r\n\t\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tfont-size: 1.1rem;\r\n\t\t\t\t\tcolor: $danger-500;\r\n\t\t\t\t}\r\n\t\t\t//}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&.ui-sortable-helper {\r\n\t\tbox-shadow: 0 5px 16px 0 rgba(0,0,0,0.05), 0 5px 20px 0 rgba(0,0,0,0.09);\r\n\t\t/*.panel-toolbar {\r\n\t\t\t-webkit-filter: grayscale(100%);  Safari 6.0 - 9.0 \r\n\t\t\tfilter: grayscale(100%);\r\n\t\t}*/\r\n\t}\r\n}\r\n\r\n/* panel tag can be used globally */\r\n.panel-tag {\r\n\tpadding: 1rem $panel-spacer-x;\r\n\tmargin-bottom: 2rem;\r\n\tborder-left: 3px solid $success-500;\r\n\tbackground: #eef7fd;\r\n\topacity: 0.8;\r\n\tfont-weight: 400;\r\n\tfont-size: 0.875rem;\r\n\tborder-radius: 0px 8px 8px 0px;\t\r\n\r\n\t//remove p tag margin\r\n\t>*:last-child,\r\n\t>*:only-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\t\r\n\r\n\t&:only-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n}\r\n\r\n/* panel header */\r\n.panel-hdr {\r\n\tdisplay: flex;\r\n\talign-items:center;\r\n\tbackground: $panel-hdr-background;\r\n\tmin-height: $panel-hdr-height;\r\n\t//@include box-sizing(border-box); //why did we need this?\r\n\t//border: 1px solid transparent;\r\n\tborder-bottom:  1px solid rgba(0, 0, 0, 0.07);\r\n\t//box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.05);\r\n\tborder-radius: $panel-edge-radius $panel-edge-radius 0 0;\r\n\r\n\t//@extend %common-animation-easeout;\r\n\r\n\t@include transition-background-color(0.4s, ease-out)\r\n\r\n\t.panel-collapsed & {\r\n\t\tborder-radius: $panel-edge-radius;\r\n\t}\r\n\t\r\n\t/* add padding to first and last child */\r\n\t> :first-child {\r\n\t\tpadding-left: $panel-spacer-x;\r\n\t}\r\n\t> :last-child {\r\n\t\tpadding-right: $panel-spacer-x;\r\n\t}\r\n\r\n\t/* adjusts title */\r\n\th2 {\r\n\t\tflex: 1;\r\n\t\tfont-size: rem($panel-hdr-font-size);\r\n\t\tmargin: 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items:center;\r\n\t\tline-height: $panel-hdr-height;\r\n\t\tcolor: inherit;\r\n\t\tcolor: #333;\r\n\t\tposition: relative;\r\n\r\n\t\tfont-weight: 500;\r\n\r\n\t\t&:not(:only-child) {\r\n\t\t\tmargin-right: $panel-spacer-x / 1.5;\r\n\t\t}\r\n\r\n\t\t> [class*='fw-'] {\r\n\t\t\tmargin-left: 4px;\r\n\t\t}\r\n\r\n\t\tsmall {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tmargin:0;\r\n\t\t\topacity: 0.8;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tfont-size: rem($panel-hdr-font-size - 2);\r\n\t\t\tmargin-left: $spacer * 0.5;\r\n\t\t}\r\n\r\n\t\t/* panel header icon */\r\n\t\t.panel-icon {\r\n\t\t\tmargin-right: $p-2;\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n.panel-hdr[class^=\"bg-\"], \r\n.panel-hdr[class*=\" bg-\"] {\r\n\th2 {\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n\r\n\r\n/* panel tap highlight */\r\n.panel-sortable:not(.panel-locked).ui-sortable-helper {\r\n\r\n\t&:active {\r\n\t\tborder-color: rgba($black,  0.15);\r\n\t}\r\n\r\n\t/*.panel-hdr {\r\n\t\t&:active {\r\n\t\t\tborder-top-color: rgba($primary-300, 0.7);\r\n\t\t\tborder-left-color: rgba($primary-500, 0.7);\r\n\t\t\tborder-right-color: rgba($primary-500, 0.7);\r\n\r\n\t\t\t& + .panel-container {\r\n\r\n\t\t\t\tborder-color: transparent rgba($primary-500, 0.7) rgba($primary-600, 0.7);\r\n\t\t\t}\r\n\t\t}\r\n\t}*/\r\n}\r\n\r\n/*.panel-sortable .panel-hdr:active,\r\n.panel-sortable .panel-hdr:active + .panel-container {\r\n\t@include transition-border(0.4s, ease-out);\r\n}*/\r\n\r\n.panel-sortable.panel-locked {\r\n\t/*.panel-hdr {\r\n\t\t&:active {\r\n\t\t\tborder-top-color: $danger-300;\r\n\t\t\tborder-left-color: $danger;\r\n\t\t\tborder-right-color: $danger;\r\n\r\n\t\t\t& + .panel-container {\r\n\t\t\t\tborder-color: transparent $danger $danger;\r\n\t\t\t}\r\n\t\t}\r\n\t}*/\r\n}\r\n\r\n/* panel toolbar (sits inside panel header) */\r\n.panel-toolbar {\r\n\tdisplay: flex;\r\n\talign-items:center;\r\n\r\n\t.btn-panel {\r\n\t\tmargin-left: $panel-btn-spacing;\r\n\r\n\t\tpadding: 0;\r\n\t\twidth: $panel-btn-size;\r\n\t\theight: $panel-btn-size;\r\n\r\n\t\t@include box-sizing(border-box);\r\n\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 0.8;\r\n\r\n\t\t@extend %common-animation;\r\n\r\n\t\t&:hover {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\r\n\t\t/* add default colors for action buttons */\r\n\t\t&[data-action=\"panel-collapse\"],\r\n\t\t&.js-panel-collapse {\r\n\t\t\tbackground: $success-500;\r\n\t\t}\r\n\r\n\t\t&[data-action=\"panel-fullscreen\"],\r\n\t\t&.js-panel-fullscreen {\r\n\t\t\tbackground: $warning-500;\r\n\t\t}\r\n\r\n\t\t&[data-action=\"panel-close\"],\r\n\t\t&.js-panel-close {\r\n\t\t\tbackground: $danger-500;\r\n\t\t}\r\n\r\n\t\t/*&:after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\tborder: 1px solid rgba($white, 0.6);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\twidth: calc(100% + 2px);\r\n\t\t\theight: calc(100% + 2px);\r\n\t\t\tright: -1px;\r\n\t\t\ttop: -1px;\r\n\t\t}*/\r\n\r\n\t}\r\n\r\n\t.btn-toolbar-master {\r\n\t\theight: $panel-hdr-height;\r\n\t\twidth:  1.826875rem; /* stop flickering bug due to cpu latency */\r\n\t\tborder-radius: 0;\r\n\t\tmargin-right: -$panel-spacer-x;\r\n\t\tborder-top-right-radius: 3px;\r\n\t\tmargin-left: 0.5rem;\r\n\t\tpadding: 0 13px;\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tfont-size: $panel-toolbar-icon;\r\n\t\tcolor: inherit;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t&[aria-expanded=\"true\"] {\r\n\t\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset\r\n\t\t}\r\n\r\n\t\t& + .dropdown-menu {\r\n\t\t\tright: 0;\r\n\t\t\ttop: $panel-hdr-height;\r\n\t\t\tleft: auto !important;\r\n\t\t\tmargin:0;\r\n\t\t\tborder-radius:0;\r\n\t\t\t//min-width: 150px;\r\n\t\t}\r\n\t}\r\n\r\n\t/* we curve the last button to make it seamless with panel's border radius */\r\n\t.btn-panel-flat:last-child {\r\n\t\tborder-top-right-radius: $border-radius - 1px;\r\n\t}\r\n\r\n}\r\n\r\n.panel-sortable:not(.panel-fullscreen):not(.panel-locked) .ui-sortable-handle {\r\n\tcursor: move;\r\n}\r\n\r\n/* placeholder */\r\n.panel-placeholder {\r\n\tbackground-color: $panel-placeholder-color;\r\n\tbox-sizing: border-box;\r\n\tmargin-bottom: $grid-gutter-width;\r\n\tborder-radius: $border-radius + 1;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\t//exprimental...\r\n\tborder: 1px solid transparent;\r\n\t/* placeholder border animation */\r\n\t&:before,\r\n\t&:after {\r\n\t\tcontent:\" \";\r\n\t\tbackground-image: url(data:image/gif;base64,R0lGODlhCAAIAJAAAAAAAP///yH/C05FVFNDQVBFMi4wAwEAAAAh+QQECgD/ACwAAAAACAAIAAACD4SDYZB6udpiaMJYsXuoAAAh+QQECgD/ACwAAAAACAAIAAACDYQRGadrzVRMB9FZ5SwAIfkEBAoA/wAsAAAAAAgACAAAAg8MDqGYaudeW9ChyOyltQAAIfkEBAoA/wAsAAAAAAgACAAAAg9MgGCXm+rQYtC0WGl9oQAAIfkEBAoA/wAsAAAAAAgACAAAAg+MgWCRernaYmjCWLF7qAAAIfkEBAoA/wAsAAAAAAgACAAAAg2MAwmna81UTAfRWeUsACH5BAQKAP8ALAAAAAAIAAgAAAIPRB6gmGrnXlvQocjspbUAACH5BAQKAP8ALAAAAAAIAAgAAAIPBIJhl5vq0GLQtFhpfaAAADs=);\r\n\t\tborder-radius: $border-radius + 1;\r\n\t\tposition: absolute;\r\n\t\ttop:0;\r\n\t\tright:0;\r\n\t\tbottom:0;\r\n\t\tleft:0;\r\n\t\topacity: 0.3;\r\n\t}\r\n\r\n\t&:before {\r\n\t\tbackground: $panel-placeholder-color;\r\n\t\tmargin: 1px;\r\n\t\tbox-sizing: border-box;\r\n\t\topacity: 1;\r\n\t\tz-index: 1;\r\n\t}\r\n}\r\n\r\n.mod-panel-clean {\r\n\r\n\t.panel-hdr {\r\n\t\tbackground: $white;\r\n\t\tbackground-image: linear-gradient(to bottom,#f7f7f7, $white);\r\n\t\tbox-shadow: none;\r\n\t\t\r\n\t\th2 {\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t/*.panel-container {\r\n\t\t.panel-content:first-child {\r\n\t\t\tpadding-top: $p-1;\r\n\t\t}\r\n\t}*/\r\n\t\r\n}\t\r\n\r\n@media only screen and ( max-width: 420px ){\r\n\t/* making mobile spacing a little narrow */\r\n\t.panel {\r\n\t\t.panel-hdr {\r\n\t\t\tfont-size: rem($panel-hdr-font-size);\r\n\t\t\t//color: #060606;\r\n\t\t\tmin-height: $panel-hdr-height - 4;\r\n\r\n\t\t\t> :first-child {\r\n\t\t\t\tpadding-left: 10px;\r\n\t\t\t}\r\n\t\t\t> :last-child {\r\n\t\t\t  \tpadding-right: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t.panel-toolbar .btn-toolbar-master {\r\n\t\t\t\tmargin-right: -10px;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.panel-container {\r\n\t\t\t.panel-content:first-child {\r\n\t\t\t\tpadding: 10px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* print only selected panel when on fullscreen */\r\n@media print {\r\n\t.panel-fullscreen {\r\n\t\t.subheader,\r\n\t\t.page-breadcrumb,\r\n\t\t.page-content .panel:not(.panel-fullscreen) {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t.panel-hdr,\r\n\t\t.panel-container,\r\n\t\t.panel-content,\r\n\t\th2 {\r\n\t\t\tborder: none;\r\n\t\t\tpadding: 0 !important;\r\n\t\t}\r\n\r\n\t\t.panel {\r\n\t\t\tmargin: 0;\r\n\t\t}\r\n\t}\r\n}", ".popover {\r\n\tbox-shadow: $dropdown-shadow;\r\n\tmargin: 12px;\r\n\r\n\t.arrow {\r\n\t\tborder-color: $popover-arrow-outer-color;\r\n\r\n\t}\r\n\r\n\t.popover-header {\r\n\t\tfont-weight: 500;\r\n\t\tfont-size: rem($popover-font-size);\r\n\t\tborder-radius: $popover-border-radius $popover-border-radius 0 0;\r\n\t\tborder-bottom-width:0px;\r\n\t}\r\n\r\n\t.popover-body {\r\n\t\tpadding: 0;\r\n\t}\r\n}\r\n\r\n\r\n.popover-body:not(:empty) {\r\n\tpadding: 0 $popover-header-padding-x $popover-header-padding-y;\r\n}\r\n\r\n.popover-header:empty + .popover-body {\r\n\tpadding-top: $popover-header-padding-y;\r\n}", ".progress-xs { height: 5px }\r\n.progress-sm { height: 8px }\r\n.progress-md { height: 14px }\r\n.progress-lg { height: 20px }\r\n.progress-xl { height: 30px }", "$app-shortcut-btn-size: 49px;\r\n\r\n$menu-item-size: 45px;\r\n$menu-items:5;\r\n$menu-grid-icon: 5px;\r\n$menu-item-direction: 'top'; //top or left\r\n\r\n%ball{\r\n  background:$primary-500;\r\n  border-radius:50%;\r\n  width:$menu-item-size;\r\n  height:$menu-item-size;\r\n  position:absolute !important;\r\n  padding:0;\r\n  right:0;\r\n  bottom:0;\r\n  color:$white !important;\r\n  text-align:center;\r\n  line-height:$menu-item-size;\r\n  transform:translate3d(0,0,0);\r\n  transition:transform ease-out 200ms;\r\n  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), \r\n              0 1px 2px rgba(0, 0, 0, 0.1);\r\n\r\n    &:hover {\r\n      background: $primary-700;\r\n    }\r\n}\r\n\r\n.shortcut-menu {\r\n  position:fixed;\r\n  right: $grid-gutter-width;\r\n  bottom: $footer-height + $grid-gutter-width;\r\n  z-index: $footer-zindex + 1; \r\n}\r\n\r\n.menu-open {\r\n  display:none;\r\n}\r\n\r\n.menu-item,\r\nlabel.menu-open-button {\r\n  @extend %ball;\r\n  font-size: 16px;\r\n}\r\n\r\nlabel.menu-open-button {\r\n  z-index: $footer-zindex + 2;\r\n  transition-timing-function: cubic-bezier(0.175, 0.885, 0.320, 1.275);\r\n  transition-duration: 400ms;\r\n  cursor:pointer;\r\n  margin:0;\r\n\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.app-shortcut-icon {\r\n  width: $menu-grid-icon;\r\n  height: $menu-grid-icon;\r\n  background: #ecf0f1;\r\n  color: #ecf0f1;\r\n  transition: .3s;\r\n  box-shadow: -8px -8px, 0 -8px, 8px -8px, -8px 0, 8px 0, -8px 8px, 0 8px, 8px 8px;\r\n}\r\n\r\n.menu-open:checked+.menu-open-button{\r\n  transition-timing-function: linear;\r\n  transition-duration: 200ms;\r\n  transform: scale(0.9,0.9) translate3d(0,0,0);\r\n  background: $fusion-500;\r\n\r\n  .app-shortcut-icon {\r\n    box-shadow: 0 -5px, 0 -8px, 5px 0, -5px 0, 8px 0, -8px 0, 0 8px, 0 5px !important;\r\n    -webkit-transform: rotate3d(0,0,1,-45deg) scale3d(.8,.8,.8);\r\n    transform: rotate3d(0,0,1,-45deg) scale3d(.8,.8,.8);\r\n  }\r\n}\r\n\r\n.menu-open:checked~.menu-item{\r\n  transition-timing-function:cubic-bezier(0.165, 0.840, 0.440, 1.000);\r\n\r\n    @if $menu-item-direction == 'top' {\r\n\r\n      @for $i from 1 through $menu-items{\r\n        &:nth-child(#{$i+2}){\r\n          transition-duration:50ms+(100ms*$i);\r\n          transform:translate3d(0,-($menu-item-size+3)*$i,0);\r\n        }\r\n      }\r\n\r\n    } @else {\r\n\r\n      @for $i from 1 through $menu-items{\r\n        &:nth-child(#{$i+2}){\r\n          transition-duration:50ms+(100ms*$i);\r\n          transform:translate3d(-($menu-item-size+3)*$i,0,0);\r\n        }\r\n      }\r\n    }\r\n}", "/* set base height for slider */\r\n.slide-on-mobile {\r\n     width: $slider-width;\r\n}\r\n\r\n@media only screen and ( max-width: $mobile-breakpoint-size ){\r\n    /* SIDE PANELS */\r\n    .slide-on-mobile {\r\n        @include translate3d(0,0,0);\r\n        z-index: $cloud;\r\n        position: absolute !important;\r\n        top:0;\r\n        bottom:0;\r\n\r\n        background-color: $gray-100; \r\n\r\n        transition: $nav-hide-animate;\r\n    }\r\n\r\n    .slide-backdrop {\r\n        background: transparent;\r\n        transition: background 300ms;\r\n    }\r\n\r\n    .slide-on-mobile-left {\r\n        //@include translate3d(-$slider-width,0,0);  /* issue with translate 3d with padding */\r\n        border-right: 1px solid rgba($black,0.09);\r\n\r\n        /* new solution */\r\n        left: -$slider-width;\r\n    }\r\n\r\n    .slide-on-mobile-left-show {\r\n        //@include translate3d(0,0,0);\r\n        left:0;\r\n    }\r\n\r\n    .slide-on-mobile-right  {\r\n        //@include translate3d(calc(100vw), 0, 0);  /* issue with translate 3d with padding */\r\n        border-left: 1px solid rgba($black,0.09);\r\n\r\n        right: -$slider-width;\r\n    }\r\n\r\n    .slide-on-mobile-right-show {\r\n        //@include translate3d(calc(100vw - #{$slider-width}), 0, 0);  /* issue with translate 3d with padding */\r\n        right: 0;\r\n    }\r\n\r\n    /* place the backdrop right after these classes */\r\n    .slide-on-mobile-right-show,\r\n    .slide-on-mobile-left-show {\r\n        & + .slide-backdrop {\r\n            background: rgba($black,0.09);\r\n            position: absolute;\r\n            z-index: $cloud - 2;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            top: 0;\r\n        }\r\n    }\t\r\n}", "/* bootstrap override table stripe */\r\n.table {\r\n\t&.table-striped:not(.table-bordered) {\r\n\t\tth, \r\n\t\ttbody th,\r\n\t\ttbody td {\r\n\t\t\t border: 0;\r\n\t\t}\r\n\t}\r\n\r\n\tthead[class^=\"bg-\"] tr > th,\r\n\tthead[class*=\" bg-\"] tr > th {\r\n\t\tborder-top: 0;\r\n\t\tborder-bottom: 0; \t\r\n\t}\r\n\r\n\ttr[class^=\"bg-\"] > td,\r\n\ttr[class^=\"bg-\"] > th,\r\n\ttr[class*=\" bg-\"] > td,\r\n\ttr[class*=\" bg-\"] > th {\r\n\t\tborder-top:0 !important;\r\n\t}\r\n}\r\n\r\n.thead-themed {\r\n\t@include gradient-img(#f2f2f2,#fafafa)\r\n}\r\n\r\n.table-dark {\r\n\t.thead-themed {\r\n\t\t@include gradient-img($fusion-700,$fusion-800)\r\n\t}\r\n}\r\n\r\n.table-bordered {\r\n\t&[class*=\" bg-\"],\r\n\t&[class*=\" bg-\"] td,\r\n\t&[class*=\" bg-\"] th,\r\n\t&[class^=\"bg-\"],\r\n\t&[class^=\"bg-\"] td,\r\n\t&[class^=\"bg-\"] th {\r\n\t\tborder: 1px solid rgba($white, 0.1);\r\n\t}\r\n\r\n\t[class*=\" bg-\"] td,\r\n\t[class*=\" bg-\"] th,\r\n\t[class^=\"bg-\"] td,\r\n\t[class^=\"bg-\"] th, {\r\n\t\tborder: 1px solid rgba($black, 0.1);\r\n\t}\r\n}\r\n/* table hover */\r\n.table-hover {\r\n\ttbody {\r\n\t\ttr {\r\n\t\t\t&:hover {\r\n\t\t\t\tbox-shadow: inset 1px 0 0 #dadce0, inset -1px 0 0 #dadce0, 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* reset table (global) */\r\nth {\r\n    font-weight: 500;\r\n}\r\n\r\n.table-clean {\r\n\tbackground: transparent;\r\n\tborder: none;\r\n\r\n\ttr,\r\n\ttd,\r\n\tth {\r\n\t\tborder: none;\r\n\t\tbackground: none;\r\n\t}\r\n}\r\n\r\n.table-scale-border-top {\r\n\tborder-top: 2px solid $fusion-500 !important;\r\n}\r\n.table-scale-border-bottom {\r\n\tborder-bottom: 2px solid $fusion-500 !important;\r\n}\r\n", "/* nav tabs panel */\r\n.nav-tabs-clean {\r\n\r\n\theight: $nav-tabs-clean-link-height;\r\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n\r\n\t.nav-item {\r\n\r\n\t\t.nav-link {\r\n\t\t\tborder-radius: 0;\r\n\t\t\tborder: 0;\r\n\t\t\theight: $nav-tabs-clean-link-height;\r\n\r\n\t\t\t/*font-size: rem($fs-md);\r\n\t\t\tfont-weight: 500;*/\r\n\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-bottom: 1px solid transparent;\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tborder-bottom: 1px solid $primary-500;\r\n\t\t\t\tcolor: $primary-500;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tcolor: $primary-500;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* hack for waves effect breaking tabs */\r\n/*.nav-tabs .nav-item > .nav-link.waves-effect {\r\n\theight: 100% !important;\r\n\tdisplay: block;\r\n}*/\r\n\r\n/* fontsize for tabs */\r\n.nav-tabs .nav-item .nav-link {\r\n\t&:not(:hover) {\r\n\t\tcolor: inherit;\r\n\t}\r\n\r\n\t&.active:not(:hover) {\r\n\t\tcolor: #333;\r\n\t}\r\n\t&:hover:not(.active) {\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n.nav .nav-link:not([class^=\"btn-\"]):not([class*=\" btn-\"]) {\r\n\tfont-weight: 500;\r\n\tfont-size: rem($fs-base);\r\n}\r\n\r\n.nav-tabs .nav-link.active, \r\n.nav-tabs .nav-item.show .nav-link {\r\n\tcolor: $primary-500;\r\n}", ".tooltip-inner {\r\n\tfont-family: $page-font;\r\n\tfont-weight: 500;\r\n\tbox-shadow: $box-shadow-sm;\r\n}\r\n/*.tooltip .arrow {\r\n\tdisplay: none;\r\n}*/", "/* height & width auto */\r\n.h-auto    { height: auto !important }\r\n.w-auto    { width: auto !important  }\r\n.min-height-reset { min-height: initial !important}\r\n.max-width-reset { max-width: none !important;}\r\n.max-height-reset { max-height: none !important;}\r\n\r\n/* width preset */\r\n.min-width-0 { min-width: 0 }\r\n\r\n.width-0   { width: 0}\r\n.width-1   { width: 1.5rem }\r\n.width-2   { width: 2rem }\r\n.width-3   { width: 2.5rem }\r\n.width-4   { width: 2.75rem }\r\n.width-5   { width: 3rem }\r\n.width-6   { width: 3.25rem }\r\n.width-7   { width: 3.5rem }\r\n.width-8   { width: 3.75rem }\r\n.width-9   { width: 4rem }\r\n.width-10  { width: 4.25rem }\r\n\r\n.width-xs  { min-width: 5rem;}\r\n.width-sm  { min-width: 10rem;}\r\n.width-lg  { min-width: 15rem;}\r\n.width-xl  { min-width: 20rem;}\r\n\r\n.height-0  { height: 0}\r\n.height-1  { height: 1.5rem }\r\n.height-2  { height: 2rem }\r\n.height-3  { height: 2.5rem }\r\n.height-4  { height: 2.75rem; }\r\n.height-5  { height: 3rem }\r\n.height-6  { height: 3.25rem }\r\n.height-7  { height: 3.5rem }\r\n.height-8  { height: 3.75rem }\r\n.height-9  { height: 4rem }\r\n.height-10 { height: 4.25rem }\r\n\r\n.height-xs  { min-height: 5rem;}\r\n.height-sm  { min-height: 10rem;}\r\n.height-lg  { min-height: 15rem;}\r\n.height-xl  { min-height: 20rem;}\r\n\r\n/* line-heights */\r\n.l-h-n { line-height:normal }\r\n\r\n/* no bg image */\r\n.bg-img-none { background-image: none !important;}\r\n\r\n/* flex */\r\n.flex-1 { flex:1 }\r\n\r\n/* margins */\r\n.m-g     { margin: map-get($grid-gutter-widths, xl) }\r\n.mb-g    { margin-bottom: map-get($grid-gutter-widths, xl) !important }\r\n.mb-gb   { margin-bottom:$grid-gutter-width-base }\r\n\r\n/* paddings */\r\n.p-g    { padding: map-get($grid-gutter-widths, xl) }\r\n\r\n/* text turncaters */\r\n.text-truncate-header {\tmax-width:  map-get($grid-gutter-widths, xl) + 4.5625rem }\r\n.text-truncate-xs {\tmax-width:  4.5625rem }\r\n.text-truncate-sm {\tmax-width: 117px }\r\n.text-truncate-md {\tmax-width: 160px; }\r\n.text-truncate-default { max-width: 180px }\r\n.text-truncate-lg {\tmax-width: 200px }\r\n\r\n/* blur text */\r\n.text-blur {\r\n\tcolor: transparent !important;\r\n\ttext-shadow: 0 0 5px rgba(0,0,0,0.5);\r\n}\r\na.text-blur {\r\n\ttext-shadow: 0 0 5px rgba($primary-500,0.5);\r\n}\r\n\r\n/* positions usage: \r\n   .position-absolute|relative|static|fixed .pos-top|left|right|bottom */\r\n.pos-top    { top: 0    }\r\n.pos-left   { left: 0   }\r\n.pos-right  { right: 0  }\r\n.pos-bottom { bottom: 0 }\r\n\r\n/* font weights */\r\n.fw-300  { font-weight: 300 !important }\r\n.fw-400  { font-weight: 400 !important }\r\n.fw-500  { font-weight: 500 !important }\r\n.fw-700  { font-weight: 700 !important }\r\n.fw-900  { font-weight: 900 !important }\r\n.fw-n    { font-weight:normal !important }\r\n\r\n/* font sizes */\r\n.fs-nano { font-size: rem($fs-nano) !important}        /* 10px */\r\n.fs-xs   { font-size: rem($fs-xs) !important}          /* 12px */\r\n.fs-sm   { font-size: rem($fs-sm) !important}          /* 12.5px */\r\n.fs-b    { font-size: rem($fs-base) !important} \t   /* 13px */\r\n.fs-md   { font-size: rem($fs-md) !important}          /* 14px */\r\n.fs-lg   { font-size: rem($fs-lg) !important}          /* 15px */\r\n.fs-xl   { font-size: rem($fs-xl) !important}          /* 16px */\r\n.fs-xxl  { font-size: rem($fs-xxl) !important}         /* page header */\r\n\r\n/* alphas */\r\n.opacity-5   { opacity: 0.05 }\r\n.opacity-10  { opacity: 0.1  }\r\n.opacity-15  { opacity: 0.15 }\r\n.opacity-20  { opacity: 0.2  }\r\n.opacity-25  { opacity: 0.25 }\r\n.opacity-30  { opacity: 0.3  }\r\n.opacity-35  { opacity: 0.35 }\r\n.opacity-40  { opacity: 0.4  }\r\n.opacity-45  { opacity: 0.45 }\r\n.opacity-50  { opacity: 0.5  }\r\n.opacity-55  { opacity: 0.55 }\r\n.opacity-60  { opacity: 0.6  }\r\n.opacity-65  { opacity: 0.65 }\r\n.opacity-70  { opacity: 0.7  }\r\n.opacity-75  { opacity: 0.75 }\r\n.opacity-80  { opacity: 0.8  }\r\n.opacity-85  { opacity: 0.85 }\r\n.opacity-90  { opacity: 0.9  }\r\n.opacity-95  { opacity: 0.95 }\r\n.opacity-100 { opacity: 1    }\r\n\r\n/* backgrounds */\r\n.bg-white { background-color: $white; color: $base-text-color}\r\n.bg-faded {\tbackground-color: $frame-border-color }\r\n.bg-offwhite-fade {\t@include gradient-img($white, lighten($color-fusion, 66%)) }\r\n.bg-subtlelight { background-color: lighten($color-primary, 44%) }\r\n.bg-subtlelight-fade { @include gradient-img($white, #f5fcff)\t}\r\n.bg-highlight { background-color: lighten($warning-50, 9%)}\r\n\r\n\r\n.bg-gray-50  { background-color: $gray-50;  }\r\n.bg-gray-100 { background-color: $gray-100; }\r\n.bg-gray-200 { background-color: $gray-200; }\r\n.bg-gray-300 { background-color: $gray-300; }\r\n.bg-gray-400 { background-color: $gray-400; }\r\n.bg-gray-500 { background-color: $gray-500; }\r\n.bg-gray-600 { background-color: $gray-600; }\r\n.bg-gray-700 { background-color: $gray-700; }\r\n.bg-gray-800 { background-color: $gray-800; }\r\n.bg-gray-900 { background-color: $gray-900; }\r\n\r\n/* borders */\r\n.border-faded { border: 1px solid rgba($fusion-900, 0.07) }\r\n.border-transparent {border: 1px solid transparent !important;}\r\n\r\n/* border radius */\r\n//.border-radius-0 { border-radius:0 !important }\r\n.border-top-left-radius-0  { border-top-left-radius: 0 !important;}\r\n.border-bottom-left-radius-0 { border-bottom-left-radius: 0 !important; }\r\n.border-top-right-radius-0 { border-top-right-radius: 0 !important; }\r\n.border-bottom-right-radius-0 { border-bottom-right-radius: 0 !important; }\r\n.rounded-plus { border-radius: $border-radius-plus }\r\n.rounded-bottom { border-radius: 0 0 $panel-edge-radius }\r\n.rounded-top { border-radius: $panel-edge-radius $panel-edge-radius 0 0; }\r\n\r\n/* progressbars */\r\n/*.progress-xs { height: 5px }\r\n.progress-sm { height: 8px }\r\n.progress-md { height: 14px }\r\n.progress-lg { height: 20px }\r\n.progress-xl { height: 30px }*/\r\n\r\n/* rotate */\r\n.rotate-90  { @include rotate (90)  }\r\n.rotate-180 { @include rotate (180) }\r\n.rotate-270 { @include rotate (270) }\r\n\r\n/* shadows */\r\n.shadow-0 { box-shadow: none !important}\r\n.shadow-1, .shadow-hover-1:hover { box-shadow: 0 2px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.10) }\r\n.shadow-2, .shadow-hover-2:hover { box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.1) }\r\n.shadow-3, .shadow-hover-3:hover { box-shadow: 0 0.1rem 0.15rem rgba(0, 0, 0, 0.125) }\r\n.shadow-4, .shadow-hover-4:hover { box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.125) }\r\n.shadow-5, .shadow-hover-5:hover { box-shadow: 0 0.125rem 0.325rem rgba(0, 0, 0, 0.175) }\r\n\r\n.shadow-inset-1, .shadow-hover-inset-1:hover { box-shadow: inset 0 .25rem .125rem 0 rgba(33, 37, 41, .025) }\r\n.shadow-inset-2, .shadow-hover-inset-2:hover { box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.04)  }\r\n.shadow-inset-3, .shadow-hover-inset-3:hover { box-shadow: inset 0 0.2rem 0.325rem rgba(0, 0, 0, 0.05)   }\r\n.shadow-inset-4, .shadow-hover-inset-4:hover { box-shadow: inset 0 0.25rem 0.5rem rgba(0, 0, 0, 0.06)   }\r\n.shadow-inset-5, .shadow-hover-inset-5:hover { box-shadow: inset 0 0.35rem 0.5rem rgba(0, 0, 0, 0.07)   }\r\n\r\n.shadow-sm-hover {\r\n\t&:hover {\r\n\t\tbox-shadow: $box-shadow-sm !important;\r\n\t}\r\n}\r\n.shadow-hover {\r\n\t&:hover {\r\n\t\tbox-shadow: $box-shadow !important;\r\n\t}\r\n}\r\n.shadow-lg-hover {\r\n\t&:hover {\r\n\t\tbox-shadow: $box-shadow-lg !important;\r\n\t}\r\n}\r\n\r\n\r\n/*.shadow-unique-hover {\r\n   @extend %shadow-hover;\r\n}*/\r\n\r\n\r\n/* hover any bg */\r\n/* inherits the parent background on hover */\r\n.hover-bg {\r\n\tbackground: $white;\r\n\ttransition: all .1s ease-in;\r\n\tcolor: inherit;\r\n\t\r\n\t&:hover {\r\n\t\tbackground: inherit;\r\n\t\tcolor: inherit;\r\n\t}\r\n}\r\n\r\n/* hover alpha effect */\r\n/* example found in the buttons page */\r\n/* example of use could be found inside panel buttons top right */\r\n.hover-effect-dot {\r\n\tposition: relative;\r\n\t&:before {\r\n\t\tcontent: \"\";\r\n\t\tbackground: rgba(0, 0, 0, 0.2);\r\n\t\twidth: 0%;\r\n\t\theight: 0%;\r\n\t\tdisplay: block;\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\tborder-radius: 100%;\r\n\t\ttransition: all 100ms ease-in;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\t&:before {\r\n\t\t\twidth: 75%;\r\n\t\t\theight: 75%;\r\n\t\t\ttop: 12%;\r\n\t\t\tleft: 12%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* hover light bg effect */\r\n.hover-highlight {\r\n\t&:hover {\r\n\t\tbackground-image: linear-gradient(rgba(29, 33, 41, .03), rgba(29, 33, 41, .04));\r\n\t}\r\n\t&:active {\r\n\t\tbackground-image: linear-gradient(rgba(29, 33, 41, .05), rgba(29, 33, 41, .06));\r\n\t}\r\n}\r\n\r\n/* hover and hide items on show */\r\n.show-child-on-hover {\r\n\t.show-on-hover-parent {\r\n\t   display:none; \r\n\t}\r\n\r\n\t&:hover {\r\n\t\t.show-on-hover-parent {\r\n\t\t\tdisplay: block; //changed from flex\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.hide-child-on-hover {\r\n\t.hide-on-hover-parent {\r\n\t   display: block; //changed from flex \r\n\t}\r\n\r\n\t&:hover {\r\n\t\t.hide-on-hover-parent {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* z-indexes */\r\n.z-index-space {z-index: $space}\r\n.z-index-cloud {z-index: $cloud}\r\n.z-index-ground {z-index: $ground}\r\n.z-index-water {z-index: $water}\r\n\r\n/* cursor \r\n\tusage: cursor-default;\r\n*/\r\n$cursor: (\r\n\tauto,\r\n\tcrosshair,\r\n\tdefault,\r\n\te-resize,\r\n\thelp,\r\n\tmove,\r\n\tn-resize,\r\n\tne-resize,\r\n\tnw-resize,\r\n\tpointer,\r\n\tprogress,\r\n\ts-resize,\r\n\tse-resize,\r\n\tsw-resize,\r\n\ttext,\r\n\tw-resize,\r\n\twait,\r\n\tinherit\r\n);\r\n\r\n@each $i in $cursor {\r\n  .cursor-#{nth($i, 1)} {\r\n\tcursor: nth($i, 1) !important; /* had to insert important since bootstrap has some peculiar classes */\r\n  }\r\n}\r\n\r\n/* states */\r\n.state-selected {\r\n\tbackground: lighten($info-500, 41%) !important;\r\n}\r\n\r\n/* collapse toggle to reveal and hide elements */\r\n[aria-expanded=\"false\"] {\r\n\t& ~ .collapsed-reveal {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.collapsed-reveal {\r\n\t\tdisplay: none;\r\n\t}\r\n\t& ~ .collapsed-hidden {\r\n\t\tdisplay: block;\r\n\t}\r\n\t.collapsed-hidden {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n}\r\n[aria-expanded=\"true\"] {\r\n\t& ~ .collapsed-reveal {\r\n\t\tdisplay: block;\r\n\t}\r\n\t.collapsed-reveal {\r\n\t\tdisplay: block;\r\n\t}\r\n\t& ~ .collapsed-hidden {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.collapsed-hidden {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n/* demo window */\r\n.demo-window {\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\toverflow: hidden;\r\n\tpadding-top: 23px;\r\n\tbox-shadow: 0 2px 10px rgba(0,0,0,0.12);\r\n\tuser-select: none;\r\n\r\n\t&:before,\r\n\t&:after,\r\n\t.demo-window-content:before,\r\n\t.demo-window-content:after {\r\n\t\tcontent: \"\";\r\n\t\tposition: absolute;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t&:before {\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 3;\r\n\t\theight: 23px;\r\n\t\tbackground: #e5e5e5;\r\n\t}\r\n\r\n\t&:after,\r\n\t.demo-window-content:before,\r\n\t.demo-window-content:after {\r\n\t\tleft: 10px;\r\n\t\tbackground: #ccc;\r\n\t\ttop: 6px;\r\n\t\tz-index: 4;\r\n\t\twidth: 11px;\r\n\t\theight: 11px;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.demo-window-content {\r\n\t\twidth: 100%;\r\n\r\n\t\timg {\r\n\t\t\tdisplay: block;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t&:before {\r\n\t\t\tleft: 26px;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tleft: 43px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* layout composed */\r\n.layout-composed {\r\n\t.page-content {\r\n\t\t.page-breadcrumb,\r\n\t\t.subheader {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\tpadding: 0 !important;\r\n\t}\r\n}\r\n\r\n/* responsive helpers */\r\n@media only screen and ( max-width: $mobile-breakpoint-size ){\r\n\r\n\t/* layout composed mobile only */\r\n\t.layout-composed-mobile {\r\n\t\t.page-content {\r\n\t\t\t.page-breadcrumb,\r\n\t\t\t.subheader {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t\tpadding: 0 !important;\r\n\t\t}\r\n\t}\t\r\n\r\n\t/* positions on mobile view */\r\n\t.position-on-mobile-absolute {\r\n\t\tposition: absolute !important;\r\n\t}\r\n\t.position-on-mobile-relative {\r\n\t\tposition: relative !important;\r\n\t}\r\n\t.position-on-mobile-static {\r\n\t\tposition: static !important;\r\n\t}\r\n\r\n\t/* RESET HEIGHTS */\r\n\t.height-mobile-auto {\r\n\t\theight: auto;\r\n\t\tmin-height: auto;\r\n\t\tmax-height: auto;\r\n\t}\r\n\r\n\t.width-mobile-auto {\r\n\t\twidth: auto;\r\n\t\tmin-width: auto;\r\n\t\tmax-width: auto; \r\n\t}\r\n\r\n\t/* FULL HEIGHT ON MOBILE */\r\n\t.expand-full-height-on-mobile {\r\n\t\theight: calc(100vh - #{$header-height}) !important;\r\n\t}\r\n\t.expand-full-width-on-mobile {\r\n\t\twidth: 100vw !important;\r\n\t\tmax-width: 100vw !important;\r\n\t}\r\n\r\n}\r\n\r\n/* row grid */\r\n.row-grid {\r\n  > {\r\n  \t.col, \r\n\t[class^=\"col-\"], \r\n\t[class*=\" col-\"], \r\n\t[class^=\"col \"], \r\n\t[class*=\" col \"], \r\n\t[class$=\" col\"], \r\n\t[class=\"col\"] {\r\n\t\tposition: relative;\r\n\t}\r\n\t.col:after, \r\n\t[class^=\"col-\"]:after, \r\n\t[class*=\" col-\"]:after, \r\n\t[class^=\"col \"]:after, \r\n\t[class*=\" col \"]:after, \r\n\t[class$=\" col\"]:after, \r\n\t[class=\"col\"]:after {\r\n\t\tcontent: \"\";\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\tleft: -1px;\r\n\t\tdisplay: block;\r\n\t\twidth: 0;\r\n\t\tborder-left: 1px solid rgba($fusion-900, 0.07);\r\n\t}\r\n\t.col:before, \r\n\t[class^=\"col-\"]:before, \r\n\t[class*=\" col-\"]:before, \r\n\t[class^=\"col \"]:before, \r\n\t[class*=\" col \"]:before, \r\n\t[class$=\" col\"]:before, \r\n\t[class=\"col\"]:before {\r\n\t\tcontent: \"\";\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\tbottom: -1px;\r\n\t\tleft: 0;\r\n\t\tdisplay: block;\r\n\t\theight: 0;\r\n\t\tborder-top: 1px solid rgba($fusion-900, 0.07);\r\n\t}\r\n  }\r\n\toverflow: hidden;\r\n}\r\n", "/* List table */\r\n.list-table {\r\n\theight: auto;\r\n\tdisplay:table;\r\n\tmargin: 0;\r\n\tpadding:0;\r\n\t> li {\r\n\t\tdisplay: table-cell;\r\n\t\tvertical-align: middle;\r\n\t\tposition: relative;\r\n\t\tpadding:0;\r\n\r\n\t\t&.search {\r\n\t\t\tposition: static;\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\n/* mostly used for nav items */\r\n.disabled:not(.btn),\r\n.disabled:not(.btn) > * {\t\r\n\tfilter: grayscale(80%);\r\n\topacity: 0.80;\r\n\tcursor: not-allowed;\r\n\r\n\tul,\r\n\t.collapse-sign {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\nul.list-verticle {\r\n\tmargin:0;\r\n\tpadding:0;\r\n\tlist-style: none; \r\n}\r\n\r\n.show {\r\n\t& > .dropdown-menu-animated {\r\n\t\t@include transform( scale(1) !important);\r\n\t\t@include transform-origin( 29px -50px )\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t}\r\n}\r\n\r\nhr {\r\n\tborder: none;\r\n\tborder-bottom: 1px dashed #eee;\r\n\r\n\t&.hr-xl {\r\n\t\tmargin: 3rem 0;\r\n\t}\r\n}\r\n\r\n\r\n.bg-trans-gradient {\r\n\tbackground: -webkit-linear-gradient(250deg, desaturate($info-500, 25%), desaturate($primary-500, 10%));\r\n\tbackground: linear-gradient(250deg, desaturate($info-500, 25%), desaturate($primary-500, 10%));\r\n}\r\n\r\n.bg-brand-gradient {\r\n\t@extend %nav-bg;\r\n}\r\n\r\n/* custom scroll */\r\n.custom-scroll,\r\n.custom-scrollbar {\r\n\t@extend %custom-scroll;\r\n}\r\n\r\n/* table no border */\r\n.table-border-0 {\r\n\tth,\r\n\ttd {\r\n\t\tborder:0 !important;\r\n\t}\r\n}\r\n\r\n/* table calendar */\r\n.table-calendar {\r\n\r\n\ttable-layout:fixed;\r\n\r\n\tth {\r\n\t\tborder: 0px !important;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\ttr {\r\n\t\ttd:first-child {\r\n\t\t\tborder-left:0;\r\n\t\t}\r\n\t\ttd:last-child {\r\n\t\t\tborder-right:0;\r\n\t\t\tpadding-right:10px;\r\n\t\t}\r\n\t}\r\n\r\n\ttd, th {\r\n\t\ttext-align: right;\r\n\t\tvertical-align: top;\r\n\t\tpadding: 5px 8px;\r\n\t\tposition: relative;\r\n\t}\r\n}\r\n\r\n/* list spaced */\r\n.list-spaced {\r\n\tli {\r\n\t\tmargin-top: 7px;\r\n\t\tmargin-bottom: 7px;\r\n\r\n\t\t&:first-child {\r\n\t\t\tmargin-top:0;\r\n\t\t}\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom:0;\r\n\t\t}\r\n\t}\r\n}\r\n.list-spaced {\r\n\t> li {\r\n\t\tpadding: 0 0 $spacer * 0.2;\r\n\t}\r\n}\r\n\r\n/* profile images */\r\n.profile-image {\r\n\twidth: $profile-image-width;\r\n\theight: $profile-image-width;\r\n}\r\n\r\n.profile-image-md {\r\n\twidth: $profile-image-width-md;\r\n\theight: $profile-image-width-md;\r\n}\r\n\r\n.profile-image-sm {\r\n\twidth: $profile-image-width-sm;\r\n\theight: $profile-image-width-sm;\r\n}\r\n\r\n/* image share */\r\n.img-share {\r\n\twidth: auto;\r\n\theight: $image-share-height;\r\n}\r\nspan.img-share {\r\n\twidth: $image-share-height + ($image-share-height * 0.4670);\r\n\theight: $image-share-height;\r\n}\r\n\r\n.notes {\r\n\tpadding: 5px;\r\n\tbackground: #f9f4b5;\r\n}\r\n\r\n\r\n/*\r\n.shadow-2 {\r\n  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);\r\n}\r\n.shadow-3 {\r\n  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);\r\n}\r\n.shadow-4 {\r\n  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);\r\n}\r\n.shadow-5 {\r\n  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);\r\n}*/\r\n\r\n/* disclaimer class */\r\n.disclaimer {\r\n\tpadding-left: 10px;\r\n\tfont-size: rem($fs-base - 5);\r\n\tcolor: #a2a2a2;\r\n\tletter-spacing: 1px;\r\n\ttext-transform: uppercase;\r\n\tfont-style: italic;\r\n}\r\n\r\n/* horizontal scrolling */\r\n.scrolling-wrapper {\r\n\tdisplay: flex;\r\n\tflex-wrap: nowrap;\r\n\toverflow-x: auto;\r\n\r\n\t-webkit-overflow-scrolling: touch;\r\n\r\n  .card {\r\n    flex: 0 0 auto;\r\n  }\r\n\t&::-webkit-scrollbar {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n\r\n/* online status */\r\n.status {\r\n\tposition: relative;\r\n\r\n\t&:before {\r\n\t\tcontent: \" \";\r\n\t\tposition: absolute;\r\n\t\twidth: 15px;\r\n\t\theight: 15px;\r\n\t\tdisplay: block;\r\n\t\ttop: -2px;\r\n\t\tright: -2px;\r\n\t\tbackground: $fusion-500;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2px solid #fff;\r\n\t}\r\n\r\n\t&.status-sm:before {\r\n\t\twidth: 10px;\r\n\t\theight: 10px;\t\r\n\t\tborder-width: 1px;\r\n\t\ttop:0;\r\n\t\tright:0;\r\n\t}\r\n\r\n\t&.status-success:before {\r\n\t\tbackground: $success-500;\r\n\t}\r\n\r\n\t&.status-danger:before {\r\n\t\tbackground: $danger-500;\r\n\t}\r\n\r\n\t&.status-warning:before {\r\n\t\tbackground: $warning-500;\r\n\t}\r\n}\r\n\r\n/* containers */\r\n.container {\r\n\r\n\t&.container-sm {\r\n\t\tmax-width: map-get($grid-breakpoints, sm)\r\n\t}\r\n\r\n\t&.container-md {\r\n\t\tmax-width: map-get($grid-breakpoints, md)\r\n\t}\r\n\r\n\t&.container-lg {\r\n\t\tmax-width: map-get($grid-breakpoints, lg)\r\n\t}\r\n\r\n}\r\n\r\n/* responsive visibility */\r\n/* https://getbootstrap.com/docs/3.4/css/#responsive-utilities */\r\n@each $bp in map-keys($grid-breakpoints) {\r\n  .hidden-#{$bp}-up {\r\n\t@include media-breakpoint-up($bp) {\r\n\t  display: none !important;\r\n\t}\r\n  }\r\n  .hidden-#{$bp}-down {\r\n\t@include media-breakpoint-down($bp) {\r\n\t  display: none !important;\r\n\t}\r\n  }\r\n}\r\n\r\n/* display frame */\r\n.frame-heading {\r\n\tfont-size: rem($fs-base);\r\n\tmargin-bottom: 1rem;\r\n\tcolor: lighten($fusion-50, 7%);\r\n\tfont-weight: 500;\r\n\t\r\n\tsmall {\r\n\t\tfont-size: rem($fs-base);\r\n\t\tmargin-bottom: 0.5rem;\r\n\t}\r\n}\r\n.frame-wrap {\r\n\tbackground: white;\r\n\tpadding: 0;\r\n\tmargin-bottom: 3rem;\r\n}\r\n* > .frame-wrap:last-child {\r\n\tmargin-bottom:0 !important;\r\n}\r\n\r\n/* time stamp */\r\n.time-stamp {\r\n\tfont-size: rem($fs-sm);\r\n\tmargin: $p-1 0 0 0;\r\n\tcolor: $fusion-200;\r\n\tfont-weight: 300;\r\n}\r\n\r\n/* data-hasmore */\r\n[data-hasmore] {\r\n\tposition: relative;\r\n\tcolor: $white;\r\n\t&:before {\r\n\t\tcontent: attr(data-hasmore);\r\n\t\tborder-radius: inherit;\r\n\t\tbackground: rgba($black, 0.4);\r\n\t\theight: inherit;\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-weight: 500;\r\n\t\tfont-size: inherit;\r\n\t}\r\n}\r\n\r\n/* code */\r\ncode {\r\n\tbackground: lighten(desaturate($primary-800, 60%), 56%);\r\n\tpadding: 4px 7px;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n\r\n/* star checkbox */\r\n.star {\r\n    visibility:hidden;\r\n    font-size:1.5em;\r\n    cursor:pointer;\r\n}\r\n.star:before {\r\n   content: \"☆\";\r\n   position: absolute;\r\n   visibility:visible;\r\n}\r\n.star:checked:before {\r\n   content: \"★\";\r\n   position: absolute;\r\n}", ".shadow-top,\r\n.shadow-bottom,\r\n.mobile-view-activated.header-function-fixed .page-header,\r\n.mobile-view-activated.header-function-fixed .page-footer {\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\theight: 6px;\r\n\t\tposition: absolute;\r\n\t\tbackground-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPYAAAAICAMAAAD9VPKTAAAATlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADEoqZZAAAAGnRSTlMACRVXPCMeNMEsdZ98SZBDbFDIrZhkXreCiM2g9MAAAAD1SURBVDjLzZHbDoIwEAVdEC94raLi//+otD1Lu4tgCWqYhPC0J5np4jeQ+2gKYWZukCLrYTWKvpVOl/9AAeVUMNuWtWA5AnkZFgtGlaHA901jTeEYa50cB4/x5A6Tp2H478GU342ziBJRBsqmNIAqaVWIwpMVW7l7w81y9pSS/QdKCUbcoF1GEMTgDm0ETqALpPraA6nLskKVRWEZzOq6fjYcQQV2CVSAb+1OMxeaoANHEAk4gNQn6A+/sBDu+kayrApPKMLuCh6ezQhwwgvogRSowAmiAG/ttXyPs35lLW0MpNOstfJlEK2e5g1xY7S4fnUPzF+TRjAMoku43AAAAABJRU5ErkJggg==);\r\n\t -webkit-background-size: cover;\r\n\t    -moz-background-size: cover;\r\n\t      -o-background-size: cover;\r\n\t         background-size: cover;\r\n\t\ttop: -5px;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\topacity: 0.06;\t\t    \t\r\n\t}\r\n}\r\n\r\n.shadow-bottom,\r\n.mobile-view-activated.header-function-fixed .page-header {\r\n\t&:after {\r\n\t\ttop: auto;\r\n\t\tbottom: -5px;\r\n\t\tbackground-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPYAAAAICAMAAAD9VPKTAAAANlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC3dmhyAAAAEnRSTlMACRUgPnjDV51MNCxhkIVsrbfUWo5iAAAA/UlEQVQ4y8zR7WqEMBCF4Xy4HeNos73/m605M+HQlIB2oeyjP5XwngQTXTLZqOp2WqCAiOxQa32Yz+Zo1u5pvpqPP8CPT7N2R4PDHqbWuoOIFFhgO6lqNslEFwaoHsKV4cXCpZl3H+z2cLidjGjozfNqaX5F6xANYSKO8aDjtXMAqJwAK8Dh1hXvNf41oJKdUL2VsZNeBp8Pii+Inp9cdsoBTHECe1fN4yXV7J1AYSpjUesSeyHcF8EXcLlT2HwFKj+IKXJNYRwt4LZGITO1i8Dc10VKjjNwCa5Byw0beR8bmUmRwv+IgzSRb0kTcRDezfcYwRhEUAKgxtAEAACGmyM6KW9inQAAAABJRU5ErkJggg==)\r\n\t}\r\n}\r\n\r\n.shadow-bottom-line,\r\n.shadow-top-line {\r\n\tposition: relative;\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\twidth: 100%;\r\n\t\theight: 5px;\r\n\t\tposition: absolute;\r\n\t\tbottom: -5px;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\t@include gradient-img(rgba($fusion-500, 0.09), transparent)\r\n\r\n\t\t/*background-image: -webkit-linear-gradient(top, rgba($fusion-500, 0.09), transparent);\r\n\t\tbackground-image: linear-gradient(top, rgba($fusion-500, 0.09), transparent);*/\r\n\t}\r\n}\r\n\r\n.shadow-top-line {\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\ttop: -5px;\r\n\r\n\t\t@include gradient-img(transparent, rgba($fusion-500, 0.09))\r\n\r\n\t\t/*background-image: -webkit-linear-gradient(top, transparent, rgba($fusion-500, 0.09));\r\n\t\tbackground-image: linear-gradient(top, transparent, rgba($fusion-500, 0.09));*/\r\n\t}\t\r\n}\r\n\r\n/* press animation */\r\n.press-scale-down {\r\n\t//transition: transform 0.2s ease;\r\n\r\n\t@include transition (0.2s,ease)\r\n\r\n\t&:active {\r\n\t\t@include scale(0.95);\r\n\t}\r\n}\r\n\r\n.hover-white {\r\n\t&:hover {\r\n\t\tbackground-image: linear-gradient(rgba(29,33,41,.03),rgba(29,33,41,.04));\r\n\t}\r\n\t&:active {\r\n\t\tbackground: darken(lighten($black, 95.5%), 5%);\r\n\t}\r\n}", "/*\r\n\r\n\tDOC: In Bootstrap there is a small snippet added by the team for IE10 in windows 8 the \r\n\tfollowing comments by the author states:\r\n\r\n\t\tIE10 in Windows (Phone) 8\r\n\r\n\t\tSupport for responsive views via media queries is kind of borked in IE10, for\r\n\t\tSurface/desktop in split view and for Windows Phone 8. This particular fix\r\n\t\tmust be accompanied by a snippet of JavaScript to sniff the user agent and\r\n\t\tapply some conditional CSS to *only* the Surface/desktop Windows 8. Look at\r\n\t\tour Getting Started page for more information on this bug.\r\n\r\n\t\tFor more information, see the following:\r\n\r\n\t\tIssue: https://github.com/twbs/bootstrap/issues/10497\r\n\t\tDocs: http://getbootstrap.com/getting-started/#support-ie10-width\r\n\t\tSource: http://timkadlec.com/2013/01/windows-phone-8-and-device-width/\r\n\t\tSource: http://timkadlec.com/2012/10/ie10-snap-mode-and-responsive-design/\r\n\r\n\t\t@-ms-viewport {\r\n\t\twidth: device-width;\r\n\t\t}\r\n\r\n\tSolution: \r\n\thttp://msdn.microsoft.com/en-us/library/ie/hh771902(v=vs.85).aspx\r\n\tWe add the following instead:\r\n\r\n*/\r\nbody.desktop-detected {\r\n    -ms-overflow-style: scrollbar;\r\n}\r\n\r\n/* Reset elms pos when js-waves-off is used */\r\n/*.js-waves-off {\r\n\tposition: relative;\r\n    overflow: hidden;\r\n    user-select: none;\r\n    z-index: 0;\r\n}*/\r\n\r\n/*.btn {\r\n\tbox-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), \r\n\t\t\t\t0 1px 2px rgba(0, 0, 0, 0.1);\r\n\r\n\t&:hover,\r\n\t&:active {\r\n\t\tbox-shadow: none;\r\n\t}\r\n}*/\r\n\r\n/* change the white to any color ;) */\r\ninput:-webkit-autofill {\r\n    -webkit-box-shadow: 0 0 0px 1000px white inset;\r\n    -webkit-text-fill-color: inherit !important;\r\n}\r\n/* select background */\r\n::selection {\r\n  background: $color-fusion;\r\n  color: $white;\r\n}\r\n::-moz-selection {\r\n  background: $color-fusion;\r\n  color: $white;\r\n}\r\n/* remove dotted line from focus */\r\ninput:focus,\r\nselect:focus,\r\ntextarea:focus,\r\nbutton:focus {\r\n    outline: none;\r\n}\r\n/* IE input clear field \"X\" input remove */\r\n::-ms-clear {\r\n  width : 0;\r\n  height: 0;\r\n}\r\n/* links */\r\na { text-decoration: none !important }\r\n/* touch action */\r\na, area, button, [role=\"button\"], input, label, select, summary, textarea {\r\n    touch-action: manipulation;\r\n}\r\na[target]:not(.btn){\r\n\tfont-weight:500;\r\n    text-decoration-skip-ink: auto;\r\n    text-decoration: underline !important;\r\n}\r\n/* btn active */\r\n/*.btn.active, \r\n.btn:active {\r\n    background-image: none;\r\n    outline: 0;\r\n    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);\r\n    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);\r\n}*/\r\n/* dot bullet */\r\n.dot {\r\n\t/*width: 4px;\r\n\theight: 4px;\r\n\tdisplay: inline-block;\r\n\tline-height: 0;\r\n\tborder-radius: 100%;*/\r\n\tfont-size: 4px !important;\r\n\tmargin-right: $p-2 !important;\r\n\tmargin-left:  -8px !important;\r\n}\r\n/* forms */\r\n/* fix alignment for custom controls */\r\n/*.custom-control {\r\n\tdisplay: flex;\r\n    align-items: center;\r\n}*/\r\nselect.custom-select {\r\n    -webkit-appearance: none;\r\n    -moz-appearance: none;\r\n    text-indent: 1px;\r\n    text-overflow: '';\r\n}\r\nselect.custom-select::-ms-expand {\r\n    display: none;\r\n}\r\n/* bootstrap modal remove padding */\r\n/* you need to disable this if you do not plan on using _addon-custom-scrollbar.scss */\r\nbody:not(.mod-main-boxed):not(.mobile-view-activated).chrome.modal-open {\r\n\tpadding-right: 8px !important;\r\n}\r\nbody:not(.mobile-view-activated).mod-main-boxed.modal-open {\r\n\tpadding-right: 0px !important;\r\n}\r\n\r\n/* hover adjustment for close buttons */\r\n.close:not(:disabled):not(.disabled):hover, \r\n.close:not(:disabled):not(.disabled):focus {\r\n    color: inherit;\r\n}\r\n\r\n/* add borders to button groups */\r\n.btn-group .btn:not([class*=\"btn-outline-\"]):not(.btn-icon):not(.btn-light) {\r\n\tborder-right: 1px solid rgba($black, 0.1);\r\n\tborder-left: 1px solid rgba($black, 0.1);\r\n}\r\n\r\n.input-group-prepend .btn:not([class*=\"btn-outline-\"]):not(.btn-icon):not(:first-child) {\r\n\tborder-left: 1px solid rgba($black, 0.1);\r\n}\r\n.input-group-append .btn:not([class*=\"btn-outline-\"]):not(.btn-icon):not(:first-child) {\r\n\tborder-left: 1px solid rgba($black, 0.1);\r\n}\r\n\r\n.btn-group-vertical .btn:not([class*=\"btn-outline-\"]):not(:first-child),\r\n.btn-group-vertical .btn-group {\r\n\tborder-top: 1px solid rgba($black, 0.1);\r\n}", "@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\r\n    /* IE10+ CSS styles go here */\r\n    .text-gradient {\r\n    \tbackground: transparent;\r\n    }\r\n\r\n    .nav-function-minify:not(.nav-function-top) .primary-nav .nav-menu > li > a + ul:before {\r\n    \tleft: -0.25rem !important;\r\n    }\r\n\r\n    .ie-only {\r\n    \tdisplay: inline-block !important;\r\n    }\r\n\r\n\t/* table hover */\r\n\t.table-hover {\r\n\t\ttbody {\r\n\t\t\ttr {\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbox-shadow:none;\r\n\t\t\t\t\tbackground-color: lighten($warning-50, 9%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/*  Counters IE bug\r\n\t\tDOC: If you've ever used CSS transitions on structural elements on your page, \r\n\t\tyou may have noticed a case where a transition occur immideately after page load.\r\n\t\tAs a result the user may experience the illusion of \"broken page elements\"\r\n\r\n\t\tBelow solution insures that pace's indication of \"everything loaded\" before applying the CSS transitions\r\n\t*/\r\n\t.pace-running .page-sidebar,\r\n\t.pace-running .page-sidebar *,\r\n\t.pace-running .page-content-wrapper {\r\n\t\t-webkit-transition: none !important;\r\n\t\t-moz-transition: none !important;\r\n\t\t-ms-transition: none !important;\r\n\t\t-o-transition: none !important;\r\n\t}\t\r\n\t// encounters BUG when .nav-function-minified & .mod-main-boxed\r\n}\r\n\r\n@supports (-ms-accelerator:true) {\r\n\r\n}", "/********************************************************\r\n\t\t\t\t\tRESPONSIVE REFERENCES\r\n\r\n.col-xs-\t.col-sm-\t.col-md-\t.col-lg-\t.col-xl-\r\n<544px\t\t≥544px\t\t≥768px\t\t≥992px\t\t≥1200px\r\n\r\n$grid-breakpoints: (\r\n  // Extra small screen / phone\r\n  xs: 0,\r\n  // Small screen / phone\r\n  sm: 544px,\r\n  // Medium screen / tablet\r\n  md: 768px,\r\n  // Large screen / desktop\r\n  lg: 992px,\r\n  // Extra large screen / wide desktop\r\n  xl: 1200px\r\n) !default;\r\n\r\n*********************************************************/\r\n\r\n@include media-breakpoint-up(lg) {\r\n\t.page-header {\r\n\t\tpadding: 0 $header-inner-padding-x;\r\n\t}\r\n\t.page-content {\r\n\t\tpadding: 1.5rem 2rem;\r\n\r\n\t\t.panel {\r\n\t\t\tmargin-bottom: 1.5rem;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/*@include media-breakpoint-up(xl) {\r\n\t.page-header {\r\n\t\tpadding: 0 $header-inner-padding-x;\r\n\t}\r\n\t.page-content {\r\n\t\tpadding: 1.5rem 2rem;\r\n\r\n\t\t.card.panel {\r\n\t\t\tmargin-bottom: 1.5rem;\r\n\t\t}\r\n\t}\r\n}*/\r\n\r\n\r\n\r\n@media only screen and ( max-width: $mobile-breakpoint-size ){\r\n\r\n\t//casuing some issues so disabled it.\r\n\t/*html {\r\n\t\tfont-size: 18px;\r\n\t}*/\r\n\r\n\t.dropdown-menu .dropdown-menu {\r\n\t\tposition: static;\r\n\t\t//display: block;\r\n\t\t//margin: 0;\r\n\t\tpadding: .5rem 0 0 .75rem;\r\n\t\twidth: 100%;\r\n\t\tborder: 0;\r\n\t\tbox-shadow: none;\r\n\r\n\t\t@include transform( scale(1) );\r\n\t\topacity: 1;\r\n\t\t//visibility: visible;\r\n\t\ttransition: none;\r\n\t}\r\n\r\n\t.show .dropdown-menu .dropdown-menu {\r\n\t\tvisibility: visible;\r\n\t}\r\n\r\n\t.dropdown-menu .dropdown-multilevel > .dropdown-item:first-child:after {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\tbody {\r\n\t\toverflow-x: hidden;\r\n\t}\t\r\n\r\n\t.page-logo-text {\r\n\t\tfont-size: 1rem;\r\n\t}\r\n\r\n\t.page-content-overlay {\r\n\t\tbackground: transparent;\r\n\t\ttransition: background 300ms;\r\n\t\tposition: fixed;\r\n\t\tz-index: $depth-nav - 1;\r\n\t}\r\n\t\r\n\t.page-wrapper {\r\n\t\tpadding-left:0;\r\n\t\tbackground: $white;\r\n\r\n\t\t.page-header {\r\n\t\t\tpadding:0 $grid-gutter-width-base/2;\r\n\t\t\twidth:100%;\r\n\t\t\tborder-bottom: 1px solid rgba($black,0.09);\r\n\r\n\t\t\t[data-toggle=\"dropdown\"] + .dropdown-menu {\r\n\t\t\t\tright: 1.5rem;\r\n\t\t\t}\r\n\t\r\n\t\t}\r\n\r\n\t\t.page-sidebar {\r\n\t\t\tz-index: $space + 1000;\r\n\t\t\ttransition: $nav-hide-animate;\r\n\t\t\t@include translate3d (-$nav-width, 0, 0);\r\n\r\n\t\t\tposition: fixed !important;\r\n\t\t\ttop:0;\r\n\t\t\tbottom:0;\r\n\r\n\t\t\t.primary-nav {\r\n\t\t\t\toverflow: auto;\r\n\t\t\t\toverflow-x: hidden;\r\n\t\t\t\t-webkit-overflow-scrolling: touch;\r\n\t\t\t\theight: unquote(\"calc(100% - #{$header-height + $footer-height})\");\r\n\r\n\t\t\t\t.nav-menu {\r\n\t\t\t\t\t.dl-ref {\r\n\t\t\t\t\t\tvertical-align: text-top;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-content {\r\n\t\t\tpadding: map-get($grid-gutter-widths, lg) $grid-gutter-width-base/2;\r\n\t\t\tcolor: #222;\r\n\t\t\tfont-size: $fs-base + 1;\r\n\r\n\t\t\tmin-height: calc(100vh - #{$header-height + $footer-height});\r\n\r\n\t\t\t.breadcrumb > .breadcrumb-item {\r\n\t\t\t\tmax-width: $page-breadcrumb-maxwidth/2 - 20px;\r\n\t\t\t}\r\n\r\n\t\t\t.subheader {\r\n\t\t\t\tmargin-bottom: 1.5rem;\r\n\r\n\t\t\t\t.subheader-title {\r\n\t\t\t\t\tline-height: 32px;\r\n\t\t\t\t\tfont-weight: 300;\r\n\t\t\t\t\tcolor: #22282d;\r\n\r\n\t\t\t\t\tsmall {\r\n\t\t\t\t\t\tfont-size: 68%;\r\n\t\t\t\t\t\tletter-spacing: normal;\r\n\t\t\t\t\t\tmargin-top: 0px;\r\n\t\t\t\t\t\tcolor: #181c21;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\twidth: calc(100% - 30px);\r\n\t\t\t\t\t\tfont-weight: 300;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.p-g {\r\n\t\t\t\tpadding: map-get($grid-gutter-widths, xs);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-footer {\r\n\t\t\tborder-top: 1px solid rgba($black,0.09);\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&.header-function-fixed {\r\n\r\n\t\t.page-header {\r\n\t\t\tmargin-left: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tposition: fixed;\r\n\t\t\tright: 0;\r\n\t\t\ttop: 0;\r\n\t\t    \r\n\t\t    transition: $nav-hide-animate;\r\n\t\t}\r\n\r\n\t\t.page-header,\r\n\t\t.page-logo {\r\n\t\t\tbox-shadow: none !important;\r\n\r\n\t\t} \r\n\t\t\r\n\t\t/* this was conflicting with the new DOM change where we swtiched header with nav */\r\n\t\t/*&:not(.nav-function-fixed) {\r\n\r\n\t\t\t.page-sidebar {\r\n\t\t\t\t.page-logo {\r\n\t\t\t\t\tposition: absolute !important;\r\n\t\t\t\t\ttop:0px !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}*/\r\n\r\n\t\t.page-content {\r\n\t\t\tmargin-top: $header-height;\r\n\t\t}\r\n\t}\r\n\r\n\t/* Push content */\r\n\t&.nav-mobile-push:not(.nav-mobile-slide-out) {\r\n\r\n\t\t.page-wrapper {\r\n\t\t\t.page-sidebar {\r\n\t\t\t\t@include translate3d (-$nav-width, 0, 0);\r\n\t\t\t}\r\n\t\t\t.page-header,\r\n\t\t\t.page-content,\r\n\t\t\t.page-footer,\r\n\t\t\t.page-footer-push {\r\n\t\t\t\ttransition: $nav-hide-animate;\r\n\t\t\t\t//@include translate3d (0px, 0, 0);\r\n\t\t\t}\t\t\r\n\t\t}\r\n\t\t\r\n\t}\r\n\r\n\t/* Off canvas */\r\n\t&.nav-mobile-slide-out {\r\n\r\n\t\tmin-height: 100vh !important; /*new*/\r\n\r\n\t\t.page-wrapper {\r\n\t\t\t.page-sidebar {\r\n\t\t\t\tz-index: 0;\r\n\t\t\t\ttransition:none;\r\n\t\t\t\t@include translate3d (0px, 0, 0);\t\r\n\t\t\t}\r\n\r\n\t\t\t.page-header,\r\n\t\t\t.page-content,\r\n\t\t\t.page-footer,\r\n\t\t\t.page-footer-push {\r\n                transition: $nav-hide-animate;\r\n\t\t\t    @include translate3d (0, 0, 0);        \t\t      \r\n\t\t\t}\r\n\r\n\t\t\t.page-content {\r\n\t\t\t\t/* min-height: calc(100vh - #{$header-height + $footer-height});  no longer needed here */\r\n\t\t\t\tbackground: $page-bg;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* mobile nav show & hide button */\r\n\t/* general */\r\n\t&.mobile-nav-on {\r\n\r\n\t\ttouch-action: none;\r\n\t\t\r\n\t\toverflow: hidden;\r\n\t\theight: 100vh;\r\n\r\n\t\t.page-sidebar {\r\n\t\t\tborder-right:1px solid rgba(0,0,0,0.03);\r\n\t\t\t@include box-shadow( 0 3px 35px 3px rgba(0,0,0,0.52) );\r\n\t\t}\r\n\r\n\t\t/*.page-content:before {\r\n\t\t\tcontent:\" \";\r\n\t\t\tposition:fixed;\r\n\t\t\tz-index: $space;\r\n\t\t\tbackground:rgba(0,0,0,0);\r\n\t\t\tdisplay: block;\r\n\t\t\theight: 100vh;\r\n\t\t\twidth: 100vw;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t}*/\r\n\r\n\t\t.page-content-overlay {\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tbackground: rgba($black,0.09);\r\n\t\t}\t\t\r\n\t\t\r\n\t\t&:not(.nav-mobile-push) {\r\n\r\n\t\t\t.page-sidebar {\r\n\r\n\t\t-webkit-transform: translate3d(0px, 0, 0) !important;\r\n\t\t    -ms-transform: translate3d(0px, 0, 0) !important;\r\n\t\t        transform: translate3d(0px, 0, 0) !important;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-fixed:not(.nav-function-top) {\r\n\r\n\t\t\t\t.page-sidebar {\r\n\r\n\t\t-webkit-transform: translate3d(0px, 0, 0) !important;\r\n\t\t    -ms-transform: translate3d(0px, 0, 0) !important;\r\n\t\t        transform: translate3d(0px, 0, 0) !important;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* Push content */\r\n\t\t&.nav-mobile-push:not(.nav-mobile-slide-out) {\r\n\r\n\t\t\t.page-wrapper {\r\n\r\n\t\t\t\t.page-sidebar {\r\n\t\t\t\t\tleft:0;\r\n\t\t\t\t\t@include translate3d (0px, 0, 0);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.page-header,\r\n\t\t\t\t.page-content,\r\n\t\t\t\t.page-footer,\r\n\t\t\t\t.page-footer-push {\r\n\t\t\t\t\t@include translate3d ($nav-width, 0, 0);\r\n\t\t\t\t}\t\r\n\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t/* Off canvas turned ON*/\r\n\t\t&.nav-mobile-slide-out {\r\n\r\n\t\t\t.page-wrapper {\r\n\r\n\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t.page-header,\r\n\t\t\t\t.page-content,\r\n\t\t\t\t.page-footer,\r\n\t\t\t\t.page-footer-push {\r\n\t\t\t\t\t@include translate3d ($nav-width, 0, 0);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/*.page-content:before {\r\n\t\t\t\t\tbackground:transparent !important;\r\n\t\t\t\t}*/\r\n\r\n\t\t\t\t.page-content-overlay {\r\n\t\t\t\t\tbackground: transparent !important;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tleft: $nav-width;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.page-header,\r\n\t\t\t\t.page-content,\r\n\t\t\t\t.page-footer,\r\n\t\t\t\t.page-footer-push {\r\n\t\t\t\t\tbox-shadow: 0 9px 0px 0px $page-bg, 0 -9px 0px 0px $page-bg, 12px 0 15px -4px rgba(0, 0, 0, 0.32), -12px 0 15px -4px rgba(0, 0, 0, 0.32);       \r\n\t\t\t\t}\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* 'not' is ON by default */\r\n\t\t&.nav-mobile-no-overlay {\r\n\r\n\t\t\t.page-wrapper {\r\n\r\n\t\t\t\t/*.page-content:before {\r\n\t\t\t\t\tbackground:rgba(0,0,0,0.3);\r\n\t\t\t\t}*/\r\n\r\n\t\t\t\t.page-content-overlay {\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n@media only screen and ( max-width: map-get($grid-breakpoints, sm) ){\r\n\r\n\t/* here we turn on mobile font for smaller screens */\r\n\t/*body {\r\n\t\tfont-family: $mobile-page-font !important;\r\n\t}*/\r\n\r\n\t/* mobile nav search */\r\n\t.mobile-search-on:not(.mobile-nav-on) {\r\n\r\n\t\t.page-header > * {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\r\n\r\n\t\t.search {\r\n\t\t\tdisplay: flex !important;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.app-forms {\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.btn-search-close {\r\n\t\t\t\t\tdisplay: flex !important;\r\n\t\t\t\t\tright:10px;\r\n\t\t\t\t\twidth: 26px;\r\n\t\t\t\t\theight: 26px;\r\n\t\t\t\t\tfont-size: 1rem;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t#search-field {\r\n\t\t\t\t\tborder: 1px solid $primary-500;\r\n\t\t\t\t\tpadding-left: 1rem;\r\n\t\t\t\t\tpadding-right: 3rem;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tmax-width: none;\r\n\t\t\t\t\tbackground: $white;\r\n\r\n\r\n\t\t\t\t\t&:focus {\r\n\t\t\t\t\t\tborder-color: $primary-500;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t[data-class=\"mobile-nav-on\"] {\r\n\t\t\tdisplay: none !important;\r\n\t\t}\t\t\r\n\r\n\t}\r\n\r\n\t.page-header {\r\n\r\n\t\t[data-toggle=\"dropdown\"] + .dropdown-menu {\r\n\t\t\twidth: calc(100% - #{$grid-gutter-width-base / 1.5}) !important;\r\n\t\t\tright: $grid-gutter-width-base / 3 !important;\r\n\t\t}\r\n\t}\r\n\r\n\t.page-header,\r\n\t.page-content {\r\n\t\tpadding-left: $grid-gutter-width-base/3 !important;\r\n\t\tpadding-right: $grid-gutter-width-base/3 !important;\r\n\t}\r\n\r\n\t.primary-nav .nav-menu li a > .badge {\r\n\t\tfont-size: 10px !important;\r\n\t}\r\n\r\n\r\n\t.card {\r\n\t\t.card-header,\r\n\t\t.card-body {\r\n\t\t\tpadding: 1rem;\r\n\t\t}\r\n\t}\r\n\t.alert,\r\n\t.panel .panel-tag,\r\n\t.accordion .card .card-header .card-title {\r\n\t\tpadding: 1rem;\r\n\t}\r\n}\r\n", "/* DOCS : https://developer.mozilla.org/en-US/docs/Web/CSS/%40media/light-level */\r\n\r\n/* The device is used in a environment with a light level in the ideal range for the screen, \r\n * and which does not necessitate any particular adjustment.. */\r\n@media (light-level: normal) {\r\n \r\n}\r\n\r\n/* The device is used in a dim environment, where excessive contrast and brightness would be \r\n * distracting or uncomfortable to the reader. For example: night time, or a dimly \r\n * illuminated indoor environment. */\r\n@media (light-level: dim) {\r\n  \r\n}\r\n\r\n/* The device is used in an exceptionally bright environment, causing the screen to be washed \r\n * out and difficult to read. For example: bright daylight. */\r\n@media (light-level: washed) {\r\n \r\n}", "/* text area */\r\n.form-content-editable[contenteditable=\"true\"] {\r\n\toverflow:auto;\r\n\t-webkit-user-modify: read-write-plaintext-only;\r\n\r\n\tline-height: normal;\r\n\r\n\t&:focus {\r\n\t\toutline: 0;\r\n\t}\r\n\r\n\t&:empty:not(:focus):before{\r\n\t\tcontent: attr(data-placeholder);\r\n\t\tcolor: $fusion-50;\r\n\t}\r\n\r\n\t&::selection {\r\n\t\tbackground: rgba(0, 132, 255, .2);\r\n\t\tcolor: $black;\r\n\t}\r\n\r\n\t&::-moz-selection {\r\n\t\tbackground: rgba(0, 132, 255, .2);\r\n\t\tcolor: $black;\r\n\t}\r\n}\r\n\r\n//why did we need this?\r\n/*.form-control:not(.form-control-sm),\r\n.custom-select:not(.custom-select-sm),\r\n.input-group:not(.input-group-sm) {\r\n\tmin-height: calc(2.25rem + 2px);\r\n}*/\r\n\r\n.form-label {\r\n\tfont-weight: 500;\r\n}\r\n\r\n/* select arrow */\r\n\r\n/*select:not(.custom-select):not([multiple]) {\r\n  background-image:\r\n    linear-gradient(45deg, transparent 50%, red 60%),\r\n    linear-gradient(135deg, red 40%, transparent 50%) !important;\r\n  background-position:\r\n    calc(100% - 30px) 14px,\r\n    calc(100% - 20px) 14px,\r\n    100% 0;\r\n  background-size:\r\n    10px 10px,\r\n    10px 10px;\r\n  background-repeat: no-repeat;\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n}*/\r\n\r\n/* fix */\r\n.custom-range {\r\n\t-webkit-appearance: none;    \r\n\t-moz-appearance: none;\r\n}\r\n.custom-range::-moz-range-thumb {\r\n\t-moz-appearance: none;\r\n}\r\n.custom-range::-webkit-slider-thumb {\r\n\t-webkit-appearance: none;\r\n}\r\n\r\n/* add background to focused inpur prepend and append */\r\n.form-control:focus ~ .input-group-prepend {\r\n\tbackground: $primary-500;\r\n}\r\n.has-length {\r\n\t.input-group-text {\r\n\t\tborder-color: $primary-500;\r\n\t\t& + .input-group-text {\r\n\t\t\tborder-left: 1px solid rgba($black, 0.1);\r\n\t\t}\r\n\t}\r\n\t.input-group-text:not([class^=\"bg-\"]):not([class*=\" bg-\"]) {\r\n\t\tbackground: $primary-500;\r\n\t\tcolor: $white !important;\r\n\t}\r\n\r\n}\r\n.input-group-text {\r\n\ttransition: all 0.15s ease-in-out, \r\n}\r\n\r\n/* input group animation for multiple inputs */\r\n.input-group.input-group-multi-transition {\r\n\tinput[type=\"text\"] {\r\n\r\n\t\ttransition: width 470ms cubic-bezier(0.34, 1.25, 0.3, 1);\r\n\t\t&:focus {\r\n\t\t\twidth:50%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* BS form hack for checkbox tick mark */\r\n.custom-checkbox .custom-control-label::after {\r\n\tbackground-size: $custom-control-indicator-bg-size-checkbox;\r\n}\r\n\r\n/* circle checkbox */\r\n.custom-checkbox-circle {\r\n\t.custom-control-label:before {\r\n\t\tborder-radius: 50%;\r\n\t}\r\n}\r\n/* rounded radio */\r\n.custom-radio-rounded {\r\n\t.custom-control-label:before {\r\n\t\tborder-radius: 4px;\r\n\t}\r\n}\r\n\r\n/* not sure if we need this? */\r\n/*.custom-control {\r\n\tmin-height: $custom-control-indicator-size;\r\n}*/\r\n\r\n/* make checked label bold */\r\ninput[type=\"radio\"]:checked + .custom-control-label, \r\ninput[type=\"checkbox\"]:checked + .custom-control-label {\r\n\tfont-weight: 500;\r\n}\r\n\r\n/* help block and validation feedback texts*/\r\n.help-block {\r\n\tcolor: $fusion-50;\r\n}\r\n\r\n/* on feedback error */\r\n.help-block, .invalid-feedback, .valid-feedback {\r\n\tfont-size: rem($fs-nano);\r\n\tmargin-top: 0.325rem;\r\n}\r\n\r\n/* when form group is last child show now margin */\r\n.form-group:last-child,\r\n.form-group:only-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n/* fix alignment for generic checkbox and radio */\r\n.form-check-input {\r\n\tmargin-top: 0;\r\n} \r\n.form-check-label {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n.form-check {\r\n\tmargin: 5px 0;\r\n}", "// here we are attemping to fool the browser and harness the power of GPU usage\r\n// using \"transform: translate3d(0, 0, 0);\" will use the Graphic Processor (GPU) and avoid the CSS transition using CPU...\r\n\r\n@include media-breakpoint-up(xl) {\r\n\t.page-sidebar {\r\n\t\t@include translate3d(0,0,0);\r\n\t}\r\n\r\n\t&.header-function-fixed,\r\n\t&.nav-function-fixed,\r\n\t&.header-function-fixed.nav-function-fixed {\r\n\t\t .page-wrapper,\r\n\t\t .page-sidebar {\r\n            transform: none;\r\n    -webkit-transform: none;\r\n        -ms-transform: none;\r\n\t\t }\r\n\t}\r\n}", ".nav-mobile-slide-out {\r\n\t#nmp,\r\n\t#nmno {\r\n\t\t@extend %not-compatible;\r\n\t}\r\n}\r\n\r\n.nav-function-top,\r\n.nav-function-minify,\r\n.mod-hide-nav-icons {\r\n\t#mnl {\r\n\t\t@extend %not-compatible;\r\n\t}\r\n}\r\n\r\n@include media-breakpoint-up($mobile-breakpoint-size) {\r\n\t.nav-function-top {\r\n\t\t#nfh {\r\n\t\t\t@extend %not-compatible;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media only screen and (max-width: $mobile-breakpoint-size) {\r\n\r\n\t.mobile-view-activated {\r\n\t\t#nff,\r\n\t\t#nfm,\r\n\t\t#nfh,\r\n\t\t#nft,\r\n\t\t#mmb {\r\n\t\t      position:relative;\r\n\t\t    \r\n\t\t    .onoffswitch-title {\r\n\t\t      color: $settings-incompat-title !important; \r\n\t\t    }\r\n\t\t    .onoffswitch-title-desc {\r\n\t\t      color: $settings-incompat-desc !important;\r\n\t\t    }\r\n\t\t    &:after {\r\n\t\t      content: \"DISABLED\";\r\n\t\t      font-size: 10px;\r\n\t\t      position: absolute;\r\n\t\t      background: $settings-incompat-bg;\r\n\t\t      width: 65px;\r\n\t\t      text-align: center;\r\n\t\t      border: 1px solid $settings-incompat-border;\r\n\t\t      height: 22px;\r\n\t\t      line-height: 20px;\r\n\t\t      border-radius: $border-radius-plus;\r\n\t\t      display: block;\r\n\t\t      right: 13px;\r\n\t\t      top: 26%;\r\n\t\t      color:$fusion-900;\r\n\t\t    }\r\n\t\t}\r\n\t}\r\n}\r\n", ".settings-panel {\r\n\t.expanded {\r\n\t\t&.theme-colors {\r\n\t\t\tdisplay: block;\r\n\t\t\tbox-shadow: none;\r\n\t\t\tborder: 0;\r\n\t\t\tbackground: transparent;\r\n\r\n\r\n\r\n\t\t\t> ul {\r\n\t\t\t\theight: auto;\r\n\r\n\t\t\t\t> li {\r\n\r\n\t\t\t\t\t[data-action] {\r\n\r\n\r\n\t\t\t\t\t\twidth: 36px;\r\n\t\t\t\t\t\theight: 36px;\r\n\t\t\t\t\t\tmargin-right: 4px;\r\n\t\t\t\t\t\tmargin-bottom: 4px;\r\n\t\t\t\t\t\tborder-radius: 4px;\r\n\r\n\t\t\t\t\t\t&:hover {\r\n\t\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-right:0;\r\n\t\t\t\t}\r\n\t\t\t}\t\r\n\r\n\t\t\t/*@include theme-button-color ($theme-1-fusion, $theme-1-primary, $theme-1-info, $theme-1-success, $theme-1-warning, $theme-1-danger)*/\r\n\r\n\t\t\t#myapp-0 { \r\n\t\t\t\tbackground: #886ab5;\r\n\t\t\t\tbox-shadow: inset 0 0 0 3px rgb(43, 161, 255);\r\n\t\t\t }\r\n\t\t\t#myapp-1 { \r\n\t\t\t\tbackground: #b56a9f;\r\n\t\t\t }\r\n\t\t\t#myapp-2 { \r\n\t\t\t\tbackground: #9fcb3d;\r\n\t\t\t} \r\n\t\t\t#myapp-3 { \r\n\t\t\t\tbackground: #4679cc;\r\n\t\t\t} \r\n\t\t\t#myapp-4 { \r\n\t\t\t\tbackground: #2198F3;\r\n\t\t\t}\r\n\t\t\t#myapp-5 { \r\n\t\t\t\tbackground: #6ab5b4;\r\n\t\t\t}\r\n\t\t\t#myapp-6 { \r\n\t\t\t\tbackground: #dd5293;\r\n\t\t\t}\r\n\t\t\t#myapp-7 { \r\n\t\t\t\tbackground: #868e96;\r\n\t\t\t}\r\n\t\t\t#myapp-8 { \r\n\t\t\t\tbackground: #7c91df;\r\n\t\t\t}\r\n\t\t\t#myapp-9 { \r\n\t\t\t\tbackground: #e59c6c;\r\n\t\t\t}\r\n\t\t\t#myapp-10 { \r\n\t\t\t\tbackground: #778c85;\r\n\t\t\t}\r\n\t\t\t#myapp-11 { \r\n\t\t\t\tbackground: #a2b077;\r\n\t\t\t}\r\n\t\t\t#myapp-12 { \r\n\t\t\t\tbackground: #7976b3;\r\n\t\t\t}\r\n\t\t\t#myapp-13 { \r\n\t\t\t\tbackground: #55ce5f;\r\n\t\t\t}\r\n\t\t\t#myapp-14 { \r\n\t\t\t\tbackground: #5c4581;\r\n\t\t\t}\r\n\t\t\t#myapp-15 { \r\n\t\t\t\tbackground: #5c4581;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n}\r\n\t\t\t\t\t\t\t", ".settings-panel {\r\n\r\n\t&:first-child {\r\n\t\th5 {\r\n\t\t\tmargin-top:0;\r\n\t\t\tpadding-top:5px;\r\n\t\t}\r\n\t}\r\n\r\n\th5 {\r\n\t\tmargin:0;\r\n\t\tfont-weight:500;\r\n\t\tfont-size: rem($fs-base + 1);\r\n\t\tpadding: $p-3 $p-3 5px;\r\n\t\tbox-sizing: content-box;\r\n\t\tdisplay: block;\r\n\t\toverflow:hidden;\r\n\t\ttext-decoration: none;\r\n\t\tmargin-top: 5px;\r\n\t\tcolor: $fusion-500;\r\n\t\ttext-align: left;\r\n\r\n\t\tsmall {\r\n\t\t\tdisplay: inline;\r\n\t\t}\r\n\t}\r\n\r\n\t.list {\r\n\t\tfont-weight: 400;\r\n\t\tmin-height: 45px;\r\n\t\tpadding: 0.25rem 1rem 0.25rem 2rem;\r\n\t\tcolor:darken($white, 60%);\r\n\t\tposition: relative;\r\n\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\r\n\t\t@extend %common-animation;\r\n\r\n\t\t&:hover{\r\n\t\t\tcolor:darken($white, 80%);\r\n\t\t\tbackground:rgba( $white, .7 );\r\n\r\n\t\t\t.onoffswitch {\r\n\t\t\t\t@include scale(1.13);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.onoffswitch,\r\n\t\t&:hover .onoffswitch {\r\n\t\t\t@extend %common-animation;\r\n\t\t}\r\n\r\n\t\t.btn-switch {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 1rem;\r\n\t\t\tmargin: 0;\r\n\t\t\ttop: 30%;\r\n\t\t}\r\n\r\n\t\t.onoffswitch-title {\r\n\t\t\tmargin-top:0.5px;\r\n\t\t\tfont-size: rem($fs-base);\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.onoffswitch-title-desc {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: rem(12px);\r\n\t\t\tcolor: #989da5;\r\n\t\t\ttext-transform: lowercase;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.expanded {\r\n\t\t@extend %expanded-box;\r\n\t\tposition: relative;\r\n\t\tdisplay: none;\r\n\r\n\t\t&:before {\r\n\t\t\tborder-bottom-color: $fusion-400;\r\n\t\t\tbottom: 1px;\r\n\t\t}\r\n\r\n\t\t> ul {\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin: 0;\r\n\t\t\tmargin: 0 0 0 1rem;\r\n\t\t\theight: 50px;\r\n\t\t\t\r\n\t\t\t> li {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tmargin:0;\r\n\t\t\t\tpadding:0;\r\n\r\n\t\t\t\t[data-action] {\r\n\t\t\t\t\twidth: 50px;\r\n\t\t\t\t\theight: 50px;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tmargin-right: 2px;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\tbox-shadow: 0 1px 10px rgba(0,0,0,0.05), 0 1px 2px rgba(0,0,0,0.1);\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list {\r\n\t\t\tpadding: 0;\r\n\t\t\tmin-height: auto;\r\n\t\t\tmargin: 0.5rem 0 0 1rem;\r\n\r\n\t\t\t.btn-switch {\r\n\t\t\t\tmargin-top:2px;\r\n\t\t\t\ttop: -2px;\r\n\t\t\t\tright: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground:transparent;\r\n\t\t\t}\r\n\t\t} \r\n\t}\r\n}\r\n\r\n#saving {\r\n\tposition: absolute;\r\n\tz-index: 1;\r\n\ttop: 0;\r\n\tleft: -40px;\r\n\tdisplay: none;\r\n}\r\n\r\n/* localstorage success fade animation */\r\n.saving {\r\n\r\n\t#saving {\r\n\t\t@extend %spin-loader;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t[data-action=\"app-reset\"] {\r\n\t\topacity: 0.5;\r\n\t\tcursor: not-allowed !important;\r\n\t}\r\n\r\n}\r\n\r\n@include set-settings(header-function-fixed);\r\n@include set-settings(nav-function-fixed);\r\n@include set-settings(nav-function-minify);\r\n@include set-settings(nav-function-hidden);\r\n@include set-settings(nav-function-top);\r\n@include set-settings(nav-mobile-push);\r\n@include set-settings(nav-mobile-no-overlay);\r\n@include set-settings(nav-mobile-slide-out);\r\n@include set-settings(mod-main-boxed);\r\n@include set-settings(mod-fixed-bg);\r\n@include set-settings(mod-clean-page-bg);\r\n@include set-settings(mod-pace-custom);\r\n@include set-settings(mod-bigger-font);\r\n@include set-settings(mod-high-contrast);\r\n@include set-settings(mod-color-blind);\r\n@include set-settings(mod-hide-nav-icons);\r\n@include set-settings(mod-hide-info-card);\r\n@include set-settings(mod-lean-subheader);\r\n@include set-settings(mod-disable-animation);\r\n@include set-settings(mod-nav-link);\r\n@include set-settings(mod-app-rtl);", ".color-disp-demo {\r\n\ttr {\r\n\r\n\t\t&:first-child {\r\n\t\t\ttd {\r\n\t\t\t\theight: 100px;\r\n\t\t\t\tbox-shadow: inset 0 -5px 0 rgba(255,255,255,0.8);\r\n\t\t\t}\r\n\t\t}\r\n\t\ttd {\r\n\t\t\tborder:none;\r\n\t\t\tpadding-top: 7px;\r\n\t\t\tpadding-bottom: 7px;\r\n\r\n\t\t\t@extend %common-animation;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\t@include transform( scale(1.1) );\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t}\t\r\n\t}\r\n}\r\n\r\n.cr-c {\r\n\twidth: 21px;\r\n\theight: 21px;\r\n\tdisplay: block;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.icon-demo {\r\n\tlist-style: none;\r\n\tpadding:0;\r\n\tli {\r\n\t\tborder:1px solid #7f8995;\r\n\t\tpadding:10px;\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size:20px;\r\n\t\twidth:60px;\r\n\t\theight:60px;\r\n\t\t//line-height: 43px;\r\n\t\toverflow: hidden;\r\n\t\tmargin: 0 6px 0 0;\r\n\t\ttext-align: center;\r\n\t\tbackground:#fff;\r\n\r\n\t\t@extend %common-animation;\r\n\r\n\t\t&:hover {\r\n\t\t\t@include transform( scale(1.4) );\r\n\t\t}\r\n\t}\r\n}\r\n\r\n//display demo codes\r\n//works with vendor/prism/prism.css\r\n\tcode[class*=\"language-\"],\r\n\tpre[class*=\"language-\"] {\r\n\t\tdisplay: none !important;\r\n\t}\r\n\r\n.show-codes {\r\n\tcode[class*=\"language-\"],\r\n\tpre[class*=\"language-\"] {\r\n\t\tdisplay: block !important;\r\n\t}\r\n}\r\n\r\n.container-demo {\r\n\tmax-width:100%;\r\n\twidth:100%;\r\n}\r\n\r\n.bd-example {\r\n    position: relative;\r\n    padding: 1rem 0;\r\n}\r\n\r\n.bd-example-row-flex-cols .row {\r\n    min-height: 10rem;\r\n    background-color: rgba(255,0,0,.1);\r\n}\r\n.bd-example-row .row>.col, \r\n.bd-example-row .row>[class^=col-] {\r\n    padding-top: .75rem;\r\n    padding-bottom: .75rem;\r\n    background-color: rgba(86,61,124,.15);\r\n    border: 1px solid rgba(86,61,124,.2);\r\n}\r\n\r\n.bd-example-row .row+.row {\r\n    margin-top: 1rem;\r\n}\r\n\r\n.bd-highlight {\r\n  background-color: rgba(86,61,124,.2);\r\n  border: 1px solid rgba(86,61,124,.2);\r\n}\r\n\r\n//demo\r\n\r\n.demo {\r\n\tmargin: 0;\r\n\r\n\t> * {\r\n\t\tmargin: 0 .375rem 1rem 0 !important;\r\n\t}\r\n\r\n\t&.demo-no-mb {\r\n\t\t> * {\r\n\t\tmargin-bottom: 0 !important;\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n.demo-v-spacing-sm,\r\n.demo-v-spacing,\r\n.demo-v-spacing-lg {\r\n\t> * + *  {\r\n\t\tmargin-top: 0.875rem !important;\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n\r\n\t> * {\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n}\r\n\r\n\r\n\r\n.demo-v-spacing {\r\n\t> * + *  {\r\n\t\tmargin-top: 1rem !important;\r\n\t}\r\n}\r\n\r\n.demo-v-spacing-lg {\r\n\t> * + *  {\r\n\t\tmargin-top: 1.5rem !important;\r\n\t}\r\n}\r\n\r\n.demo-h-spacing > *:not(last-child):not(only-child) {\r\n\tmargin-right: 1rem !important;\r\n}\r\n\r\n/*.demo-vh-spacing > *:not(last-child):not(only-child) {\r\n\tmargin: 0 .375rem 1rem 0 !important;\r\n}*/\r\n", "$pace-height: 3px;\r\n$pace-incontent-height: 13px;\r\n\r\nbody:not(.mod-pace-custom) {\r\n\t.pace {\r\n\t\t-webkit-pointer-events: none;\r\n\t\tpointer-events: none;\r\n\r\n\t\t-webkit-user-select: none;\r\n\t\t-moz-user-select: none;\r\n\t\tuser-select: none;\r\n\r\n\t    .pace-progress {\r\n\t\t\tbackground: $color-primary;\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 2000;\r\n\t\t\ttop: 0;\r\n\t\t\tright: 100%;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: $pace-height;\r\n\t\t}\r\n\t}\r\n\r\n\t.pace-inactive {\r\n\t  display: none;\r\n\t}\r\n\r\n}\r\n\r\nbody.mod-pace-custom {\r\n\r\n\t.pace {\r\n\t\t-webkit-pointer-events: none;\r\n\t\tpointer-events: none;\r\n\r\n\t\t-webkit-user-select: none;\r\n\t\t-moz-user-select: none;\r\n\t\tuser-select: none;\r\n\r\n\t    z-index: 2000;\r\n\t    position: fixed;\r\n\t   \tmargin: auto;\r\n\t    top: 45vh;\r\n\t    left: 0;\r\n\t    right: 0;\r\n\t    //right: unquote(\"calc( (100% - #{$nav-width}) / 2 )\")\r\n\t    height: $pace-incontent-height;\r\n\t    border: 2px solid $white;\r\n\t    width: $nav-width / 1.111;\r\n\t    background: $white;\r\n\t    overflow: hidden; \r\n\r\n\t\t.pace-progress {\r\n\t\t    box-sizing: border-box;\r\n\r\n\t\t    @include translate3d (0, 0, 0);\r\n\r\n\t\t    max-width: $nav-width / 1.111;\r\n\t\t    z-index: 2000;\r\n\t\t    display: block;\r\n\t\t    position: absolute;\r\n\t\t    top: 0;\r\n\t\t    right: 100%;\r\n\t\t    height: 100%;\r\n\t\t    width: 100%;\r\n\t\t    background-color:$color-primary;\r\n\t\t    background-image: linear-gradient(135deg, $color-primary 0%, $color-primary 25%, darken($color-primary,10%) 25%, darken($color-primary,10%)\r\n\t\t        50%, $color-primary 50%, $color-primary 75%, darken($color-primary,10%) 75%, darken($color-primary,10%) 100%);\r\n\t\t    background-repeat: repeat;\r\n\t\t    background-position: 0 0;\r\n\t\t    background-size: $pace-incontent-height $pace-incontent-height;\r\n\t\t    background-clip: content-box;\r\n\r\n\t\t    animation: loading 0.5s linear infinite;\r\n\t\t    -o-animation: loading 0.5s linear infinite;\r\n\t\t    -moz-animation: loading 0.5s linear infinite;\r\n\t\t    -webkit-animation: loading 0.5s linear infinite;\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t.pace-inactive {\r\n\t  display: none;  \r\n\t}\r\n\r\n\t&.pace-running {\r\n\t\t.page-content {\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t    position: fixed;\r\n\t\t\t    top: 0;\r\n\t\t\t    bottom: 0;\r\n\t\t\t    left: 0;\r\n\t\t\t    right: 0;\r\n\t\t\t    background-color:$page-bg;\r\n\t\t\t    z-index: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t/*&.pace-done {\r\n\t\t.page-content {\r\n\t\t\t&:before{\r\n\t\t\t\tz-index:-2;\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}*/\r\n}\r\n\r\n\r\n@keyframes loading {\r\n\tfrom {\r\n\t\tbackground-position: 0 0;\r\n\t}\r\n\tto {\r\n\t\tbackground-position: -$pace-incontent-height 0;\r\n\t}\r\n}\r\n@-webkit-keyframes loading {\r\n\tfrom {\r\n\t\tbackground-position: 0 0;\r\n\t}\r\n\tto {\r\n\t\tbackground-position:-$pace-incontent-height 0;\r\n\t}\r\n}\r\n@-moz-keyframes loading {\r\n\tfrom {\r\n\t\tbackground-position: 0 0;\r\n\t}\r\n\tto {\r\n\t\tbackground-position: -$pace-incontent-height 0;\r\n\t}\r\n}\r\n@-o-keyframes loading {\r\n\tfrom {\r\n\t\tbackground-position: 0 0;\r\n\t}\r\n\tto {\r\n\t\tbackground-position: -$pace-incontent-height 0;\r\n\t}\r\n}", ".slimScrollBar {\r\n\tborder-radius: 3px !important;\r\n}\r\n\r\nbody:not(.no-slimscroll) {\r\n\r\n\t.custom-scroll {\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n}\r\n\r\n", "/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n\r\n@mixin waves-transition($transition){\r\n    -webkit-transition: $transition;\r\n    -moz-transition: $transition; \r\n    -o-transition: $transition;\r\n    transition: $transition;  \r\n}\r\n\r\n@mixin waves-transform($string){\r\n    -webkit-transform: $string;\r\n    -moz-transform: $string;\r\n    -ms-transform: $string;\r\n    -o-transform: $string;\r\n    transform: $string;\r\n}\r\n\r\n@mixin waves-box-shadow($shadow){\r\n    -webkit-box-shadow: $shadow;\r\n    box-shadow: $shadow;\r\n}\r\n\r\n.waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n    \r\n    .waves-ripple {\r\n        position: absolute;\r\n        border-radius: 50%;\r\n        width: 100px;\r\n        height: 100px;\r\n        margin-top:-50px;\r\n        margin-left:-50px;\r\n        opacity: 0;\r\n\t\t$bgcolor: $white;\r\n\t\tbackground: rgba($bgcolor,0.6);\r\n\t\t$gradient: rgba($bgcolor,0.3) 0,rgba($bgcolor,.4) 40%,rgba($bgcolor,.5) 50%,rgba($bgcolor,.6) 60%,rgba($bgcolor,0) 70%;\r\n\t\tbackground: -webkit-radial-gradient($gradient);\r\n\t\tbackground: radial-gradient($gradient);\r\n        @include waves-transition(all 0.5s ease-out);\r\n        -webkit-transition-property: -webkit-transform, opacity;\r\n        -moz-transition-property: -moz-transform, opacity;\r\n        -o-transition-property: -o-transform, opacity;\r\n        transition-property: transform, opacity;\r\n        @include waves-transform(scale(0) translate(0,0));\r\n        pointer-events: none;\r\n    }\r\n\r\n}\r\n\r\n.waves-notransition {\r\n    @include waves-transition(none #{\"!important\"});\r\n}\r\n\r\n\r\n.waves-themed {\r\n\r\n\t&.btn-warning,\r\n\t&.btn-default,\r\n\t&.btn-outline-default {\r\n\t\t.waves-ripple {\r\n\t\t\t$bgcolor: $black;\r\n\t\t\tbackground: rgba($bgcolor,0.6);\r\n\t\t\t$gradient: rgba($bgcolor,0.1) 0,rgba($bgcolor,.2) 40%,rgba($bgcolor,.3) 50%,rgba($bgcolor,.4) 60%,rgba($bgcolor,0) 70%;\r\n\t\t\tbackground: -webkit-radial-gradient($gradient);\r\n\t\t\tbackground: radial-gradient($gradient);\r\n\t\t}\r\n\t}\r\n\r\n\t&.btn-primary,\r\n\t&.btn-outline-primary,\r\n\t&.btn-info,\r\n\t&.btn-outline-info,\r\n\t&.btn-danger,\r\n\t&.btn-outline-danger,\r\n\t&.btn-success,\r\n\t&.btn-outline-success,\r\n\t&.btn-dark,\r\n\t&.btn-outline-dark {\r\n\t\t.waves-ripple {\r\n\t\t\t$bgcolor: $white;\r\n\t\t\tbackground: rgba($bgcolor,0.6);\r\n\t\t\t$gradient: rgba($bgcolor,0.3) 0,rgba($bgcolor,.4) 40%,rgba($bgcolor,.5) 50%,rgba($bgcolor,.6) 60%,rgba($bgcolor,0) 70%;\r\n\t\t\tbackground: -webkit-radial-gradient($gradient);\r\n\t\t\tbackground: radial-gradient($gradient);\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n\r\n.page-sidebar .primary-nav .nav-menu li  a.waves-themed .waves-ripple {\r\n\t$bgcolor: darken($primary-800, 25%);\r\n\tbackground: rgba($bgcolor,0.6);\r\n\t$gradient: rgba($bgcolor,0.2) 0,rgba($bgcolor,.3) 40%,rgba($bgcolor,.4) 50%,rgba($bgcolor,.5) 60%,rgba($bgcolor,0) 70%;\r\n\tbackground: -webkit-radial-gradient($gradient);\r\n\tbackground: radial-gradient($gradient);\r\n}\r\n\r\n.panel-hdr:not([class^=\"bg-\"]):not([class*=\" bg-\"]) .waves-themed.btn-toolbar-master,\r\n.waves-themed.nav-link {\r\n\t.waves-ripple {\r\n\t\t$bgcolor: $primary-500;\r\n\t\tbackground: rgba($bgcolor,0.6);\r\n\t\t$gradient: rgba($bgcolor,0.2) 0,rgba($bgcolor,.3) 40%,rgba($bgcolor,.4) 50%,rgba($bgcolor,.5) 60%,rgba($bgcolor,0) 70%;\r\n\t\tbackground: -webkit-radial-gradient($gradient);\r\n\t\tbackground: radial-gradient($gradient);\r\n\t}\r\n}\r\n", "// fade\r\n@-webkit-keyframes seconds {\r\n  0% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes seconds {\r\n  0% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n//delayed entry\r\n@-webkit-keyframes delayed {\r\n  99% {\r\n    visibility: hidden;\r\n  }\r\n  100% {\r\n    visibility: visible;\r\n  }\r\n}\r\n@keyframes delayed {\r\n  99% {\r\n    visibility: hidden;\r\n  }\r\n  100% {\r\n    visibility: visible;\r\n  }\r\n}\r\n\r\n@keyframes subtle{\r\n    0% { opacity:1; }\r\n    100% { opacity:0.2; }\r\n}\r\n\r\n@-webkit-keyframes subtle{\r\n    0% { opacity:1; }\r\n    100% { opacity:0.2; }\r\n}", "//highlight on-demand\r\n@-webkit-keyframes highlight {\r\n    from { background: $warning-50; }\r\n    to { background: transparent; }\r\n}\r\n\r\n@keyframes highlight {\r\n    from { background: $warning-50; }\r\n    to { background: transparent; }\r\n}\r\n\r\n.highlight {\r\n    -webkit-animation: highlight 1.5s;\r\n       -moz-animation: highlight 1.5s;\r\n            animation: highlight 1.5s;\r\n}", "// infinite spin\r\n@-webkit-keyframes spin {\r\n    from { \r\n      -webkit-transform: rotate(0deg); \r\n    }\r\n\r\n    to { \r\n      -webkit-transform: rotate(360deg); \r\n    }\r\n}\r\n\r\n@keyframes spin {\r\n    from {\r\n        transform:rotate(0deg);\r\n    }\r\n\r\n    to {\r\n        transform:rotate(360deg);\r\n    }\r\n}\r\n\r\n\r\n// spin loader\r\n/*.spinner {\r\n  margin: 5px;\r\n  height: 20px;\r\n  width: 20px;\r\n  animation: rotate 0.7s infinite linear;\r\n  border: 2px solid $color-primary;\r\n  border-right-color: transparent;\r\n  border-radius: 50%;\r\n}*/\r\n\r\n// fontawesome spinner faster\r\n.fa-spin-4x {\r\n  animation: spin 0.5s infinite linear;\r\n}\r\n\r\n.fa-spin-2x {\r\n  animation: spin 1s infinite linear;\r\n}", "$animateFadeInDown_distance: 5px;\r\n\r\n\r\n/*=== Animations start here  ===*/\r\n\r\n/*=== FADE IN DOWN ===*/\r\n@-webkit-keyframes animateFadeInDown {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(0, -$animateFadeInDown_distance, 0);\r\n\t\ttransform: translate3d(0, -$animateFadeInDown_distance, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n@keyframes animateFadeInDown {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(0, -$animateFadeInDown_distance, 0);\r\n\t\ttransform: translate3d(0, -$animateFadeInDown_distance, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n\r\n/*==== FADE IN UP ===*/\r\n@-webkit-keyframes animateFadeInUp {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(0, $animateFadeInDown_distance, 0);\r\n\t\ttransform: translate3d(0, $animateFadeInDown_distance, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n@keyframes animateFadeInUp {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(0, $animateFadeInDown_distance, 0);\r\n\t\ttransform: translate3d(0, $animateFadeInDown_distance, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n\r\n/*=== FADE IN LEFT ===*/\r\n@-webkit-keyframes animateFadeInLeft {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(-$animateFadeInDown_distance, 0, 0);\r\n\t\ttransform: translate3d(-$animateFadeInDown_distance, 0, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n@keyframes animateFadeInLeft {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(-$animateFadeInDown_distance, 0, 0);\r\n\t\ttransform: translate3d(-$animateFadeInDown_distance, 0, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n\r\n/*==== FADE IN RIGHT ===*/\r\n@-webkit-keyframes animateFadeInRight {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d($animateFadeInDown_distance, 0, 0);\r\n\t\ttransform: translate3d($animateFadeInDown_distance, 0, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n@keyframes animateFadeInRight {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d($animateFadeInDown_distance, 0, 0);\r\n\t\ttransform: translate3d($animateFadeInDown_distance, 0, 0);\r\n\t}\r\n\r\n\tto {\r\n\t\topacity: 1;\r\n\t\t-webkit-transform: none;\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n\r\n/* remove transition delay */\r\n.no-transition-delay {\r\n\ttransition-delay: 0ms !important;\r\n}\r\n\r\n/* fade transitions for page elements */\r\n.page-content > .alert {\r\n\tanimation: animateFadeInUp 0.3s;\r\n\t-webkit-animation: animateFadeInUp 0.3s;\r\n}\r\n\r\n.page-content  > .card,\r\n.page-content > .row {\r\n\tanimation: animateFadeInUp 0.7s;\r\n\t-webkit-animation: animateFadeInUp 0.7s;\r\n}\r\n\r\n.tab-content >.active:not(.fade) {\r\n\tanimation: animateFadeInUp 0.5s;\r\n\t-webkit-animation: animateFadeInUp 0.5s;\r\n}\r\n\r\n/* repeated transitions */\r\n.fadeinup {\r\n\tanimation: animateFadeInUp 0.5s;\r\n\t-webkit-animation: animateFadeInUp 0.5s;  \r\n}\r\n.fadeindown {\r\n\tanimation: animateFadeInDown 0.5s;\r\n\t-webkit-animation: animateFadeInDown 0.5s;  \r\n}\r\n.fadeinleft {\r\n\tanimation: animateFadeInLeft 0.5s;\r\n\t-webkit-animation: animateFadeInLeft 0.5s;  \r\n}\r\n.fadeinright {\r\n\tanimation: animateFadeInRight 0.5s;\r\n\t-webkit-animation: animateFadeInRight 0.5s;  \r\n}", "$mod-bg-1: url(\"#{$baseURL}img/backgrounds/bg-1.png\") !default;\r\n$mod-bg-1-prev: url(\"#{$baseURL}img/backgrounds/prev-bg-1.png\") !default;\r\n\r\n$mod-bg-2: url(\"#{$baseURL}img/backgrounds/bg-2.png\") !default;\r\n$mod-bg-2-prev: url(\"#{$baseURL}img/backgrounds/prev-bg-2.png\") !default;\r\n\r\n$mod-bg-3: url(\"#{$baseURL}img/backgrounds/bg-3.png\") !default;\r\n$mod-bg-3-prev: url(\"#{$baseURL}img/backgrounds/prev-bg-3.png\") !default;\r\n\r\n$mod-bg-4: url(\"#{$baseURL}img/backgrounds/bg-4.png\") !default;\r\n$mod-bg-4-prev: url(\"#{$baseURL}img/backgrounds/prev-bg-4.png\") !default;\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n\t.mod-main-boxed.mod-bg-1 [data-class=\"mod-bg-1\"],\r\n\t.mod-main-boxed.mod-bg-2 [data-class=\"mod-bg-2\"],\r\n\t.mod-main-boxed.mod-bg-3 [data-class=\"mod-bg-3\"],\r\n\t.mod-main-boxed.mod-bg-4 [data-class=\"mod-bg-4\"] {\r\n\t\t&:before {\r\n\t\t\tcontent: \" \";\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: inherit;\r\n\t\t\tbackground-image: none;\r\n\t\t\tborder: 2px solid rgba(0,0,0,0.2);\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 15px;\r\n\t\t\tleft: 15px;\r\n\t\t\theight: 20px;\r\n\t\t\twidth: 20px;\r\n\t\t}\r\n\t\t&:after {\r\n\t\t\tcontent: \" \";\r\n\t\t\theight: inherit;\r\n\t\t\twidth: inherit;\r\n\t\t\tborder: 5px solid rgba(0,0,0,0.1);\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\tborder-radius: 50%;\r\n\t\t} \t\t\t\r\n\t}\r\n\r\n\r\n\t.mod-main-boxed {\r\n\t\t.settings-panel {\r\n\t\t\t.expanded {\r\n\t\t\t\tdisplay: block;\r\n\r\n\t\t\t\t> ul {\r\n\r\n\t\t\t\t\t> li {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tmargin:0;\r\n\t\t\t\t\t\tpadding:0;\r\n\r\n\t\t\t\t\t\t[data-action] {\r\n\r\n\t\t\t\t\t\t\t&[data-class=\"mod-bg-1\"] {\r\n\t\t\t\t\t\t\t\tbackground-image: $mod-bg-1-prev;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t&[data-class=\"mod-bg-2\"] {\r\n\t\t\t\t\t\t\t\tbackground-image: $mod-bg-2-prev;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t&[data-class=\"mod-bg-3\"] {\r\n\t\t\t\t\t\t\t\tbackground-image: $mod-bg-3-prev;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t&[data-class=\"mod-bg-4\"] {\r\n\t\t\t\t\t\t\t\tbackground-image: $mod-bg-4-prev;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\t[data-action=\"toggle\"] {\r\n\t\t\t\t\t\t\t\tmargin-right:0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.mod-bg-1 {\r\n\t\t\tbackground-image: $mod-bg-1;\r\n\t\t}\r\n\t\t&.mod-bg-2 {\r\n\t\t\tbackground-image: $mod-bg-2;\r\n\t\t}\r\n\t\t&.mod-bg-3 {\r\n\t\t\tbackground-image: $mod-bg-3;\r\n\t\t}\r\n\t\t&.mod-bg-4 {\r\n\t\t\tbackground-image: $mod-bg-4;\r\n\t\t}\r\n\t\t&.mod-fixed-bg {\r\n\t\t\tbackground-attachment: fixed;\r\n\t\t}\r\n\t}\r\n}", ".mod-clean-page-bg {\r\n\t.page-content-wrapper {\r\n\t\tbackground: $white !important;\r\n\t}\r\n\t.page-header {\r\n\t\tborder-bottom-color: lighten($header-border-bottom-color, 25%);\r\n\t}\r\n}", ".mod-color-blind .page-wrapper {\r\n\t-webkit-filter: grayscale(65%);\r\n\tfilter: grayscale(55%);\r\n}\r\n\r\n.mod-color-blind .page-sidebar .primary-nav .nav-menu > li.active > a + ul > li.active > a,\r\n.pattern-1 {\r\n\t@extend %pattern-1;\r\n}\r\n\r\n.mod-color-blind .page-sidebar .primary-nav .nav-menu > li.active > a,\r\n.mod-color-blind [class*=\"bg-danger-\"],\r\n.mod-color-blind .btn-danger,\r\n.mod-color-blind .btn-outline-danger,\r\n.mod-color-blind .alert-danger,\r\n.pattern-0 {\r\n\t@extend %pattern-0;\r\n}\r\n\r\n.mod-color-blind [class*=\"bg-primary-\"],\r\n.mod-color-blind .btn-primary,\r\n.mod-color-blind .btn-outline-primary,\r\n.mod-color-blind .alert-primary,\r\n.pattern-2 {\r\n\t@extend %pattern-2;\r\n}\r\n\r\n.mod-color-blind [class*=\"bg-success-\"],\r\n.mod-color-blind .btn-success,\r\n.mod-color-blind .btn-outline-success,\r\n.mod-color-blind .alert-success,\r\n.pattern-3 {\r\n\t@extend %pattern-3;\r\n}\r\n\r\n.mod-color-blind  [class*=\"bg-info-\"],\r\n.mod-color-blind  .btn-info,\r\n.mod-color-blind  .btn-outline-info,\r\n.mod-color-blind .alert-info,\r\n.pattern-4 {\r\n\t@extend %pattern-4;\r\n}\r\n\r\n.mod-color-blind [class*=\"bg-warning-\"],\r\n.mod-color-blind .btn-warning,\r\n.mod-color-blind .btn-outline-warning,\r\n.mod-color-blind .alert-warning,\r\n.pattern-5 {\r\n\t@extend %pattern-5;\r\n}\r\n\r\n.mod-color-blind [class*=\"btn-\"].active {\r\n\tbackground-image: none !important;\r\n}\r\n", ".mod-disable-animation *,\r\n.mod-disable-animation *:before,\r\n.mod-disable-animation *:after {\r\n  -webkit-transition: none !important;\r\n  -moz-transition: none !important;\r\n  -ms-transition: none !important;\r\n  -o-transition: none !important;\r\n  transition: none !important;\r\n\r\n  -webkit-animation:  none !important; \r\n  animation: none !important;\r\n}", ".mod-hide-info-card {\r\n\t/*.page-logo {\r\n\t\tborder-bottom: 1px solid lighten($header-logo-border-bottom, 13%);\r\n\t}*/\r\n\t.page-sidebar {\r\n\t\t.info-card {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t}\r\n}", ".mod-hide-nav-icons:not(.nav-function-top):not(.nav-function-minify) {\r\n\t.page-sidebar {\r\n\t\t.primary-nav {\r\n\t\t\t.nav-menu {\r\n\r\n\t\t\t\ta {\r\n\t\t\t\t\t> [class*='fa-'], \r\n\t\t\t\t\t> .#{$cust-icon-prefix},\r\n\t\t\t\t\t> img {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t> .badge {\r\n\t\t\t\t\t\tright: 40px;\r\n\t\t\t\t\t\tleft: auto;\r\n\t\t\t\t\t\ttop: 30%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tspan {\r\n\t\t\t\t\t> [class*='fa-'], \r\n\t\t\t\t\t> .#{$cust-icon-prefix},\r\n\t\t\t\t\t> img {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.dl-ref {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tli > ul > li > a {\r\n\t\t\t\t\tpadding-left: $nav-padding-x + 0.9375rem;\r\n\t\t\t\t\t& + ul > li > a {\r\n\t\t\t\t\t\tpadding-left: $nav-padding-x + 2.1875rem;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.nav-function-top,\r\n.nav-function-minify {\r\n\t#mhni {\r\n\t\t@extend %not-compatible;\r\n\t}\r\n}", ".mod-high-contrast {\r\n\r\n\t.nav-menu li a,\r\n\t.nav-title,\r\n\t.nav-menu li a [class*='fa-'], \r\n\t.nav-menu li a .#{$cust-icon-prefix},\r\n\t.dl-ref,\r\n\t.btn {\r\n\t\ttext-shadow:\r\n\t\t\t   -1px -1px 0 $black,  \r\n\t\t\t    1px -1px 0 $black,\r\n\t\t\t    -1px 1px 0 $black,\r\n\t\t\t     1px 1px 0 $black;\r\n\t\tcolor: $white !important;\r\n\t\tfont-weight:500 !important;\r\n\t}\r\n\r\n\t.subheader-title,\r\n\th1,\r\n\th2,\r\n\th3,\r\n\th4,\r\n\th5,\r\n\t.settings-panel-title a,\r\n\t.panel-header,\r\n\t.badge-detached,\r\n\t.btn-secondary,\r\n\t.btn-default,\r\n\t.page-header .btn,\r\n\t[class*=\"btn-outline-\"] {\r\n\t\ttext-shadow:\r\n\t\t\t   -1px -1px 0 $white,  \r\n\t\t\t    1px -1px 0 $white,\r\n\t\t\t    -1px 1px 0 $white,\r\n\t\t\t     1px 1px 0 $white;\r\n\t\tcolor: $black !important;\r\n\t\tfont-weight:500;\r\n\t}\r\n\r\n\r\n\t.subheader-title small,\r\n\t.breadcrumb > li > a,\r\n\t.page-content,\r\n\th1 small,\r\n\th2 small,\r\n\th3 small,\r\n\th4 small,\r\n\th5,\r\n\th6,\r\n\tp,\r\n\t.btn-switch + .onoffswitch-title,\r\n\t.onoffswitch-title + .onoffswitch-title-desc,\r\n\t.panel-container,\r\n\t.panel-header .btn {\r\n\t\ttext-shadow: none;\r\n\t\tcolor: $black !important;\r\n\t\tfont-weight: normal !important;\r\n\t}\r\n\r\n}", ".mod-lean-subheader {\r\n\t.subheader {\r\n\t\tmargin:0;\r\n\r\n\t\t.subheader-title {\r\n\r\n\t\t\tmargin-top: 0;\r\n\t\t\tfont-size:1rem;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-weight: 400;\r\n\t\t\ttext-transform: capitalize;\r\n\t\t\tmargin-bottom: 1.5rem;\r\n\r\n\t\t\t&:not(:only-child) {\r\n\t\t\t\tmargin-top: 23px;\r\n\t\t\t}\r\n\r\n\t\t\tsmall {\r\n\r\n\t\t\t\tfont-size: 0.875rem;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\ttext-transform: capitalize;\r\n\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tcontent:\" - \";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.breadcrumb {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t}\r\n\t}\r\n}", "\r\n//$mod-main-boxed-width: map-get($grid-breakpoints, xxl);\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n\t.mod-main-boxed {\r\n\t\t.page-wrapper {\r\n\t\t\tmax-width: $mod-main-boxed-width;\r\n\t\t\tmargin: 0 auto;\t\t\r\n\t\t}\r\n\r\n\t\t&:not(.nav-function-top) {\r\n\t\t\t\r\n\t\t\t#nff {\r\n\t\t\t\tposition:relative;\r\n\r\n\t\t\t\t.onoffswitch-title {\r\n\t\t\t\t\tcolor: $settings-incompat-title;\r\n\t\t\t\t}\r\n\t\t\t\t.onoffswitch-title-desc {\r\n\t\t\t\t\tcolor: $settings-incompat-desc;\r\n\t\t\t\t}\r\n\t\t\t\t&:after {\r\n\t\t\t\t\tcontent: \"DISABLED\";\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbackground: $settings-incompat-bg;\r\n\t\t\t\t\tfont-size: rem($fs-base - 3);\r\n\t\t\t\t\twidth: 65px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tborder: 1px solid $settings-incompat-border;\r\n\t\t\t\t\theight: 22px;\r\n\t\t\t\t\tline-height: 20px;\r\n\t\t\t\t\tborder-radius: $border-radius-plus;\r\n\t\t\t\t\tright: 13px;\r\n\t\t\t\t\ttop: 26%;\r\n\t\t\t\t\tcolor:$fusion-900;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t&.header-function-fixed {\r\n\t\t\t.page-wrapper {\r\n\t\t\t\t.page-header {\r\n\t\t\t\t\twidth: 100%; //IE bug.\r\n\t\t\t\t\tmax-width: $mod-main-boxed-width - 2px; //targets borders\r\n\t\t\t\t\tmargin: 0 auto !important;\r\n\t\t\t\t}\t\t\t\r\n\t\t\t}\r\n\r\n\t\t\t&:not(.nav-function-top):not(.nav-function-fixed) {\r\n\t\t\t\t.page-wrapper {\r\n\t\t\t\t\t.page-sidebar {\r\n\t\t\t\t\t\tposition: absolute !important;\r\n\t\t\t\t\t\ttop:0;\r\n\t\t\t\t\t\tbottom:0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&:not(.nav-function-top):not(.nav-function-hidden):not(.nav-function-minify) {\r\n\t\t\t\t.page-wrapper {\r\n\t\t\t\t\t.page-header {\r\n\t\t\t\t\t\tpadding-left: $nav-width + $header-inner-padding-x; \r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:not(.nav-function-fixed) {\r\n\t\t\t\t\t.page-content {\r\n\t\t\t\t\t\tmargin-left: $nav-width;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-minify:not(.nav-function-top):not(.nav-function-hidden) {\r\n\t\t\t\t.page-wrapper {\r\n\t\t\t\t\t.page-header {\r\n\t\t\t\t\t\tpadding-left:$nav-minify-width + $header-inner-padding-x;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.page-content-wrapper {\r\n\t\t\t\t\t\tmargin-left: $nav-minify-width;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t&.nav-function-hidden {\r\n\r\n\t\t\t&:not(.nav-function-top) {\r\n\t\t\t\t.page-sidebar:after {\r\n\t\t\t\t\tposition:absolute;\r\n\t\t\t\t\tleft: $nav-width;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.nav-function-minify {\r\n\t\t\t\t.page-sidebar:after {\r\n\t\t\t\t\tposition:absolute;\r\n\t\t\t\t\tleft: $nav-minify-width;\r\n\t\t\t\t}\r\n\t\t\t}\t\t\t\r\n\r\n\t\t}\r\n\r\n\t\t/*\r\n\t\twhy did we add this again?\r\n\t\tthis was buggy when open modal with mod main boxed then click on logo for the dropdown, it won't close\r\n\t\t&.nav-function-fixed:not(.nav-function-top):not(.header-function-fixed) {\r\n\t\t\t.page-sidebar {\r\n\t\t\t\ttransform: translateX(0) !important;\r\n\t\t\t\tbox-shadow: none;\r\n\t\t\t}\r\n\r\n\t\t\t.page-wrapper {\r\n\t\t\t\ttransform: translateX(0) !important;\r\n\t\t\t}\r\n\r\n\t\t}*/\r\n\r\n\t\t&.nav-function-fixed {\r\n\t\t\t&:not(.nav-function-top) {\r\n\t\t\t\t.page-wrapper {\r\n\t\t\t\t\t.page-sidebar {\r\n\t\t\t\t\t\tposition:absolute;\r\n\r\n\t\t\t\t\t\t.page-logo {\r\n\t\t\t\t\t\t\tposition:fixed;\r\n\t\t\t\t\t\t\ttop:0;\r\n\t\t\t\t\t\t\tz-index: $cloud;\r\n\t\t\t\t\t\t\t@include box-shadow(0 2px 2px -1px rgba(0,0,0,.1));\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.primary-nav {\r\n\t\t\t\t\t\t\tmargin-top: $header-height;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.nav-function-hidden {\r\n\r\n\t\t\t\t\t.page-logo {\r\n\t\t\t\t\t\tposition: absolute !important;\r\n\t\t\t\t\t\ttransition: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.page-wrapper{\r\n\t\t\tborder-left:1px solid rgba($black, .15);\r\n\t\t\tborder-right:1px solid rgba($black, .15);\r\n\t\t\tbox-shadow: 5px 0 20px 0px rgba(0, 0, 0, 0.1), -5px 0 20px 0px rgba(0, 0, 0, 0.1);\r\n\t\t\toverflow: hidden;\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\r\n\t\t&:not(.header-function-fixed):not(.nav-function-top):not(.modal-open):not(.panel-fullscreen) {\r\n\t\t\t.page-wrapper{\r\n\t\t\t\ttransform: translateX(0) !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n}\r\n\r\n", "/* Hierarchical Navigation */\r\n\r\n.mod-nav-link:not(.nav-function-top):not(.nav-function-minify):not(.mod-hide-nav-icons) {\r\n\r\n\tul.nav-menu:not(.nav-menu-compact) {\r\n\r\n\t\t> li {\r\n\r\n\t\t\ta {\r\n\t\t\t\t> .dl-ref:first-child {\r\n\t\t\t\t\tmargin-left:0 !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t> ul {\r\n\t\t\t\t&:before {\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tz-index: 1;\r\n\t\t\t\t\tleft: $nav-padding-x + 0.5625rem;\r\n\t\t\t\t\ttop: 44px;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tborder-left: 1px solid darken($nav-icon-color, 15%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* addressing all second, third children */\r\n\t\t\t\t> li {\r\n\t\t\t\t\ta {\r\n\t\t\t\t\t\t&:after {\r\n\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 0.4rem;\r\n\t\t\t\t\t\t\theight: 0.4rem;\r\n\t\t\t\t\t\t\tbackground-color: $nav-icon-color;\r\n\t\t\t\t\t\t\tleft: $nav-padding-x + 0.4rem;\r\n\t\t\t\t\t\t\ttop: unquote(\"calc(50% - 0.3rem)\");\r\n\t\t\t\t\t\t\tborder: 1px solid #333;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tz-index: 1;\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&:hover:after {\r\n\t\t\t\t\t\t\tborder-color: transparent;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tli {\r\n\t\t\t\t\t\t> a {\r\n\t\t\t\t\t\t\t&:after{\r\n\t\t\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tli {\r\n\t\t\t\t\ta {\r\n\t\t\t\t\t\ti {\r\n\t\t\t\t\t\t\tmargin-left: 0 !important;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n}", "html:not(.root-text-sm):not(.root-text-lg):not(.root-text-xl) [data-class=\"root-text\"] {\r\n\tbox-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\t\r\n}\r\n\r\n.root-text-sm {\r\n\tfont-size: 15px;\r\n\r\n\t[data-class=\"root-text-sm\"] {\r\n\t\tbox-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;\r\n\t}\r\n}\r\n\r\n.root-text-lg {\r\n\tfont-size: 17px;\r\n\r\n\t[data-class=\"root-text-lg\"] {\r\n\t\tbox-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;\r\n\t}\r\n}\r\n\r\n.root-text-xl {\r\n\tfont-size: 18px;\r\n\r\n\t[data-class=\"root-text-xl\"] {\r\n\t\tbox-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125) !important;\r\n\t}\r\n}\r\n\r\n.mod-bigger-font {\r\n\tfont-size: 18px;\r\n}", "// primary\r\n@include paint($primary-50, bg-primary-50);\r\n@include paint($primary-100, bg-primary-100);\r\n@include paint($primary-200, bg-primary-200);\r\n@include paint($primary-300, bg-primary-300);\r\n@include paint($primary-400, bg-primary-400);\r\n@include paint($primary-500, bg-primary-500);\r\n@include paint($primary-600, bg-primary-600);\r\n@include paint($primary-700, bg-primary-700);\r\n@include paint($primary-800, bg-primary-800);\r\n@include paint($primary-900, bg-primary-900);\r\n\r\n@include brush($primary-50, color-primary-50);\r\n@include brush($primary-100, color-primary-100);\r\n@include brush($primary-200, color-primary-200);\r\n@include brush($primary-300, color-primary-300);\r\n@include brush($primary-400, color-primary-400);\r\n@include brush($primary-500, color-primary-500);\r\n@include brush($primary-600, color-primary-600);\r\n@include brush($primary-700, color-primary-700);\r\n@include brush($primary-800, color-primary-800);\r\n@include brush($primary-900, color-primary-900);\r\n\r\n// success\r\n@include paint($success-50, bg-success-50);\r\n@include paint($success-100, bg-success-100);\r\n@include paint($success-200, bg-success-200);\r\n@include paint($success-300, bg-success-300);\r\n@include paint($success-400, bg-success-400);\r\n@include paint($success-500, bg-success-500);\r\n@include paint($success-600, bg-success-600);\r\n@include paint($success-700, bg-success-700);\r\n@include paint($success-800, bg-success-800);\r\n@include paint($success-900, bg-success-900);\r\n\r\n@include brush($success-50, color-success-50);\r\n@include brush($success-100, color-success-100);\r\n@include brush($success-200, color-success-200);\r\n@include brush($success-300, color-success-300);\r\n@include brush($success-400, color-success-400);\r\n@include brush($success-500, color-success-500);\r\n@include brush($success-600, color-success-600);\r\n@include brush($success-700, color-success-700);\r\n@include brush($success-800, color-success-800);\r\n@include brush($success-900, color-success-900);\r\n\r\n// info\r\n@include paint($info-50, bg-info-50);\r\n@include paint($info-100, bg-info-100);\r\n@include paint($info-200, bg-info-200);\r\n@include paint($info-300, bg-info-300);\r\n@include paint($info-400, bg-info-400);\r\n@include paint($info-500, bg-info-500);\r\n@include paint($info-600, bg-info-600);\r\n@include paint($info-700, bg-info-700);\r\n@include paint($info-800, bg-info-800);\r\n@include paint($info-900, bg-info-900);\r\n\r\n@include brush($info-50, color-info-50);\r\n@include brush($info-100, color-info-100);\r\n@include brush($info-200, color-info-200);\r\n@include brush($info-300, color-info-300);\r\n@include brush($info-400, color-info-400);\r\n@include brush($info-500, color-info-500);\r\n@include brush($info-600, color-info-600);\r\n@include brush($info-700, color-info-700);\r\n@include brush($info-800, color-info-800);\r\n@include brush($info-900, color-info-900);\r\n\r\n// warning\r\n@include paint($warning-50, bg-warning-50);\r\n@include paint($warning-100, bg-warning-100);\r\n@include paint($warning-200, bg-warning-200);\r\n@include paint($warning-300, bg-warning-300);\r\n@include paint($warning-400, bg-warning-400);\r\n@include paint($warning-500, bg-warning-500);\r\n@include paint($warning-600, bg-warning-600);\r\n@include paint($warning-700, bg-warning-700);\r\n@include paint($warning-800, bg-warning-800);\r\n@include paint($warning-900, bg-warning-900);\r\n\r\n@include brush($warning-50, color-warning-50);\r\n@include brush($warning-100, color-warning-100);\r\n@include brush($warning-200, color-warning-200);\r\n@include brush($warning-300, color-warning-300);\r\n@include brush($warning-400, color-warning-400);\r\n@include brush($warning-500, color-warning-500);\r\n@include brush($warning-600, color-warning-600);\r\n@include brush($warning-700, color-warning-700);\r\n@include brush($warning-800, color-warning-800);\r\n@include brush($warning-900, color-warning-900);\r\n\r\n// danger\r\n@include paint($danger-50, bg-danger-50);\r\n@include paint($danger-100, bg-danger-100);\r\n@include paint($danger-200, bg-danger-200);\r\n@include paint($danger-300, bg-danger-300);\r\n@include paint($danger-400, bg-danger-400);\r\n@include paint($danger-500, bg-danger-500);\r\n@include paint($danger-600, bg-danger-600);\r\n@include paint($danger-700, bg-danger-700);\r\n@include paint($danger-800, bg-danger-800);\r\n@include paint($danger-900, bg-danger-900);\r\n\r\n@include brush($danger-50, color-danger-50);\r\n@include brush($danger-100, color-danger-100);\r\n@include brush($danger-200, color-danger-200);\r\n@include brush($danger-300, color-danger-300);\r\n@include brush($danger-400, color-danger-400);\r\n@include brush($danger-500, color-danger-500);\r\n@include brush($danger-600, color-danger-600);\r\n@include brush($danger-700, color-danger-700);\r\n@include brush($danger-800, color-danger-800);\r\n@include brush($danger-900, color-danger-900);\r\n\r\n// fusion\r\n@include paint($fusion-50, bg-fusion-50);\r\n@include paint($fusion-100, bg-fusion-100);\r\n@include paint($fusion-200, bg-fusion-200);\r\n@include paint($fusion-300, bg-fusion-300);\r\n@include paint($fusion-400, bg-fusion-400);\r\n@include paint($fusion-500, bg-fusion-500);\r\n@include paint($fusion-600, bg-fusion-600);\r\n@include paint($fusion-700, bg-fusion-700);\r\n@include paint($fusion-800, bg-fusion-800);\r\n@include paint($fusion-900, bg-fusion-900);\r\n\r\n@include brush($fusion-50, color-fusion-50);\r\n@include brush($fusion-100, color-fusion-100);\r\n@include brush($fusion-200, color-fusion-200);\r\n@include brush($fusion-300, color-fusion-300);\r\n@include brush($fusion-400, color-fusion-400);\r\n@include brush($fusion-500, color-fusion-500);\r\n@include brush($fusion-600, color-fusion-600);\r\n@include brush($fusion-700, color-fusion-700);\r\n@include brush($fusion-800, color-fusion-800);\r\n@include brush($fusion-900, color-fusion-900);\r\n\r\n//white\r\n@include brush($white, color-white);\r\n@include brush(lighten($black, 13.5%), color-black);\r\n\r\n\r\n@include paint-gradient($primary-900, bg-primary-gradient);\r\n@include paint-gradient($danger-900, bg-danger-gradient);\r\n@include paint-gradient($info-900, bg-info-gradient);\r\n@include paint-gradient($warning-900, bg-warning-gradient);\r\n@include paint-gradient($success-900, bg-success-gradient);\r\n@include paint-gradient($fusion-900, bg-fusion-gradient);", "/* Custom Webkit Scrollbar */\r\n/* http://css-tricks.com/custom-scrollbars-in-webkit/ */\r\n\r\n$trackPieceColor:                #efefef;\r\n$handleBar:                      $fusion-100;\r\n$handleBarHover:                 darken($handleBar, 10%);\r\n$modalScrollSize:                8px;\r\n\r\nbody:not(.mobile-detected),\r\nbody:not(.mobile-detected).modal-open .modal {\r\n    &::-webkit-scrollbar {\r\n        height: $modalScrollSize;\r\n        width: $modalScrollSize;\r\n    }\r\n\r\n    &::-webkit-scrollbar:hover{\r\n    \tbackground-color:rgba(0,0,0,.01)\r\n    }\r\n\r\n    &::-webkit-scrollbar-track-piece {\r\n        background-color: $trackPieceColor;\r\n    }\r\n\r\n    &::-webkit-scrollbar-track-piece:hover {\r\n        background-color: darken($trackPieceColor, 12%);\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb:vertical {\r\n        background-color: $handleBar;\r\n    }\r\n\r\n    &::-webkit-scrollbar-thumb:vertical:hover {\r\n        background-color: $handleBarHover;\r\n    }\r\n}\r\n\r\n/* \r\n * Left Panel custom scroll \r\n */\r\n\r\n.page-sidebar .primary-nav::-webkit-scrollbar-track-piece {\r\n    background-color: $trackPieceColor;\r\n}\r\n\r\n.page-sidebar .primary-nav::-webkit-scrollbar-thumb:vertical {\r\n    background-color: #666;\r\n}\r\n\r\n.page-sidebar .primary-nav::-webkit-scrollbar {\r\n    height: $modalScrollSize/2;\r\n    width: $modalScrollSize/2;\r\n}\r\n\r\n.page-sidebar .primary-nav:hover::-webkit-scrollbar-corner {\r\n    width: 40px;\r\n}\r\n\r\n.page-sidebar .primary-nav::-webkit-scrollbar-track-piece {\r\n\tbackground-color: $trackPieceColor;\r\n}\r\n\r\n.page-sidebar .primary-nav::-webkit-scrollbar-thumb:vertical {\r\n\tbackground-color: #666;\r\n}", ":-webkit-full-screen  {\r\n   \t[data-action=\"app-fullscreen\"] {\r\n\t\tcolor: $disabled;\r\n\t} \r\n}\r\n\r\n:-moz-full-screen {\r\n   \t[data-action=\"app-fullscreen\"] {\r\n\t\tcolor: $disabled;\r\n\t} \r\n}", "@media print {\r\n    @page {\r\n        size: $print-page-size $print-page-type;\r\n        margin: $print-page-margin;\r\n\t\tpadding: 0;\r\n\t\tborder: none;\r\n\t\tborder-collapse: collapse;\r\n    }\r\n\r\n    *:not(.keep-print-font) {\r\n    \tcolor:#333 !important;\r\n    \tbackground: transparent !important;\r\n    \tfont-family: Arial, Helvetica, sans-serif !important;\r\n    \tletter-spacing: normal !important;\r\n    \tfont-size: 10pt !important;\r\n    \tline-height: 1.7 !important;\r\n    \ttext-transform: none !important;\r\n    \ttransition: none !important;\r\n    }\r\n\r\n    table { font-size: 80%; }\r\n\r\n    .card,\r\n    .card-body,\r\n    .container {\r\n    \tdisplay: inline;\r\n    \tpadding: 0;\r\n    \tmargin: 0;\r\n    \tborder: 0;\r\n    }\r\n\r\n\r\n\ta:link { \r\n\t\tfont-weight: bold; \r\n\t\ttext-decoration: underline; \r\n\t\tcolor: #06c;\r\n\t}\r\n\r\n\t.subheader-title {\r\n\t\tfont-size: 14pt !important;\r\n\r\n\t\tsmall {\r\n\t\t\tfont-size: 12pt !important;\r\n\t\t}\r\n\t}\r\n\r\n\th1, h2, h3, h4, h5, h6 {\r\n\t\tfont-weight: bold !important;\r\n\t}\r\n\r\n\t.page-sidebar,\r\n\t.btn,\r\n\t.page-header,\r\n\t.page-footer {\r\n\t\tdisplay: none !important;\r\n\t}\r\n\r\n\t.page-wrapper,\r\n\t.page-content,\r\n\t.container.card {\r\n\t   \tpadding: 0;\r\n\t   \tdisplay: block;\r\n\t   \tmargin:0;\r\n\t   \tborder:0 !important;\r\n\t   \twidth:auto;\r\n\t   \tfloat: none;\r\n\t}\r\n\t.panel-header > * {\r\n\t\tfont-weight: bold !important;\r\n\t}\r\n\t.card.panel {\r\n\t\tborder-color: #333 !important;\r\n\t}\r\n\r\n\t.clearfix:after {\r\n\t    content: '';\r\n\t    clear: both;\r\n\t    display: table;\r\n\t}\r\n}"]}