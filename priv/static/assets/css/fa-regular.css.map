{"version": 3, "sources": ["fa-regular.scss", "fa-regular.css"], "names": [], "mappings": "AAAA;;;ECGE;ADGF;EACE,iCAAiC;EACjC,kBAAkB;EAClB,gBAAgB;EAChB,0CAA+C;EAC/C,wTAIoE,EAAA;;AAGtE;EACE,iCAAiC;EACjC,gBAAgB,EAAA", "file": "fa-regular.css", "sourcesContent": ["/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\r\n@import 'variables';\r\n\r\n@font-face {\r\n  font-family: 'Font Awesome 5 Pro';\r\n  font-style: normal;\r\n  font-weight: 400;\r\n  src: url('#{$fa-font-path}/fa-regular-400.eot');\r\n  src: url('#{$fa-font-path}/fa-regular-400.eot?#iefix') format('embedded-opentype'),\r\n  url('#{$fa-font-path}/fa-regular-400.woff2') format('woff2'),\r\n  url('#{$fa-font-path}/fa-regular-400.woff') format('woff'),\r\n  url('#{$fa-font-path}/fa-regular-400.ttf') format('truetype'),\r\n  url('#{$fa-font-path}/fa-regular-400.svg#fontawesome') format('svg');\r\n}\r\n\r\n.far {\r\n  font-family: 'Font Awesome 5 Pro';\r\n  font-weight: 400;\r\n}\r\n", "/*!\r\n * Font Awesome Pro 5.0.7 by @fontawesome - https://fontawesome.com\r\n * License - https://fontawesome.com/license (Commercial License)\r\n */\n@font-face {\n  font-family: 'Font Awesome 5 Pro';\n  font-style: normal;\n  font-weight: 400;\n  src: url(\"../webfonts/fa-regular-400.eot\");\n  src: url(\"../webfonts/fa-regular-400.eot?#iefix\") format(\"embedded-opentype\"), url(\"../webfonts/fa-regular-400.woff2\") format(\"woff2\"), url(\"../webfonts/fa-regular-400.woff\") format(\"woff\"), url(\"../webfonts/fa-regular-400.ttf\") format(\"truetype\"), url(\"../webfonts/fa-regular-400.svg#fontawesome\") format(\"svg\"); }\n\n.far {\n  font-family: 'Font Awesome 5 Pro';\n  font-weight: 400; }\n"]}