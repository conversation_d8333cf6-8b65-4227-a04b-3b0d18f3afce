#progressbar li {
    margin-left: 10%;
}

.flex-container { 
    display: flex;
     justify-content: center;
     align-items: center;
  
  }
  
  .flex-container > div {  
    margin: 10px;
    padding: 5px;
    font-size: 30px;
  }
body {
    color: #000;
    overflow-x: hidden;
    height: 100%;
    background-image: linear-gradient(to right, #fff, #fff);
    /* background-image: linear-gradient(to right, #23BFCD, #4ED16A); */
    background-repeat: no-repeat
}
.progress {
    height: 13px;
    width: 100%;
    margin: 10px 20px;
    background-color: #b6cde6
}
.progress-bar {
    background-color: #23BFCD
}
.count {
    width: 350px
}
.fit-image {
    width: 100%;
    object-fit: cover
}
.card {
    padding-bottom: 20px
}
.card-header {
    padding: 20px 60px
}
.card-body {
    display: none;
    padding-left: 55px;
    padding-right: 55px
}
.card-body.show {
    display: block
}
.yellow-text {
    color: #252021
}
.card-block {
    width: 235px;
    border: 1px solid lightgrey;
    padding: 20px;
    border-radius: 5px !important;
    background-color: #FAFAFA;
    margin-bottom: 30px
}
.fa-check {
    color: #FBC02D;
    border-radius: 50%;
    border: 2px solid #FBC02D;
    padding: 5px
}
.fa-circle {
    color: #FAFAFA;
    border-radius: 50%;
    border: 2px solid #BDBDBD;
    padding: 5px 6.155px
}
.pic,
.pic-0 {
    height: 90px;
    width: 130px
}
.pic {
    margin-top: 30px;
    margin-bottom: 20px
}
.btn-blue {
    margin-top: 40px;
    background-color: #252021;
    color: #fff;
    width: 28%
}
.btn-blue:hover {
/* background-color: #000 */
    background-color: #23BFCD
}

.btn-prev {
    margin-top: 40px;
    background-color: #23BFCD;
    color: #fff;
    width: 28%;
}
.btn-prev:hover {
    margin-top: 40px;
    background-color: #252021;
    color: #fff;
    width: 28%;
}


.fa-long-arrow-right {
    float: right;
    margin-top: 4px
}
.fa-long-arrow-left {
    float: left;
    margin-top: 4px
}

.tab_active{
    background-color:#23BFCD !important;
    color:#fff !important;
}

button:focus {
    -moz-box-shadow: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    outline-width: 0
}