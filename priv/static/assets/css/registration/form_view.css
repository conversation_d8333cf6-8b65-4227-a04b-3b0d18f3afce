.cards{
    display: none;
}

.required{
    color: red;
}

.wizard .content {
    width: 100%;
    height: auto;
    position: relative;
}

.wizard .content > .body {
    width: 100%;
    height: auto;
    padding: 15px;
    position: relative;
}

/* form input[type='text'], form input[type='email']{
    text-transform: uppercase
} */

control[readonly], fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #eeeeee;
    opacity: 1;
}

.col-md-6 {
    width: 50%;
}

.form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px;
}

.form-group {
    margin-bottom: 15px;
}

* {
    box-sizing: border-box;
}

label {
    display: inline-block;
    margin-bottom: 0.3rem;
    font-size: 15px;
   
}

.form-control {
    display: block;
    width: 90%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555555;
    background-color: #ffffff;
    background-image: none;
    border: 1px solid #cccccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%);
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}


label {
    font-weight: 400;
}
label {
   
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
}

label {
    
    margin-bottom: 0.3rem;
}