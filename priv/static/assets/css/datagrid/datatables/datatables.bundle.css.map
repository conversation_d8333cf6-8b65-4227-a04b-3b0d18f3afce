{"version": 3, "sources": ["datatables.bundle.css", "../../../scss/_modules/variables.scss"], "names": [], "mappings": "AAAA;EACE,WAAW;EACX,0BAA0B;EAC1B,6BAA6B;EAC7B,0BAA0B;EAC1B,oCAAoC;EACpC,iBAAiB,EAAA;;AAEnB;;EAEE,+BAA+B;EAC/B,uBAAuB,EAAA;;AAEzB;;EAEE,kBAAkB,EAAA;;AAEpB;;EAEE,mBAAmB,EAAA;;AAGrB;EACE,mBAAmB;EACnB,gBAAgB;EAChB,mBAAmB,EAAA;;AAErB;EACE,WAAW;EACX,qBAAqB,EAAA;;AAEvB;EACE,iBAAiB,EAAA;;AAEnB;EACE,mBAAmB;EACnB,mBAAmB;EACnB,gBAAgB,EAAA;;AAElB;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW,EAAA;;AAEb;EACE,mBAAmB;EACnB,mBAAmB,EAAA;;AAErB;EACE,SAAS;EACT,mBAAmB;EACnB,iBAAiB,EAAA;;AAEnB;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAyB;MAAzB,kBAAyB;UAAzB,yBAAyB,EAAA;;AAE3B;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,mBAAmB;EACnB,iBAAiB;EACjB,kBAAkB;EAClB,cAAc,EAAA;;AAGhB;;;;EAIE,mBAAmB,EAAA;;AAErB;;EAEE,aAAa,EAAA;;AAEf;;;;;EAKE,eAAe;EACf,kBAAkB,EAAA;;AAEpB;;;;;;;;;EASE,kBAAkB;EAClB,aAAa;EACb,cAAc;EACd,YAAY,EAAA;;AAEd;;;;;EAKE,UAAU;EACV,gBAAgB,EAAA;;AAElB;;;;;EAKE,YAAY;EACZ,gBAAgB,EAAA;;AAElB;;EAEE,UAAU,EAAA;;AAEZ;;EAEE,UAAU,EAAA;;AAGZ;EACE,2BAA2B,EAAA;;AAG7B;EACE,gBAAgB;EAChB,wBAAwB;EACxB,2BAA2B,EAAA;;AAE7B;;;;;;EAME,aAAa,EAAA;;AAEf;;EAEE,gBAAgB,EAAA;;AAGlB;EACE,+BAAuB;UAAvB,uBAAuB,EAAA;;AAEzB;EACE,wBAAwB;EACxB,gBAAgB,EAAA;;AAGlB;EACE;;;;IAIE,kBAAkB,EAAA,EACnB;;AAEH;EACE,mBAAmB,EAAA;;AAErB;;;EAGE,QAAQ;EACR,aAAa,EAAA;;AAEf;;;EAGE,QAAQ,EAAA;;AAGV;;EAEE,oBAAoB,EAAA;;AAEtB;;;EAGE,qBAAqB,EAAA;;AAEvB;;EAEE,sBAAsB,EAAA;;AAGxB;EACE,sBAAsB,EAAA;;AAGxB;EACE,SAAS,EAAA;;AAEX;EACE,eAAe,EAAA;;AAEjB;EACE,gBAAgB,EAAA;;AAGlB;EACE,kBAAkB;EAClB,WAAW;EACX,UAAU;EACV,YAAY;EACZ,8BAAsB;UAAtB,sBAAsB;EACtB,mBAAmB;EACnB,eAAe,EAAA;;AAGjB;EACE,mBAAmB,EAAA;;AAGrB;EACE,kBAAkB;EAClB,aAAa;EACb,yBAAyB;EACzB,6IAA6I,EAAA;;AAE/I;EACE,WAAW;EACX,gBAAgB,EAAA;;AAElB;EACE,UAAU;EACV,iBAAiB,EAAA;;AAGnB;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,gCAAwB;UAAxB,wBAAwB;EACxB,sBAAsB;EACtB,WAAW;EACX,8BAAsB;UAAtB,sBAAsB;EACtB,kBAAkB,EAAA;;AAEpB;EACE,cAAc;EACd,SAAS;EACT,UAAU;EACV,gBAAgB;EAChB,WAAW,EAAA;;AAEb;EACE,kBAAkB,EAAA;;AAEpB;EACE,mBAAmB,EAAA;;AAErB;EACE,yBAAyB,EAAA;;AAE3B;EACE,mBAAmB;EACnB,gBAAgB;EAChB,6BAA6B,EAAA;;AAE/B;EACE,YAAY;EACZ,WAAW;EACX,cAAc,EAAA;;AAEhB;EACE,mBAAmB;EACnB,gBAAgB;EAChB,6BAA6B,EAAA;;AAG/B;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,8BAA8B;EAC9B,8GAA8G;EAC9G,WAAW,EAAA;;AAGb;EACE,YAAY;EACZ,WAAW;EACX,cAAc,EAAA;;AAGhB;EACE;IACE,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAe7B;EACE;IACE,iCAAiC;IACjC,yBAAyB,EAAA,EAAA;;AAS7B;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,uBAAuB;EACvB,sBAAsB;EACtB,kDAA0C;UAA1C,0CAA0C;EAC1C,kBAAkB;EAClB,kBAAkB;EAClB,WAAW,EAAA;;AAEb;EACE,cAAc;EACd,SAAS;EACT,mBAAmB;EACnB,6BAA6B;EAC7B,yBAAyB,EAAA;;AAE3B;EACE,YAAY,EAAA;;AAGd;EACE,kBAAkB;EAClB,sBAAsB;EACtB,gBAAgB,EAAA;;AAGlB;EACE,aAAa,EAAA;;AAGf;EACE,cAAc;EACd,aAAa;EACb,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,kBAAkB;EAClB,gBAAgB,EAAA;;AAElB;EACE,mBAAmB,EAAA;;AAErB;EACE,mBAAmB,EAAA;;AAErB;EACE,mBAAmB,EAAA;;AAErB;EACE,kCAAkC;EAClC,+BAAmB;OAAnB,mBAAmB,EAAA;;AAErB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,gBAAgB,EAAA;;AAGlB;EACE,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,kBAAkB;EAClB,gBAAgB,EAAA;;AAElB;EACE,mBAAmB,EAAA;;AAErB;EACE,mBAAmB,EAAA;;AAErB;EACE,mBAAmB,EAAA;;AAErB;EACE,kCAAkC;EAClC,+BAAmB;OAAnB,mBAAmB,EAAA;;AAErB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,mBAAmB;EACnB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;EACE,gBAAgB,EAAA;;AAElB;EACE,eAAe,EAAA;;AAEjB;EACE,aAAa,EAAA;;AAGf;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,YAAY,EAAA;;AAGd;EACE;IACE,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,oBAAoB,EAAA;EAEtB;IACE,WAAW,EAAA,EACZ;;AAEH;;;EAGE,yBAAyB,EAAA;;AAE3B;;;EAGE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;EACZ,qBAAqB;EACrB,8BAAsB;UAAtB,sBAAsB;EACtB,cAAc;EACd,YAAY;EACZ,yBAAyB;EACzB,kBAAkB;EAClB,8BAA8B;EAC9B,+BAA+B;EAC/B,6CAA6C;EAC7C,gDAAgD;EAChD,iDAAiD;EACjD,qDAAqD;EACrD,kDAAkD,EAAA;;AAGpD;EACE,6BAA6B;EAC7B,0CAA0C;EAC1C,YAAY,EAAA;;AAGd;EACE,UAAU;EACV,yBAAyB;EACzB,YAAY,EAAA;;AAGd;EACE,uBAAuB;EACvB,gBAAgB,EAAA;;AAGlB;;EAEE,8BAA8B;EAC9B,2BAA2B;EAC3B,uBAAuB,EAAA;;AAGzB;;EAEE,gBAAgB;EAChB,oBAAoB,EAAA;;AAEtB;;;;;;;;;;;;EAYE,aAAa,EAAA;;AAEf;;;;EAIE,gBAAgB,EAAA;;AAGlB;;EAEE,gBAAgB;EAChB,wBAAwB;EACxB,uBAAuB,EAAA;;AAGzB;EACE,uBAAuB,EAAA;;AAGzB;EACE,uBAAuB,EAAA;;AAGzB;;EAEE,uBAAuB;EACvB,wBAAwB;EACxB,2BAA2B,EAAA;;AAG7B;EACE,0BAA0B,EAAA;;AAG5B;EACE,6BAA6B,EAAA;;AAG/B;EACE;IACE,aAAa,EAAA,EACd;;AAGH;;EAEE,6CAAqC;UAArC,qCAAqC,EAAA;;AAGvC;;EAEE,6CAAqC;UAArC,qCAAqC,EAAA;;AAGvC;;;EAGE,0BAA0B,EAAA;;AAE5B;;;EAGE,wBAAwB,EAAA;;AAE1B;;EAEE,kBAAkB;EAClB,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;;EAEE,SAAS;EACT,SAAS;EACT,YAAY;EACZ,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,YAAY;EACZ,uBAAuB;EACvB,mBAAmB;EACnB,gCAAwB;UAAxB,wBAAwB;EACxB,+BAAuB;UAAvB,uBAAuB;EACvB,kBAAkB;EAClB,yBAAyB;EACzB,8CAA8C;EAC9C,iBAAiB;EACjB,YAAY;EACZ,yBAAyB,EAAA;;AAE3B;;EAEE,YAAY;EACZ,yBAAyB,EAAA;;AAE3B;;EAEE,kBAAkB,EAAA;;AAEpB;;EAEE,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,WAAW;EACX,mBAAmB;EACnB,iBAAiB;EACjB,gBAAgB,EAAA;;AAElB;;EAEE,kBAAkB;EAClB,eAAe,EAAA;;AAEjB;;EAEE,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,WAAW;EACX,iBAAiB;EACjB,kBAAkB;EAClB,cAAc;EACd,kBAAkB;EAClB,YAAY;EACZ,uBAAuB;EACvB,mBAAmB;EACnB,gCAAwB;UAAxB,wBAAwB;EACxB,+BAAuB;UAAvB,uBAAuB;EACvB,kBAAkB;EAClB,yBAAyB;EACzB,8CAA8C;EAC9C,iBAAiB;EACjB,YAAY;EACZ,yBAAyB,EAAA;;AAE3B;;EAEE,YAAY;EACZ,yBAAyB,EAAA;;AAE3B;EACE,kBAAkB,EAAA;;AAEpB;EACE,kCAAkC,EAAA;;AAEpC;EACE,qBAAqB;EACrB,qBAAqB;EACrB,SAAS;EACT,UAAU,EAAA;;AAEZ;EACE,gCAAgC;EAChC,gBAAgB,EAAA;;AAElB;EACE,cAAc,EAAA;;AAEhB;EACE,mBAAmB,EAAA;;AAErB;EACE,qBAAqB;EACrB,eAAe;EACf,iBAAiB,EAAA;;AAGnB;EACE,eAAe;EACf,8BAAsB;UAAtB,sBAAsB;EACtB,MAAM;EACN,OAAO;EACP,YAAY;EACZ,WAAW;EACX,YAAY;EACZ,iBAAiB,EAAA;;AAEnB;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,SAAS;EACT,QAAQ;EACR,UAAU;EACV,WAAW;EACX,cAAc;EACd,YAAY;EACZ,YAAY;EACZ,cAAc;EACd,yBAAyB;EACzB,uBAAuB;EACvB,oBAAoB;EACpB,kDAA0C;UAA1C,0CAA0C,EAAA;;AAE5C;EACE,kBAAkB;EAClB,YAAY,EAAA;;AAEd;EACE,kBAAkB;EAClB,QAAQ;EACR,UAAU;EACV,WAAW;EACX,YAAY;EACZ,yBAAyB;EACzB,yBAAyB;EACzB,kBAAkB;EAClB,kBAAkB;EAClB,eAAe;EACf,WAAW,EAAA;;AAEb;EACE,yBAAyB,EAAA;;AAE3B;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,8BAA8B,EAAA;;AAGhC;EACE;IACE,UAAU,EAAA,EACX;;AAEH;EACE,gBAAgB,EAAA;;AAGlB;EACE,yBAAyB,EAAA;;AAG3B;EACE,iBAAiB,EAAA;;AAGnB;;EAEE,yBAAyB;EACzB,mBAAmB;EACnB,sBAAsB;EACtB,iBAAiB;EACjB,gBAAgB,EAAA;;AAGlB;EACE,yBAAyB,EAAA;;AAG3B;EACE,6BAA6B;EAC7B,YAAY;EACZ,mBAAmB;EACnB,0BAA0B;EAC1B,oBAAoB;EACpB,aAAa,EAAA;;AAGf;EACE,0BAA0B;EAC1B,oBAAoB,EAAA;;AAGtB;EACE,kBAAkB,EAAA;;AAGpB;EACE,kBAAkB;EAClB,YAAY,EAAA;;AAGd;EACE,yBAAyB,EAAA;;AAE3B;;EAEE,mBAAmB,EAAA;;AAErB;EACE,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,WAAW;EACX,8BAA8B;EAC9B,YAAY;EACZ,mDAA2C;UAA3C,2CAA2C;EAC3C,iBAAiB;EACjB,kBAAkB;EAClB,cAAc;EACd,UAAU;EACV,aAAa,EAAA;;AAEf;EACE,2FAA2F,EAAA;;AAE7F;EACE,UAAU,EAAA;;AAEZ;;EAEE,aAAa,EAAA;;AAGf;EACE,uBAAuB,EAAA;;AAGzB;;EAEE,yBAAyB,EAAA;;AAE3B;;;EAGE,yBAAyB,EAAA;;AAE3B;;;EAGE,yBAAyB,EAAA;;AAE3B;;;;;;;EAOE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;EACE,yBAAyB,EAAA;;AAE3B;;;EAGE,yBAAyB,EAAA;;AAE3B;;EAEE,kBAAkB,EAAA;;AAEpB;;;EAGE,cAAc;EACd,kBAAkB;EAClB,UAAU;EACV,SAAS;EACT,WAAW;EACX,YAAY;EACZ,8BAAsB;UAAtB,sBAAsB,EAAA;;AAExB;;EAEE,YAAY;EACZ,gBAAgB;EAChB,iBAAiB;EACjB,uBAAuB;EACvB,kBAAkB,EAAA;;AAEpB;;EAEE,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;EACjB,kBAAkB;EAClB,mFAAmF,EAAA;;AAGrF;;EAEE,kBAAkB,EAAA;;AAGpB;EACE;;IAEE,cAAc;IACd,cAAc,EAAA,EACf;;AAEH;;;EAGE,YAAY,EAAA;;AAEd;;;EAGE,cAAc,EAAA;;AC9+BhB;4EDk7B4E;ACh7B5E,+CAAA;AAQA;;;;;;kFDg7BkF;ACx6BlF;4ED06B4E;ACp6B5E;4EDs6B4E;ACp6B5E,cAAA;AAYA,kBAAA;AAYA,iBAAA;AAYA,kBAAA;AAYA,cAAA;AAYA,eAAA;AAYA,kBAAA;AA6EA;4EDwxB4E;ACpxB5E;4EDsxB4E;ACvwBR,kGAAA;AACG,2EAAA;AAavE,+BAAA;AAgBA,6BAAA;AACA,wFAAA;AAQA;4EDuuB4E;AC9sB5E,oCAAA;AAYA,UAAA;AACA,wIAAA;AASA,UAAA;AAIA,aAAA;AAMA,qDAAA;AAGA,mCAAA;AAGA,oBAAA;AAKA,iBAAA;AASA,WAAA;AAEA,UAAA;AAIA,UAAA;AAOA,gBAAA;AAMA,UAAA;AAKA,UAAA;AAKA,eAAA;AAIA,iBAAA;AAUA,aAAA;AAIA,qBAAA;AAKA,WAAA;AASA,cAAA;AASA,oBAAA;AAOA,aAAA;AAcA,aAAA;AAYA,UAAA;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;CD8lBC;ACjkBD,UAAA;AAuBA,aAAA;AAIA;4ED0iB4E;ACliB5E,6EAAA;AAEiC,WAAA;AACD,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACA,WAAA;AACC,WAAA;AAEjC;4EDkiB4E;AChiBlE,mFAAA;AAOV;4ED4hB4E;AC1hBG,mEAAA;AAE/E;4ED2hB4E;ACrhB5E,oEAAA;AAUA;4ED8gB4E;AC1gB5E;4ED4gB4E;AC1gB5B,0BAAA;AACH,iBAAA;AAG7C;4ED0gB4E;ACrgB5E;4EDugB4E;ACjgB5E;4EDmgB4E;AC/f5E;4EDigB4E;AC9f5E,WAAA;AAOA,WAAA;AAMA,SAAA;AAEoD,6DAAA;AACC,8DAAA;AACC,qDAAA;AAEtD,gCAAA;AAGA,qBAAA;AAC4D,uBAAA;AAO5D,QAAA;AAYA,uBAAA;AASA,UAAA;AAKA,sBAAA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4ED8e4E;AC/c5E,oBAAA;AACA,eAAA;AAMA,uBAAA;AAOA,mBAAA;AAOA,kBAAA;AAIA,cAAA;AAIA,cAAA;AAKA,eAAA;AAIA,gCAAA;AAGA,qBAAA;AACA,mCAAA;AAGA,mBAAA;AAQA,2CAAA;AAK6C,kBAAA;AAE7C,gCAAA;AAKyE,+CAAA;AAEzE;4ED8Z4E;AC5Z5E,eAAA;AAIA;4ED2Z4E;ACpZ5E;4EDsZ4E;AClZ5E;4EDoZ4E;ACvY5E;4EDyY4E;AClY5E;4EDoY4E;AC5X5E;4ED8X4E;ACtX5E;4EDwX4E;ACnX5E,oBAAA;ADkPA;EAAoD,WAAY,EAAA;;AAEhE;;;EAGC,2CCr/B2B,EAAA;;ADw/B5B;EACC,2CCz/B2B,EAAA;;AD4/B5B;;;EAIE,0CC7/B0B,EAAA;;ADy/B5B;EAQE,0CCjgC0B,EAAA;;ADqgC5B,kHAAA;AACA,kGAAA;AACA;EACC,wBAAwB;EACxB,8BAA8B,EAAA;;AAI/B;;EAEC,kBAAkB,EAAA;;AAGnB;;;;EAKC,MAAM;EACN,SAAS;EACT,OAAO;EACP,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,YAAW;EACX,WAAW;EACX,wBAAgB;UAAhB,gBAAgB;EAChB,gBAAgB;EAChB,SAAS;EACT,SAAS;EACT,yBCtiC2B,EAAA;;ADyiC5B;;;;EAIC,yBC5iC2B,EAAA;;AD+iC5B;;;;;EAKC,sBAAsB;EACtB,gBAAgB;EAChB,eAAe;EACf,iCAAiC,EAAA;;AAElC;;;;;EAKC,wBAAwB;EACxB,gBAAgB;EAChB,eAAe;EACf,iCAAiC,EAAA;;AAGlC;EACC,cClkC0B;EDmkC1B,eAAe;EACf,8BAA8B;EAC9B,iCAAiC,EAAA;;AAGlC;EACC,2BAAoB;EAApB,2BAAoB;EAApB,oBAAoB,EAAA;;AAGrB;EACC,UAAU,EAAA;EADX;IAIE,iCAAiC;IACjC,kBAAkB,EAAA;IALpB;MAQG,gBAAgB;MAChB,iCAAiC;MACjC,cCtiC2C;MDuiC3C,WAAW;MACX,YAAY;MACZ,kBAAkB;MAClB,UAAU;MACV,UAAU;MACV,iBAAiB,EAAA;IAhBpB;MAoBG,WAAW;MACX,kBAAkB;MAClB,MAAM;MACN,SAAS;MACT,OAAO;MACP,WAAW;MACX,mBCxjC4C,EAAA;;AD+jC/C;EACC;;;;IAIC,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,wBAAuB;QAAvB,qBAAuB;YAAvB,uBAAuB,EAAA,EACvB;;AAKF;EACC,uBAAuB,EAAA;;AAGxB;EACC,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,eAAe;EACf,kBAAkB;EAClB,eAAe;EACf,mBAAmB;EACnB,qBAAqB,EAAA;;AAGtB;EACC,mBAAmB,EAAA;;AAGpB,aAAA;AACA;EACC,iBAAiB;EACjB,eAAe;EAEf,iDCnoCkB;UDmoClB,yCCnoCkB,EAAA;ED+nCnB;;IAQE,iBAAiB,EAAA;EARnB;IAYE,4BAA4B,EAAA;;AAI9B;EACC,YAAY;EACZ,sBCjpCkB,EAAA;;ADopCnB;EACC,6BAA6B,EAAA;;AAG9B;EACC,8BAA8B,EAAA;;AAG/B;EACC,mBC/qC2B;EDgrC3B,gBAAgB;EACb,eAAe,EAAA;;AAGnB;EACE,yBCrrC0B,EAAA;;ADwrC5B,iBAAA;AACA;;;EAKE,WAAU;EACV,kBAAiB;EACjB,UAAS;EACT,QAAO;EACP,WAAU;EACV,UAAS;EACT,mBChsCyB;EDisCzB,UAAU;EACP,mDCprCc;UDorCd,2CCprCc,EAAA;;ADuqCnB;;;EAiBE,qBAAqB,EAAA;;AAKvB,aAAA;AACA;;EAEI,6CCltCwB;UDktCxB,qCCltCwB;EDmtCxB,oCCntCwB;EDotCxB,gBAAgB,EAAA;;AAGpB,aAAA;AACA;EACC,uBAAuB;EACvB,eAAe;EACZ,gBAAgB;EAChB,oBAAoB,EAAA;;AAGxB;EACC,iBAAiB,EAAA;;AAGlB;EACC,gBCntCkB,EAAA;;ADstCnB,gBAAA;AACA;EACC,YAAY,EAAA;;AAGb,eAAA;AACA;EACC,mBAAmB,EAAA;;AAGpB;EACE,0BCjvC0B;EDkvC1B,oBAAoB,EAAA;;AAEtB;EACE,0BCtvC0B;EDuvC1B,oBAAoB,EAAA;;AAGtB,8BAAA;AACA;;;EAGC,yBAAyB,EAAA;;AAG1B,uCAAA;AACA;EACC,UAAU,EAAA;;AAGX,WAAA;AACA;EAKI,gCC1vCe,EAAA;;ADqvCnB;EASG,gCC9vCgB,EAAA;;ADqvCnB;;EAgBG,6CCvxCyB;UDuxCzB,qCCvxCyB;EDwxCzB,oCCxxCyB;EDyxCzB,gBAAgB;EAChB,cAAc,EAAA", "file": "datatables.bundle.css", "sourcesContent": ["table.dataTable {\n  clear: both;\n  margin-top: 6px !important;\n  margin-bottom: 6px !important;\n  max-width: none !important;\n  border-collapse: separate !important;\n  border-spacing: 0;\n}\ntable.dataTable td,\ntable.dataTable th {\n  -webkit-box-sizing: content-box;\n  box-sizing: content-box;\n}\ntable.dataTable td.dataTables_empty,\ntable.dataTable th.dataTables_empty {\n  text-align: center;\n}\ntable.dataTable.nowrap th,\ntable.dataTable.nowrap td {\n  white-space: nowrap;\n}\n\ndiv.dataTables_wrapper div.dataTables_length label {\n  font-weight: normal;\n  text-align: left;\n  white-space: nowrap;\n}\ndiv.dataTables_wrapper div.dataTables_length select {\n  width: auto;\n  display: inline-block;\n}\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right;\n}\ndiv.dataTables_wrapper div.dataTables_filter label {\n  font-weight: normal;\n  white-space: nowrap;\n  text-align: left;\n}\ndiv.dataTables_wrapper div.dataTables_filter input {\n  margin-left: 0.5em;\n  display: inline-block;\n  width: auto;\n}\ndiv.dataTables_wrapper div.dataTables_info {\n  padding-top: 0.85em;\n  white-space: nowrap;\n}\ndiv.dataTables_wrapper div.dataTables_paginate {\n  margin: 0;\n  white-space: nowrap;\n  text-align: right;\n}\ndiv.dataTables_wrapper div.dataTables_paginate ul.pagination {\n  margin: 2px 0;\n  white-space: nowrap;\n  justify-content: flex-end;\n}\ndiv.dataTables_wrapper div.dataTables_processing {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 200px;\n  margin-left: -100px;\n  margin-top: -26px;\n  text-align: center;\n  padding: 1em 0;\n}\n\ntable.dataTable thead > tr > th.sorting_asc, table.dataTable thead > tr > th.sorting_desc, table.dataTable thead > tr > th.sorting,\ntable.dataTable thead > tr > td.sorting_asc,\ntable.dataTable thead > tr > td.sorting_desc,\ntable.dataTable thead > tr > td.sorting {\n  padding-right: 30px;\n}\ntable.dataTable thead > tr > th:active,\ntable.dataTable thead > tr > td:active {\n  outline: none;\n}\ntable.dataTable thead .sorting,\ntable.dataTable thead .sorting_asc,\ntable.dataTable thead .sorting_desc,\ntable.dataTable thead .sorting_asc_disabled,\ntable.dataTable thead .sorting_desc_disabled {\n  cursor: pointer;\n  position: relative;\n}\ntable.dataTable thead .sorting:before, table.dataTable thead .sorting:after,\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc:before,\ntable.dataTable thead .sorting_desc:after,\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_asc_disabled:after,\ntable.dataTable thead .sorting_desc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:after {\n  position: absolute;\n  bottom: 0.9em;\n  display: block;\n  opacity: 0.3;\n}\ntable.dataTable thead .sorting:before,\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_desc:before,\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:before {\n  right: 1em;\n  content: \"\\2191\";\n}\ntable.dataTable thead .sorting:after,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc:after,\ntable.dataTable thead .sorting_asc_disabled:after,\ntable.dataTable thead .sorting_desc_disabled:after {\n  right: 0.5em;\n  content: \"\\2193\";\n}\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_desc:after {\n  opacity: 1;\n}\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:after {\n  opacity: 0;\n}\n\ndiv.dataTables_scrollHead table.dataTable {\n  margin-bottom: 0 !important;\n}\n\ndiv.dataTables_scrollBody table {\n  border-top: none;\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\ndiv.dataTables_scrollBody table thead .sorting:before,\ndiv.dataTables_scrollBody table thead .sorting_asc:before,\ndiv.dataTables_scrollBody table thead .sorting_desc:before,\ndiv.dataTables_scrollBody table thead .sorting:after,\ndiv.dataTables_scrollBody table thead .sorting_asc:after,\ndiv.dataTables_scrollBody table thead .sorting_desc:after {\n  display: none;\n}\ndiv.dataTables_scrollBody table tbody tr:first-child th,\ndiv.dataTables_scrollBody table tbody tr:first-child td {\n  border-top: none;\n}\n\ndiv.dataTables_scrollFoot > .dataTables_scrollFootInner {\n  box-sizing: content-box;\n}\ndiv.dataTables_scrollFoot > .dataTables_scrollFootInner > table {\n  margin-top: 0 !important;\n  border-top: none;\n}\n\n@media screen and (max-width: 767px) {\n  div.dataTables_wrapper div.dataTables_length,\n  div.dataTables_wrapper div.dataTables_filter,\n  div.dataTables_wrapper div.dataTables_info,\n  div.dataTables_wrapper div.dataTables_paginate {\n    text-align: center;\n  }\n}\ntable.dataTable.table-sm > thead > tr > th {\n  padding-right: 20px;\n}\ntable.dataTable.table-sm .sorting:before,\ntable.dataTable.table-sm .sorting_asc:before,\ntable.dataTable.table-sm .sorting_desc:before {\n  top: 5px;\n  right: 0.85em;\n}\ntable.dataTable.table-sm .sorting:after,\ntable.dataTable.table-sm .sorting_asc:after,\ntable.dataTable.table-sm .sorting_desc:after {\n  top: 5px;\n}\n\ntable.table-bordered.dataTable th,\ntable.table-bordered.dataTable td {\n  border-left-width: 0;\n}\ntable.table-bordered.dataTable th:last-child, table.table-bordered.dataTable th:last-child,\ntable.table-bordered.dataTable td:last-child,\ntable.table-bordered.dataTable td:last-child {\n  border-right-width: 0;\n}\ntable.table-bordered.dataTable tbody th,\ntable.table-bordered.dataTable tbody td {\n  border-bottom-width: 0;\n}\n\ndiv.dataTables_scrollHead table.table-bordered {\n  border-bottom-width: 0;\n}\n\ndiv.table-responsive > div.dataTables_wrapper > div.row {\n  margin: 0;\n}\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=\"col-\"]:first-child {\n  padding-left: 0;\n}\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=\"col-\"]:last-child {\n  padding-right: 0;\n}\n\ndiv.dt-autofill-handle {\n  position: absolute;\n  height: 8px;\n  width: 8px;\n  z-index: 102;\n  box-sizing: border-box;\n  background: #0275d8;\n  cursor: pointer;\n}\n\ndiv.dtk-focus-alt div.dt-autofill-handle {\n  background: #ff8b33;\n}\n\ndiv.dt-autofill-select {\n  position: absolute;\n  z-index: 1001;\n  background-color: #0275d8;\n  background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(255, 255, 255, 0.5) 5px, rgba(255, 255, 255, 0.5) 10px);\n}\ndiv.dt-autofill-select.top, div.dt-autofill-select.bottom {\n  height: 3px;\n  margin-top: -1px;\n}\ndiv.dt-autofill-select.left, div.dt-autofill-select.right {\n  width: 3px;\n  margin-left: -1px;\n}\n\ndiv.dt-autofill-list {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  width: 500px;\n  margin-left: -250px;\n  background-color: white;\n  border-radius: 6px;\n  box-shadow: 0 0 5px #555;\n  border: 2px solid #444;\n  z-index: 11;\n  box-sizing: border-box;\n  padding: 1.5em 2em;\n}\ndiv.dt-autofill-list ul {\n  display: table;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  width: 100%;\n}\ndiv.dt-autofill-list ul li {\n  display: table-row;\n}\ndiv.dt-autofill-list ul li:last-child div.dt-autofill-question, div.dt-autofill-list ul li:last-child div.dt-autofill-button {\n  border-bottom: none;\n}\ndiv.dt-autofill-list ul li:hover {\n  background-color: #f6f6f6;\n}\ndiv.dt-autofill-list div.dt-autofill-question {\n  display: table-cell;\n  padding: 0.5em 0;\n  border-bottom: 1px solid #ccc;\n}\ndiv.dt-autofill-list div.dt-autofill-question input[type=number] {\n  padding: 6px;\n  width: 30px;\n  margin: -2px 0;\n}\ndiv.dt-autofill-list div.dt-autofill-button {\n  display: table-cell;\n  padding: 0.5em 0;\n  border-bottom: 1px solid #ccc;\n}\n\ndiv.dt-autofill-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.7);\n  background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);\n  z-index: 10;\n}\n\ndiv.dt-autofill-list div.dt-autofill-question input[type=number] {\n  padding: 6px;\n  width: 60px;\n  margin: -2px 0;\n}\n\n@keyframes dtb-spinner {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@-o-keyframes dtb-spinner {\n  100% {\n    -o-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-ms-keyframes dtb-spinner {\n  100% {\n    -ms-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-webkit-keyframes dtb-spinner {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-moz-keyframes dtb-spinner {\n  100% {\n    -moz-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\ndiv.dt-button-info {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  width: 400px;\n  margin-top: -100px;\n  margin-left: -200px;\n  background-color: white;\n  border: 2px solid #111;\n  box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.3);\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21;\n}\ndiv.dt-button-info h2 {\n  padding: 0.5em;\n  margin: 0;\n  font-weight: normal;\n  border-bottom: 1px solid #ddd;\n  background-color: #f3f3f3;\n}\ndiv.dt-button-info > div {\n  padding: 1em;\n}\n\ndiv.dt-button-collection-title {\n  text-align: center;\n  padding: 0.3em 0 0.5em;\n  font-size: 0.9em;\n}\n\ndiv.dt-button-collection-title:empty {\n  display: none;\n}\n\ndiv.dt-button-collection.dropdown-menu {\n  display: block;\n  z-index: 2002;\n  -webkit-column-gap: 8px;\n  -moz-column-gap: 8px;\n  -ms-column-gap: 8px;\n  -o-column-gap: 8px;\n  column-gap: 8px;\n}\ndiv.dt-button-collection.dropdown-menu.fixed {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-left: -75px;\n  border-radius: 0;\n}\ndiv.dt-button-collection.dropdown-menu.fixed.two-column {\n  margin-left: -150px;\n}\ndiv.dt-button-collection.dropdown-menu.fixed.three-column {\n  margin-left: -225px;\n}\ndiv.dt-button-collection.dropdown-menu.fixed.four-column {\n  margin-left: -300px;\n}\ndiv.dt-button-collection.dropdown-menu > * {\n  -webkit-column-break-inside: avoid;\n  break-inside: avoid;\n}\ndiv.dt-button-collection.dropdown-menu.two-column {\n  width: 300px;\n  padding-bottom: 1px;\n  -webkit-column-count: 2;\n  -moz-column-count: 2;\n  -ms-column-count: 2;\n  -o-column-count: 2;\n  column-count: 2;\n}\ndiv.dt-button-collection.dropdown-menu.three-column {\n  width: 450px;\n  padding-bottom: 1px;\n  -webkit-column-count: 3;\n  -moz-column-count: 3;\n  -ms-column-count: 3;\n  -o-column-count: 3;\n  column-count: 3;\n}\ndiv.dt-button-collection.dropdown-menu.four-column {\n  width: 600px;\n  padding-bottom: 1px;\n  -webkit-column-count: 4;\n  -moz-column-count: 4;\n  -ms-column-count: 4;\n  -o-column-count: 4;\n  column-count: 4;\n}\ndiv.dt-button-collection.dropdown-menu .dt-button {\n  border-radius: 0;\n}\n\ndiv.dt-button-collection {\n  -webkit-column-gap: 8px;\n  -moz-column-gap: 8px;\n  -ms-column-gap: 8px;\n  -o-column-gap: 8px;\n  column-gap: 8px;\n}\ndiv.dt-button-collection.fixed {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  margin-left: -75px;\n  border-radius: 0;\n}\ndiv.dt-button-collection.fixed.two-column {\n  margin-left: -150px;\n}\ndiv.dt-button-collection.fixed.three-column {\n  margin-left: -225px;\n}\ndiv.dt-button-collection.fixed.four-column {\n  margin-left: -300px;\n}\ndiv.dt-button-collection > * {\n  -webkit-column-break-inside: avoid;\n  break-inside: avoid;\n}\ndiv.dt-button-collection.two-column {\n  width: 300px;\n  padding-bottom: 1px;\n  -webkit-column-count: 2;\n  -moz-column-count: 2;\n  -ms-column-count: 2;\n  -o-column-count: 2;\n  column-count: 2;\n}\ndiv.dt-button-collection.three-column {\n  width: 450px;\n  padding-bottom: 1px;\n  -webkit-column-count: 3;\n  -moz-column-count: 3;\n  -ms-column-count: 3;\n  -o-column-count: 3;\n  column-count: 3;\n}\ndiv.dt-button-collection.four-column {\n  width: 600px;\n  padding-bottom: 1px;\n  -webkit-column-count: 4;\n  -moz-column-count: 4;\n  -ms-column-count: 4;\n  -o-column-count: 4;\n  column-count: 4;\n}\ndiv.dt-button-collection .dt-button {\n  border-radius: 0;\n}\ndiv.dt-button-collection.fixed {\n  max-width: none;\n}\ndiv.dt-button-collection.fixed:before, div.dt-button-collection.fixed:after {\n  display: none;\n}\n\ndiv.dt-button-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 999;\n}\n\n@media screen and (max-width: 767px) {\n  div.dt-buttons {\n    float: none;\n    width: 100%;\n    text-align: center;\n    margin-bottom: 0.5em;\n  }\n  div.dt-buttons a.btn {\n    float: none;\n  }\n}\ndiv.dt-buttons button.btn.processing,\ndiv.dt-buttons div.btn.processing,\ndiv.dt-buttons a.btn.processing {\n  color: rgba(0, 0, 0, 0.2);\n}\ndiv.dt-buttons button.btn.processing:after,\ndiv.dt-buttons div.btn.processing:after,\ndiv.dt-buttons a.btn.processing:after {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 16px;\n  height: 16px;\n  margin: -8px 0 0 -8px;\n  box-sizing: border-box;\n  display: block;\n  content: ' ';\n  border: 2px solid #282828;\n  border-radius: 50%;\n  border-left-color: transparent;\n  border-right-color: transparent;\n  animation: dtb-spinner 1500ms infinite linear;\n  -o-animation: dtb-spinner 1500ms infinite linear;\n  -ms-animation: dtb-spinner 1500ms infinite linear;\n  -webkit-animation: dtb-spinner 1500ms infinite linear;\n  -moz-animation: dtb-spinner 1500ms infinite linear;\n}\n\ntable.DTCR_clonedTable.dataTable {\n  position: absolute !important;\n  background-color: rgba(255, 255, 255, 0.7);\n  z-index: 202;\n}\n\ndiv.DTCR_pointer {\n  width: 1px;\n  background-color: #0275d8;\n  z-index: 201;\n}\n\ntable.DTFC_Cloned tr {\n  background-color: white;\n  margin-bottom: 0;\n}\n\ndiv.DTFC_LeftHeadWrapper table,\ndiv.DTFC_RightHeadWrapper table {\n  border-bottom: none !important;\n  margin-bottom: 0 !important;\n  background-color: white;\n}\n\ndiv.DTFC_LeftBodyWrapper table,\ndiv.DTFC_RightBodyWrapper table {\n  border-top: none;\n  margin: 0 !important;\n}\ndiv.DTFC_LeftBodyWrapper table thead .sorting:after,\ndiv.DTFC_LeftBodyWrapper table thead .sorting_asc:after,\ndiv.DTFC_LeftBodyWrapper table thead .sorting_desc:after,\ndiv.DTFC_LeftBodyWrapper table thead .sorting:after,\ndiv.DTFC_LeftBodyWrapper table thead .sorting_asc:after,\ndiv.DTFC_LeftBodyWrapper table thead .sorting_desc:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting_asc:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting_desc:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting_asc:after,\ndiv.DTFC_RightBodyWrapper table thead .sorting_desc:after {\n  display: none;\n}\ndiv.DTFC_LeftBodyWrapper table tbody tr:first-child th,\ndiv.DTFC_LeftBodyWrapper table tbody tr:first-child td,\ndiv.DTFC_RightBodyWrapper table tbody tr:first-child th,\ndiv.DTFC_RightBodyWrapper table tbody tr:first-child td {\n  border-top: none;\n}\n\ndiv.DTFC_LeftFootWrapper table,\ndiv.DTFC_RightFootWrapper table {\n  border-top: none;\n  margin-top: 0 !important;\n  background-color: white;\n}\n\ndiv.DTFC_Blocker {\n  background-color: white;\n}\n\ntable.dataTable.table-striped.DTFC_Cloned tbody {\n  background-color: white;\n}\n\ntable.dataTable.fixedHeader-floating,\ntable.dataTable.fixedHeader-locked {\n  background-color: white;\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\ntable.dataTable.fixedHeader-floating {\n  position: fixed !important;\n}\n\ntable.dataTable.fixedHeader-locked {\n  position: absolute !important;\n}\n\n@media print {\n  table.fixedHeader-floating {\n    display: none;\n  }\n}\n\ntable.dataTable tbody th.focus,\ntable.dataTable tbody td.focus {\n  box-shadow: inset 0 0 1px 2px #0275d8;\n}\n\ndiv.dtk-focus-alt table.dataTable tbody th.focus,\ndiv.dtk-focus-alt table.dataTable tbody td.focus {\n  box-shadow: inset 0 0 1px 2px #ff8b33;\n}\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.child,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > th.child,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {\n  cursor: default !important;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {\n  display: none !important;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > td:first-child,\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > th:first-child {\n  position: relative;\n  padding-left: 30px;\n  cursor: pointer;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > th:first-child:before {\n  top: 12px;\n  left: 4px;\n  height: 14px;\n  width: 14px;\n  display: block;\n  position: absolute;\n  color: white;\n  border: 2px solid white;\n  border-radius: 14px;\n  box-shadow: 0 0 3px #444;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  font-family: 'Courier New', Courier, monospace;\n  line-height: 14px;\n  content: '+';\n  background-color: #0275d8;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  content: '-';\n  background-color: #d33333;\n}\ntable.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child,\ntable.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child {\n  padding-left: 27px;\n}\ntable.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child:before {\n  top: 5px;\n  left: 4px;\n  height: 14px;\n  width: 14px;\n  border-radius: 14px;\n  line-height: 14px;\n  text-indent: 3px;\n}\ntable.dataTable.dtr-column > tbody > tr > td.control,\ntable.dataTable.dtr-column > tbody > tr > th.control {\n  position: relative;\n  cursor: pointer;\n}\ntable.dataTable.dtr-column > tbody > tr > td.control:before,\ntable.dataTable.dtr-column > tbody > tr > th.control:before {\n  top: 50%;\n  left: 50%;\n  height: 16px;\n  width: 16px;\n  margin-top: -10px;\n  margin-left: -10px;\n  display: block;\n  position: absolute;\n  color: white;\n  border: 2px solid white;\n  border-radius: 14px;\n  box-shadow: 0 0 3px #444;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  font-family: 'Courier New', Courier, monospace;\n  line-height: 14px;\n  content: '+';\n  background-color: #0275d8;\n}\ntable.dataTable.dtr-column > tbody > tr.parent td.control:before,\ntable.dataTable.dtr-column > tbody > tr.parent th.control:before {\n  content: '-';\n  background-color: #d33333;\n}\ntable.dataTable > tbody > tr.child {\n  padding: 0.5em 1em;\n}\ntable.dataTable > tbody > tr.child:hover {\n  background: transparent !important;\n}\ntable.dataTable > tbody > tr.child ul.dtr-details {\n  display: inline-block;\n  list-style-type: none;\n  margin: 0;\n  padding: 0;\n}\ntable.dataTable > tbody > tr.child ul.dtr-details > li {\n  border-bottom: 1px solid #efefef;\n  padding: 0.5em 0;\n}\ntable.dataTable > tbody > tr.child ul.dtr-details > li:first-child {\n  padding-top: 0;\n}\ntable.dataTable > tbody > tr.child ul.dtr-details > li:last-child {\n  border-bottom: none;\n}\ntable.dataTable > tbody > tr.child span.dtr-title {\n  display: inline-block;\n  min-width: 75px;\n  font-weight: bold;\n}\n\ndiv.dtr-modal {\n  position: fixed;\n  box-sizing: border-box;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  z-index: 100;\n  padding: 10em 1em;\n}\ndiv.dtr-modal div.dtr-modal-display {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  width: 50%;\n  height: 50%;\n  overflow: auto;\n  margin: auto;\n  z-index: 102;\n  overflow: auto;\n  background-color: #f5f5f7;\n  border: 1px solid black;\n  border-radius: 0.5em;\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);\n}\ndiv.dtr-modal div.dtr-modal-content {\n  position: relative;\n  padding: 1em;\n}\ndiv.dtr-modal div.dtr-modal-close {\n  position: absolute;\n  top: 6px;\n  right: 6px;\n  width: 22px;\n  height: 22px;\n  border: 1px solid #eaeaea;\n  background-color: #f9f9f9;\n  text-align: center;\n  border-radius: 3px;\n  cursor: pointer;\n  z-index: 12;\n}\ndiv.dtr-modal div.dtr-modal-close:hover {\n  background-color: #eaeaea;\n}\ndiv.dtr-modal div.dtr-modal-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 101;\n  background: rgba(0, 0, 0, 0.6);\n}\n\n@media screen and (max-width: 767px) {\n  div.dtr-modal div.dtr-modal-display {\n    width: 95%;\n  }\n}\ndiv.dtr-bs-modal table.table tr:first-child td {\n  border-top: none;\n}\n\ntable.dataTable tr.dtrg-group td {\n  background-color: #e0e0e0;\n}\n\ntable.dataTable tr.dtrg-group.dtrg-level-0 td {\n  font-weight: bold;\n}\n\ntable.dataTable tr.dtrg-group.dtrg-level-1 td,\ntable.dataTable tr.dtrg-group.dtrg-level-2 td {\n  background-color: #f0f0f0;\n  padding-top: 0.25em;\n  padding-bottom: 0.25em;\n  padding-left: 2em;\n  font-size: 0.9em;\n}\n\ntable.dataTable tr.dtrg-group.dtrg-level-2 td {\n  background-color: #f3f3f3;\n}\n\ntable.dt-rowReorder-float {\n  position: absolute !important;\n  opacity: 0.8;\n  table-layout: fixed;\n  outline: 2px solid #0275d8;\n  outline-offset: -2px;\n  z-index: 2001;\n}\n\ntr.dt-rowReorder-moving {\n  outline: 2px solid #888888;\n  outline-offset: -2px;\n}\n\nbody.dt-rowReorder-noOverflow {\n  overflow-x: hidden;\n}\n\ntable.dataTable td.reorder {\n  text-align: center;\n  cursor: move;\n}\n\ndiv.dts {\n  display: block !important;\n}\ndiv.dts tbody th,\ndiv.dts tbody td {\n  white-space: nowrap;\n}\ndiv.dts div.dts_loading {\n  z-index: 1;\n}\ndiv.dts div.dts_label {\n  position: absolute;\n  right: 10px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.5);\n  text-align: right;\n  border-radius: 3px;\n  padding: 0.4em;\n  z-index: 2;\n  display: none;\n}\ndiv.dts div.dataTables_scrollBody {\n  background: repeating-linear-gradient(45deg, #edeeff, #edeeff 10px, white 10px, white 20px);\n}\ndiv.dts div.dataTables_scrollBody table {\n  z-index: 2;\n}\ndiv.dts div.dataTables_paginate,\ndiv.dts div.dataTables_length {\n  display: none;\n}\n\ndiv.DTS div.dataTables_scrollBody table {\n  background-color: white;\n}\n\ntable.dataTable tbody > tr.selected,\ntable.dataTable tbody > tr > .selected {\n  background-color: #0275d8;\n}\ntable.dataTable.stripe tbody > tr.odd.selected,\ntable.dataTable.stripe tbody > tr.odd > .selected, table.dataTable.display tbody > tr.odd.selected,\ntable.dataTable.display tbody > tr.odd > .selected {\n  background-color: #0172d2;\n}\ntable.dataTable.hover tbody > tr.selected:hover,\ntable.dataTable.hover tbody > tr > .selected:hover, table.dataTable.display tbody > tr.selected:hover,\ntable.dataTable.display tbody > tr > .selected:hover {\n  background-color: #0170d0;\n}\ntable.dataTable.order-column tbody > tr.selected > .sorting_1,\ntable.dataTable.order-column tbody > tr.selected > .sorting_2,\ntable.dataTable.order-column tbody > tr.selected > .sorting_3,\ntable.dataTable.order-column tbody > tr > .selected, table.dataTable.display tbody > tr.selected > .sorting_1,\ntable.dataTable.display tbody > tr.selected > .sorting_2,\ntable.dataTable.display tbody > tr.selected > .sorting_3,\ntable.dataTable.display tbody > tr > .selected {\n  background-color: #0172d3;\n}\ntable.dataTable.display tbody > tr.odd.selected > .sorting_1, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_1 {\n  background-color: #016ecc;\n}\ntable.dataTable.display tbody > tr.odd.selected > .sorting_2, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_2 {\n  background-color: #016fcd;\n}\ntable.dataTable.display tbody > tr.odd.selected > .sorting_3, table.dataTable.order-column.stripe tbody > tr.odd.selected > .sorting_3 {\n  background-color: #0170cf;\n}\ntable.dataTable.display tbody > tr.even.selected > .sorting_1, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_1 {\n  background-color: #0172d3;\n}\ntable.dataTable.display tbody > tr.even.selected > .sorting_2, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_2 {\n  background-color: #0173d5;\n}\ntable.dataTable.display tbody > tr.even.selected > .sorting_3, table.dataTable.order-column.stripe tbody > tr.even.selected > .sorting_3 {\n  background-color: #0174d7;\n}\ntable.dataTable.display tbody > tr.odd > .selected, table.dataTable.order-column.stripe tbody > tr.odd > .selected {\n  background-color: #016ecc;\n}\ntable.dataTable.display tbody > tr.even > .selected, table.dataTable.order-column.stripe tbody > tr.even > .selected {\n  background-color: #0172d3;\n}\ntable.dataTable.display tbody > tr.selected:hover > .sorting_1, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_1 {\n  background-color: #016bc6;\n}\ntable.dataTable.display tbody > tr.selected:hover > .sorting_2, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_2 {\n  background-color: #016cc7;\n}\ntable.dataTable.display tbody > tr.selected:hover > .sorting_3, table.dataTable.order-column.hover tbody > tr.selected:hover > .sorting_3 {\n  background-color: #016dca;\n}\ntable.dataTable.display tbody > tr:hover > .selected,\ntable.dataTable.display tbody > tr > .selected:hover, table.dataTable.order-column.hover tbody > tr:hover > .selected,\ntable.dataTable.order-column.hover tbody > tr > .selected:hover {\n  background-color: #016bc6;\n}\ntable.dataTable tbody td.select-checkbox,\ntable.dataTable tbody th.select-checkbox {\n  position: relative;\n}\ntable.dataTable tbody td.select-checkbox:before, table.dataTable tbody td.select-checkbox:after,\ntable.dataTable tbody th.select-checkbox:before,\ntable.dataTable tbody th.select-checkbox:after {\n  display: block;\n  position: absolute;\n  top: 1.2em;\n  left: 50%;\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n}\ntable.dataTable tbody td.select-checkbox:before,\ntable.dataTable tbody th.select-checkbox:before {\n  content: ' ';\n  margin-top: -6px;\n  margin-left: -6px;\n  border: 1px solid black;\n  border-radius: 3px;\n}\ntable.dataTable tr.selected td.select-checkbox:after,\ntable.dataTable tr.selected th.select-checkbox:after {\n  content: '\\2714';\n  margin-top: -11px;\n  margin-left: -4px;\n  text-align: center;\n  text-shadow: 1px 1px #B0BED9, -1px -1px #B0BED9, 1px -1px #B0BED9, -1px 1px #B0BED9;\n}\n\ndiv.dataTables_wrapper span.select-info,\ndiv.dataTables_wrapper span.select-item {\n  margin-left: 0.5em;\n}\n\n@media screen and (max-width: 640px) {\n  div.dataTables_wrapper span.select-info,\n  div.dataTables_wrapper span.select-item {\n    margin-left: 0;\n    display: block;\n  }\n}\ntable.dataTable tbody tr.selected,\ntable.dataTable tbody th.selected,\ntable.dataTable tbody td.selected {\n  color: white;\n}\ntable.dataTable tbody tr.selected a,\ntable.dataTable tbody th.selected a,\ntable.dataTable tbody td.selected a {\n  color: #a2d4ed;\n}\n\n@import \"./src/scss/_modules/variables\";\r\n\r\ntable.dataTable > tbody > tr.child ul.dtr-details { width: 100% }\r\n\r\n.sorting_asc,\r\n.sorting_desc, \r\n.even .sorting_1 {\r\n\tbackground-color: rgba($primary-500, 0.03);\r\n}\r\n\r\n.odd .sorting_1 {\r\n\tbackground-color: rgba($primary-500, 0.04);\r\n}\r\n\r\n.table-dark {\r\n\t.sorting_asc,\r\n\t.sorting_desc, \r\n\t.even .sorting_1 {\r\n\t\tbackground-color: rgba($warning-500, 0.15);\r\n\t}\r\n\r\n\t.odd .sorting_1 {\r\n\t\tbackground-color: rgba($warning-500, 0.15);\r\n\t}\r\n}\r\n\r\n/* some idiot put \"important\" rule on the main css file now I have to put important here to override it... eghhh */\r\n/* I had to set it to 0 because auto fill cannot \"compute\" for the margin ... nice job Allan >.> */\r\ntable.dataTable {\r\n\tmargin-top: 0 !important;\r\n\tmargin-bottom: 1rem !important;\r\n}\r\n\r\n\r\ntable.dataTable.dtr-column > tbody > tr > td.control, \r\ntable.dataTable.dtr-column > tbody > tr > th.control {\r\n\tpadding-left: 30px;\r\n}\r\n\r\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > td:first-child:before, \r\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=\"row\"] > th:first-child:before,\r\ntable.dataTable.dtr-column > tbody > tr > td.control:before, \r\ntable.dataTable.dtr-column > tbody > tr > th.control:before {\r\n\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight:100%;\r\n\twidth: 1rem;\r\n\tbox-shadow: none;\r\n\tborder-radius: 0;\r\n\tborder: 0;\r\n\tmargin: 0;\r\n\tbackground-color: $primary-500;\r\n}\r\n\r\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before, \r\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before,\r\ntable.dataTable.dtr-column > tbody > tr.parent td.control:before, \r\ntable.dataTable.dtr-column > tbody > tr.parent th.control:before {\r\n\tbackground-color: $success-500;\r\n}\r\n\r\ntable.dataTable thead .sorting:before,\r\ntable.dataTable thead .sorting_asc:before,\r\ntable.dataTable thead .sorting_desc:before,\r\ntable.dataTable thead .sorting_asc_disabled:before,\r\ntable.dataTable thead .sorting_desc_disabled:before {\r\n\tright: 1rem !important;\r\n\tcontent: \"\\f175\";\r\n\tfont-size: 14px;\r\n\tfont-family: 'Font Awesome 5 Pro';\r\n}\r\ntable.dataTable thead .sorting:after,\r\ntable.dataTable thead .sorting_asc:after,\r\ntable.dataTable thead .sorting_desc:after,\r\ntable.dataTable thead .sorting_asc_disabled:after,\r\ntable.dataTable thead .sorting_desc_disabled:after {\r\n\tright: 0.5rem !important;\r\n\tcontent: \"\\f176\";\r\n\tfont-size: 14px;\r\n\tfont-family: 'Font Awesome 5 Pro';\r\n}\r\n\r\n.dataTables_empty {\r\n\tcolor: $danger-500;\r\n\tfont-size: 2rem;\r\n\tpadding-top: 2.5rem !important;\r\n\tpadding-bottom: 2.5rem !important;\r\n}\r\n\r\n.dataTables_wrapper .dataTables_filter label {\r\n\tdisplay: inline-flex;\r\n}\r\n\r\n.dataTables_wrapper tr.child td.child {\r\n\tpadding: 0;\r\n\r\n\t.dtr-details {\r\n\t\tpadding: 1rem 1rem 1.5rem 1.95rem;\r\n\t\tposition: relative;\r\n\r\n\t\t&:before {\r\n\t\t\tcontent: \"\\f149\";\r\n\t\t\tfont-family: 'Font Awesome 5 Pro';\r\n\t\t\tcolor: $success-400;\r\n\t\t\twidth: 20px;\r\n\t\t\theight: 30px;\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: -15px;\r\n\t\t\tright: 0px;\r\n\t\t\tfont-size: 1.2rem;\r\n\t\t}\r\n\r\n\t\t&:after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 1rem;\r\n\t\t\tbackground: $success-200;\r\n\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n@media screen and (max-width: 767px) {\r\n\tdiv.dataTables_wrapper div.dataTables_length,\r\n\tdiv.dataTables_wrapper div.dataTables_filter,\r\n\tdiv.dataTables_wrapper div.dataTables_info,\r\n\tdiv.dataTables_wrapper div.dataTables_paginate {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n}\r\n\r\n\r\n//take out elipsis background during pagination\r\n.paginate_button.page-item.disabled .page-link {\r\n\tbackground: transparent;\r\n}\r\n\r\n.dataTables_wrapper .dataTables_paginate .pagination .page-item > .page-link {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\theight: 1.85rem;\r\n\tmin-width: 1.85rem;\r\n\tpadding: 0.5rem;\r\n\tfont-size: 0.825rem;\r\n\tline-height: 0.825rem;\r\n}\r\n\r\ndiv.dataTables_wrapper div.dataTables_filter {\r\n\ttext-align: inherit;\r\n}\r\n\r\n/* AutoFill */\r\ndiv.dt-autofill-list {\r\n\tborder-width: 0px;\r\n\tpadding: 1rem 0;\r\n\r\n\tbox-shadow: 0 0 20px 0 rgba($black, 0.2); \r\n\r\n\tdiv.dt-autofill-question,\r\n\tdiv.dt-autofill-button {\r\n\t\tborder-width: 0px;\r\n\t}\r\n\r\n\t.dt-autofill-button {\r\n\t\ttext-align: right !important;\r\n\t}\r\n}\r\n\r\ndiv.dt-autofill-background {\r\n\topacity: 0.2;\r\n\tbackground-color: $black;\r\n}\r\n\r\n.dt-autofill-question {\r\n\tpadding-left: 2rem !important;\r\n}\r\n\r\n.dt-autofill-button {\r\n\tpadding-right: 2rem !important;\r\n}\r\n\r\ndiv.dt-autofill-handle {\r\n\tbackground: $primary-500;\r\n\theight: 0.875rem;\r\n    width: 0.875rem;\r\n}\r\n\r\ndiv.dt-autofill-select {\r\n  background-color: $primary-500;\r\n}\r\n\r\n/* FixedColumns */\r\n.DTFC_LeftHeadWrapper,\r\n.DTFC_LeftBodyWrapper,\r\n.DTFC_LeftFootWrapper {\r\n\t\r\n\t&:before {\r\n\t\tcontent:\"\";\r\n\t\tposition:absolute;\r\n\t\tright:0px;\r\n\t\ttop:1px;\r\n\t\tbottom:0px;\r\n\t\twidth:1px;\r\n\t\tbackground: $danger-500;\r\n\t\tz-index: 1;\r\n    \tbox-shadow: -4px 0px 4px rgba($black,0.4);\r\n\t}\r\n\r\n\ttable.table-bordered {\r\n\t\tborder-right-width: 0;\r\n\t}\r\n\r\n}\r\n\r\n/* KeyTable */\r\ntable.dataTable tbody th.focus, \r\ntable.dataTable tbody td.focus {\r\n    box-shadow: inset 0 0 0px 1px $primary-500;\r\n    background: rgba($primary-500, 0.1);\r\n    font-weight: 500;\r\n}\r\n\r\n/* RowGroup */\r\ntable.dataTable tr.dtrg-group td {\r\n\tbackground: transparent;\r\n\tfont-size: 1rem;\r\n    font-weight: 400;\t\r\n    padding-top: 1.25rem;\r\n}\r\n\r\ntable.dataTable.table-sm tr.dtrg-group td {\r\n\tpadding-top: 1rem;\r\n}\r\n\r\ntable.dataTable:not(.table-dark) tr.dtrg-group td {\r\n\tbackground: $white;\r\n}\r\n\r\n/* FixedHeader */\r\ntable.dataTable.fixedHeader-floating {\r\n\topacity: 0.9;\r\n}\r\n\r\n/* RowReorder */\r\ntable.dataTable td.reorder {\r\n\ttext-align: inherit;\r\n}\r\n\r\ntr.dt-rowReorder-moving {\r\n  outline: 2px solid $success-500;\r\n  outline-offset: -2px; }\r\n\r\ntable.dt-rowReorder-float {\r\n  outline: 2px solid $primary-500;\r\n  outline-offset: -2px; }  \r\n\r\n\r\n/* w-100 bug for some tables */\r\n.DTCR_clonedTable.w-100,\r\n.dt-rowReorder-float.w-100,\r\n.fixedHeader-floating.w-100 {\r\n\twidth: inherit !important;\r\n}\r\n\r\n/* fix z-index for processing message */\r\n.dataTables_processing {\r\n\tz-index: 2;\r\n}\r\n\r\n/* Select */\r\ntable.dataTable  {\r\n\r\n\t&.table-bordered {\r\n\t\t.selected {\r\n\t\t\ttd {\r\n\t\t\t\tborder-color: rgba($black,0.1);\r\n\t\t\t}\r\n\t\t}\r\n\t\ttd.selected {\r\n\t\t\tborder-color: rgba($black,0.1);\r\n\t\t}\r\n\t}\r\n\r\n\ttbody {\r\n\t\t> tr.selected, \r\n\t\t> tr > .selected {\r\n\t\t\tbox-shadow: inset 0 0 0px 1px $primary-500;\r\n\t\t\tbackground: rgba($primary-500, 0.1);\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: inherit;\r\n\t\t}\r\n\t}\r\n\r\n\t\t\r\n}\r\n\r\n", "/*  THEME COLORs\r\n========================================================================== */\r\n/* Looks good on chrome default color profile */\r\n$color-primary:\t\t\t\t\t\t#886ab5;\r\n$color-success:\t\t\t\t\t\t#1dc9b7;\r\n$color-info:\t\t\t\t\t\t#2196F3;\r\n$color-warning:\t\t\t\t\t\t#ffc241;\r\n$color-danger:\t\t\t\t\t\t#fd3995;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); \r\n\r\n/* looks good in sRGB but washed up on chrome default \r\n$color-primary:\t\t\t\t\t\t#826bb0;\r\n$color-success:\t\t\t\t\t\t#31cb55;\r\n$color-info:\t\t\t\t\t\t#5e93ec;\r\n$color-warning:\t\t\t\t\t\t#eec559;\r\n$color-danger:\t\t\t\t\t\t#dc4b92;\r\n$color-fusion:\t\t\t\t\t\tdarken(desaturate(adjust-hue($color-primary, 5), 80%), 25%); */\r\n\r\n/*  Color Polarity\r\n========================================================================== */\r\n$white:\t\t\t\t\t\t\t\t#fff !default;\r\n$black:\t\t\t\t\t\t\t\t#000 !default;\r\n$disabled:\t\t\t\t\t\t\tdarken($white, 20%) !default;\r\n\r\n/*  PAINTBUCKET MIXER\r\n========================================================================== */\r\n/* the grays */ \r\n$gray-50:\t\t\t\t\t\t\t#f9f9f9;\r\n$gray-100:\t\t\t\t\t\t\t#f8f9fa;\r\n$gray-200:\t\t\t\t\t\t\t#f3f3f3;\r\n$gray-300:\t\t\t\t\t\t\t#dee2e6;\r\n$gray-400:\t\t\t\t\t\t\t#ced4da;\r\n$gray-500:\t\t\t\t\t\t\t#adb5bd;\r\n$gray-600:\t\t\t\t\t\t\t#868e96;\r\n$gray-700:\t\t\t\t\t\t\t#495057;\r\n$gray-800:\t\t\t\t\t\t\t#343a40;\r\n$gray-900:\t\t\t\t\t\t\t#212529;\r\n\r\n/* the sapphires */\r\n$primary-50:\t\t\t\t\t\tlighten($color-primary, 25%) !default;\t\r\n$primary-100:\t\t\t\t\t\tlighten($color-primary, 20%) !default;\t\r\n$primary-200:\t\t\t\t\t\tlighten($color-primary, 15%) !default;\t\r\n$primary-300:\t\t\t\t\t\tlighten($color-primary, 10%) !default;\t\r\n$primary-400:\t\t\t\t\t\tlighten($color-primary, 5%) !default;\r\n$primary-500:\t\t\t\t\t\t$color-primary !default;\r\n$primary-600:\t\t\t\t\t\tdarken($color-primary, 5%) !default;\r\n$primary-700:\t\t\t\t\t\tdarken($color-primary, 10%) !default;\r\n$primary-800:\t\t\t\t\t\tdarken($color-primary, 15%) !default;\r\n$primary-900:\t\t\t\t\t\tdarken($color-primary, 20%) !default;\r\n\r\n/* the emeralds */\r\n$success-50:\t\t\t\t\t\tlighten($color-success, 25%) !default;\t\r\n$success-100:\t\t\t\t\t\tlighten($color-success, 20%) !default;\t\r\n$success-200:\t\t\t\t\t\tlighten($color-success, 15%) !default;\t\r\n$success-300:\t\t\t\t\t\tlighten($color-success, 10%) !default;\t\r\n$success-400:\t\t\t\t\t\tlighten($color-success, 5%) !default;\r\n$success-500:\t\t\t\t\t\t$color-success !default;\r\n$success-600:\t\t\t\t\t\tdarken($color-success, 5%) !default;\r\n$success-700:\t\t\t\t\t\tdarken($color-success, 10%) !default;\r\n$success-800:\t\t\t\t\t\tdarken($color-success, 15%) !default;\r\n$success-900:\t\t\t\t\t\tdarken($color-success, 20%) !default;\r\n\r\n/* the amethyths */\r\n$info-50:\t\t\t\t\t\t\tlighten($color-info, 25%) !default;\t\r\n$info-100:\t\t\t\t\t\t\tlighten($color-info, 20%) !default;\t\r\n$info-200:\t\t\t\t\t\t\tlighten($color-info, 15%) !default;\t\r\n$info-300:\t\t\t\t\t\t\tlighten($color-info, 10%) !default;\t\r\n$info-400:\t\t\t\t\t\t\tlighten($color-info, 5%) !default;\r\n$info-500:\t\t\t\t\t\t\t$color-info !default;\r\n$info-600:\t\t\t\t\t\t\tdarken($color-info, 5%) !default;\r\n$info-700:\t\t\t\t\t\t\tdarken($color-info, 10%) !default;\r\n$info-800:\t\t\t\t\t\t\tdarken($color-info, 15%) !default;\r\n$info-900:\t\t\t\t\t\t\tdarken($color-info, 20%) !default;\r\n\r\n/* the topaz */\r\n$warning-50:\t\t\t\t\t\tlighten($color-warning, 25%) !default;\t\r\n$warning-100:\t\t\t\t\t\tlighten($color-warning, 20%) !default;\t\r\n$warning-200:\t\t\t\t\t\tlighten($color-warning, 15%) !default;\t\r\n$warning-300:\t\t\t\t\t\tlighten($color-warning, 10%) !default;\t\r\n$warning-400:\t\t\t\t\t\tlighten($color-warning, 5%) !default;\r\n$warning-500:\t\t\t\t\t\t$color-warning !default;\r\n$warning-600:\t\t\t\t\t\tdarken($color-warning, 5%) !default;\r\n$warning-700:\t\t\t\t\t\tdarken($color-warning, 10%) !default;\r\n$warning-800:\t\t\t\t\t\tdarken($color-warning, 15%) !default;\r\n$warning-900:\t\t\t\t\t\tdarken($color-warning, 20%) !default;\r\n\r\n/* the rubies */\r\n$danger-50:\t\t\t\t\t\t\tlighten($color-danger, 25%) !default;\t\r\n$danger-100:\t\t\t\t\t\tlighten($color-danger, 20%) !default;\t\r\n$danger-200:\t\t\t\t\t\tlighten($color-danger, 15%) !default;\t\r\n$danger-300:\t\t\t\t\t\tlighten($color-danger, 10%) !default;\t\r\n$danger-400:\t\t\t\t\t\tlighten($color-danger, 5%) !default;\r\n$danger-500:\t\t\t\t\t\t$color-danger !default;\r\n$danger-600:\t\t\t\t\t\tdarken($color-danger, 5%) !default;\r\n$danger-700:\t\t\t\t\t\tdarken($color-danger, 10%) !default;\r\n$danger-800:\t\t\t\t\t\tdarken($color-danger, 15%) !default;\r\n$danger-900:\t\t\t\t\t\tdarken($color-danger, 20%) !default;\r\n\r\n/* the graphites */\r\n$fusion-50:\t\t\t\t\t\t\tlighten($color-fusion, 25%) !default;\t\r\n$fusion-100:\t\t\t\t\t\tlighten($color-fusion, 20%) !default;\t\r\n$fusion-200:\t\t\t\t\t\tlighten($color-fusion, 15%) !default;\t\r\n$fusion-300:\t\t\t\t\t\tlighten($color-fusion, 10%) !default;\t\r\n$fusion-400:\t\t\t\t\t\tlighten($color-fusion, 5%) !default;\r\n$fusion-500:\t\t\t\t\t\t$color-fusion !default;\r\n$fusion-600:\t\t\t\t\t\tdarken($color-fusion, 5%) !default;\r\n$fusion-700:\t\t\t\t\t\tdarken($color-fusion, 10%) !default;\r\n$fusion-800:\t\t\t\t\t\tdarken($color-fusion, 15%) !default;\r\n$fusion-900:\t\t\t\t\t\tdarken($color-fusion, 20%) !default;\r\n\r\n$theme-colors-extended: () !default;\r\n$theme-colors-extended: map-merge((\r\n\t\"primary-50\":\t\t\t\t\t$primary-50,\r\n\t\"primary-100\":\t\t\t\t\t$primary-100,\r\n\t\"primary-200\":\t\t\t\t\t$primary-200,\r\n\t\"primary-300\":\t\t\t\t\t$primary-300,\r\n\t\"primary-400\":\t\t\t\t\t$primary-400,\r\n\t\"primary-500\":\t\t\t\t\t$primary-500,\r\n\t\"primary-600\":\t\t\t\t\t$primary-600,\r\n\t\"primary-700\":\t\t\t\t\t$primary-700,\r\n\t\"primary-800\":\t\t\t\t\t$primary-800,\r\n\t\"primary-900\":\t\t\t\t\t$primary-900,\r\n\t\"success-50\":\t\t\t\t\t$success-50,\r\n\t\"success-100\":\t\t\t\t\t$success-100,\r\n\t\"success-200\":\t\t\t\t\t$success-200,\r\n\t\"success-300\":\t\t\t\t\t$success-300,\r\n\t\"success-400\":\t\t\t\t\t$success-400,\r\n\t\"success-500\":\t\t\t\t\t$success-500,\r\n\t\"success-600\":\t\t\t\t\t$success-600,\r\n\t\"success-700\":\t\t\t\t\t$success-700,\r\n\t\"success-800\":\t\t\t\t\t$success-800,\r\n\t\"success-900\":\t\t\t\t\t$success-900,\r\n\t\"info-50\":\t\t\t\t\t\t$info-50,\r\n\t\"info-100\":\t\t\t\t\t\t$info-100,\r\n\t\"info-200\":\t\t\t\t\t\t$info-200,\r\n\t\"info-300\":\t\t\t\t\t\t$info-300,\r\n\t\"info-400\":\t\t\t\t\t\t$info-400,\r\n\t\"info-500\":\t\t\t\t\t\t$info-500,\r\n\t\"info-600\":\t\t\t\t\t\t$info-600,\r\n\t\"info-700\":\t\t\t\t\t\t$info-700,\r\n\t\"info-800\":\t\t\t\t\t\t$info-800,\r\n\t\"info-900\":\t\t\t\t\t\t$info-900,\r\n\t\"warning-50\":\t\t\t\t\t$warning-50,\r\n\t\"warning-100\":\t\t\t\t\t$warning-100,\r\n\t\"warning-200\":\t\t\t\t\t$warning-200,\r\n\t\"warning-300\":\t\t\t\t\t$warning-300,\r\n\t\"warning-400\":\t\t\t\t\t$warning-400,\r\n\t\"warning-500\":\t\t\t\t\t$warning-500,\r\n\t\"warning-600\":\t\t\t\t\t$warning-600,\r\n\t\"warning-700\":\t\t\t\t\t$warning-700,\r\n\t\"warning-800\":\t\t\t\t\t$warning-800,\r\n\t\"warning-900\":\t\t\t\t\t$warning-900,  \r\n\t\"danger-50\":\t\t\t\t\t$danger-50,\r\n\t\"danger-100\":\t\t\t\t\t$danger-100,\r\n\t\"danger-200\":\t\t\t\t\t$danger-200,\r\n\t\"danger-300\":\t\t\t\t\t$danger-300,\r\n\t\"danger-400\":\t\t\t\t\t$danger-400,\r\n\t\"danger-500\":\t\t\t\t\t$danger-500,\r\n\t\"danger-600\":\t\t\t\t\t$danger-600,\r\n\t\"danger-700\":\t\t\t\t\t$danger-700,\r\n\t\"danger-800\":\t\t\t\t\t$danger-800,\r\n\t\"danger-900\":\t\t\t\t\t$danger-900,\r\n\t\"fusion-50\":\t\t\t\t\t$fusion-50,\r\n\t\"fusion-100\":\t\t\t\t\t$fusion-100,\r\n\t\"fusion-200\":\t\t\t\t\t$fusion-200,\r\n\t\"fusion-300\":\t\t\t\t\t$fusion-300,\r\n\t\"fusion-400\":\t\t\t\t\t$fusion-400,\r\n\t\"fusion-500\":\t\t\t\t\t$fusion-500,\r\n\t\"fusion-600\":\t\t\t\t\t$fusion-600,\r\n\t\"fusion-700\":\t\t\t\t\t$fusion-700,\r\n\t\"fusion-800\":\t\t\t\t\t$fusion-800,\r\n\t\"fusion-900\":\t\t\t\t\t$fusion-900\r\n\r\n), $theme-colors-extended);\r\n\r\n/*  Define universal border difition (div outlines, etc)\r\n========================================================================== */\r\n$theme-border-utility-size:\t\t\t\t0px;\r\n\r\n/*  MOBILE BREAKPOINT & GUTTERS (contains some bootstrap responsive overrides)\r\n========================================================================== */\r\n$grid-breakpoints: (\r\n\t// Extra small screen / phone\r\n\txs: 0,\r\n\t// Small screen / phone\r\n\tsm: 576px,\r\n\t// Medium screen / tablet\r\n\tmd: 768px,\r\n\t// Large screen / desktop\r\n\tlg: 992px, // also change 'mobileResolutionTrigger' in app.config.js\r\n\t// Decently size screen / wide laptop\r\n\txl: 1399px \r\n);\r\n\r\n$mobile-breakpoint:\t\t\t\t\t\tlg !default;                               /* define when mobile menu activates, here we are declearing (lg) so it targets the one after it */\r\n$mobile-breakpoint-size:\t\t\t\tmap-get($grid-breakpoints, lg) !default;   /* bootstrap reference xs: 0,  sm: 544px, md: 768px, lg: 992px, xl: 1200px*/\r\n$grid-gutter-width-base:\t\t\t\t3rem;\r\n$grid-gutter-width:\t\t\t\t\t\t1.5rem;\r\n\r\n$grid-gutter-widths: (\r\n\txs: $grid-gutter-width-base / 2,         \r\n\tsm: $grid-gutter-width-base / 2,          \r\n\tmd: $grid-gutter-width-base / 2,        \r\n\tlg: $grid-gutter-width-base / 2,        \r\n\txl: $grid-gutter-width-base / 2        \r\n);\r\n\r\n\r\n/* global var used for spacing*/\r\n$spacer:                  1rem;\r\n$spacers: () ;\r\n$spacers: map-merge(\r\n\t(\r\n\t\t0: 0,\r\n\t\t1: ($spacer * .25),\r\n\t\t2: ($spacer * .5),\r\n\t\t3: $spacer,\r\n\t\t4: ($spacer * 1.5),\r\n\t\t5: ($spacer * 2),\r\n\t\t6: ($spacer * 2.5)\r\n\t),\r\n\t$spacers\r\n);\r\n\r\n/* Uniform Padding variable */\r\n/* Heads up! This is a global scoped variable - changing may impact the whole template */\r\n$p-1:\t\t\t\t\t\t\t\t\t0.25rem;\r\n$p-2:\t\t\t\t\t\t\t\t\t0.5rem;\r\n$p-3:\t\t\t\t\t\t\t\t\t1rem;\r\n$p-4:\t\t\t\t\t\t\t\t\t1.5rem;\r\n$p-5:\t\t\t\t\t\t\t\t\t2rem;\r\n\r\n\r\n/*   BOOTSTRAP OVERRIDES (bootstrap variables)\r\n========================================================================== */ \r\n$grays: (\r\n\t\"100\": $gray-100,\r\n\t\"200\": $gray-200,\r\n\t\"300\": $gray-300,\r\n\t\"400\": $gray-400,\r\n\t\"500\": $gray-500,\r\n\t\"600\": $gray-600,\r\n\t\"700\": $gray-700,\r\n\t\"800\": $gray-800,\r\n\t\"900\": $gray-900\r\n);\r\n\r\n$colors: (\r\n\t\"blue\": $color-primary,\r\n\t\"red\": $color-danger,\r\n\t\"orange\": $color-warning,\r\n\t\"yellow\": $color-warning,\r\n\t\"green\": $color-success,\r\n\t\"white\": $white,\r\n\t\"gray\": $gray-600,\r\n\t\"gray-dark\": $gray-700\r\n);\r\n\r\n/* usage: theme-colors(\"primary\"); */\r\n$theme-colors: (\r\n\t\"primary\": $color-primary,\r\n\t\"secondary\": $gray-600,\r\n\t\"success\": $color-success,\r\n\t\"info\": $color-info,\r\n\t\"warning\": $color-warning,\r\n\t\"danger\": $color-danger,\r\n\t\"light\": $white,\r\n\t\"dark\": $fusion-500\r\n);\r\n\r\n/* forms */\r\n/*$input-height:\t\t\t\t\t\t\tcalc(2.25rem + 1px); //I had to add this because the input gruops was having improper height for some reason... */\r\n$input-border-color:\t\t\t\t\t#E5E5E5;\r\n$input-focus-border-color:\t\t\t\t$color-primary;\r\n$input-btn-focus-color:\t\t\t\t\ttransparent;\r\n$input-padding-y:\t\t\t\t\t\t.5rem;  \r\n$input-padding-x:\t\t\t\t\t\t.875rem;\r\n$label-margin-bottom:\t\t\t\t\t.3rem;\r\n$form-group-margin-bottom:\t\t\t\t1.5rem;\r\n\r\n/* links */\r\n$link-color:\t\t\t\t\t\t\t$primary-500;\r\n$link-hover-color:\t\t\t\t\t\t$primary-400;\r\n\r\n/* checkbox */ \r\n$custom-control-indicator-size:\t\t\t\t\t1.125rem;\r\n$custom-checkbox-indicator-border-radius:\t\t2px;\r\n$custom-control-indicator-border-width: \t\t2px;\r\n$custom-control-indicator-bg-size:\t\t\t\t0.5rem;\r\n\r\n/*$custom-file-height-inner:\t\t\t\tcalc(2.25rem - 1px);*/\r\n//$custom-file-padding-y:\t\t\t\t\t$input-padding-y;\r\n\r\n/* not part of bootstrap variable */\r\n$custom-control-indicator-bg-size-checkbox:  50% 50% !default;\r\n\r\n/* custom checkbox */\r\n// the checkbox needs to be a little darker for input groups\r\n$custom-control-indicator-checked-bg:\t\t\t\t$primary-600;\r\n$custom-control-indicator-checked-border-color: \t$primary-700;\r\n\r\n/* custom range */\r\n$custom-range-thumb-width:\t\t\t\t1rem;\r\n$custom-range-thumb-border-radius:\t\t50%;\r\n$custom-range-track-height:\t\t\t\t0.325rem;\r\n$custom-range-thumb-bg:\t\t\t\t\t$primary-500;\r\n$custom-range-thumb-active-bg:\t\t\t$primary-300;\r\n$custom-range-thumb-focus-box-shadow:\t0 0 0 1px $white, 0 0 0 0.2rem rgba($primary-500, 0.25);\r\n\r\n\r\n/* select */\r\n\r\n/* badge */\r\n$badge-font-size:\t\t\t\t\t\t85%;\r\n$badge-font-weight:\t\t\t\t\t\t500;\r\n\r\n/* cards */\r\n$card-spacer-y:\t\t\t\t\t\t\t1rem;\r\n$card-spacer-x:\t\t\t\t\t\t\t1rem;\r\n$card-cap-bg:\t\t\t\t\t\t\tinherit;\r\n$card-border-color:\t\t\t\t\t\trgba(0, 0, 0, 0.08);\r\n$list-group-border-color:\t\t\t\t$card-border-color;\r\n\r\n/*border radius*/\r\n$border-radius:\t\t\t\t\t\t\t4px;\r\n$border-radius-lg:\t\t\t\t\t\t$border-radius;\r\n$border-radius-sm:\t\t\t\t\t\t$border-radius;\r\n$border-radius-plus:\t\t\t\t\t10px;\r\n\r\n/* alert */\r\n$alert-padding-y:\t\t\t\t\t\t1rem;\r\n$alert-padding-x:\t\t\t\t\t\t1.25rem;\r\n$alert-margin-bottom:\t\t\t\t\t$grid-gutter-width + 0.5rem;\r\n\r\n/* toast */\r\n$toast-padding-y:\t\t\t\t\t\t0.5rem;\r\n$toast-padding-x:\t\t\t\t\t\t0.75rem;\r\n$toast-header-color:\t\t\t\t\t$fusion-500;\r\n\r\n/* breadcrumb */\r\n$breadcrumb-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$breadcrumb-divider-color:\t\t\t\tinherit;\r\n\r\n/* input button */\r\n$input-btn-padding-y-sm:\t\t\t\t.375rem;\r\n$input-btn-padding-x-sm:\t\t\t\t.844rem;\r\n\r\n$input-btn-padding-y:\t\t\t\t\t.5rem;\r\n$input-btn-padding-x:\t\t\t\t\t1.125rem;\r\n\r\n$input-btn-padding-y-lg:\t\t\t\t.75rem;\r\n$input-btn-padding-x-lg:\t\t\t\t1.5rem;\r\n\r\n/* nav link */\r\n$nav-link-padding-y:\t\t\t\t\t$input-btn-padding-y;\r\n$nav-link-padding-x:\t\t\t\t\t$input-btn-padding-x;\r\n\r\n/* nav, tabs, pills */\r\n$nav-tabs-border-color:\t\t\t\t\trgba($black, 0.1);\r\n$nav-tabs-link-active-border-color:\t\trgba($black, 0.1) rgba($black, 0.1) $white;\r\n$nav-tabs-link-hover-border-color:\t\trgba($black, 0.07) rgba($black, 0.07) transparent;\r\n\r\n/* tables */\r\n$table-border-color:\t\t\t\t\tlighten(desaturate($primary-500, 60%), 35%); //rgba($black, 0.09);\r\n$table-hover-bg:\t\t\t\t\t\tlighten(desaturate($primary-900, 70%), 63%);\r\n$table-accent-bg:\t\t\t\t\t\trgba($fusion-500,.02);\r\n$table-dark-bg:\t\t\t\t\t\t\t$fusion-300;\r\n$table-dark-border-color:\t\t\t\t$fusion-400;\r\n$table-dark-accent-bg:\t\t\t\t\trgba($white, .05);\r\n$table-dark-hover-bg:\t\t\t\t\t$color-primary;\r\n\r\n/* dropdowns */\r\n$dropdown-border-width:\t\t\t\t\t$theme-border-utility-size; \r\n$dropdown-padding-y:\t\t\t\t\t.3125rem;\r\n$dropdown-item-padding-y:\t\t\t\t.75rem;\r\n$dropdown-item-padding-x:\t\t\t\t1.5rem; \r\n$dropdown-link-active-bg:\t\t\t\tlighten($primary-50, 13%);  \r\n$dropdown-link-active-color:\t\t\t$primary-900;\r\n$dropdown-link-hover-color:\t\t\t\t$primary-700;\r\n\r\n/* dropdowns sizes */\r\n$dropdown-xl-width:\t\t\t\t\t\t21.875rem !default;\r\n$dropdown-lg-width:\t\t\t\t\t\t17.5rem !default;\r\n$dropdown-md-width:\t\t\t\t\t\t14rem !default;\r\n$dropdown-sm-width:\t\t\t\t\t\t8rem !default;\r\n$dropdown-shadow:\t\t\t\t\t\t0 0 15px 1px rgba(desaturate($primary-900, 20%), (20/100));   \r\n\r\n/* popovers */\r\n$popover-border-color:\t\t\t\t\trgba(0, 0, 0, 0.2);\r\n$popover-header-padding-y:\t\t\t\t1rem;\r\n$popover-header-padding-x:\t\t\t\t1rem;\r\n$popover-header-bg:\t\t\t\t\t\ttransparent;\r\n$popover-border-width:\t\t\t\t\t3px;\r\n$popover-arrow-width:\t\t\t\t\t15px;\r\n$popover-arrow-height:\t\t\t\t\t7px;\r\n$popover-arrow-outer-color:\t\t\t\tinherit;\r\n$popover-arrow-color:\t\t\t\t\ttransparent;\r\n$popover-font-size:\t\t\t\t\t\t14px;\r\n$popover-box-shadow:\t\t\t\t\t1px 0 13px rgba(90, 80, 105, 0.2);\r\n$popover-border-radius:\t\t\t\t\t0.5rem;\r\n\r\n/* tooltips */\r\n$tooltip-max-width:\t\t\t\t\t\t200px;\r\n$tooltip-color:\t\t\t\t\t\t\t$white;\r\n$tooltip-bg:\t\t\t\t\t\t\trgba($fusion-700, 0.9);\r\n$tooltip-border-radius:\t\t\t\t\t5px;\r\n$tooltip-opacity:\t\t\t\t\t\t1;\r\n$tooltip-padding-y:\t\t\t\t\t\t.3rem;\r\n$tooltip-padding-x:\t\t\t\t\t\t.6rem;\r\n$tooltip-margin:\t\t\t\t\t\t2px;\r\n$tooltip-arrow-width:\t\t\t\t\t8px;\r\n$tooltip-arrow-height:\t\t\t\t\t5px;\r\n\r\n/* modal */\r\n$modal-header-padding-y:\t\t\t\t1.25rem;\r\n$modal-header-padding-x:\t\t\t\t1.25rem;\r\n$modal-header-padding:\t\t\t\t\t$modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\r\n$modal-inner-padding:\t\t\t\t\t1.25rem;\r\n$modal-backdrop-opacity:\t\t\t\t0.2;\r\n$modal-content-border-color:\t\t\ttransparent;\r\n$modal-header-border-width:\t\t\t\t0px;\r\n$modal-footer-border-width:\t\t\t\t0px;\r\n\r\n/* reference guide\r\nhttp://www.standardista.com/px-to-rem-conversion-if-root-font-size-is-16px/\r\n8px = 0.5rem\r\n9px = 0.5625rem\r\n10px = 0.625rem\r\n11px = 0.6875rem\r\n12px = 0.75rem\r\n13px = 0.8125rem\r\n14px = 0.875rem\r\n15px = 0.9375rem\r\n16px = 1rem (base)\r\n17px = 1.0625rem\r\n18px = 1.125rem\r\n19px = 1.1875rem\r\n20px = 1.25rem\r\n21px = 1.3125rem\r\n22px = 1.375rem\r\n24px = 1.5rem\r\n25px = 1.5625rem\r\n26px = 1.625rem\r\n28px = 1.75rem\r\n30px = 1.875rem\r\n32px = 2rem\r\n34px = 2.125rem\r\n36px = 2.25rem\r\n38px = 2.375rem\r\n40px = 2.5rem\r\n*/\r\n\r\n/* Fonts */\r\n$font-size-base:\t\t\t\t\t\t0.8125rem;\r\n$font-size-lg:\t\t\t\t\t\t\t1rem;\r\n$font-size-sm:\t\t\t\t\t\t\t0.75rem;\r\n$line-height-base:\t\t\t\t\t\t1.47;\r\n$headings-line-height:\t\t\t\t\t1.57;\r\n\r\n$h1-font-size:\t\t\t\t\t\t\t1.5rem;\r\n$h2-font-size:\t\t\t\t\t\t\t1.375rem;\r\n$h3-font-size:\t\t\t\t\t\t\t1.1875rem;\r\n$h4-font-size:\t\t\t\t\t\t\t1.0625rem;\r\n$h5-font-size:\t\t\t\t\t\t\t0.9375rem;\r\n$h6-font-size:\t\t\t\t\t\t\t0.875rem;\r\n\r\n$display1-size:\t\t\t\t\t\t\t5rem;\r\n$display2-size:\t\t\t\t\t\t\t4.5rem;\r\n$display3-size:\t\t\t\t\t\t\t3.5rem;\r\n$display4-size:\t\t\t\t\t\t\t2.5rem;\r\n\r\n$navbar-toggler-font-size:\t\t\t\t21px;\r\n$navbar-toggler-padding-y:\t\t\t\t7.5px; \r\n$navbar-toggler-padding-x:\t\t\t\t18px;\r\n\r\n/* carousel */\r\n$carousel-indicator-height:\t\t\t\t13px;\r\n$carousel-indicator-width:\t\t\t\t13px;\r\n\r\n/*  BASE VARS\r\n========================================================================== */\r\n// usage: background-image: url(\"#{$baseURL}img/bg.png\"); \r\n\r\n$baseURL:\t\t\t\t\t\t\t\t\"../\" !default;\r\n$webfontsURL:\t\t\t\t\t\t\t\"../webfonts\" !default;\r\n$base-text-color:\t\t\t\t\t\tdarken($white,60%) !default;\r\n\r\n/* font vars below will auto change to rem values using function rem($value)*/\r\n$fs-base:\t\t\t\t\t\t\t\t13px !default;\r\n$fs-nano:\t\t\t\t\t\t\t\t$fs-base - 2;   /* 11px   */\r\n$fs-xs: \t\t\t\t\t\t\t\t$fs-base - 1;   /* 12px   */\r\n$fs-sm: \t\t\t\t\t\t\t\t$fs-base - 0.5; /* 12.5px */\r\n$fs-md: \t\t\t\t\t\t\t\t$fs-base + 1;   /* 14px   */\r\n$fs-lg: \t\t\t\t\t\t\t\t$fs-base + 2;   /* 15px   */\r\n$fs-xl: \t\t\t\t\t\t\t\t$fs-base + 3;   /* 16px   */\r\n$fs-xxl: \t\t\t\t\t\t\t\t$fs-base + 15;  /* 28px   */\r\n\r\n/*  Font Family\r\n========================================================================== */\r\n\t\t\t\t\t\t\t\t\t\t/*hint: you can also try the font called 'Poppins' by replacing the font 'Roboto' */\r\n$font-import:\t\t\t\t\t\t\t\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900\" !default;\r\n$page-font:\t\t\t\t\t\t\t\t\"Roboto\", 'Helvetica Neue', Helvetica, Arial !default;\r\n$nav-font:\t\t\t\t\t\t\t\t$page-font !default;\r\n$heading-font-family:\t\t\t\t\t$page-font !default; \r\n$mobile-page-font:\t\t\t\t\t\t'HelveticaNeue-Light','Helvetica Neue Light','Helvetica Neue',Helvetica,Arial,sans-serif;\r\n\r\n/*  ANIMATIONS\r\n========================================================================== */\r\n$nav-hide-animate: \t\t\t\t\t\tall 470ms cubic-bezier(0.34, 1.25, 0.3, 1) !default;\t\t/* this addresses all animation related to nav hide to nav minify */\r\n\r\n/*  Z-INDEX declearation\r\n========================================================================== */\r\n$space:\t\t\t\t\t\t\t\t\t1000 !default;\r\n$cloud:\t\t\t\t\t\t\t\t\t950 !default;\r\n$ground:\t\t\t\t\t\t\t\t0 !default;\r\n$water:\t\t\t\t\t\t\t\t\t-99 !default;\r\n/* we adjust bootstrap z-index to be higher than our higest z-index*/\r\n$zindex-dropdown:\t\t\t\t\t\t$space + 1000;\r\n$zindex-sticky:\t\t\t\t\t\t\t$space + 1020;\r\n$zindex-fixed:\t\t\t\t\t\t\t$space + 1030;\r\n$zindex-modal-backdrop:\t\t\t\t\t$space + 1040;\r\n$zindex-modal:\t\t\t\t\t\t\t$space + 1050;\r\n$zindex-panel-fullscreen:\t\t\t\t$space + 1055;\r\n$zindex-popover:\t\t\t\t\t\t$space + 1060;\r\n$zindex-tooltip:\t\t\t\t\t\t$space + 1070;\r\n\r\n/*  CUSTOM ICON PREFIX \r\n========================================================================== */\r\n$cust-icon-prefix:\t\t\t\t\t\tni;\r\n\r\n/*  PRINT CSS (landscape or portrait)\r\n========================================================================== */\r\n$print-page-type: \t\t\t\t\t\tportrait; \t\t\t\t\t\t\t\t\t\t\t\t  /* landscape or portrait */\r\n$print-page-size:\t\t\t\t\t\tletter;\t\t\t\t\t\t\t\t\t\t\t\t\t  /* auto, letter */\r\n$print-page-margin:\t\t\t\t\t\t1.0cm;\r\n\r\n/*  Common Element Variables\r\n========================================================================== */\r\n$body-background-color:\t\t\t\t\t$white !default;\r\n$page-bg:\t\t\t\t\t\t\t\tdesaturate(lighten($primary-500, 41.7%), 5%)  !default; //#f9f9fc\r\n\r\n/* Z-index decleartion \"birds eye view\"\r\n========================================================================== */\r\n$depth:\t\t\t\t\t\t\t\t\t999 !default;\r\n$depth-header:\t\t\t\t\t\t\t$depth + 1 !default;\r\n$depth-nav:\t\t\t\t\t\t\t\t$depth-header + 2 !default;\r\n\r\n/*  Components\r\n========================================================================== */\r\n$frame-border-color:\t\t\t\t\t#f7f9fa !default;\r\n\r\n/*  PAGE HEADER STUFF\r\n========================================================================== */\r\n\r\n/* colors */\r\n$header-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$header-border-color:\t\t\t\t\t#ccc !default;\r\n$header-border-bottom-color:\t\t\trgba(darken($primary-700, 10%), (13/100)) !default;\t\t\r\n$header-link-color:\t\t\t\t\t\t$primary-500 !default;\r\n$header-link-hover-color:\t\t\t\tdarken($header-bg, 75%) !default;\r\n\r\n/* height */\r\n$header-height:\t\t\t\t\t\t\t4.125rem !default;\r\n$header-height-nav-top:\t\t\t\t\t4.125rem !default;\r\n$header-inner-padding-x:\t\t\t\t2rem !default;\r\n$header-inner-padding-y:\t\t\t\t0 !default;\r\n\r\n/* logo */\r\n$header-logo-border-bottom:\t\t\t\trgba(darken($primary-700, 10%), (30/100)) !default;\r\n$header-logo-width:\t\t\t\t\t\tauto !default; \t\t\t\t\t\t\t\t\t\t  /* try not to go beywond the width of $main_nav_width value */\r\n$header-logo-height:\t\t\t\t\tauto !default \t\t\t\t\t\t\t\t\t\t    /* you may need to change this depending on your logo design */\r\n$header-logo-text-align:\t\t\t\tcenter; \t\t\t\t\t\t\t\t\t\t\t\t      /* adjust this as you see fit : left, right, center */\r\n\r\n/* icon font size (not button) */\r\n$header-icon-size:\t\t\t\t\t\t21px;\r\n\r\n/* search input box */\r\n$header-search-border-color:\t\t\ttransparent !default;\t\t\t\t\t\t\t\t/* suggestion: #ccced0*/\r\n$header-search-bg:\t\t\t\t\t\ttransparent !default;\r\n$header-search-width:\t\t\t\t\t25rem !default;\r\n$header-search-height:\t\t\t\t\t$header-height - 1.5rem !default; \r\n$header-search-font-size:\t\t\t\t$fs-base + 2;\r\n$header-search-padding:\t\t\t\t\t$spacer * 0.38;\r\n\r\n/* btn */\r\n$header-btn-active-bg:\t\t\t\t\t$fusion-500 !default;\r\n$header-btn-color:\t\t\t\t\t\tdarken($header-bg, 35%) !default;\r\n$header-btn-hover-color:\t\t\t\t$header-link-hover-color !default;\r\n$header-btn-active-color:\t\t\t\t$white !default;\r\n$header-btn-height: \t\t\t\t\t$header-height/2 + 0.1875rem !default;\r\n$header-btn-width: \t\t\t\t\t\t3.25rem !default;\r\n$header-btn-font-size:\t\t\t\t\t21px !default; //works only for font icons\r\n$header-btn-border-radius:\t\t\t\t$border-radius !default;\r\n$header-non-btn-width:\t\t\t\t\t3.125rem !default;\r\n$header-dropdown-arrow-color:\t\t\t$primary-700 !default;\r\n\r\n/* dropdown: app list */\r\n$header-applist-link-block-height:\t\t5.9375rem;\r\n$header-applist-link-block-width:\t\t6.25rem;\r\n$header-applist-rows-width:\t\t\t\t21.875rem;\r\n$header-applist-rows-height:\t\t\t22.5rem; \r\n$header-applist-box-padding-x:\t\t\t$p-2;\r\n$header-applist-box-padding-y:\t\t\t$p-3;\r\n$header-applist-icon-size:\t\t\t\t3.125rem;\r\n\r\n/* badge */\r\n$header-badge-min-width:\t\t\t\t1.25rem !default;\r\n$header-badge-left:\t\t\t\t\t\t1.5625rem !default;\r\n$header-badge-top:\t\t\t\t\t\t($header-height / 2 - $header-badge-min-width) + 0.28125rem !default; \r\n\r\n/* COMPONENTS & MODS */\r\n$nav-tabs-clean-link-height:\t\t\t45px !default;\r\n\r\n/*  NAVIGATION STUFF\r\n\r\nGuide:\r\n\r\naside.page-sidebar ($nav-width, $nav-background)\r\n\t.page-logo\r\n\t.primary-nav\r\n\t\t.info-card\r\n\t\tul.nav-menu\r\n\t\t\tli\r\n\t\t\t\ta (parent level-0..., $nav-link-color, $nav-link-hover-color, $nav-link-hover-bg-color, $nav-link-hover-left-border-color)\r\n\t\t\t\t\ticon \r\n\t\t\t\t\tspan\r\n\t\t\t\t\tcollapse-sign \r\n\t\t\t\t\t\r\n\t\t\t\tul.nav-menu-sub-one  \r\n\t\t\t\t\tli\r\n\t\t\t\t\t\ta ($nav-level-1... $nav-sub-link-height)\r\n\t\t\t\t\t\t\tspan\r\n\t\t\t\t\t\t\tcollapse-sign\r\n\r\n\t\t\t\t\t\tul.nav-menu-sub-two\r\n\t\t\t\t\t\t\tli\r\n\t\t\t\t\t\t\t\ta ($nav-level-2... $nav-sub-link-height)\r\n\t\t\t\t\t\t\t\t\tspan\r\n\r\n\t\tp.nav-title ($nav-title-*...)\r\n\r\n\r\n========================================================================== */\r\n\r\n/* main navigation */\r\n/* left panel */\r\n$nav-background:\t\t\t\t\t\tdesaturate($primary-900, 7%) !default;\r\n$nav-background-shade:\t\t\t\t\trgba(desaturate($info-500, 15%), 0.18) !default;                  \r\n$nav-base-color:\t\t\t\t\t\tlighten($nav-background, 7%) !default;\r\n$nav-width:\t\t\t\t\t\t\t\t16.875rem !default; \r\n\r\n/* nav parent level-0 */\r\n$nav-link-color: \t\t\t\t\t\tlighten($nav-base-color, 32%) !default;\r\n$nav-font-link-size: \t\t\t\t\t$fs-base + 1 !default;\r\n$nav-collapse-sign-font-size:\t\t\tinherit !default;\t\r\n$nav-padding-x:\t\t\t\t\t\t\t2rem !default; \r\n$nav-padding-y:\t\t\t\t\t\t\t0.8125rem !default;\r\n\r\n/* nav icon sizes */\r\n$nav-font-icon-size:\t\t\t\t\t1.125rem !default; //23px for Fontawesome & 20px for NextGen icons\r\n$nav-font-icon-size-sub:\t\t\t\t1.125rem !default;\r\n\r\n$nav-icon-width:\t\t\t\t\t\t1.75rem !default;\r\n$nav-icon-margin-right:\t\t\t\t\t0.25rem !default;\r\n\r\n/* badge default */\r\n$nav-badge-color: \t\t\t\t\t\t$white !default;\r\n$nav-badge-bg-color: \t\t\t\t\t$danger-500 !default;\r\n\r\n/* all child */\r\n$nav-icon-color:\t\t\t\t\t\tlighten(darken($nav-base-color, 15%),27%) !default;\r\n$nav-icon-hover-color:\t\t\t\t\tlighten(desaturate($color-primary, 30%), 10%) !default;\r\n\r\n/* nav title */\r\n$nav-title-color: \t\t\t\t\t\tlighten($nav-base-color, 10%) !default;\r\n$nav-title-border-bottom-color: \t\tlighten($nav-base-color, 3%) !default;\r\n$nav-title-font-size: \t\t\t\t\t$fs-base - 1.8px;\r\n\r\n/* nav Minify */\r\n$nav-minify-hover-bg:\t\t\t\t\tdarken($nav-base-color, 3%) !default;\r\n$nav-minify-hover-text:\t\t\t\t\t$white !default;\r\n$nav-minify-width:\t\t\t\t\t\t4.6875rem !default;\r\n/* when the menu pops on hover */\r\n$nav-minify-sub-width:\t\t\t\t\t$nav-width - ($nav-minify-width - 1.5625rem) !default; \t\t\t\t\r\n\r\n/* navigation Width */\r\n/* partial visibility of the menu */\r\n$nav-hidden-visiblity:\t\t\t\t\t0.625rem !default; \t\t\t\t\t\t\t\t\t\t\t\r\n\r\n/* top navigation */\r\n$nav-top-height:\t\t\t\t\t\t3.5rem !default;\r\n$nav-top-drowndown-width:\t\t\t\t13rem !default;\r\n$nav-top-drowndown-background:\t\t\t$nav-base-color;\r\n$nav-top-drowndown-hover:\t\t\t\trgba($black, 0.1);;\r\n$nav-top-drowndown-color:\t\t\t\t$nav-link-color;\r\n$nav-top-drowndown-hover-color:\t\t\t$white;\r\n\r\n/* nav Info Card (appears below the logo) */\r\n$nav-infocard-height:\t\t\t\t\t9.530rem !default;\r\n$profile-image-width:\t\t\t\t\t3.125rem !default; \r\n$profile-image-width-md:\t\t\t\t2rem !default;\r\n$profile-image-width-sm:\t\t\t\t1.5625rem !default;\r\n$image-share-height:\t\t\t\t\t2.8125rem !default; /* width is auto */\r\n\r\n/* nav DL labels for all child */\r\n$nav-dl-font-size:\t\t\t\t\t\t0.625rem !default;\r\n$nav-dl-width:\t\t\t\t\t\t\t1.25rem !default;\r\n$nav-dl-height:\t\t\t\t\t\t\t1rem !default;\r\n$nav-dl-margin-right:\t\t\t\t\t0.9375rem !default;\r\n$nav-dl-margin-left:\t\t\t\t\t$nav-dl-width + $nav-dl-margin-right !default; \t/* will be pulled to left as a negative value */\r\n\r\n/*   MISC Settings\r\n========================================================================== */\r\n/* List Table */\r\n$list-table-padding-x:\t\t\t\t\t11px !default;\r\n$list-table-padding-y:\t\t\t\t\t0 !default;\r\n\r\n/*   PAGE SETTINGS\r\n========================================================================== */\r\n$settings-incompat-title:\t\t\t\t#d58100 !default;\r\n$settings-incompat-desc:\t\t\t\t#ec9f28 !default;\r\n$settings-incompat-bg:\t\t\t\t\t$warning-50 !default;\r\n$settings-incompat-border:\t\t\t\t$warning-700 !default;\r\n\r\n/*   PAGE BREADCRUMB \r\n========================================================================== */\r\n$page-breadcrumb-maxwidth:\t\t\t\t200px;\r\n\r\n/*   PAGE COMPONENT PANELS \r\n========================================================================== */\r\n$panel-spacer-y:\t\t\t\t\t\t1rem;\r\n$panel-spacer-x:\t\t\t\t\t\t1rem;\r\n$panel-hdr-font-size:\t\t\t\t\t14px;\r\n$panel-hdr-height:\t\t\t\t\t\t3rem;\r\n$panel-btn-size:\t\t\t\t\t\t1rem;\r\n$panel-btn-spacing:\t\t\t\t\t\t0.3rem;\r\n$panel-toolbar-icon:\t\t\t\t\t1.5625rem;\r\n$panel-hdr-background:\t\t\t\t\t$white; //#fafafa;\r\n$panel-edge-radius:\t\t\t\t\t\t$border-radius;\r\n$panel-placeholder-color:\t\t\t\tlighten(desaturate($primary-50, 20%), 10%);\r\n\r\n/*   PAGE COMPONENT PROGRESSBARS \r\n========================================================================== */\r\n$progress-height:\t\t\t\t\t\t.75rem;\r\n$progress-font-size:\t\t\t\t\t.625rem;\r\n$progress-bg:\t\t\t\t\t\t\tlighten($fusion-50, 40%);\r\n$progress-border-radius:\t\t\t\t10rem;\r\n\r\n/*   PAGE COMPONENT MESSENGER \r\n========================================================================== */\r\n$msgr-list-width:\t\t\t\t\t\t14.563rem;\r\n$msgr-list-width-collapsed:\t\t\t\t3.125rem;\r\n$msgr-get-background:\t\t\t\t\t#f1f0f0;\r\n$msgr-sent-background:\t\t\t\t\t$success-500;\r\n$msgr-animation-delay:\t\t\t\t\t100ms;\r\n\r\n/*   FOOTER\r\n========================================================================== */\r\n$footer-bg:\t\t\t\t\t\t\t\t$white !default;\r\n$footer-text-color:\t\t\t\t\t\tdarken($base-text-color, 10%);\r\n$footer-height:\t\t\t\t\t\t\t2.8125rem !default;\r\n$footer-font-size:\t\t\t\t\t\t$fs-base !default;\r\n$footer-zindex:\t\t\t\t\t\t\t$cloud - 20 !default;\r\n\r\n/*   GLOBALS\r\n========================================================================== */\r\n$mod-main-boxed-width:\t\t\t\t\tmap-get($grid-breakpoints, xl);\r\n$slider-width:\t\t\t\t\t\t\t15rem;\r\n\r\n/* ACCESSIBILITIES */\r\n$enable-prefers-reduced-motion-media-query:   false;"]}