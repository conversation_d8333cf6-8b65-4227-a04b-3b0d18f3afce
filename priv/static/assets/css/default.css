td.details-control {
  background: url(/images/details_open.png) no-repeat center center;
  cursor: pointer;
}
tr.shown td.details-control {
  background: url(/images/details_close.png) no-repeat center center;
}
.dataTables_empty {
    color: #fd3995;
    font-size: 14px;
    padding-top: 15px !important;
    padding-bottom: 15px !important;
}


.call_dropdown {
  position: absolute;
  overflow: visible!important;
  box-shadow: 0 0 15px 1px rgb(90 80 105 / 20%);
  float: left;
  min-width: 10rem;
  padding: 0.3125rem 0;
  margin: 0.125rem 0 0;
  box-sizing: border-box;
  font-size: 0.8125rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 0px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 2000!important;
}
.text-phin {
  width: auto;
  display: inline-block;
  margin-right: 5px;
  font-weight: 600;
  text-align: right;
  font-size: 15px;
}
.hide-phin {
  display: none;
}




/* css/app.css */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.alert p {
  margin-bottom: 0;
}
.alert:empty {
  display: none;
}
.invalid-feedback {
  color: #a94442;
  display: block;
  margin: -1rem 0 2rem;
}
.phx-no-feedback.invalid-feedback,
.phx-no-feedback .invalid-feedback {
  display: none;
}
.phx-click-loading {
  opacity: 0.5;
  transition: opacity 1s ease-out;
}
.phx-loading {
  cursor: wait;
}
.phx-modal {
  opacity: 1 !important;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}
.phx-modal-content {
  background-color: #fefefe;
  margin: 15vh auto;
  padding: 20px;
  border: 1px solid #888;
  width: 80%;
}
.phx-modal-close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}
.phx-modal-close:hover,
.phx-modal-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}
.fade-in-scale {
  animation: 0.2s ease-in 0s normal forwards 1 fade-in-scale-keys;
}
.fade-out-scale {
  animation: 0.2s ease-out 0s normal forwards 1 fade-out-scale-keys;
}
.fade-in {
  animation: 0.2s ease-out 0s normal forwards 1 fade-in-keys;
}
.fade-out {
  animation: 0.2s ease-out 0s normal forwards 1 fade-out-keys;
}
@keyframes fade-in-scale-keys {
  0% {
    scale: 0.95;
    opacity: 0;
  }
  100% {
    scale: 1.0;
    opacity: 1;
  }
}
@keyframes fade-out-scale-keys {
  0% {
    scale: 1.0;
    opacity: 1;
  }
  100% {
    scale: 0.95;
    opacity: 0;
  }
}
@keyframes fade-in-keys {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-out-keys {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}