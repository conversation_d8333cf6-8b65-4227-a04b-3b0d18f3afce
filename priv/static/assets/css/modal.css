/* This file is for your main application css. */
@import './phoenix.css';

.custom-modal-container1 {
    position: fixed;
    z-index: 1060;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    grid-template-areas: "top-start     top            top-end""center-start  center         center-end""bottom-start  bottom-center  bottom-end";
    grid-template-rows: minmax(-webkit-min-content, auto) minmax(-webkit-min-content, auto) minmax(-webkit-min-content, auto);
    grid-template-rows: minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);
    height: 100%;
    padding: .625em;
    transition: background-color .1s;
    -webkit-overflow-scrolling: touch;
    background: rgba(0, 0, 0, .4);
    -webkit-animation: custom-modal-show .3s;
    animation: custom-modal-show .3s;
    animation-delay: .25s;
}
.custom-modal-container1.custom-modal-bottom-start,
.custom-modal-container1.custom-modal-center-start,
.custom-modal-container1.custom-modal-top-start {
    grid-template-columns: minmax(0, 1fr) auto auto
}
.custom-modal-container1.custom-modal-bottom,
.custom-modal-container1.custom-modal-center,
.custom-modal-container1.custom-modal-top {
    grid-template-columns: auto minmax(0, 1fr) auto
}
.custom-modal-container1.custom-modal-bottom-end,
.custom-modal-container1.custom-modal-center-end,
.custom-modal-container1.custom-modal-top-end {
    grid-template-columns: auto auto minmax(0, 1fr)
}
.custom-modal-container {
    position: absolute;
    z-index: 1060;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    grid-template-columns: auto minmax(0, 1fr) auto;
    transition: background-color .5s;
    box-sizing: border-box;
    overflow-x: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    -webkit-overflow-scrolling: touch;
    background-color: rgba(0, 0, 0, 0.4);
    -webkit-animation: custom-modal-show .3s;
    animation: custom-modal-show .3s;
    animation-delay: .25s;
}

.custom-modal-container-fill {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
}

.custom-modal-inner-container {
    position: relative;
    z-index: 999;
}

.custom-modal-card {
    height: auto;
    width: 28rem;
    max-width: 51rem;
    margin: 0.5rem;
    padding: 15px;
    border-radius: 0.4rem;
    background-color: #fff;
}

.custom-custom-modal-inner-card {
    padding-top: 1rem;
    padding-bottom: 0.5rem;
    padding-left: 2.25rem;
    padding-right: 2.25rem;
}

.custom-modal-title {
    color: #0069d9;
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
    font-size: 1.5rem;
    letter-spacing: 0.075em;
}
.custom-modal-error-title {
    color: #da9400;;
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
    font-size: 1.5rem;
    letter-spacing: 0.075em;
}

.custom-modal-form-title {
    color: #000;
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
    font-size: 1.5rem;
    letter-spacing: 0.075em;
    margin-bottom: 1.75rem;
}

.custom-modal-body {
    margin-top: 0.8rem;
    text-align: center;
    font-size: 1.6rem;
}

.custom-modal-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1.75rem;
}

.custom-left-button {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}

.custom-right-button {
    margin-left: 0.6rem;
}

.custom-right-button-error {
    margin-left: 0.6rem;
    color: white;
    background-color: blue;
}

.custom-fix-position {
    position: fixed;
    right: 0;
    left: 0;
    overflow: hidden;
}

@-webkit-keyframes custom-modal-show {
     0% {
         transform: scale(.0)
     }
     100% {
         transform: scale(1)
     }
 }