{"version": 3, "sources": ["chartjs.css"], "names": [], "mappings": "AAAA;;;EAGE;AACF;EACC;IAAO,aAAa,EAAA;EACpB;IAAK,UAAU,EAAA,EAAA;AAFhB;EACC;IAAO,aAAa,EAAA;EACpB;IAAK,UAAU,EAAA,EAAA;;AAGhB;EACC,kDAA0C;UAA1C,0CAA0C,EAAA;;AAG3C;;;EAGE;AACF;;;EAGC,kBAAkB;EAClB,cAAc;EACd,OAAO;EACP,MAAM;EACN,QAAQ;EACR,SAAS;EACT,gBAAgB;EAChB,oBAAoB;EACpB,kBAAkB;EAClB,WAAW,EAAA;;AAGZ;EACC,kBAAkB;EAClB,gBAAgB;EAChB,iBAAiB;EACjB,OAAO;EACP,MAAM,EAAA;;AAGP;EACC,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,OAAO;EACP,MAAM,EAAA", "file": "chartjs.css", "sourcesContent": ["/*\n * DOM element rendering detection\n * https://davidwalsh.name/detect-node-insertion\n */\n@keyframes chartjs-render-animation {\n\tfrom { opacity: 0.99; }\n\tto { opacity: 1; }\n}\n\n.chartjs-render-monitor {\n\tanimation: chartjs-render-animation 0.001s;\n}\n\n/*\n * DOM element resizing detection\n * https://github.com/marcj/css-element-queries\n */\n.chartjs-size-monitor,\n.chartjs-size-monitor-expand,\n.chartjs-size-monitor-shrink {\n\tposition: absolute;\n\tdirection: ltr;\n\tleft: 0;\n\ttop: 0;\n\tright: 0;\n\tbottom: 0;\n\toverflow: hidden;\n\tpointer-events: none;\n\tvisibility: hidden;\n\tz-index: -1;\n}\n\n.chartjs-size-monitor-expand > div {\n\tposition: absolute;\n\twidth: 1000000px;\n\theight: 1000000px;\n\tleft: 0;\n\ttop: 0;\n}\n\n.chartjs-size-monitor-shrink > div {\n\tposition: absolute;\n\twidth: 200%;\n\theight: 200%;\n\tleft: 0;\n\ttop: 0;\n}\n"]}