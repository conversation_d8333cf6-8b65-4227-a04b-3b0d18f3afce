$(document).ready(function() {
    // ===================================== EMPLOYEE CREATION===================================
    var selec_d = document.getElementById("district_select");
    var selec_c = document.getElementById("constit_select");
    selec_d.addEventListener("change", e => selec_c.disabled = selec_d.value === "" ? true : false, false);

    // $('#district_select').on('change', function () {
    //     var selected_district = $(this).val();
    //     $('#constit_select').val(null).trigger('change');
    //     $('#constit_select').trigger('change');
    //     selec_c.disabled = false;
    //     if (selected_district == '') {
    //         $('#constit_select').attr('disabled','disabled');
    //         return false;
    //     }

    //     $('#constit_select option').hide();  //hide all options initially
    //     $('#constit_select option.' + selected_district).show();  //show options with the right class   
    // }); 

    // function getAge(dateString) {
    //     var ageInMilliseconds = new Date() - new Date(dateString);
    //     return Math.floor(ageInMilliseconds/1000/60/60/24/365); // convert to years
    // }

    // $.validator.addMethod('val_age', function(value, element, param) {
    //     var age = getAge(value)
    //     return (age < 18)? false : true;
    // }, 'Age limit is 18 and above!');
    
    


    //Put our input DOM element into a jQuery Object
    var $national_id = jQuery('input[name="personalInformation[national_id]"]');
    
    //Bind keyup/keydown to the input
    $national_id.bind('keyup','keydown', function(e){        
    //To accomdate for backspacing, we detect which key was pressed - if backspace, do nothing:
        if(e.which !== 8) {	
            var numChars = $national_id.val().length;
            if(numChars === 6 || numChars === 9){
                var thisVal = $national_id.val();
                thisVal += '/';
                $national_id.val(thisVal);
            }
        }
    });

    // $.validator.addMethod('val_national_id', function(value, element, param) {
    //     return national_id_exist(value);
    // }, 'Provided National ID already exists!');

    function national_id_exist(no){
        var valid = false;
        if($('#nationality').val() != "ZAMBIAN") {
            valid = true;
        } else {
            $.ajax({
                url: "/validate/national_id/no",
                type: 'POST',
                async: false,
                data: {nrc_no: no,
                    _csrf_token: $("#csrf").val()
                },
                success: function(result) {
                    valid = (result.data)? true : false;
                }
            });
        };
        return valid
    }

    // $.validator.addMethod('format_nrc_no', function(value, element, param) {
    //     return valid_nrc_format(value);
    // }, 'Valid NRC format is ******/**/*');

    function valid_nrc_format(no){
        var valid = false;
        if($('#nationality').val() == "ZAMBIAN") {
            $.ajax({
                url: "/format/national_id/no",
                type: 'POST',
                async: false,
                data: {nrc_no: no,
                    _csrf_token: $("#csrf").val()
                },
                success: function(result) {
                    valid = (result.data)? true : false;
                }
            });
        }else {
            valid = true;
        };
        return valid
    }




    // $('.select_constituency').on('change', function() {
    //     $.ajax({
    //         url: "/get/location/details",
    //         type: 'POST',
    //         async: false,
    //         data: {
    //             _csrf_token: $("#csrf").val(),
    //             constituency: $("#constituency_code").val(),
    //         },
    //         success: function(result) {
    //             if (result.data){
    //                 $('#address_line4').val(result.data.district_code);
    //                 $('#district').val(result.data.income_district);
    //                 $('#provinces_code').val(result.data.province_code);               
    //             }else{
    //                 Swal.fire(
    //                     'Sorry..',
    //                     result.error,
    //                     'error'
    //                 )
    //             }
    //         },
    //     });
    // });



    function setProgressBar(curStep){
    var percent = parseFloat(100 / 4) * curStep;
    percent = percent.toFixed();
        $(".progress-bar").css("width",percent+"%");
    }
    
    
    
    
    
    $(".next").click(function(){
        var form = $("#employeeForm");
        // form.validate({
        //     errorElement: 'span',
        //     errorClass: 'help-block',
        //     highlight: function(element, errorClass, validClass) {
        //         $(element).closest('.form-group').addClass("has-error");
        //     },
        //     unhighlight: function(element, errorClass, validClass) {
        //         $(element).closest('.form-group').removeClass("has-error");
        //     },
        //     rules: {
        //         "personalInformation[title]": {
        //             required: true,
        //         },
        //         "personalInformation[marital_status]" : {
        //             required: true,
        //         },
        //         "personalInformation[nationality]": {
        //             required: true,
        //         },
        //         "personalInformation[national_id]": {
        //             required: true,    
        //             minlength: 11,
        //             maxlength: 11,              
        //             val_national_id: true,
        //             format_nrc_no: true,
        //         },
        //         "personalInformation[date_of_birth]": {
        //             required: true,
        //         },
        //         "personalInformation[gender]" : {
        //             required: true,
        //         },
        //         "personalInformation[sector]" : {
        //             required: true,
        //         },
        //         "personalInformation[tpin]":{
        //             minlength:10,
        //             maxlength:10,
        //             digits: true
        //         },
        //         "personalInformation[first_name]":{
        //             required: true,
        //             minlength: 3,
        //         },
        //         "personalInformation[surname]": {
        //             required: true,
        //             minlength: 3,
        //         },
        //         // "personalInformation[monthly_income]": {
        //         //     required: true,
        //         //     digits:true
        //         // },
        //         "contactInformation[address_line1]": {
        //             required: true,
        //         },
        //         "contactInformation[email]": {
        //             // required: true,
        //             email: true,
        //         },                
        //         "contactInformation[address_line2]": {
        //             required: true,
        //         },
        //         "contactInformation[city]": {
        //             required: true,
        //         },
        //         "contactInformation[mobile]": {
        //             required:true,
        //             minlength: mobile_length("MIN"),
        //             maxlength: mobile_length("MAX"),
        //             digits:true
        //         },
        //         "contactInformation[address_line3]": {
        //             required: true,
        //         },
        //         "contactInformation[address_line4]": {
        //             required: true,
        //         },
        //         "contactInformation[address_line5]": {
        //             required: true,
        //         },
        //         "nrc_front": {
        //             required: true,
        //         },
        //         "nrc_back": {
        //             required: true,
        //         },

        //         // Income estimate fields
        //         "income_estimate[education_level]": {
        //             required: true,
        //         },
        //         "income_estimate[employment_status]": {
        //             required: true,
        //         },
        //         "income_estimate[occupation]": {
        //             required: true,
        //         },
        //         "income_estimate[leave]": {
        //             required: true,
        //         },
        //         "income_estimate[district]": {
        //             required: true,
        //         },
        //         "income_estimate[disability]": {
        //             required: true,
        //         },
        //         "income_estimate[industry]": {
        //             required: true,
        //         },
        //         "income_estimate[pension]": {
        //             required: true,
        //         },
        //         // "income_estimate[constituency]": {
        //         //     required: true,
        //         // },
                
                
                
        //     },
        //     messages: {
        //         "personalInformation[title]": {
        //             required: "Title is required",
        //         },
        //         "personalInformation[marital_status]" : {
        //             required: "Marital status is required",
        //         },
        //         "personalInformation[nationality]": {
        //             required: "Nationality is required",
        //         },
        //         "personalInformation[national_id]": {
        //             required: "National ID is required",
        //             minlength:"Minimum length is 11",
        //             maxlength:"Max length is 11",
        //         },
        //         "personalInformation[date_of_birth]": {
        //             required: "Date of Birth is required",
        //         },
        //         "personalInformation[gender]" : {
        //             required: "Gender is required",
        //         },
        //         "personalInformation[sector]" : {
        //             required: "Sector is required",
        //         },
        //         "personalInformation[tpin]": {
        //             minlength:"Minimum length is 10",
        //             maxlength:"Max length is 10",
        //             digits: "Only numbers are allowed in this field"
        //         },
        //         "personalInformation[first_name]": {
        //             required: "First name required",
        //         },
        //         "personalInformation[surname]": {
        //             required: "Lastname required",
        //         },
        //         // "personalInformation[monthly_income]": {
        //         //     required: "Montly income is required",
        //         // },
        //         "contactInformation[address_line1]": {
        //             required: "Plot number is required",
        //         },
        //         "contactInformation[email]": {
        //             // required:   "Email is required",
        //             email:      "Please enter a valid e-mail",
        //         },
        //         "contactInformation[city]": {
        //             required:   "City is required",                    
        //         },
        //         "contactInformation[address_line2]": {
        //             required: "Street name is required",
        //         },
        //         "contactInformation[mobile]": {
        //             required:   "Phone number is requied",
        //             minlength:  mobile_length_msg("MIN"),
        //             maxlength:  mobile_length_msg("MAX"),
        //             digits: "Only numbers are allowed in this field"
        //         },
        //         "contactInformation[address_line3]": {
        //             required: "Constituency is required",
        //         },
        //         "contactInformation[address_line4]": {
        //             required: "District is required",
        //         },
        //         "contactInformation[address_line5]": {
        //             required: "Country of origin is required",
        //         },
        //         "nrc_front": {
        //             required: "Document is required",
        //         },
        //         "nrc_back": {
        //             required: "Document is required",
        //         },


        //         // Income estimate fields
        //         "income_estimate[education_level]": {
        //             required: "Education Level is required",
        //         },
        //         "income_estimate[employment_status]": {
        //             required: "Employment status is required",
        //         },
        //         "income_estimate[occupation]": {
        //             required: "Occupation is required",
        //         },
        //         "income_estimate[leave]": {
        //             required: "Leave is required",
        //         },
        //         "income_estimate[district]": {
        //             required: "District is required",
        //         },
        //         "income_estimate[disability]": {
        //             required: "Disability is required",
        //         },
        //         "income_estimate[industry]": {
        //             required: "Industry is required",
        //         },
        //         "income_estimate[pension]": {
        //             required: "Pension is required",
        //         },
        //         // "income_estimate[constituency]": {
        //         //     required: "Constituency is required",
        //         // },
        //     }
        // });
        
        // if (form.valid() === true){
            var c = document.getElementById('count').textContent;
            document.getElementById('count').textContent = Number(c) + 25;

            if ($('#personal_information').is(":visible")){
                current_fs = $('#personal_information');
                next_fs = $('#contact_information');
                var current = 1;
                setProgressBar(current);
            }else if($('#contact_information').is(":visible")){
                current_fs = $('#contact_information');
                next_fs = $('#attachments');
                var current = 2;
                setProgressBar(current);
            }else if ($('#attachments').is(":visible")){
                current_fs = $('#attachments');
                next_fs = $('#assessment_estimate');
                var current = 3;
                setProgressBar(current);
            }else if ($('#assessment_estimate').is(":visible")){
                current_fs = $('#assessment_estimate');
                summary_details()
                next_fs = $('#employee_summary');
                var current = 4;
                setProgressBar(current);
            }
            
            next_fs.show();
            current_fs.hide();
        // }


        // var date_of_birth = new Date($('#date_of_birth').val());
        // var today = new Date();
        // var age = today.getFullYear() - date_of_birth.getFullYear();
        // if(age < 65){
        //     document.getElementById("member_income_assessment").style.display = "block";
        //     document.getElementById("excepted_personels").style.display = "none";
        //     document.getElementById("exempt").value = false;
        // }else{
        //     document.getElementById("excepted_personels").style.display = "block";
        //     document.getElementById("member_income_assessment").style.display = "none";
        //     document.getElementById("exempt").value = true;
        // }
        validate_date_of_birth($('#date_of_birth').val())   


        if($('#nationality').val() == "ZAMBIAN") {
            $( "#dis_date_of_entry" ).hide()
            $( "#dis_country" ).hide()
        } else {
            $( "#dis_date_of_entry" ).show()
            $( "#dis_country" ).show()
        };
    });

    $('.previous').click(function(){
        var c = document.getElementById('count').textContent;
        document.getElementById('count').textContent = Number(c) - 25;

        if($('#contact_information').is(":visible")){
            current_fs = $('#contact_information');
            next_fs = $('#personal_information');
            var current = 0;
            setProgressBar(current);
        }else if ($('#attachments').is(":visible")){
            current_fs = $('#attachments');
            next_fs = $('#contact_information');
            var current = 1;
            setProgressBar(current);
        }else if ($('#assessment_estimate').is(":visible")){
            current_fs = $('#assessment_estimate');
            next_fs = $('#attachments');
            var current = 2;
            setProgressBar(current);
        }else if ($('#employee_summary').is(":visible")){
            current_fs = $('#employee_summary');
            next_fs = $('#assessment_estimate');
            var current = 3;
            setProgressBar(current);            
        }
        next_fs.show();
        current_fs.hide();
    }); 

    

    function validate_date_of_birth(dob) {     
        $.ajax({
            url: "/validate/date-of-birth",
            type: 'POST',
            async: false,
            data: {dob: dob,
                _csrf_token: $("#csrf").val()
            },
            success: function(result) {                
                valid = result.data
                if(valid == false){
                    document.getElementById("member_income_assessment").style.display = "block";
                    document.getElementById("excepted_personels").style.display = "none";
                    document.getElementById("exempt").value = false;
                }else{
                    document.getElementById("excepted_personels").style.display = "block";
                    document.getElementById("member_income_assessment").style.display = "none";
                    document.getElementById("exempt").value = true;
                }
            }
        });
    }



    function mobile_length(type) {
        if(type == 'MIN') {
            if($('#nationality').val() == 'ZAMBIAN') {
                return 12;
            } else {
                return 5;
            }
        }else {
            if($('#nationality').val() == 'ZAMBIAN') {
                return 12;
            } else {
                return 15;
            }
        }
    }


    function mobile_length_msg(type) {
        if(type == 'MIN') {
            if($('#nationality').val() == 'ZAMBIAN') {
                return "Please enter 12 digit mobile number";
            } else {
                return "Please enter atleast 5 digit mobile number";
            }
        }else {
            if($('#nationality').val() == 'ZAMBIAN') {
                return "Please enter 12 digit mobile number";
            } else {
                return "Please enter 15 digit mobile number";
            }
        }
    }



    $('#nationality').change(function () {
        if ($(this).val() == 'ZAMBIAN') {
            $('.district-wrapper').show();
            $('.country_wrapper').hide();
            $('#mobile').val("260");
        } else {
            $('.district-wrapper').hide();
            $('.country_wrapper').show();
        }
    }); 
    
    

    function display_summary_details(data) {

        // ---------------------- Personal Details ---------------------------      
        $('#summary_summary_title').html(data.title);
        $('#summary_middle_name').html(data.middle_name);
        $('#summary_nationality').html(data.nationality);
        $('#summary_tpin').html(data.tpin);
        $('#summary_sector').html(data.sector);
        $('#summary_monthly_income').html(data.monthly_income);
        $('#summary_first_name').html(data.first_name);
        $('#summary_last_name').html(data.last_name);
        $('#summary_national_id').html(data.national_id);
        $('#summary_marital_status').html(data.m_marital_status);
        $('#summary_gender').html(data.m_gender);
        $('#summary_date_of_birth').html(data.date_of_birth);

        // ---------------------- Contact & Address --------------------------
        $('#summary_plot_number').html(data.plot_number);
        $('#summary_email').html(data.email);
        $('#summary_country').html(data.country);
        $('#summary_constituency').html(data.constituency);
        $('#summary_street_name').html(data.street_name);
        $('#summary_agent').html(data.agent);
        $('#summary_mobile').html(data.mobile);
        $('#summary_district').html(data.m_district);
        $('#summary_entry_date').html(data.entry_date);

        // ----------------------- Member Income Assessment -------------------
        $('#summary_education_level').html(data.education);
        $('#summary_employment_status').html(data.employment);
        $('#summary_occupation').html(data.m_occupation);
        $('#summary_leave').html(data.m_leave);
        $('#summary_disability').html(data.m_disability);
        $('#summary_industry').html(data.m_industry);
        $('#summary_pension').html(data.m_pension);
        $('#summary_districtt').html(data.m_district);
    }
    


    function registration_by(type) {
        if (type == 'by_member') {
            return "/member/details/summary";
        } else {
            return "/agent/members/details/summary";
        }
    } 


    function summary_details() {
        // $.blockUI();
        var type= $('#type').val()
        var url = registration_by(type)        
        $.ajax({
            type: "POST",
            url: url,
            dataType: "json",
            data: {
                _csrf_token: $("#csrf").val(),
                title: $('#title').val(),
                middle_name: $('#middle_name').val(),
                nationality: $('#nationality').val(),
                tpin: $('#tpin').val(),
                sector: $('#sector').val(),
                monthly_income: $('#monthly_income').val(),
                first_name: $('#first_name').val(),
                last_name: $('#last_name').val(),
                national_id: $('#national_id').val(),
                marital_status: $('#marital_status').val(),
                gender: $('#gender').val(),
                date_of_birth: $('#date_of_birth').val(),

                plot_number: $('#address_line1').val(),
                email: $('#email').val(),
                country: $('#address_line5').val(),
                constituency_code: $('#constit_select').find('option:selected').val(),
                street_name: $('#address_line2').val(),
                agent_code: $('#agent_code').val(),
                mobile: $('#mobile').val(),
                district: $('#district_select').find('option:selected').val(),
                entry_date: $('#entry_date').val(),

                education_level: $('#education_level').val(),
                employment_status: $('#employment_status').val(),
                occupation: $('#occupation').val(),
                leave: $('#leave').val(),
                disability: $('#disability').val(),
                industry: $('#industry').val(),
                pension: $('#pension').val(),
                income_district: $('#district').val(),

            },
            success: function(data) {
                if(data.data){
                    display_summary_details(data.data)
                }
                else{
                    Swal.fire(
                        'Sorry..',
                        data.error,
                        'error'
                    ) 
                }
            },
        });
    }

});