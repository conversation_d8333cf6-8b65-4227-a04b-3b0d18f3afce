


$(document).ready(function() {
    $.ajax({
        url: "/Admin/dashboard/stats",
        type: 'GET',
        data: {
            _csrf_token: $('#csrf').val()
        },
        success: function(result) {
            if (result.data){  
                phin(result.data)
                    
            }else{
                console.log("failed");
            }
        },
        error: function(request, msg, error) {
            console.log("failed terribly");
        }
    });



    // function phin(data) {   
    
    //     Highcharts.chart('container', {
    //         title: {
    //             text: 'TRANSACTION HISTORY',
    //             align: 'left'
    //         },
    //         xAxis: {
    //             categories: data.dash_chart.categories
    //         },
    //         yAxis: {
    //             title: {
    //                 text: 'TRANSACTIONS VALUE'
    //             }
    //         },
    //         tooltip: {
    //             // valueSuffix: ' million liters'
    //             valueSuffix: '<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b> ({point.percentage:.0f}%)<br/>',
    //         },
    //         plotOptions: {
    //             series: {
    //                 borderRadius: '25%'
    //             }
    //         },
    //         series: data.dash_chart.series
    //     });  
    // }



    function phin(data) {    
    
        Highcharts.chart('container', {
            title: {
                text: 'Sales of petroleum products March, Norway',
                align: 'left'
            },
            xAxis: {
                categories: ['Jet fuel', 'Duty-free diesel', 'Petrol', 'Diesel', 'Gas oil']
            },
            yAxis: {
                title: {
                    text: 'Million liters'
                }
            },
            tooltip: {
                valueSuffix: ' million liters'
            },
            plotOptions: {
                series: {
                    borderRadius: '25%'
                }
            },
            series: [{
                type: 'column',
                name: '2020',
                data: [59, 83, 65, 228, 184]
            }, {
                type: 'column',
                name: '2021',
                data: [24, 79, 72, 240, 167]
            }, {
                type: 'column',
                name: '2022',
                data: [58, 88, 75, 250, 176]
            },{
                type: 'pie',
                name: 'Total',
                data: [{
                    name: '2020',
                    y: 619,
                    color: Highcharts.getOptions().colors[0], // 2020 color
                    dataLabels: {
                        enabled: true,
                        distance: -50,
                        format: '{point.total} M',
                        style: {
                            fontSize: '15px'
                        }
                    }
                }, {
                    name: '2021',		
                    y: 586,
                    color: Highcharts.getOptions().colors[1] // 2021 color
                }, {
                    name: '2022',
                    y: 647,
                    color: Highcharts.getOptions().colors[2] // 2022 color
                }],
                center: [75, 65],
                size: 100,
                innerSize: '70%',
                showInLegend: false,
                dataLabels: {
                    enabled: false
                }
            }]
        });
    }
});



    

